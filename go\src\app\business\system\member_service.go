package system

import (
	"fincore/model"
	"fincore/utils/gform"
	"fmt"
	"strings"
)

// MemberService 成员管理服务层
type MemberService struct{}

// GetMemberList 获取成员列表
// @param businessID 业务主账号ID，用于数据隔离
// @return 成员列表数据和错误信息
func (s *MemberService) GetMemberList(businessID int64) ([]gform.Data, error) {
	// 1. 构建查询，只查询必要字段以优化性能
	query := model.DB().Table("business_account").
		Fields(`
			id, username, name, nickname, avatar, tel, mobile, email,
			status, dept_id, createtime, updatetime, remark,
			company, city, address, province, area
		`).
		Where("businessID", businessID)

	// 2. 按创建时间倒序排列，确保新成员在前
	query = query.Order("createtime DESC")

	// 3. 执行查询
	memberList, err := query.Get()
	if err != nil {
		return nil, fmt.Errorf("查询成员数据失败: %v", err)
	}

	// 4. 如果没有数据，返回空数组而不是nil
	if memberList == nil {
		memberList = make([]gform.Data, 0)
	}

	// 5. 处理查询结果，补充关联信息
	err = s.enrichMemberData(memberList)
	if err != nil {
		return nil, fmt.Errorf("处理成员数据失败: %v", err)
	}

	return memberList, nil
}

// enrichMemberData 丰富成员数据，添加角色、部门等关联信息
// @param memberList 成员列表数据
// @return 错误信息
func (s *MemberService) enrichMemberData(memberList []gform.Data) error {
	// 1. 获取系统根URL，用于拼接头像地址
	rootURL, err := s.getRootURL()
	if err != nil {
		return fmt.Errorf("获取根URL失败: %v", err)
	}

	// 2. 遍历处理每个成员数据
	for _, member := range memberList {
		// 2.1 处理头像URL
		s.processAvatarURL(member, rootURL)

		// 2.2 获取成员角色信息
		err = s.getMemberRoles(member)
		if err != nil {
			return fmt.Errorf("获取成员角色失败: %v", err)
		}

		// 2.3 获取成员部门信息
		err = s.getMemberDepartment(member)
		if err != nil {
			return fmt.Errorf("获取成员部门失败: %v", err)
		}
	}

	return nil
}

// getRootURL 获取系统根URL配置
// @return 根URL字符串和错误信息
func (s *MemberService) getRootURL() (string, error) {
	rootURL, err := model.DB().Table("common_config").
		Where("keyname", "rooturl").
		Value("keyvalue")

	if err != nil {
		return "", err
	}

	if rootURL == nil {
		return "", nil
	}

	return rootURL.(string), nil
}

// processAvatarURL 处理头像URL，确保返回完整的访问地址
// @param member 成员数据
// @param rootURL 系统根URL
func (s *MemberService) processAvatarURL(member gform.Data, rootURL string) {
	avatar := member["avatar"]

	// 如果没有头像，使用默认头像
	if avatar == nil || avatar == "" {
		if rootURL != "" {
			member["avatar"] = rootURL + "resource/staticfile/avatar.png"
		} else {
			member["avatar"] = "resource/staticfile/avatar.png"
		}
		return
	}

	// 如果头像地址不包含http且有根URL，则拼接完整地址
	avatarStr := avatar.(string)
	if !strings.Contains(avatarStr, "http") && rootURL != "" {
		member["avatar"] = rootURL + avatarStr
	}
}

// getMemberRoles 获取成员角色信息
// @param member 成员数据
// @return 错误信息
func (s *MemberService) getMemberRoles(member gform.Data) error {
	memberID := member["id"]
	if memberID == nil {
		return nil
	}

	// 1. 获取成员的角色ID列表
	roleIDs, err := model.DB().Table("business_auth_role_access").
		Where("uid", memberID).
		Pluck("role_id")

	if err != nil {
		return err
	}

	// 2. 如果没有角色，设置空值
	if roleIDs == nil {
		member["roleid"] = make([]interface{}, 0)
		member["rolename"] = make([]interface{}, 0)
		return nil
	}

	// 3. 根据角色ID获取角色名称
	roleNames, err := model.DB().Table("business_auth_role").
		WhereIn("id", roleIDs.([]interface{})).
		Pluck("name")

	if err != nil {
		return err
	}

	// 4. 设置角色信息
	member["roleid"] = roleIDs
	if roleNames == nil {
		member["rolename"] = make([]interface{}, 0)
	} else {
		member["rolename"] = roleNames
	}

	return nil
}

// getMemberDepartment 获取成员部门信息
// @param member 成员数据
// @return 错误信息
func (s *MemberService) getMemberDepartment(member gform.Data) error {
	deptID := member["dept_id"]
	if deptID == nil {
		member["depname"] = ""
		return nil
	}

	// 查询部门名称
	deptName, err := model.DB().Table("business_auth_dept").
		Where("id", deptID).
		Value("name")

	if err != nil {
		return err
	}

	// 设置部门名称
	if deptName == nil {
		member["depname"] = ""
	} else {
		member["depname"] = deptName.(string)
	}

	return nil
}
