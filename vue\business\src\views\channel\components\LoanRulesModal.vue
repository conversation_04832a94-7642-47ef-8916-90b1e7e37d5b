<template>
  <a-modal
    v-model:visible="modalVisible"
    title="放款规则管理"
    :width="1000"
    @cancel="handleCancel"
  >
    <div class="loan-rules-container">
      <!-- 产品规则选择 -->
      <div class="product-rules-section">
        <h4>产品规则列表</h4>
        <a-table
          :columns="productColumns"
          :data-source="productRules"
          :pagination="false"
          :row-selection="productSelection"
          size="small"
          :scroll="{ y: 200 }"
        />
      </div>

      <!-- 放款规则配置 -->
      <div class="loan-rules-section">
        <div class="section-header">
          <h4>放款规则配置</h4>
          <a-button type="primary" size="small" @click="addLoanRule">
            添加规则
          </a-button>
        </div>
        
        <a-table
          :columns="loanColumns"
          :data-source="loanRules"
          :pagination="false"
          size="small"
          :scroll="{ y: 300 }"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'product_rule'">
              <div class="product-rule-info">
                <div class="rule-name">{{ record.product_rule?.rule_name || '未选择产品规则' }}</div>
                <div class="rule-details" v-if="record.product_rule">
                  <span>额度: {{ record.product_rule.loan_amount }}元</span>
                  <span>周期: {{ record.product_rule.loan_period }}天</span>
                  <span>利率: {{ record.product_rule.annual_interest_rate }}%</span>
                </div>
              </div>
            </template>
            <template v-else-if="column.key === 'risk_score'">
              <div class="risk-score-range">
                {{ record.min_risk_score }} - {{ record.max_risk_score }}
              </div>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="editLoanRule(index)">
                  编辑
                </a-button>
                <a-button type="link" size="small" danger @click="removeLoanRule(index)">
                  删除
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 编辑规则弹窗 -->
    <a-modal
      v-model:visible="editModalVisible"
      title="编辑放款规则"
      :width="600"
      @ok="handleEditSubmit"
      @cancel="handleEditCancel"
    >
      <a-form
        ref="editFormRef"
        :model="editForm"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        :rules="editRules"
      >
        <a-form-item label="产品规则" name="rule_id">
          <a-select
            v-model:value="editForm.rule_id"
            placeholder="请选择产品规则"
            show-search
            :filter-option="filterProductOption"
          >
            <a-option
              v-for="rule in productRules"
              :key="rule.id"
              :value="rule.id"
            >
              {{ rule.rule_name }} ({{ rule.loan_amount }}元/{{ rule.loan_period }}天)
            </a-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="风控阈值下限" name="min_risk_score">
          <a-input-number
            v-model:value="editForm.min_risk_score"
            placeholder="请输入风控阈值下限"
            :min="0"
            :max="100"
            :precision="2"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="风控阈值上限" name="max_risk_score">
          <a-input-number
            v-model:value="editForm.max_risk_score"
            placeholder="请输入风控阈值上限"
            :min="0"
            :max="100"
            :precision="2"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSubmit" :loading="submitLoading">
          保存
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue';
// import { message } from 'ant-design-vue';
import {Message} from "@arco-design/web-vue";
import { 
  getProductRules,
  getChannelLoanRules,
  updateChannelLoanRules,
  type LoanRule,
  type ProductRule
} from '@/api/channel';

interface Props {
  visible: boolean;
  channelId: number;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  channelId: 0,
});

const emit = defineEmits<Emits>();

// 弹窗显示状态
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 数据状态
const productRules = ref<ProductRule[]>([]);
const loanRules = ref<LoanRule[]>([]);
const submitLoading = ref(false);

// 产品规则表格列定义
const productColumns = [
  {
    title: '规则名称',
    dataIndex: 'rule_name',
    key: 'rule_name',
  },
  {
    title: '贷款额度',
    dataIndex: 'loan_amount',
    key: 'loan_amount',
    customRender: ({ text }: { text: number }) => `${text}元`,
  },
  {
    title: '贷款周期',
    dataIndex: 'loan_period',
    key: 'loan_period',
    customRender: ({ text }: { text: number }) => `${text}天`,
  },
  {
    title: '年利率',
    dataIndex: 'annual_interest_rate',
    key: 'annual_interest_rate',
    customRender: ({ text }: { text: number }) => `${text}%`,
  },
  {
    title: '还款方式',
    dataIndex: 'repayment_method',
    key: 'repayment_method',
  },
];

// 放款规则表格列定义
const loanColumns = [
  {
    title: '产品规则',
    key: 'product_rule',
    width: 300,
  },
  {
    title: '风控阈值范围',
    key: 'risk_score',
    width: 150,
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
  },
];

// 产品规则选择
const productSelection = reactive({
  selectedRowKeys: [] as number[],
  onChange: (selectedRowKeys: number[]) => {
    productSelection.selectedRowKeys = selectedRowKeys;
  },
});

// 编辑弹窗状态
const editModalVisible = ref(false);
const editFormRef = ref();
const editForm = reactive({
  rule_id: undefined as number | undefined,
  min_risk_score: 0,
  max_risk_score: 100,
});

// 编辑表单验证规则
const editRules = {
  rule_id: [
    { required: true, message: '请选择产品规则', trigger: 'change' },
  ],
  min_risk_score: [
    { required: true, message: '请输入风控阈值下限', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '风控阈值下限必须在0-100之间', trigger: 'blur' },
  ],
  max_risk_score: [
    { required: true, message: '请输入风控阈值上限', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '风控阈值上限必须在0-100之间', trigger: 'blur' },
  ],
};

// 当前编辑的规则索引
const currentEditIndex = ref(-1);

// 初始化数据
onMounted(async () => {
  await loadProductRules();
});

// 加载产品规则
const loadProductRules = async () => {
  try {
    const response = await getProductRules();
    productRules.value = response.data || [];
  } catch (error) {
    console.error('加载产品规则失败:', error);
    Message.error('加载产品规则失败');
  }
};

// 加载放款规则
const loadLoanRules = async () => {
  if (!props.channelId) return;
  
  try {
    const response = await getChannelLoanRules(props.channelId);
    loanRules.value = response.data || [];
  } catch (error) {
    console.error('加载放款规则失败:', error);
    Message.error('加载放款规则失败');
  }
};

// 监听弹窗打开
const handleModalOpen = async () => {
  if (props.visible && props.channelId) {
    await loadLoanRules();
  }
};

// 产品规则搜索过滤
const filterProductOption = (input: string, option: any) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 添加放款规则
const addLoanRule = () => {
  currentEditIndex.value = -1;
  editForm.rule_id = undefined;
  editForm.min_risk_score = 0;
  editForm.max_risk_score = 100;
  editModalVisible.value = true;
};

// 编辑放款规则
const editLoanRule = (index: number) => {
  currentEditIndex.value = index;
  const rule = loanRules.value[index];
  editForm.rule_id = rule.rule_id;
  editForm.min_risk_score = rule.min_risk_score;
  editForm.max_risk_score = rule.max_risk_score;
  editModalVisible.value = true;
};

// 删除放款规则
const removeLoanRule = (index: number) => {
  loanRules.value.splice(index, 1);
};

// 编辑表单提交
const handleEditSubmit = async () => {
  try {
    await editFormRef.value.validate();
    
    const newRule: LoanRule = {
      rule_id: editForm.rule_id!,
      min_risk_score: editForm.min_risk_score,
      max_risk_score: editForm.max_risk_score,
    };

    if (currentEditIndex.value >= 0) {
      // 编辑现有规则
      loanRules.value[currentEditIndex.value] = newRule;
    } else {
      // 添加新规则
      loanRules.value.push(newRule);
    }

    editModalVisible.value = false;
    Message.success('规则保存成功');
  } catch (error) {
    console.error('保存规则失败:', error);
  }
};

// 编辑表单取消
const handleEditCancel = () => {
  editModalVisible.value = false;
  editFormRef.value?.clearValidate();
};

// 提交所有规则
const handleSubmit = async () => {
  try {
    submitLoading.value = true;
    await updateChannelLoanRules(props.channelId, loanRules.value);
    Message.success('放款规则保存成功');
    emit('success');
    modalVisible.value = false;
  } catch (error) {
    console.error('保存放款规则失败:', error);
    Message.error('保存放款规则失败');
  } finally {
    submitLoading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  modalVisible.value = false;
};

// 监听弹窗状态
watch(() => props.visible, handleModalOpen);
</script>

<style scoped lang="less">
.loan-rules-container {
  .product-rules-section {
    margin-bottom: 24px;
    
    h4 {
      margin-bottom: 12px;
      color: #1890ff;
    }
  }
  
  .loan-rules-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      
      h4 {
        margin: 0;
        color: #1890ff;
      }
    }
  }
}

.product-rule-info {
  .rule-name {
    font-weight: 500;
    margin-bottom: 4px;
  }
  
  .rule-details {
    font-size: 12px;
    color: #666;
    
    span {
      margin-right: 8px;
    }
  }
}

.risk-score-range {
  font-family: monospace;
  color: #1890ff;
}
</style> 