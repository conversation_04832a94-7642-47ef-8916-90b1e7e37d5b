package sumpay

// 字段配置文件 - 定义每个API接口的加密和编码字段列表
// 每个API接口都有三个对应的方法：
// - GetXxxAesEncodedWords() - 获取需要AES加密的字段
// - GetXxxBase64EncodedWords() - 获取需要Base64编码的字段
// - GetXxxCharsetChangeWords() - 获取需要字符集转换的字段

// ==================== 对私代付接口 ====================

// GetPrivateAgentPayAesEncodedWords 获取对私代付需要AES加密的字段列表
func GetPrivateAgentPayAesEncodedWords() []string {
	return []string{"realname", "id_no", "card_no"}
}

// GetPrivateAgentPayBase64EncodedWords 获取对私代付需要Base64编码的字段列表
func GetPrivateAgentPayBase64EncodedWords() []string {
	return []string{"terminal_info", "notify_url", "pay_comments", "remark", "longitude", "latitude"}
}

// GetPrivateAgentPayCharsetChangeWords 获取对私代付需要字符集转换的字段列表
func GetPrivateAgentPayCharsetChangeWords() []string {
	return []string{"terminal_info", "realname", "pay_comments", "remark"}
}

// ==================== 转账到卡订单查询接口 ====================
func GetDisbursementQueryAesEncodedWords() []string {
	return []string{}
}

func GetDisbursementQueryBase64EncodedWords() []string {
	return []string{}
}

func GetDisbursementQueryCharsetChangeWords() []string {
	return []string{}
}

// ==================== 快捷支付订单申请接口 ====================

// GetQuickPayOrderApplyAesEncodedWords 获取快捷支付订单申请需要AES加密的字段列表
func GetQuickPayOrderApplyAesEncodedWords() []string {
	// 参考Java版本: card_no, realname, id_no, mobile_no, cvv, valid_year, valid_month
	return []string{"card_no", "realname", "id_no", "mobile_no", "cvv", "valid_year", "valid_month"}
}

// GetQuickPayOrderApplyBase64EncodedWords 获取快捷支付订单申请需要Base64编码的字段列表
func GetQuickPayOrderApplyBase64EncodedWords() []string {
	// 参考Java版本: terminal_info, notify_url, goods_name, remark等
	return []string{"terminal_info", "notify_url", "goods_name", "remark", "longitude", "latitude"}
}

// GetQuickPayOrderApplyCharsetChangeWords 获取快捷支付订单申请需要字符集转换的字段列表
func GetQuickPayOrderApplyCharsetChangeWords() []string {
	// 参考Java版本: terminal_info, goods_name, realname, remark
	return []string{"terminal_info", "goods_name", "realname", "remark"}
}

// ==================== 条码支付接口 ====================

// GetBarCodePayAesEncodedWords 获取条码支付需要AES加密的字段列表
func GetBarCodePayAesEncodedWords() []string {
	// 条码支付一般不需要加密敏感字段
	return []string{}
}

// GetBarCodePayBase64EncodedWords 获取条码支付需要Base64编码的字段列表
func GetBarCodePayBase64EncodedWords() []string {
	// 参考Java版本: terminal_info, notify_url, goods_name, remark等
	return []string{"terminal_info", "notify_url", "goods_name", "remark", "longitude", "latitude"}
}

// GetBarCodePayCharsetChangeWords 获取条码支付需要字符集转换的字段列表
func GetBarCodePayCharsetChangeWords() []string {
	// 参考Java版本: terminal_info, goods_name, remark
	return []string{"terminal_info", "goods_name", "remark"}
}

// ==================== 查询企业客户信息接口 ====================

// GetQueryCustomerInfoAesEncodedWords 获取查询企业客户信息需要AES加密的字段列表
func GetQueryCustomerInfoAesEncodedWords() []string {
	// TODO: 根据API文档填写具体字段
	return []string{}
}

// GetQueryCustomerInfoBase64EncodedWords 获取查询企业客户信息需要Base64编码的字段列表
func GetQueryCustomerInfoBase64EncodedWords() []string {
	// TODO: 根据API文档填写具体字段
	return []string{}
}

// GetQueryCustomerInfoCharsetChangeWords 获取查询企业客户信息需要字符集转换的字段列表
func GetQueryCustomerInfoCharsetChangeWords() []string {
	// TODO: 根据API文档填写具体字段
	return []string{}
}

// ==================== 个人客户信息上传接口 ====================

// GetAddOrUpdatePersonalCustomerAesEncodedWords 获取个人客户信息上传需要AES加密的字段列表
func GetAddOrUpdatePersonalCustomerAesEncodedWords() []string {
	// TODO: 根据API文档填写具体字段
	return []string{}
}

// GetAddOrUpdatePersonalCustomerBase64EncodedWords 获取个人客户信息上传需要Base64编码的字段列表
func GetAddOrUpdatePersonalCustomerBase64EncodedWords() []string {
	// TODO: 根据API文档填写具体字段
	return []string{}
}

// GetAddOrUpdatePersonalCustomerCharsetChangeWords 获取个人客户信息上传需要字符集转换的字段列表
func GetAddOrUpdatePersonalCustomerCharsetChangeWords() []string {
	// TODO: 根据API文档填写具体字段
	return []string{}
}

// ==================== 交易订单申请接口 ====================

// GetTradeOrderApplyAesEncodedWords 获取交易订单申请需要AES加密的字段列表
func GetTradeOrderApplyAesEncodedWords() []string {
	// TODO: 根据API文档填写具体字段
	return []string{}
}

// GetTradeOrderApplyBase64EncodedWords 获取交易订单申请需要Base64编码的字段列表
func GetTradeOrderApplyBase64EncodedWords() []string {
	// TODO: 根据API文档填写具体字段
	return []string{}
}

// GetTradeOrderApplyCharsetChangeWords 获取交易订单申请需要字符集转换的字段列表
func GetTradeOrderApplyCharsetChangeWords() []string {
	// TODO: 根据API文档填写具体字段
	return []string{}
}

// ==================== 一键签约接口 ====================

// GetSignSendMessageAesEncodedWords 获取一键签约需要AES加密的字段列表
func GetSignSendMessageAesEncodedWords() []string {
	return []string{"card_no", "mobile_no", "realname", "id_no", "verify_code"}
}

// GetSignSendMessageBase64EncodedWords 获取一键签约需要Base64编码的字段列表
func GetSignSendMessageBase64EncodedWords() []string {
	return []string{"time_remark", "notify_url", "goods_name"}
}

// GetSignSendMessageCharsetChangeWords 获取一键签约需要字符集转换的字段列表
func GetSignSendMessageCharsetChangeWords() []string {
	// TODO: 根据API文档填写具体字段
	return []string{}
}

// ==================== 银联订单支付接口 ====================

// GetOrderPayApplyAesEncodedWords 获取银联订单支付需要AES加密的字段列表
func GetOrderPayApplyAesEncodedWords() []string {
	// 参考Java版本: payer_acct_name, payer_acct_no, payee_acct_name, payee_acct_no
	return []string{"payer_acct_name", "payer_acct_no", "payee_acct_name", "payee_acct_no"}
}

// GetOrderPayApplyBase64EncodedWords 获取银联订单支付需要Base64编码的字段列表
func GetOrderPayApplyBase64EncodedWords() []string {
	// 参考Java版本: payer_info, payee_info, pay_comments, remark, notify_url, terminal_info等
	return []string{"payer_info", "payee_info", "pay_comments", "remark", "notify_url", "terminal_info", "longitude", "latitude"}
}

// GetOrderPayApplyCharsetChangeWords 获取银联订单支付需要字符集转换的字段列表
func GetOrderPayApplyCharsetChangeWords() []string {
	// 参考Java版本: terminal_info
	return []string{"terminal_info"}
}

// ==================== 退款接口 ====================

// GetRefundAesEncodedWords 获取退款需要AES加密的字段列表
func GetRefundAesEncodedWords() []string {
	// 退款接口一般不需要加密敏感字段
	return []string{}
}

// GetRefundBase64EncodedWords 获取退款需要Base64编码的字段列表
func GetRefundBase64EncodedWords() []string {
	// 退款接口的notify_url需要base64编码
	return []string{"notify_url"}
}

// GetRefundCharsetChangeWords 获取退款需要字符集转换的字段列表
func GetRefundCharsetChangeWords() []string {
	// 退款接口一般不需要字符集转换
	return []string{}
}

// ==================== 退款查询接口 ====================

// GetRefundSearchAesEncodedWords 获取退款查询需要AES加密的字段列表
func GetRefundSearchAesEncodedWords() []string {
	// 退款查询接口一般不需要加密敏感字段
	return []string{}
}

// GetRefundSearchBase64EncodedWords 获取退款查询需要Base64编码的字段列表
func GetRefundSearchBase64EncodedWords() []string {
	// 退款查询接口一般不需要base64编码
	return []string{}
}

// GetRefundSearchCharsetChangeWords 获取退款查询需要字符集转换的字段列表
func GetRefundSearchCharsetChangeWords() []string {
	// 退款查询接口一般不需要字符集转换
	return []string{}
}
