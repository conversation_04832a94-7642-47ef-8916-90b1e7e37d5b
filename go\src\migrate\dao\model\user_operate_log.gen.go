// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameUserOperateLog = "user_operate_log"

// UserOperateLog mapped from table <user_operate_log>
type UserOperateLog struct {
	ID          uint32     `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	UID         *int32     `gorm:"column:uid;type:int;index:idx_uid_created_at,priority:1;comment:用户id" json:"uid"`                         // 用户id
	OperateName *string    `gorm:"column:operate_name;type:varchar(50);index:idx_operate_name,priority:1;comment:操作名称" json:"operate_name"` // 操作名称
	Result      *string    `gorm:"column:result;type:varchar(10);comment:结果" json:"result"`                                                 // 结果
	IP          *string    `gorm:"column:ip;type:varchar(128);comment:IP地址" json:"ip"`                                                      // IP地址
	Region      *string    `gorm:"column:region;type:varchar(128);comment:归属地" json:"region"`                                               // 归属地
	Detail      *string    `gorm:"column:detail;type:varchar(512);comment:操作详情" json:"detail"`                                              // 操作详情
	CreatedAt   *time.Time `gorm:"column:created_at;type:datetime;index:idx_uid_created_at,priority:2;comment:创建时间" json:"created_at"`      // 创建时间
}

// TableName UserOperateLog's table name
func (*UserOperateLog) TableName() string {
	return TableNameUserOperateLog
}
