<template>
  <view class="webview-container">
    <web-view :src="url" @message="handleMessage" allow="camera *;autoplay *;"></web-view>
  </view>
</template>

<script>
import user from '@/store/user.js';
import userApi from '@/api/user.js';
	import {
		storeToRefs
	} from 'pinia';
export default {
  data() {
    return {
      url: "",
	  product_id: '',
	  channel_code: '',
	  loan_amount: ''
    };
  },
  onLoad(options) {
    this.url = decodeURIComponent(options.url || "");
    // console.log("加载URL:", this.url);
  },
  methods: {
    async handleMessage(e) {
      console.log("收到webview消息:", e);
      let contract_no = e.detail.data[0];
	  this.channel_code = uni.getStorageSync('cid');
	  this.product_id = uni.getStorageSync('product_id');
	  this.loan_amount = uni.getStorageSync('loan_amount');
	  
	  if (contract_no) {
	  	// 合同签约成功调用接口
	  	userApi.queryContractStatus({
	  		contract_no: contract_no
	  	}).then(res => {
	  		if(res.code == 0) {
	  			if(res.data == 2) {
	  				this.updateContract(contract_no);
	  			}else{
	  				uni.redirectTo({
	  					url: '/pages/Agreement/Agreement?product_id='+uni.getStorageSync('product_id')
	  				});
	  			}
	  		}
	  	})
	  }
    },
	updateContract(c_no) {
		const userStore = user();
		const { userInfo } = storeToRefs(userStore);
		userApi.updateContract({
			user_id: userInfo.value.uid,
			contract_no: c_no
		}).then(res => {
			// 创建订单
			userApi.createOrder({
				channel_code: this.channel_code,
				product_rule_id: this.product_id,
				loan_amount: this.loan_amount,
				customer_origin: 'H5'
			}).then(res => {
				console.log('创建订单', res)
				uni.reLaunch({
					url: '/pages/webview/htSucceed'
				})
			})
		})
		
	},
  },
};
</script>

<style>
.webview-container {
  width: 100%;
  height: 100vh;
}
</style>
