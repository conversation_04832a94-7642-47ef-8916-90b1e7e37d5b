package common

import (
	"fincore/model"
	"fincore/route/middleware"
	"fincore/utils/gf"
	"fincore/utils/oss"
	"fincore/utils/results"
	"reflect"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

func init() {
	gf.Register(&FileAccess{}, reflect.TypeOf(FileAccess{}).PkgPath())
}

type FileAccess struct {
}

// GetFileURL 获取文件的安全访问URL
func (api *FileAccess) GetFileURL(c *gin.Context) {
	// 验证用户信息
	user, exists := c.Get("user")
	if !exists {
		results.Failed(c, "用户不存在", nil)
		return
	}
	userInfo, ok := user.(middleware.UserClaims)
	if !ok {
		results.Failed(c, "用户信息不正确", nil)
		return
	}

	// 获取文件ID
	fileIdStr := c.Param("fileId")
	if fileIdStr == "" {
		results.Failed(c, "文件ID不能为空", nil)
		return
	}

	fileId, err := strconv.ParseInt(fileIdStr, 10, 64)
	if err != nil {
		results.Failed(c, "文件ID格式错误", nil)
		return
	}

	// 查询文件信息
	var fileInfo map[string]interface{}
	fileInfo, err = model.DB().Table("attachment").Where("id", fileId).First()
	if err != nil {
		results.Failed(c, "文件不存在", nil)
		return
	}

	// 验证文件访问权限
	fileUserId, _ := fileInfo["uid"].(int64)
	if fileUserId != userInfo.ID {
		results.Failed(c, "无权限访问该文件", nil)
		return
	}

	// 获取文件URL
	fileURL, exists := fileInfo["url"].(string)
	if !exists || fileURL == "" {
		results.Failed(c, "文件URL不存在", nil)
		return
	}

	// 如果启用了OSS，生成签名URL
	if oss.IsOSSEnabled() {
		ossClient, err := oss.GetOSSClient()
		if err != nil {
			results.Failed(c, "OSS客户端初始化失败", nil)
			return
		}

		// 从URL中提取ObjectKey
		objectKey := oss.GetObjectKeyFromURL(fileURL)

		// 生成15分钟有效期的签名URL
		signedURL, err := ossClient.GetSignedURL(objectKey, time.Minute*10)
		if err != nil {
			results.Failed(c, "生成签名URL失败", nil)
			return
		}

		results.Success(c, "获取文件URL成功", gin.H{
			"url":        signedURL,
			"expires_in": 600, //10分钟
			"file_id":    fileId,
			"filename":   fileInfo["name"],
		}, nil)
	} else {
		// 本地文件直接返回URL
		results.Success(c, "获取文件URL成功", gin.H{
			"url":      fileURL,
			"file_id":  fileId,
			"filename": fileInfo["name"],
		}, nil)
	}
}
