package config

import (
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"fincore/utils/log"

	"github.com/spf13/viper"
)

// SchedulerConfig 定时任务调度器配置结构
type SchedulerConfig struct {
	Scheduler SchedulerSettings `yaml:"scheduler" mapstructure:"scheduler"`
}

// SchedulerSettings 调度器基础设置
type SchedulerSettings struct {
	Name              string            `yaml:"name" mapstructure:"name"`
	Timezone          string            `yaml:"timezone" mapstructure:"timezone"`
	MaxConcurrentJobs int               `yaml:"max_concurrent_jobs" mapstructure:"max_concurrent_jobs"`
	DefaultTimeout    time.Duration     `yaml:"default_timeout" mapstructure:"default_timeout"`
	ForceKillTimeout  time.Duration     `yaml:"force_kill_timeout" mapstructure:"force_kill_timeout"`
	Concurrency       ConcurrencyConfig `yaml:"concurrency" mapstructure:"concurrency"`

	// 优雅退出配置
	CancelRunningTasks bool `yaml:"cancel_running_tasks" mapstructure:"cancel_running_tasks"` // 是否批量取消运行的任务
	ShutdownTimeout    int  `yaml:"shutdown_timeout" mapstructure:"shutdown_timeout"`         // 退出等待超时时间（秒）
}

// ConcurrencyConfig 并发控制配置
type ConcurrencyConfig struct {
	EnableParallel     bool `yaml:"enable_parallel" mapstructure:"enable_parallel"`
	MaxParallelPerTask int  `yaml:"max_parallel_per_task" mapstructure:"max_parallel_per_task"`
}

// 全局配置实例
var (
	instance *SchedulerConfig
	once     sync.Once
	mutex    sync.RWMutex
	logger   *log.Logger
)

// GetConfig 获取配置实例（单例模式）
func GetConfig() *SchedulerConfig {
	once.Do(func() {
		instance = loadConfig()
	})
	return instance
}

// ReloadConfig 重新加载配置
func ReloadConfig() *SchedulerConfig {
	mutex.Lock()
	defer mutex.Unlock()

	instance = loadConfig()
	if logger != nil {
		logger.Info("调度器配置已重新加载")
	}
	return instance
}

// SetLogger 设置日志器
func SetLogger(l *log.Logger) {
	logger = l
}

// loadConfig 加载配置文件
func loadConfig() *SchedulerConfig {
	// 获取项目根路径
	path, err := os.Getwd()
	if err != nil {
		panic(fmt.Sprintf("获取工作目录失败: %v", err))
	}

	resourcePath := filepath.Join(path, "resource")

	// 创建 viper 实例
	v := viper.New()
	v.AddConfigPath(resourcePath)
	v.SetConfigType("yml")

	// 根据环境变量选择配置文件
	configName := "scheduler"
	if os.Getenv("ENV") == "dev" {
		configName = "scheduler_dev"
	}
	v.SetConfigName(configName)

	// 读取配置文件
	if err := v.ReadInConfig(); err != nil {
		panic(fmt.Sprintf("读取调度器配置文件失败: %v", err))
	}

	// 解析配置
	var config SchedulerConfig
	if err := v.Unmarshal(&config); err != nil {
		panic(fmt.Sprintf("解析调度器配置失败: %v", err))
	}

	// 验证配置
	if err := validateConfig(&config); err != nil {
		panic(fmt.Sprintf("调度器配置验证失败: %v", err))
	}

	if logger != nil {
		logger.Info("调度器配置加载成功",
			log.String("config_file", configName+".yml"),
			log.String("scheduler_name", config.Scheduler.Name),
		)
	}

	return &config
}

// validateConfig 验证配置
func validateConfig(config *SchedulerConfig) error {
	// 验证调度器名称
	if config.Scheduler.Name == "" {
		return fmt.Errorf("调度器名称不能为空")
	}

	// 验证时区
	if config.Scheduler.Timezone == "" {
		config.Scheduler.Timezone = "Asia/Shanghai"
	}

	// 验证并发数
	if config.Scheduler.MaxConcurrentJobs <= 0 {
		config.Scheduler.MaxConcurrentJobs = 5
	}

	// 验证退出超时时间
	if config.Scheduler.ShutdownTimeout <= 0 {
		config.Scheduler.ShutdownTimeout = 30 // 默认30秒
	}

	return nil
}

// IsDevMode 判断是否为开发模式
func (c *SchedulerConfig) IsDevMode() bool {
	return os.Getenv("ENV") == "dev"
}

// GetConfigSummary 获取配置摘要信息
func (c *SchedulerConfig) GetConfigSummary() map[string]interface{} {
	return map[string]interface{}{
		"scheduler_name":        c.Scheduler.Name,
		"timezone":              c.Scheduler.Timezone,
		"max_concurrent":        c.Scheduler.MaxConcurrentJobs,
		"default_timeout":       c.Scheduler.DefaultTimeout,
		"force_kill_timeout":    c.Scheduler.ForceKillTimeout,
		"enable_parallel":       c.Scheduler.Concurrency.EnableParallel,
		"max_parallel_per_task": c.Scheduler.Concurrency.MaxParallelPerTask,
		"dev_mode":              c.IsDevMode(),
	}
}
