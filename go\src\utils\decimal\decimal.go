package decimal

import (
	"database/sql/driver"
	"fmt"
	"strconv"
)

// Decimal 自定义decimal类型，用于处理数据库的decimal字段
// 主要用于金融计算，避免浮点数精度丢失问题
type Decimal float64

// Value 实现driver.Valuer接口，用于数据库写入
func (d Decimal) Value() (driver.Value, error) {
	return float64(d), nil
}

// Scan 实现sql.Scanner接口，用于数据库读取
func (d *Decimal) Scan(value interface{}) error {
	if value == nil {
		*d = 0
		return nil
	}
	switch v := value.(type) {
	case float64:
		*d = Decimal(v)
	case []byte:
		// 处理从数据库读取的字节数据
		if len(v) == 0 {
			*d = 0
		} else {
			// 字符串到float64的转换
			if f, err := strconv.ParseFloat(string(v), 64); err == nil {
				*d = Decimal(f)
			} else {
				*d = 0
			}
		}
	case string:
		// 处理字符串类型
		if f, err := strconv.ParseFloat(v, 64); err == nil {
			*d = Decimal(f)
		} else {
			*d = 0
		}
	}
	return nil
}

// MarshalJSON 实现json.Marshaler接口，用于JSON序列化
func (d Decimal) MarshalJSON() ([]byte, error) {
	return []byte(fmt.Sprintf("%.2f", float64(d))), nil
}

// UnmarshalJSON 实现json.Unmarshaler接口，用于JSON反序列化
func (d *Decimal) UnmarshalJSON(data []byte) error {
	// 移除引号
	str := string(data)
	if len(str) >= 2 && str[0] == '"' && str[len(str)-1] == '"' {
		str = str[1 : len(str)-1]
	}

	// 解析浮点数
	f, err := strconv.ParseFloat(str, 64)
	if err != nil {
		return err
	}

	*d = Decimal(f)
	return nil
}

// Float64 转换为float64类型
func (d Decimal) Float64() float64 {
	return float64(d)
}

// String 转换为字符串，保留2位小数
func (d Decimal) String() string {
	return fmt.Sprintf("%.2f", float64(d))
}

// StringWithPrecision 转换为字符串，指定小数位数
func (d Decimal) StringWithPrecision(precision int) string {
	format := fmt.Sprintf("%%.%df", precision)
	return fmt.Sprintf(format, float64(d))
}

// IsZero 判断是否为零
func (d Decimal) IsZero() bool {
	return float64(d) == 0
}

// IsPositive 判断是否为正数
func (d Decimal) IsPositive() bool {
	return float64(d) > 0
}

// IsNegative 判断是否为负数
func (d Decimal) IsNegative() bool {
	return float64(d) < 0
}

// Add 加法运算
func (d Decimal) Add(other Decimal) Decimal {
	return Decimal(float64(d) + float64(other))
}

// Sub 减法运算
func (d Decimal) Sub(other Decimal) Decimal {
	return Decimal(float64(d) - float64(other))
}

// Mul 乘法运算
func (d Decimal) Mul(other Decimal) Decimal {
	return Decimal(float64(d) * float64(other))
}

// Div 除法运算
func (d Decimal) Div(other Decimal) Decimal {
	if other.IsZero() {
		return Decimal(0) // 避免除零错误
	}
	return Decimal(float64(d) / float64(other))
}

// Equal 判断是否相等
func (d Decimal) Equal(other Decimal) bool {
	return float64(d) == float64(other)
}

// GreaterThan 判断是否大于
func (d Decimal) GreaterThan(other Decimal) bool {
	return float64(d) > float64(other)
}

// LessThan 判断是否小于
func (d Decimal) LessThan(other Decimal) bool {
	return float64(d) < float64(other)
}

// GreaterThanOrEqual 判断是否大于等于
func (d Decimal) GreaterThanOrEqual(other Decimal) bool {
	return float64(d) >= float64(other)
}

// LessThanOrEqual 判断是否小于等于
func (d Decimal) LessThanOrEqual(other Decimal) bool {
	return float64(d) <= float64(other)
}

// Abs 绝对值
func (d Decimal) Abs() Decimal {
	if d.IsNegative() {
		return Decimal(-float64(d))
	}
	return d
}

// Round 四舍五入到指定小数位数
func (d Decimal) Round(precision int) Decimal {
	multiplier := 1.0
	for i := 0; i < precision; i++ {
		multiplier *= 10
	}
	return Decimal(float64(int(float64(d)*multiplier+0.5)) / multiplier)
}

// NewDecimal 创建新的Decimal实例
func NewDecimal(value float64) Decimal {
	return Decimal(value)
}

// NewDecimalFromString 从字符串创建Decimal实例
func NewDecimalFromString(value string) (Decimal, error) {
	f, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return Decimal(0), err
	}
	return Decimal(f), nil
}

// MustNewDecimalFromString 从字符串创建Decimal实例，失败时返回零值
func MustNewDecimalFromString(value string) Decimal {
	d, _ := NewDecimalFromString(value)
	return d
}

// Zero 返回零值
func Zero() Decimal {
	return Decimal(0)
}

// One 返回1
func One() Decimal {
	return Decimal(1)
}

// Sum 计算多个Decimal的和
func Sum(decimals ...Decimal) Decimal {
	var result Decimal
	for _, d := range decimals {
		result = result.Add(d)
	}
	return result
}

// Max 返回最大值
func Max(a, b Decimal) Decimal {
	if a.GreaterThan(b) {
		return a
	}
	return b
}

// Min 返回最小值
func Min(a, b Decimal) Decimal {
	if a.LessThan(b) {
		return a
	}
	return b
}

// FormatAmount 格式化金额为两位小数
func FormatAmount(amount float64) float64 {
	return NewDecimal(amount).Round(2).Float64()
}
