---
alwaysApply: true
---
# 组件系统指南

## 组件概览

本项目采用模块化的组件设计，各子项目中的组件结构相似。组件被分为以下几类：

- 基础组件 (Basic) - 最基本的UI构建块
- 业务组件 - 特定业务场景的复合组件
- 布局组件 - 处理页面布局的组件

## 组件目录

### Admin 和 Business 子项目

这两个子项目的组件结构几乎相同：

- [business/src/components](mdc:business/src/components) - 业务系统组件
- [admin/src/components](mdc:admin/src/components) - 管理系统组件

主要组件目录包括：

```
components/
  ├── Basic/             # 基础组件
  ├── breadcrumb/        # 面包屑导航
  ├── chart/             # 图表组件
  ├── footer/            # 页脚组件
  ├── global-setting/    # 全局设置组件
  ├── Icon/              # 图标组件
  ├── menu/              # 菜单组件
  ├── message-box/       # 消息提示框
  ├── Modal/             # 模态框组件
  ├── navbar/            # 导航栏组件
  └── tab-bar/           # 标签栏组件
```

### UniApp 子项目

UniApp 子项目主要使用 uni-modules 模式组织组件：

- [uniapp/uni_modules](mdc:uniapp/uni_modules) - UniApp模块目录

## 关键组件

### 布局组件

布局组件定义了页面的基本结构：

- [business/src/layout/default-layout.vue](mdc:business/src/layout/default-layout.vue) - 默认布局
- [business/src/layout/page-layout.vue](mdc:business/src/layout/page-layout.vue) - 页面布局

### 导航组件

- [business/src/components/navbar](mdc:business/src/components/navbar) - 顶部导航
- [business/src/components/menu](mdc:business/src/components/menu) - 侧边菜单

### 特殊组件

业务系统中的一些特殊组件：

- [business/src/components/CodeEditor](mdc:business/src/components/CodeEditor) - 代码编辑器
- [business/src/components/Editor](mdc:business/src/components/Editor) - 富文本编辑器
- [business/src/components/gfeditor](mdc:business/src/components/gfeditor) - 特定编辑器组件

## 组件注册

组件通过 [business/src/components/index.ts](mdc:business/src/components/index.ts) 或 [admin/src/components/index.ts](mdc:admin/src/components/index.ts) 进行全局注册。
