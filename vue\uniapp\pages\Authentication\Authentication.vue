<template>
	<view class="upload-container">
		<!-- 顶部步骤条 -->
		<view class="step-bar">
			<view class="step-item" :class="{ active: true }">
				<image class="step-icon" src="/static/icon/20250613-194921.png"></image>
				<text class="step-text">身份认证</text>
			</view>
			<view class="step-line"></view>
			<view class="step-item">
				<image class="step-icon" src="/static/icon/20250613-194917.png"></image>
				<text class="step-text">基本信息</text>
			</view>
		</view>
		<view class="upload-box-a">
			<view class="upload-box">
				<!-- 主标题 -->
				<view class="main-title">身份认证</view>
				<!-- 身份证上传区域 -->
				<view class="id-card-upload">
					<view class="upload-item">
						<view class="upload-box" @click="chooseFile('front')">
							<image v-if="frontImage" :src="frontImage" mode="aspectFit" class="id-card-image"></image>
							<view v-else class="upload-placeholder">
								<!-- 人像面占位图 -->
								<image src="/static/icon/id-card-icon.png.png" mode="widthFix" class="upload-icon">
								</image>
							</view>
						</view>
						<view class="upload-desc">点击上传人像面</view>
					</view>

					<view class="upload-item">
						<view class="upload-box" @click="chooseFile('back')">
							<image v-if="backImage" :src="backImage" mode="aspectFit" class="id-card-image"></image>
							<view v-else class="upload-placeholder">
								<!-- 国徽面占位图 -->
								<image src="/static/icon/lock.svg.png" mode="widthFix" class="upload-icon"></image>
							</view>
						</view>
						<view class="upload-desc">点击上传国徽面</view>
					</view>
				</view>

				<!-- 温馨提示 -->
				<view class="tips">
					<text class="tips-title">温馨提示：</text>
					<text class="tips-text"><text
							style="color: red;">实名认证不影响大数据；</text>请上传本人身份证照片，非本人信息无法通过审核；确保证件边框完整，文字清晰可见；可现场拍摄或从手机相册获取。</text>
				</view>

				<!-- 身份信息自动识别区域 -->
				<view class="info-section">
					<view class="info-item">
						<text class="info-label">姓名</text>
						<input class="info-input" v-model="name" placeholder="上传身份证后自动识别" disabled />
					</view>
					<view class="info-item">
						<text class="info-label">身份证号</text>
						<input class="info-input" v-model="id_card" placeholder="上传身份证后自动识别" disabled />
					</view>
				</view>
			</view>
			<!-- 协议勾选 -->
			<view class="agreement">
				<checkbox-group @change="handleAgreementChange">
					<checkbox color="#258ceb" :checked="agreementChecked" style="transform: scale(0.5)" />
				</checkbox-group>
				<text class="agreement-text">我已阅读并同意<text @click="book" style="color: blue;">《实名认证授权同意书》</text><text
						@click="qian" style="color: blue;">《电子签名协议》</text></text>
			</view>
			<!-- 确认上传按钮 -->
			<view class="confirm-btn" :class="{ disabled: !canSubmit || !agreementChecked }" @click="confirmUpload">
				<text>下一步</text>
			</view>
			
		</view>

	</view>
</template>


<script>
	import uploadApi from "@/api/upload";
	import settingUp from "@/api/settingUp.js";
	import user from '@/store/user.js';
	
	
	
	export default {
		data() {
			return {
				frontImage: "", // 人像面已上传图片
				backImage: "", // 国徽面已上传图片
				name: "", // 识别出的姓名
				id_card: "", // 识别出的身份证号
				agreementChecked: false, // 协议勾选状态
			};
		},
		computed: {
			canSubmit() {
				// 资料完整判断：人像、国徽都上传 + 姓名和身份证号识别到
				console.log('计算canSubmit状态:',
					this.frontImage, this.backImage, this.name, this.id_card);
				return this.frontImage && this.backImage && this.name && this.id_card;
			},
		},
		methods: {
			// 选择身份证照片（区分人像面/国徽面）
			chooseFile(type) {
				uni.chooseImage({
					count: 1,
					sizeType: ["compressed"],
					sourceType: ["album", "camera"],
					success: (res) => {
						const tempFile = res.tempFilePaths[0];
						// 判断文件大小
						const fileInfo = res.tempFiles && res.tempFiles[0];
						if (fileInfo && fileInfo.size > 1048576) { // 1M = 1048576字节
							uni.showModal({
								title: '提示',
								content: '图片大小不能超过1M，请重新选择',
								showCancel: false
							});
							return;
						}
						if (type === "front") {
							this.frontImage = tempFile;
							// 上传 + 识别流程
							this.uploadAndRecognize(tempFile, "front");
						} else {
							this.backImage = tempFile;
							this.uploadAndRecognize(tempFile, "back");
						}
					},
					fail: () => {
						uni.showToast({
							title: "选择文件失败",
							icon: "none"
						});
					},
				});
			},

			// 上传图片并调用识别接口
			async uploadAndRecognize(filePath, side) {
				uni.showLoading({
					title: "识别中...",
					mask: true
				});
				try {
					// 先上传文件到服务器
					const uploadRes = await uploadApi.uploadFile(filePath);
					// 再调用身份证识别接口
					const recognizeRes = await settingUp.postIdentity({
						file: uploadRes.data.url,
						side: side,
					});
					console.log('识别接口返回数据:', recognizeRes); // 调试日志
					if (recognizeRes.code === 0 && recognizeRes.data) {
						if (side === "front") {
							// 人像面优先更新完整信息
							this.name = recognizeRes.data.realname || this.name; // 保留已有值
							this.id_card = recognizeRes.data.idcard || this.id_card; // 保留已有值
						} else {
							// 国徽面尝试更新信息（如果接口支持）
							if (!this.name) { // 如果姓名还未识别到
								this.name = recognizeRes.data.realname || ""; // 尝试获取
							}
							if (!this.id_card) { // 如果身份证号还未识别到
								this.id_card = recognizeRes.data.idcard || ""; // 尝试获取
							}
						}

						// 检查是否已获取完整信息
						if (this.name && this.id_card) {
							console.log('已获取完整身份信息:', this.name, this.id_card);
						} else {
							console.log('身份信息不完整，可能需要上传另一面:',
								this.name ? '姓名已获取' : '姓名未获取',
								this.id_card ? '身份证号已获取' : '身份证号未获取');
						}

						uni.showToast({
							title: "识别成功",
							icon: "success"
						});
					} else {
						if (side === "front") {
							this.frontImage = ''
						}else{
							this.backImage = ''
						}
						throw new Error(recognizeRes.message || "识别失败");
					}
				} catch (error) {
					console.error('识别过程出错:', error); // 调试日志
					uni.showToast({
						title: error.message || "识别失败",
						icon: "none"
					})
				} finally {
					uni.hideLoading();
				}
			},

			// 提交认证（含活体检测跳转）
			async confirmUpload() {
				console.log('确认上传时状态:',
					this.canSubmit, this.agreementChecked,
					this.frontImage, this.backImage, this.name, this.id_card);

				if (!this.canSubmit || !this.agreementChecked) {
					const tip = !this.canSubmit ?
						"请完成身份证上传和识别(可能需要上传两面)" :
						"请阅读并同意相关协议";
					uni.showToast({
						title: tip,
						icon: "none"
					});
					return;
				}

				const userStore = user();
				const uid = userStore.userInfo?.uid;

				uni.showLoading({
					title: "准备活体验证..."
				});
				try {
					// 调用活体检测准备接口，带上uid
					const params = {
						realname: this.name,
						id_card: this.id_card,
						uid: uid
					};
					// 打印参数
					console.log('提交身份认证参数:', params);

					const verifyRes = await uploadApi.postFaceAuth(params);

					// 在跳转前，将用户信息保存到 store
					if (verifyRes.code === 0 && verifyRes.data?.faceUrl) {
						userStore.setUserInfo({
							...userStore.userInfo, // 保留store中已有的其他信息
							name: this.name,
							id_card: this.id_card
						});

						// 跳转活体检测页面
						this.openFaceVerify(verifyRes.data?.faceUrl);
					} else {
						throw new Error(verifyRes.message || "活体验证准备失败");
					}
				} catch (error) {
					uni.hideLoading();
					uni.showToast({
						title: error.message || "活体验证失败",
						icon: "none"
					});
				}
			},

			// 跳转活体检测页面（web-view 方式）
			openFaceVerify(faceUrl) {
				uni.hideLoading();
				// console.log('人脸',faceUrl)
				// #ifdef WEB
				window.location.href = faceUrl
				// uni.navigateTo({
				// 	url: "/pages/webview/webview?url="+encodeURIComponent(faceUrl)
				// })
				// #endif
				
				// #ifdef APP
				const appAuthorizeSetting = uni.getAppAuthorizeSetting()
				// console.log(appAuthorizeSetting.cameraAuthorized)
				if(appAuthorizeSetting.cameraAuthorized == 'authorized') {
					// console.log("已获得摄像头权限")
					uni.navigateTo({
						url: "/pages/webview/webview?url="+encodeURIComponent(faceUrl)
					})
				}else{
					uni.showModal({
						title: "提示",
						content: "摄像头未授权开启，是否跳转设置页'权限管理->相机'设置允许",
						success: () => {
							uni.openAppAuthorizeSetting()
						}
					})
				}
				// #endif
			},
			// 检测是否
			// 协议勾选状态变更
			handleAgreementChange(e) {
				// this.agreementChecked = e.detail.checked;
				this.agreementChecked = e.detail.value.length > 0;
			},
			book() {
				uni.navigateTo({
					url: "/pages/Authentication/Authbook",
				});
			},
			qian() {
				uni.navigateTo({
					url: "/pages/Authentication/Authqian",
				});
			}
		},
	};
</script>

<style lang="scss">
	.upload-container {
		max-width: 480px;
		margin: 0 auto;
		padding: 32rpx 20rpx 20rpx 20rpx;
		font-family: "Microsoft YaHei", Arial, Helvetica, sans-serif;
		background-color: #f5f7fa;
		min-height: 100vh;
		box-sizing: border-box;

		// 步骤条样式
		.step-bar {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 24rpx;

			.step-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				margin: 0 32rpx;

				.step-icon {
					width: 64rpx;
					height: 64rpx;
					margin-bottom: 8rpx;
				}

				.step-text {
					font-size: 22rpx;
					color: #999;
					font-family: inherit;
				}

				&.active .step-text {
					color: #258ceb;
				}
			}

			.step-line {
				flex: 0.7;
				height: 2rpx;
				background-color: #ccc;
				margin: 0 8rpx;
			}
		}

		.upload-box-a {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
		}

		.upload-box {
			width: 100%;
			max-width: 420px;
			background-color: #fff;
			border-radius: 16rpx;
			box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
			padding: 32rpx 20rpx 24rpx 20rpx;
			box-sizing: border-box;
			margin-bottom: 24rpx;

			.main-title {
				font-size: 36rpx;
				margin-left: 0;
				margin-top: 0;
				font-weight: bold;
				color: #333;
				text-align: center;
				margin-bottom: 32rpx;
				font-family: inherit;
			}

			.id-card-upload {
				margin-top: 0;
				display: flex;
				justify-content: space-between;
				gap: 16rpx;
				margin-bottom: 24rpx;
				box-sizing: border-box;
				.upload-item {
					flex: 1 1 0;
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-left: -4px;

					.upload-desc {
						font-size: 24rpx;
						color: #222;
						font-weight: 500;
						margin-top: 8rpx;
						font-family: inherit;
					}

					.upload-box {
						width: 100%;
						max-width: 170px;
						height: 120px;
						background-color: #f9f9f9;
						border-radius: 12rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						overflow: hidden;
						border: 1rpx dashed #ddd;

						.id-card-image {
							width: 100%;
							height: 100%;
							object-fit: cover;
						}

						.upload-placeholder {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 100%;
							.upload-icon {
								width: 100%;
							}
						}
					}
				}
			}

			.tips {
				margin-bottom: 20rpx;
				margin-left: 0;

				.tips-title {
					font-size: 13px;
					color: #999;
					font-weight: bold;
					display: block;
					margin-bottom: 6rpx;
					font-family: inherit;
				}

				.tips-text {
					font-size: 13px;
					color: #999;
					line-height: 36rpx;
					font-family: inherit;
				}
			}

			.info-section {
				margin-top: 16rpx;
				margin-left: 0;

				.info-item {
					display: flex;
					align-items: center;
					margin-bottom: 16rpx;

					.info-label {
						width: 110rpx;
						font-size: 26rpx;
						color: #333;
						font-family: inherit;
					}

					.info-input {
						flex: 1;
						min-width: 0;
						height: 64rpx;
						border: 1rpx solid #eee;
						border-radius: 8rpx;
						padding: 0 16rpx;
						font-size: 26rpx;
						background-color: #f9f9f9;
						font-family: inherit;
					}
				}
			}
		}

		.agreement {
			width: 100%;
			display: flex;
			align-items: center;
			font-size: 12px;
			color: #666;
			margin-left: 0;
			margin-top: 8px;
			white-space: nowrap;

			.agreement-text {
				margin-left: 8rpx;
				line-height: 1.6;
				white-space: nowrap;
			}
		}

		.confirm-btn {
			margin-top: 32rpx;
			background-color: #258ceb;
			color: #fff;
			height: 80rpx;
			border-radius: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			font-family: inherit;
			width: 100%;
			max-width: 420px;
			box-shadow: 0 2px 8px rgba(37, 140, 235, 0.08);
			transition: background 0.2s;

			&:active {
				opacity: 0.8;
			}

			&.disabled {
				background-color: #ccc;
				pointer-events: none;
			}
		}
	}

	// PC端适配
	@media (min-width: 700px) {
		.upload-container {
			max-width: 520px;
			padding: 48px 0 32px 0;
			border-radius: 18px;
			box-shadow: 0 4px 32px rgba(0, 0, 0, 0.08);

			.upload-box {
				max-width: 480px;
				padding: 40px 32px 32px 32px;
			}

			.confirm-btn {
				max-width: 480px;
			}
		}
	}

	// 小屏适配
	@media (max-width: 350px) {
		.upload-container {
			padding: 8rpx 2rpx;

			.upload-box {
				padding: 12rpx 2rpx;
			}

			.main-title {
				font-size: 28rpx;
			}

			.confirm-btn {
				font-size: 22rpx;
				height: 60rpx;
			}
		}
	}
</style>