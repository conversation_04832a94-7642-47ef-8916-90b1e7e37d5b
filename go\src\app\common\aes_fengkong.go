package common

import (
	"bytes"
	"crypto/aes"
	"encoding/base64"
	"encoding/json"
	"fmt"
)

type UserData struct {
	Name        string `json:"name"`
	IdNo 		string `json:"idNo"`
	Mobile      string `json:"mobile"`
}

func CreateJsonString(Name, identNumber, Mobile string) string {
	// 创建结构体实例
	var data UserData
	data.Name = Name
	data.IdNo = identNumber
	data.Mobile = Mobile

	// 转换为JSON字符串
	//fmt.Printf("%+v\n", data)
	jsonData, err := json.Marshal(data)
	if err != nil {
		return ""
	}
	//fmt.Println("实例结果:", string(jsonData))
	return string(jsonData)
}

// Encrypt 使用AES/ECB/PKCS5Padding加密
func Encrypt(input string, key string) (string, error) {
	// 检查密钥长度 (AES-128需要16字节)
	if len(key) != 16 {
		return "", fmt.Errorf("密钥长度必须为16字节")
	}

	// 转换输入和密钥为字节
	plaintext := []byte(input)
	keyBytes := []byte(key)

	// 创建AES加密块
	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		return "", fmt.Errorf("创建AES加密块失败: %v", err)
	}

	// PKCS5填充
	plaintext = PKCS5Padding(plaintext, block.BlockSize())

	// ECB模式加密 (Go标准库没有直接支持ECB，需要手动实现)
	ciphertext := make([]byte, len(plaintext))
	for bs, be := 0, block.BlockSize(); bs < len(plaintext); bs, be = bs+block.BlockSize(), be+block.BlockSize() {
		block.Encrypt(ciphertext[bs:be], plaintext[bs:be])
	}

	// Base64编码
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// PKCS5Padding 实现PKCS5填充 (实际在AES中与PKCS7相同)
func PKCS5Padding(src []byte, blockSize int) []byte {
	padding := blockSize - len(src)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(src, padtext...)
}

func Decrypt(encrypted string, key string) (string, error) {
	// 检查密钥长度
	if len(key) != 16 {
		return "", fmt.Errorf("密钥长度必须为16字节")
	}

	// Base64解码
	ciphertext, err := base64.StdEncoding.DecodeString(encrypted)
	if err != nil {
		return "", fmt.Errorf("Base64解码失败: %v", err)
	}

	// 创建AES解密块
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", fmt.Errorf("创建AES解密块失败: %v", err)
	}

	// ECB模式解密
	plaintext := make([]byte, len(ciphertext))
	for bs, be := 0, block.BlockSize(); bs < len(ciphertext); bs, be = bs+block.BlockSize(), be+block.BlockSize() {
		block.Decrypt(plaintext[bs:be], ciphertext[bs:be])
	}

	// PKCS5去填充
	plaintext = PKCS5Unpadding(plaintext)

	return string(plaintext), nil
}

// PKCS5Unpadding 去除PKCS5填充
func PKCS5Unpadding(src []byte) []byte {
	length := len(src)
	if length == 0 {
		return src
	}
	padding := int(src[length-1])
	if padding > length {
		return src
	}
	return src[:length-padding]
}

func main() {
	key := "1111111111111111" // 16字节密钥
	plaintext := CreateJsonString("邢芳", "340207198612030041", "18755369152")

	encrypted, err := Encrypt(plaintext, key)
	if err != nil {
		fmt.Println("加密失败:", err)
		return
	}

	fmt.Println("加密结果:", encrypted)
	// dencrypted := "nuHJfjOHt8lGhsKYSdR3n1c0S0E5pEtInXVAt599XptzkEpnIsaeAhAj6Cqu0oIGcvrJnIiznMREjrXEWRWSzSlwEQSFuK98fXiN5vcznfk"
}