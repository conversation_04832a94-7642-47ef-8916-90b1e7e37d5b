export default {
  'menu.ordermanagement': 'Order Management',
  'menu.ordermanagement.list': 'Order List',
  'menu.ordermanagement.Orderlist': 'Order List',
  'menu.ordermanagement.detail': 'Order Detail',
  'ordermanagement.form.orderNo': 'Order No',
  'ordermanagement.form.userName': 'Customer Name',
  'ordermanagement.form.userIdCard': 'ID Card',
  'ordermanagement.form.userMobile': 'Mobile',
  'ordermanagement.form.orderStatus': 'Order Status',
  'ordermanagement.form.auditAssigneeName': 'Reviewer',
  'ordermanagement.form.channelSource': 'Channel Source',
  'ordermanagement.form.initialOrderChannel': 'Initial Channel',
  'ordermanagement.form.createdTime': 'Created Time',
  'ordermanagement.form.search': 'Search',
  'ordermanagement.form.reset': 'Reset',
  'ordermanagement.table.id': 'Order ID',
  'ordermanagement.table.paymentChannel': 'Payment Channel',
  'ordermanagement.table.orderNo': 'Order No',
  'ordermanagement.table.channelSource': 'Channel Source',
  'ordermanagement.table.customerSource': 'Customer Source',
  'ordermanagement.table.initialChannel': 'Initial Channel',
  'ordermanagement.table.reviewStatus': 'Review Status',
  'ordermanagement.table.complaintStatus': 'Complaint Status',
  'ordermanagement.table.amountPaid': 'Amount Paid',
  'ordermanagement.table.name': 'Name',
  'ordermanagement.table.mobile': 'Mobile',
  'ordermanagement.table.riskScore': 'Risk Score',
  'ordermanagement.table.createdTime': 'Created Time',
  'ordermanagement.table.orderStatus': 'Order Status',
  'ordermanagement.table.closureType': 'Closure Type',
  'ordermanagement.table.loanPeriod': 'Loan Period',
  'ordermanagement.table.totalPeriods': 'Total Periods',
  'ordermanagement.table.operation': 'Operation',
  'ordermanagement.button.process': 'Process',
  'ordermanagement.button.editChannel': 'Edit Channel',
  'ordermanagement.button.refresh': 'Refresh',
  'ordermanagement.status.pending': 'Pending',
  'ordermanagement.status.processing': 'Processing',
  'ordermanagement.status.closed': 'Closed',
  'ordermanagement.status.completed': 'Completed',
  'ordermanagement.review.pending': 'Pending',
  'ordermanagement.review.approved': 'Approved',
  'ordermanagement.review.rejected': 'Rejected',
  'ordermanagement.modal.editChannel.title': 'Edit Channel',
  'ordermanagement.modal.editChannel.select': 'Select Channel',
  'ordermanagement.modal.editChannel.required': 'Please select channel',
  'ordermanagement.modal.detail.title': 'Order Detail',
  'ordermanagement.message.editSuccess': 'Channel updated successfully',
  'ordermanagement.message.editFailed': 'Failed to update channel',
  'ordermanagement.message.getDetailFailed': 'Failed to get order detail',
  'ordermanagement.detail.title': 'Order Detail',
  'ordermanagement.detail.loading': 'Loading order details...',
  'ordermanagement.detail.backToList': 'Back to List',
  'ordermanagement.detail.addRemark': 'Add Remark',
  'ordermanagement.detail.confirmCancel': 'Confirm Exit',
  'ordermanagement.detail.dataWillNotBeSaved': 'If you exit, your input will not be saved. Are you sure you want to exit?',
  'ordermanagement.detail.remarkEmpty': 'Remark content cannot be empty',
  'ordermanagement.detail.remarkSuccess': 'Remark added successfully',
  'ordermanagement.detail.remarkFailed': 'Failed to add remark',
  'ordermanagement.detail.remarkPlaceholder': 'Please enter remark content (max 1000 characters)',
  
  // Close order related
  'ordermanagement.detail.closeOrder': 'Close Order',
  'ordermanagement.detail.closeReasonType': 'Rejection Type',
  'ordermanagement.detail.closeReason': 'Order Closure Remark (Optional)',
  'ordermanagement.detail.selectCloseReasonType': 'Please select rejection type',
  'ordermanagement.detail.closeReasonPlaceholder': 'Please enter order closure remark (optional, max 1000 characters)',
  'ordermanagement.detail.closeOrderSuccess': 'Order closed successfully',
  'ordermanagement.detail.closeOrderFailed': 'Failed to close order',
}; 