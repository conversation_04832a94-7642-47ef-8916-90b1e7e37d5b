package jsonschema

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"
)

// ValidateRule 验证规则结构
type ValidateRule struct {
	Required    bool        `json:"required"`    // 是否必填
	Type        string      `json:"type"`        // 数据类型: string, int, float, bool, array
	MinLength   int         `json:"minLength"`   // 字符串最小长度
	MaxLength   int         `json:"maxLength"`   // 字符串最大长度
	Min         *float64    `json:"min"`         // 数值最小值
	Max         *float64    `json:"max"`         // 数值最大值
	Pattern     string      `json:"pattern"`     // 正则表达式模式
	Enum        []string    `json:"enum"`        // 枚举值
	Description string      `json:"description"` // 字段描述
	Default     interface{} `json:"default"`     // 默认值
}

// Schema JSON Schema结构
type Schema struct {
	Title      string                  `json:"title"`
	Type       string                  `json:"type"`
	Properties map[string]ValidateRule `json:"properties"`
	Required   []string                `json:"required"`
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string      `json:"field"`
	Message string      `json:"message"`
	Value   interface{} `json:"value"`
}

// ValidationResult 验证结果
type ValidationResult struct {
	Valid  bool                   `json:"valid"`
	Errors []ValidationError      `json:"errors"`
	Data   map[string]interface{} `json:"data"` // 处理后的数据（包含默认值等）
}

// Validator JSON Schema验证器
type Validator struct {
	schema Schema
}

// NewValidator 创建新的验证器
func NewValidator(schema Schema) *Validator {
	return &Validator{schema: schema}
}

// Validate 验证数据
func (v *Validator) Validate(data map[string]interface{}) ValidationResult {
	result := ValidationResult{
		Valid:  true,
		Errors: []ValidationError{},
		Data:   make(map[string]interface{}),
	}

	// 检查必填字段
	for _, required := range v.schema.Required {
		if _, exists := data[required]; !exists {
			result.Valid = false
			result.Errors = append(result.Errors, ValidationError{
				Field:   required,
				Message: fmt.Sprintf("字段 %s 是必填的", required),
				Value:   nil,
			})
		}
	}

	// 验证每个字段
	for fieldName, rule := range v.schema.Properties {
		value, exists := data[fieldName]

		// 如果字段不存在但有默认值，使用默认值
		if !exists && rule.Default != nil {
			result.Data[fieldName] = rule.Default
			continue
		}

		// 如果字段不存在且不是必填，跳过
		if !exists && !rule.Required {
			continue
		}

		// 验证字段
		if err := v.validateField(fieldName, value, rule); err != nil {
			result.Valid = false
			result.Errors = append(result.Errors, *err)
		} else {
			result.Data[fieldName] = value
		}
	}

	return result
}

// validateField 验证单个字段
func (v *Validator) validateField(fieldName string, value interface{}, rule ValidateRule) *ValidationError {
	// 类型验证
	if !v.validateType(value, rule.Type) {
		return &ValidationError{
			Field:   fieldName,
			Message: fmt.Sprintf("字段 %s 类型错误，期望 %s", fieldName, rule.Type),
			Value:   value,
		}
	}

	// 字符串验证
	if rule.Type == "string" && value != nil {
		str := fmt.Sprintf("%v", value)
		if rule.MinLength > 0 && len(str) < rule.MinLength {
			return &ValidationError{
				Field:   fieldName,
				Message: fmt.Sprintf("字段 %s 长度不能少于 %d 个字符", fieldName, rule.MinLength),
				Value:   value,
			}
		}
		if rule.MaxLength > 0 && len(str) > rule.MaxLength {
			return &ValidationError{
				Field:   fieldName,
				Message: fmt.Sprintf("字段 %s 长度不能超过 %d 个字符", fieldName, rule.MaxLength),
				Value:   value,
			}
		}
	}

	// 数值验证
	if (rule.Type == "int" || rule.Type == "float") && value != nil {
		var numValue float64
		switch v := value.(type) {
		case int:
			numValue = float64(v)
		case int64:
			numValue = float64(v)
		case float64:
			numValue = v
		case string:
			if parsed, err := strconv.ParseFloat(v, 64); err == nil {
				numValue = parsed
			}
		}

		if rule.Min != nil && numValue < *rule.Min {
			return &ValidationError{
				Field:   fieldName,
				Message: fmt.Sprintf("字段 %s 值不能小于 %v", fieldName, *rule.Min),
				Value:   value,
			}
		}
		if rule.Max != nil && numValue > *rule.Max {
			return &ValidationError{
				Field:   fieldName,
				Message: fmt.Sprintf("字段 %s 值不能大于 %v", fieldName, *rule.Max),
				Value:   value,
			}
		}
	}

	// 枚举验证
	if len(rule.Enum) > 0 && value != nil {
		strValue := fmt.Sprintf("%v", value)
		found := false
		for _, enumValue := range rule.Enum {
			if enumValue == strValue {
				found = true
				break
			}
		}
		if !found {
			return &ValidationError{
				Field:   fieldName,
				Message: fmt.Sprintf("字段 %s 值必须是以下之一: %s", fieldName, strings.Join(rule.Enum, ", ")),
				Value:   value,
			}
		}
	}

	return nil
}

// validateType 验证数据类型
func (v *Validator) validateType(value interface{}, expectedType string) bool {
	if value == nil {
		return true // null值总是有效的
	}

	switch expectedType {
	case "string":
		_, ok := value.(string)
		return ok
	case "int":
		switch value.(type) {
		case int, int32, int64:
			return true
		case string:
			_, err := strconv.Atoi(value.(string))
			return err == nil
		default:
			return false
		}
	case "float":
		switch value.(type) {
		case float32, float64:
			return true
		case int, int32, int64:
			return true
		case string:
			_, err := strconv.ParseFloat(value.(string), 64)
			return err == nil
		default:
			return false
		}
	case "bool":
		_, ok := value.(bool)
		return ok
	case "array":
		rv := reflect.ValueOf(value)
		return rv.Kind() == reflect.Slice || rv.Kind() == reflect.Array
	case "datetime":
		switch v := value.(type) {
		case string:
			_, err := time.Parse("2006-01-02", v)
			if err != nil {
				_, err = time.Parse("2006-01-02 15:04:05", v)
			}
			return err == nil
		case time.Time:
			return true
		default:
			return false
		}
	default:
		return true
	}
}

// LoadSchemaFromJSON 从JSON字符串加载Schema
func LoadSchemaFromJSON(jsonStr string) (*Schema, error) {
	var schema Schema
	err := json.Unmarshal([]byte(jsonStr), &schema)
	if err != nil {
		return nil, fmt.Errorf("解析Schema失败: %v", err)
	}
	return &schema, nil
}
