package notice

import (
	"context"
	"fmt"
	"strings"
	"time"

	"fincore/app/scheduler/tasks"
	"fincore/model"
	"fincore/thirdparty/sms"
	"fincore/utils/convert"
	"fincore/utils/gform"
	"fincore/utils/log"
)

// BillNoticeOverdueTask 账单逾期通知任务
// 用于处理账单到期前一天，短信通知客户
type BillNoticeOverdueTask struct {
	*tasks.BaseTask
	logger *log.Logger
	ctx    context.Context
}

// NewBillNoticeOverdueTask 创建账单逾期通知任务
func NewBillNoticeOverdueTask() *BillNoticeOverdueTask {
	baseTask := tasks.NewBaseTask(
		"bill-notice-overdue",
		"账单逾期通知任务",
		// 每天10点执行一次
		"0 0 10 * * *",
		//"0 */1 * * * *",
		1*time.Hour, // 超时时间1小时
	)

	// 设置为单例模式，避免重复执行
	baseTask.SetConcurrencyMode(tasks.ConcurrencyModeSingleton)
	// 设置重试次数和间隔
	baseTask.SetRetryCount(3).SetRetryInterval(30 * time.Second)

	logger := log.RegisterModule("bill_notice_overdue_task", "账单逾期通知任务")
	ctx := context.Background()
	return &BillNoticeOverdueTask{
		BaseTask: baseTask,
		logger:   logger,
		ctx:      ctx,
	}
}

// Execute 执行账单逾期通知任务
func (t *BillNoticeOverdueTask) Execute(ctx context.Context) error {

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "start_execution"),
	).Info("开始执行账单逾期通知任务")

	startTime := time.Now()
	var processedCount, successCount, failureCount int

	toNotices, err := t.getOverdueBills()
	if err != nil {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "query_bills"),
			log.String("error", err.Error()),
		).Error("查询逾期订单失败")
		return fmt.Errorf("查询逾期订单失败: %v", err)
	}

	if len(toNotices) == 0 {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "no_notices_found"),
		).Info("未找到需要通知的订单")
		return nil
	}
	processedCount = len(toNotices)

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "sms_bill_notices"),
		log.Int("notice_count", processedCount),
	).Info("找到需要通知的订单")

	// 分批处理
	const batchSize = 100
	for i := 0; i < len(toNotices); i += batchSize {
		end := i + batchSize
		if end > len(toNotices) {
			end = len(toNotices)
		}
		batch := toNotices[i:end]
		batchLen := len(batch)
		notices := make([]sms.NoticeOverdueParams, batchLen)
		// 遍历处理每条通知
		for j := range batchLen {

			// 检查上下文是否被取消
			select {
			case <-ctx.Done():
				t.logger.WithFields(
					log.String("task", t.GetName()),
					log.String("operation", "context_cancelled"),
					log.Int("processed_count", processedCount-1),
				).Warn("任务被取消，停止处理")
				return ctx.Err()
			default:
			}

			createDate := convert.ConvertToTime(batch[j]["created_at"]).Format("2006-01-02")
			createDateArr := strings.Split(createDate, "-")
			notices[j] = sms.NoticeOverdueParams{
				Username: convert.GetStringFromMap(batch[j], "username"),
				DueMonth: createDateArr[1],
				DueDay:   createDateArr[2],
				Mobile:   convert.GetStringFromMap(batch[j], "mobile"),
				URL:      "",
			}
		}
		// 发送短信
		err := sms.SendBillNoticeOverdueSms(notices)
		if err != nil {
			t.logger.WithFields(
				log.String("task", t.GetName()),
				log.String("operation", "send_sms"),
				log.String("error", err.Error()),
			).Error("批量发送短信失败")
			failureCount += batchLen
			continue
		}
		successCount += batchLen
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.Int("batchLen", batchLen),
			log.String("operation", "batch_send_success"),
		).Info("批量短信通知成功")
	}
	// 记录执行统计
	duration := time.Since(startTime)
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "execution_completed"),
		log.Int("processed_count", processedCount),
		log.Int("success_count", successCount),
		log.Int("failure_count", failureCount),
		log.String("duration", duration.String()),
	).Info("账单逾期通知任务执行完成")

	return nil
}

// 查询所有逾期订单，并关联查询用户名称
func (t *BillNoticeOverdueTask) getOverdueBills() ([]gform.Data, error) {
	// 获取逾期订单，用户有多个订单取还款日期最小的一个
	query := `
		SELECT 
			 u.name AS username,
			 u.mobile AS mobile,
			 MIN(o.created_at) AS created_at 
		FROM business_repayment_bills o
		JOIN business_app_account u ON o.user_id = u.id
		WHERE o.status = ? OR o.status = ? GROUP BY mobile
	`

	result, err := model.DB(model.WithContext(t.ctx)).Query(query,
		model.RepaymentBillStatusOverduePartialPaid, model.RepaymentBillStatusOverdueUnpaid,
	)

	if err != nil {
		return nil, fmt.Errorf("查询逾期订单失败: %v", err)
	}
	return result, nil
}

// OnStart 任务开始执行前的回调
func (t *BillNoticeOverdueTask) OnStart(ctx context.Context) error {

	// task_ 开头，记录整个周期所有 sql 执行日志
	requestID := "task_" + t.GetName() + "_" + time.Now().Format("**************")
	t.ctx = context.WithValue(t.ctx, log.RequestIDKey, requestID)
	t.logger = t.logger.WithRequestID(requestID)

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_starting"),
	).Info("逾期订单通知任务即将开始")
	return nil
}

// OnSuccess 任务执行成功后的回调
func (t *BillNoticeOverdueTask) OnSuccess(ctx context.Context) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_success"),
	).Info("逾期订单通知任务执行成功")
	return nil
}

// OnError 任务执行失败后的回调
func (t *BillNoticeOverdueTask) OnError(ctx context.Context, err error) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_error"),
		log.String("error", err.Error()),
	).Error("逾期订单通知任务执行失败")
	return nil
}
