---
description: src/*
alwaysApply: false
---
# 开发工具和最佳实践

## 项目构建
- 使用 `make` 命令进行项目构建，主要命令：
  - `make build`: 构建项目
  - `make run`: 运行项目
  - `make test`: 运行测试
  - `make clean`: 清理构建文件

## 测试实践
- 单元测试应该覆盖核心业务逻辑
- 测试文件命名为 `*_test.go`
- 使用表驱动测试方法编写用例
- 关键功能应有基准测试

## 代码提交规范
- 提交前应运行测试确保通过
- 提交信息应遵循格式：`[模块名] 操作内容`
- 敏感信息如密钥、密码不应提交到版本控制中

## 日志规范
- 开发环境使用 Debug 级别
- 生产环境使用 Info 级别
- 异常情况应记录 Error 级别日志
- 日志应包含关键信息，便于问题排查

## 文档维护
- API 变更应更新 Swagger 文档
- 数据库变更应添加迁移文件
- 代码注释应与实现保持同步
- 复杂逻辑应有流程图或说明文档
# 开发工具和最佳实践

## 项目构建
- 使用 `make` 命令进行项目构建，主要命令：
  - `make build`: 构建项目
  - `make run`: 运行项目
  - `make test`: 运行测试
  - `make clean`: 清理构建文件

## 测试实践
- 单元测试应该覆盖核心业务逻辑
- 测试文件命名为 `*_test.go`
- 使用表驱动测试方法编写用例
- 关键功能应有基准测试

## 代码提交规范
- 提交前应运行测试确保通过
- 提交信息应遵循格式：`[模块名] 操作内容`
- 敏感信息如密钥、密码不应提交到版本控制中

## 日志规范
- 开发环境使用 Debug 级别
- 生产环境使用 Info 级别
- 异常情况应记录 Error 级别日志
- 日志应包含关键信息，便于问题排查

## 文档维护
- API 变更应更新 Swagger 文档
- 数据库变更应添加迁移文件
- 代码注释应与实现保持同步
- 复杂逻辑应有流程图或说明文档
