openapi: 3.0.0
info:
  title: FinCore System Management API
  description: API endpoints for system management including account management
  version: 1.0.0
  
servers:
  - url: /business/system
    description: System management API server

components:
  schemas:
    ApiResponse:
      type: object
      properties:
        status:
          type: integer
          description: 响应状态码，200为成功，其他为失败
        message:
          type: string
          description: 响应消息
        data:
          type: object
          description: 响应数据
        meta:
          type: object
          description: 元数据，例如分页信息

    AccountListResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
          description: 响应状态码
        message:
          type: string
          example: "获取全部列表"
          description: 响应消息
        data:
          type: object
          properties:
            page:
              type: integer
              description: 当前页码
            pageSize:
              type: integer
              description: 每页数量
            total:
              type: integer
              description: 总记录数
            items:
              type: array
              items:
                $ref: '#/components/schemas/AccountInfo'
        meta:
          type: object
          nullable: true

    AccountInfo:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 账户ID
        status:
          type: integer
          description: 账户状态
        name:
          type: string
          description: 姓名
        username:
          type: string
          description: 用户名
        avatar:
          type: string
          description: 头像URL
        tel:
          type: string
          description: 电话号码
        mobile:
          type: string
          description: 手机号码
        email:
          type: string
          description: 邮箱地址
        remark:
          type: string
          description: 备注
        dept_id:
          type: integer
          description: 部门ID
        city:
          type: string
          description: 城市
        address:
          type: string
          description: 地址
        company:
          type: string
          description: 公司
        createtime:
          type: integer
          format: int64
          description: 创建时间戳
        rolename:
          type: array
          items:
            type: string
          description: 角色名称列表
        roleid:
          type: array
          items:
            type: integer
          description: 角色ID列表
        depname:
          type: string
          description: 部门名称

    ErrorResponse:
      type: object
      properties:
        status:
          type: integer
          description: 错误状态码
        message:
          type: string
          description: 错误消息
        data:
          type: object
          nullable: true
          description: 错误详情

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []

paths:
  /account/get_list:
    get:
      tags:
        - 账户管理
      summary: 获取成员列表
      description: |
        获取系统成员账户列表，支持分页和条件筛选。
        
        **功能特性：**
        - 支持按部门筛选
        - 支持按姓名模糊查询
        - 支持按手机号精确查询
        - 自动关联角色信息和部门信息
        - 自动处理头像URL
        
        **权限要求：**
        - 需要登录认证
        - 只能查看同一业务下的账户
      parameters:
        - name: cid
          in: query
          description: 部门ID，0表示不限制部门
          required: false
          schema:
            type: string
            default: "0"
            example: "1"
        - name: name
          in: query
          description: 姓名（支持模糊查询）
          required: false
          schema:
            type: string
            example: "张三"
        - name: mobile
          in: query
          description: 手机号码（精确查询）
          required: false
          schema:
            type: string
            pattern: '^1[3-9]\d{9}$'
            example: "***********"
        - name: page
          in: query
          description: 页码，从1开始
          required: false
          schema:
            type: string
            default: "1"
            pattern: '^[1-9]\d*$'
            example: "1"
        - name: pageSize
          in: query
          description: 每页数量
          required: false
          schema:
            type: string
            default: "10"
            pattern: '^[1-9]\d*$'
            example: "10"
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountListResponse'
              examples:
                success:
                  summary: 成功响应示例
                  value:
                    status: 200
                    message: "获取全部列表"
                    data:
                      page: 1
                      pageSize: 10
                      total: 25
                      items:
                        - id: 1
                          status: 1
                          name: "张三"
                          username: "zhangsan"
                          avatar: "http://example.com/avatar/1.jpg"
                          tel: "010-********"
                          mobile: "***********"
                          email: "<EMAIL>"
                          remark: "系统管理员"
                          dept_id: 1
                          city: "北京"
                          address: "北京市朝阳区"
                          company: "示例公司"
                          createtime: 1640995200
                          rolename: ["管理员", "操作员"]
                          roleid: [1, 2]
                          depname: "技术部"
                    meta: null
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                invalid_params:
                  summary: 参数错误示例
                  value:
                    status: 400
                    message: "参数验证失败"
                    data: null
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                unauthorized:
                  summary: 未授权示例
                  value:
                    status: 401
                    message: "未授权访问"
                    data: null
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                server_error:
                  summary: 服务器错误示例
                  value:
                    status: 500
                    message: "服务器内部错误"
                    data: null
