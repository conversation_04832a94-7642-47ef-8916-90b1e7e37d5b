<template>
	<view class="loan-container">
		<!-- 收款卡 -->
		<!-- 借款金额区域 -->
		<view class="take">

			<view class="amount-section">
				<view class="amount-display" :class="opt.setProduct.loan_amount?'active':''" @click="handleSelProduct">
					<text class="currency">￥</text>
					<text class="amount-input">{{ opt.setProduct.loan_amount || availableCreditLimit }}</text>
					<!-- <input v-model="loanAmount" type="number" class="amount-input" placeholder="1000"
						@input="validateAmount" /> -->
				</view>
				<view class="select-pupup" v-show="selShow">
					<view class="select-option" v-for="(item,index) in productsList" :key="index"
						@click="handleSelOpt(item)">
						￥{{ item.loan_amount }}
					</view>
				</view>
			</view>

			<!-- 借款期限 -->
			<view class="loan-term">
				<text class="term-label">借多久</text>
				<view class="term-value">{{ opt.setProduct.loan_period? (opt.setProduct.loan_period * opt.setProduct.total_periods) : '' }} {{ opt.setProduct.loan_period?'天':'' }}</view>
				<image class="term-more" src="/static/image/more.png" mode="widthFix"></image>
				<!-- <picker mode="selector" :range="termOptions" @change="onTermChange">
					<view class="term-value">{{ selectedTerm }}</view>
				</picker> -->
			</view>

			<!-- 利率信息 -->
			<view class="rate-info">
				<text class="rate-label">日利率</text>
				<text class="rate-value">
					<template v-if="opt.setProduct.annual_interest_rate">
						{{ (opt.setProduct.annual_interest_rate/365).toFixed(2) || 0.05 }}%
					</template>
					<template v-else>
						0.05%
					</template>
				</text>
			</view>
			<view class="rate-tip">
				按日计算，随借随还，提前还款可增加额度
			</view>
		</view>

		<view class="out">
			<!-- 收款卡设置 -->
			<view class="card-section" @click="handleSetCard('sk')">
				<text class="card-label">收款卡</text>
				<view class="card-action">
					<text class="action-text" v-if="opt.skCard.id">{{ opt.skCard.bank_name }}
						({{ opt.skCard.bank_card_no_end }})</text>
					<text class="action-text" v-else>去设置</text>
					<uni-icons type="arrowright" size="16" color="#999"></uni-icons>
				</view>
			</view>
			<!-- 还款卡设置 -->
			<view class="card-section" @click="handleSetCard('hk')">
				<text class="card-label">还款卡</text>
				<view class="card-action">
					<text class="action-text" v-if="opt.hkCard.id">{{ opt.hkCard.bank_name }}
						({{ opt.hkCard.bank_card_no_end }})</text>
					<text class="action-text" v-else>去设置</text>
					<uni-icons type="arrowright" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>


		<uni-popup ref="cardPopup" type="bottom" background-color="#fff" border-radius="20rpx 20rpx 0 0">
			<!-- 银行卡选择 -->
			<view class="card-popup">
				<view class="card-popup-title">
					{{ cardPopupType == 'sk'?'收款卡选择':cardPopupType == 'hk'?'还款卡选择':'银行卡选择' }}
				</view>
				<view class="set-card-popup">
					<view class="set-card-list">
						<view class="set-card-item" :class="(cardPopupType == 'sk' && opt.skCard && opt.skCard.id == item.id || cardPopupType == 'hk' && opt.skCard && opt.hkCard.id == item.id)?'active': ''" v-for="(item,index) in opt.cardList" :key="item.id"
							@click="handleSelCard(item)">
							<view class="set-card-item-title">
								{{ item.bank_name }} ({{ item.bank_card_no_end }})
							</view>
							<view class="set-card-item-desc">
								{{ item.bank_phone_end }}
							</view>
						</view>
					</view>
					<view class="set-add-card" @click="toAddCard()">
						<uni-icons type="plusempty" size="20" color="#555"></uni-icons>
						<text>添加银行卡</text>
					</view>
				</view>
			</view>
		</uni-popup>


		<view class="loan">
			<!-- 还款计划 -->
			<view class="repay-plan" @tap="handleGetHkjh()">
				<text class="plan-label">还款计划</text>
				<view class="plan-action">
					<text class="arrow">查看</text>
					<uni-icons type="arrowright" size="16" color="#999"></uni-icons>
				</view>
			</view>
			
			
			<!-- 还款计划弹框（优化样式） -->
			<uni-popup ref="showModal" background-color="#fff" border-radius="30rpx">
				<view class="repayment-modal">
					<!-- 弹框头部 -->
					<view class="modal-header">
						<text class="modal-title">还款计划</text>
					</view>

					<!-- 弹框内容区 -->
					<view class="modal-content">
						<!-- 还款方式 -->
						<view class="info-item">
							<text class="label">还款方式</text>
							<text class="value">等额本息</text>
						</view>

						<!-- 还款总额 -->
						<view class="info-item">
							<text class="label">还款总额</text>
							<view class="total-amount">
								<view class="amount">¥{{ opt.total_repayable_amount }}</view>
								<view class="note">（利息+担保费+综合服务费用）</view>
							</view>
						</view>

						<!-- 分期列表 -->
						<view class="period-section">
							<view class="period-title">共{{ opt.total_periods }}期</view>

							<view class="period-item-box">
								<view class="period-item" v-for="(item,index) in opt.hkjhList" :key="index">
									<view class="period-left">
										<text class="period-name">第{{ item.period_number }}期</text>
										<view class="period-time">{{ item.due_date }}</view>
									</view>
									<text class="period-price">￥{{ item.total_due_amount }}</text>
									<view class="period-line">
										<text class="period-line-x period-line-t" v-if="index != 0"></text>
										<text class="period-line-dot"></text>
										<text class="period-line-x period-line-e" v-if="index != opt.hkjhList.length-1"></text>
									</view>
								</view>
							</view>
						</view>

					</view>

					<!-- 底部按钮 -->
					<button class="confirm-btn" @click="handleSure">确定</button>
				</view>
			</uni-popup>

		</view>
		<!-- 借款提醒 -->
		<view class="loan-tip">
			<text class="tip-text">借款需谨慎，根据个人能力合理贷款消费，避免逾期</text>
		</view>

		<!-- 下一步按钮 -->
		<view class="next-btn" @click="goToNextStep">
			<text class="btn-text">下一步</text>
		</view>
	</view>
</template>

<script setup>
	import {
		reactive,
		ref
	} from "vue";

	import {
		storeToRefs
	} from 'pinia';
	import user from '@/store/user.js';
	const userStore = user();
	import {
		onLoad,
		onShow
	} from "@dcloudio/uni-app";
	const {
		userInfo,
		availableCreditLimit,
		productsList
	} = storeToRefs(userStore);
	import cardApi from '@/api/card.js';
	// 产品选择弹窗
	const selShow = ref(false);
	const showModal = ref();
	const cardPopup = ref();
	const cardPopupType = ref();
	import userApi from '@/api/user.js';
	let opt = reactive({
		setProduct: {}, // 选择的产品
		cardList: [], // 银行卡列表
		skCard: {}, // 选择的收款银行卡
		hkCard: {}, // 选择的还款银行卡
		
		total_repayable_amount: 0, //还款总额,
		total_periods: 0, //还款期数
		hkjhList: [], // 还款计划列表
	})

	function handleSelProduct() {
		selShow.value = !selShow.value;
	}

	function handleSelOpt(item) {
		opt.setProduct = item;
		selShow.value = false;
	}

	// const termOptions = ref(["7天", "14天", "30天", "60天", "90天", "120天"]);
	// const selectedTerm = ref(">");

	// const validateAmount = () => {
	// 	loanAmount.value = loanAmount.value.replace(/\D/g, "");
	// };

	// const onTermChange = (e) => {
	// 	selectedTerm.value = termOptions.value[e.detail.value];
	// };
	onLoad(async () => {
		let uid = userInfo.value?.uid;
		if (!uid) {
			await userStore.getInfo();
			uid = userInfo.value?.uid;
		}
		// 获取授信额度&贷款产品
		await userStore.getProducts(uid);
	});
	onShow(async () => {
		getList()
	})

	function getList() {
		cardApi.getCardList().then(res => {
			if(res.data && res.data.list && res.data.list.length>0) {
				let list = res.data.list.filter(item => item.card_status == 1);
				list.forEach(item => {
					let bank_card_no = item.bank_card_no;
					let bank_phone = item.bank_phone;
					item.bank_card_no_end = bank_card_no.slice(bank_card_no.length - 4, bank_card_no.length);
					item.bank_card_no_end_txt =
						`(尾号${bank_card_no.slice(bank_card_no.length-4,bank_card_no.length)})`;
					item.bank_phone_end = `手机尾号${bank_phone.slice(bank_phone.length-4,bank_phone.length)}`
				})
				opt.cardList = list;
			}
		})
	}
	const goToNextStep = () => {
		if(!opt.setProduct || !opt.setProduct.id) {
			return uni.showToast({
				title: "请先选择产品",
				icon: "none"
			})
		}
		if(!opt.skCard || !opt.skCard.id) {
			return uni.showToast({
				title: "请选择收款银行卡",
				icon: "none"
			})
		}
		if(!opt.hkCard || !opt.hkCard.id) {
			return uni.showToast({
				title: "请选择还款银行卡",
				icon: "none"
			})
		}
		userApi.checkCanCreate({ loan_amount: opt.setProduct.loan_amount }).then(res => {
			if(res.data.can_create) {
				uni.navigateTo({
					url: "/pages/Agreement/Agreement?product_id="+opt.setProduct.id+"&loan_amount="+opt.setProduct.loan_amount+"&bank_card_id="+opt.skCard.id,
				});
			}else{
				uni.showToast({
					title: res.data.message,
					icon: "none"
				})
			}
			
		})
		
	};

	

	function toAddCard() {
		uni.navigateTo({
			url: "/pages/CollectionCard/CollectionCard"
		})
	}

	function handleSetCard(type) {
		cardPopupType.value = type;
		cardPopup.value.open();
	}

	function handleSelCard(item) {
		if (cardPopupType.value == 'sk') {
			opt.skCard = item;
		} else if (cardPopupType.value == 'hk') {
			opt.hkCard = item;
		}
		cardPopupType.value = '';
		cardPopup.value.close();
	}

	// 还款计划获取展示
	function handleGetHkjh() {
		// opt.hkjhList = [{
		// 		"period_number": 1,
		// 		"due_date": "2025-08-08",
		// 		"total_due_amount": 1564.8
		// 	},
		// 	{
		// 		"period_number": 2,
		// 		"due_date": "2025-09-07",
		// 		"total_due_amount": 1564.8
		// 	}
		// ];
		// opt.total_repayable_amount = 3129.59;
		// opt.total_periods = 2;
		// showModal.value.open();
		if(!opt.setProduct || !opt.setProduct.id) {
			return uni.showToast({
				title: "请先选择产品",
				icon: "none"
			})
		}
		userApi.getRepaymentPreview({
			product_rule_id: opt.setProduct.id
		}).then(res => {
			opt.hkjhList = res.data.periods;
			opt.total_repayable_amount = res.data.total_repayable_amount;
			opt.total_periods = res.data.total_periods;
			showModal.value.open()
		})
	}
	
	
	const handleSure = () => {
		showModal.value.close();
		// uni.redirectTo({
		// 	url: "/pages/Agreement/Agreement",
		// });
	};
</script>

<style scoped lang="scss">
	page {
		background-color: #eff2f7;
	}

	.loan-container {
		padding: 20rpx 30rpx;
	}

	.take {
		background-color: #fff;
		border-radius: 10px;
		padding: 0 30rpx 30rpx;
	}

	.out {
		background-color: #fff;
		border-radius: 10px;
		padding: 0 30rpx;
		margin-top: 20rpx;
	}

	.loan {
		background-color: #fff;
		border-radius: 10px;
		padding: 0 30rpx;
		margin-top: 20rpx;
	}


	.amount-section {
		padding: 50rpx 0 30rpx 0;
		border-bottom: 2px solid #e5e5e5;
		position: relative;

		.amount-display {
			display: flex;
			align-items: center;
			color: #ccc;

			&.active {
				color: #444;
			}

			.currency {
				font-size: 18px;
			}

			.amount-input {
				font-size: 40rpx;
				font-weight: bold;
			}
		}

		.select-pupup {
			position: absolute;
			width: 100%;
			background: #fff;
			top: 101%;
			box-shadow: 0 4px 32px rgba(0, 0, 0, 0.08);
			z-index: 10;
			padding: 0 30rpx 30rpx;
			box-sizing: border-box;
			border-radius: 0 0 15rpx 15rpx;

			.select-option {
				padding: 30rpx 0;
				font-size: 40rpx;
				color: #444;
				border-bottom: 1px solid #e5e5e5;
			}
		}
	}

	.repayment-modal {
		// position: fixed;
		// top: 50%;
		// left: 50%;
		// transform: translate(-50%, -50%);
		// width: 85%;
		// max-width: 400px;
		// background-color: #ffffff;
		// border-radius: 16px;
		// box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
		// z-index: 1000;
		width: 80vw;
	}

	.modal-header {
		padding: 30rpx;
		text-align: center;
		border-bottom: 1px solid #f0f0f0;
	}

	.modal-title {
		font-size: 38rpx;
		font-weight: 500;
		color: #333333;
	}

	.modal-content {
		padding: 0 30rpx;
	}

	.action-text {
		font-size: 30rpx;
		color: #444;
	}

	.info-item {
		display: flex;
		align-items: center;
		padding: 30rpx 0;
	}

	.label {
		font-size: 30rpx;
		color: #333333;
	}

	.value {
		font-size: 30rpx;
		color: #333333;
		font-weight: 500;
		margin-left: 20rpx;
	}



	.total-amount {}

	.amount {
		font-weight: bold;
		margin-left: 20rpx;
	}

	.note {
		font-size: 12px;
		color: #999999;
		margin-left: 4px;
	}

	.period-section {
		margin-top: 20px;
		.period-item-box{
			max-height: 430rpx;
			overflow-y: auto;
		}
	}

	.period-title {
		font-size: 34rpx;
		color: #222;
		padding-bottom: 25rpx;
		border-bottom: 1px solid #e5e5e5;
	}
	
	.period-item{
		display: flex;
		padding: 20rpx 0;
		align-items: center;
		color: #444;
		position: relative;
		.period-left{
			width: 180rpx;
			.period-name{
				font-size: 30rpx;
				color: #333;
			}
			.period-time{
				font-size: 24rpx;
				color: #666;
			}
		}
		.period-price{
			margin-left: 40rpx;
		}
		.period-line{
			position: absolute;
			left: 180rpx;
			top: 0;
			width: 1px;
			height: 100%;
			.period-line-x{
				height: 50%;
				width: 1px;
				background-color: #ccc;
				&.period-line-t{
					position: absolute;
					top: 0;
					left: 0;
				}
				&.period-line-e{
					position: absolute;
					bottom: 0;
					left: 0;
				}
			}
			.period-line-dot{
				position: absolute;
				left: 50%;
				top: 50%;
				width: 14rpx;
				height: 14rpx;
				margin-left: -7rpx;
				margin-top: -7rpx;
				background-color: #ccc;
				border-radius: 100%;
			}
		}
	}






	.confirm-btn {
		// height: 50px;
		// line-height: 50px;
		// background-color: #3a76f0;
		// color: #ffffff;
		// font-size: 16px;
		// font-weight: 500;
		// border-radius: 0 0 16px 16px;
		// margin-top: 16px;
		margin: 30px auto 16px;
		width: 220rpx;
		flex-shrink: 0;
		height: 80rpx;
		line-height: 80rpx;
		background: linear-gradient(to bottom, #1a6eff 20%, #4781e3 45%);
		color: #fff;
		border: none;
		border-radius: 22px;
		font-size: 13px;
		cursor: pointer;
		padding: 0;
		box-sizing: border-box;
		text-align: center;
		white-space: nowrap;
		box-shadow: 0 2px 12px #1a6eff;
	}

	/* 保持遮罩层点击关闭功能 */
	.modal-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 999;
	}

	.loan-term,
	.card-section,
	.repay-plan,
	.rate-info {
		padding: 30rpx 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.term-label,
	.card-label,
	.plan-label,
	.rate-label {
		font-size: 30rpx;
		color: #aca9a9;
	}

	.term-value,
	.rate-value {
		font-size: 32rpx;
		color: #aca9a9;
		flex: 1;
		text-align: right;
	}

	.term-more {
		width: 30rpx;
	}

	.rate-tip {
		border-radius: 10rpx;
		background-color: #eff2f7;
		font-size: 24rpx;
		padding: 10rpx;
		text-align: center;
		color: #1a73e8;
	}

	.card-section,
	.repay-plan {

		.card-action,
		.plan-action {
			display: flex;
			align-items: center;

			.arrow {
				font-size: 28rpx;
				color: #999;
				margin-right: 10rpx;
			}
		}
	}

	.card-section {
		position: relative;
	}
	
	.card-popup{
		border-radius: 20rpx 20rpx 0 0;
		overflow: hidden;
		background-color: #fff;
	}
	.card-popup-title {
		padding: 30rpx;
		color: #333;
		text-align: center;
		border-bottom: 1px solid #eee;
	}

	.set-card-popup {
		// position: absolute;
		// width: 100%;
		// background: #fff;
		// top: 101%;
		// box-shadow: 0 4px 32px rgba(0, 0, 0, 0.08);
		z-index: 10;
		padding: 30rpx 30rpx;
		box-sizing: border-box;
		border-radius: 0 0 15rpx 15rpx;


		.set-card-list {
			background-color: #fff;
			overflow: hidden;
			// border-radius: 0 0 15rpx 15rpx;
			// min-height: 20vh;
			max-height: 40vh;
			overflow-y: auto;

			.set-card-item {
				padding: 20rpx;
				border: 1px dashed #dbdbdb;
				border-radius: 15rpx;
				margin-bottom: 20rpx;
				&.active{
					border: 1px dashed #1a73e8;
				}
				&:last-child {
					margin-bottom: 0;
				}

				.set-card-item-title {
					color: #333;
					font-size: 34rpx;
					margin-bottom: 10rpx;
				}

				.set-card-item-desc {
					color: #888;
					font-size: 28rpx;
				}
			}
		}

		.set-add-card {
			border: 1px dashed #dbdbdb;
			border-radius: 10rpx;
			padding: 20rpx;
			display: flex;
			align-items: center;
			margin-top: 20rpx;
			justify-content: center;
			text {
				font-size: 30rpx;
				color: #555;
			}
		}
	}

	.loan-tip {
		padding: 40rpx 0;

		.tip-text {
			font-size: 24rpx;
			color: #999;
			text-align: center;
			display: block;
		}
	}

	.next-btn {
		background-color: #1a73e8;
		/* 蓝色按钮 */
		border-radius: 50rpx;
		padding: 25rpx 0;
		text-align: center;
		margin-bottom: 50rpx;

		.btn-text {
			font-size: 32rpx;
			color: #fff;
			font-weight: 500;
		}
	}

	/* 还款计划入口样式 */
	.repayment-entry {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx 32rpx;
		background-color: #fff;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.entry-title {
		font-size: 32rpx;
		color: #333;
	}

	.entry-action {
		display: flex;
		align-items: center;
		color: #666;
		font-size: 28rpx;
	}

	.arrow {
		margin-left: 8rpx;
	}

	/* 弹框遮罩层 */
	.modal-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 999;
	}
</style>