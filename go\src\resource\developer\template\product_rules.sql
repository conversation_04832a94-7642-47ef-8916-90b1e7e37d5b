-- ----------------------------
-- Table structure for 产品列表
-- ----------------------------
DROP TABLE IF EXISTS `product_rules`;
CREATE TABLE product_rules (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  rule_name VARCHAR(100) NOT NULL COMMENT '规则名称，如"丰兴61万/2期15点"',
  loan_amount DECIMAL(15, 2) NOT NULL COMMENT '贷款额度(元)',
  loan_period INT UNSIGNED NOT NULL COMMENT '借款周期(天)',
  total_periods INT UNSIGNED NOT NULL COMMENT '总期数',
  guarantee_fee DECIMAL(15, 2) NOT NULL DEFAULT 0 COMMENT '担保费(元)',
  annual_interest_rate DECIMAL(5, 2) NOT NULL COMMENT '基础年利率(%)',
  other_fees DECIMAL(15, 2) NOT NULL DEFAULT 0 COMMENT '其他费用(元)',
  rule_category ENUM('新用户', '复购用户') NOT NULL COMMENT '规则类别',
  repayment_method ENUM('后置付款', '前置付款') NOT NULL COMMENT '还款方式',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品规则表';

-- ----------------------------
-- Records of product_rules (产品规则数据)
-- ----------------------------
INSERT INTO `product_rules` VALUES 
(1, '丰兴61万/2期15点', 610000.00, 60, 2, 9150.00, 15.00, 0.00, '新用户', '后置付款', '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(2, '复购用户优惠方案', 500000.00, 30, 1, 7500.00, 12.00, 0.00, '复购用户', '前置付款', '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
(3, '小额快速借款', 100000.00, 15, 1, 1500.00, 18.00, 500.00, '新用户', '后置付款', '2024-01-01 00:00:00', '2024-01-01 00:00:00');

-- ----------------------------
-- Table structure for 产品列表页面初始化
-- ----------------------------
INSERT INTO `fincore`.`business_auth_rule` (`id`, `uid`, `title`, `locale`, `orderNo`, `type`, `pid`, `icon`, `routePath`, `routeName`, `component`, `redirect`, `permission`, `status`, `isExt`, `keepalive`, `requiresAuth`, `hideInMenu`, `hideChildrenInMenu`, `activeMenu`, `noAffix`, `createtime`) VALUES (439, 1, '产品规则', '', 439, 0, 0, 'icon-apps', '/productrules', 'productrules', 'LAYOUT', '/productrules/list', '', 0, 0, 0, 1, 0, 0, 0, 0, 1749510713);
INSERT INTO `fincore`.`business_auth_rule` (`id`, `uid`, `title`, `locale`, `orderNo`, `type`, `pid`, `icon`, `routePath`, `routeName`, `component`, `redirect`, `permission`, `status`, `isExt`, `keepalive`, `requiresAuth`, `hideInMenu`, `hideChildrenInMenu`, `activeMenu`, `noAffix`, `createtime`) VALUES (440, 1, '产品规则列表', '', 440, 1, 439, '', 'list', 'list', '/productrules/list/index', '', '', 0, 0, 0, 1, 0, 0, 0, 0, 1749510760);
