# Fincore 构建和部署指南

## 概述

本文档介绍如何构建和部署Fincore项目，包括Go后端、Vue前端和UniApp移动端的完整构建流程。

## 项目结构

```
fincore/
├── go/                      # Go后端项目
│   ├── src/                 # 源代码
│   └── bin/                 # 编译产物
├── vue/                     # 前端项目
│   ├── business/            # 业务管理端
│   ├── admin/               # 系统管理端
│   └── uniapp/              # 移动端应用
├── deployer/                # 部署目录
│   ├── build/               # 编译产物
│   ├── build.sh             # 构建脚本
│   └── watchdog/            # 监控系统
├── build.sh                 # 构建包装脚本
├── deploy.sh                # 部署脚本
└── BUILD_DEPLOY.md          # 本文档
```

## 环境要求

### 开发环境

- **Go**: 1.19+ 
- **Node.js**: 16.0+
- **包管理器**: npm 或 yarn
- **Git**: 版本控制

### 生产环境

- **操作系统**: Ubuntu 18.04+ / CentOS 7+
- **systemd**: 服务管理
- **网络连接**: 能够访问阿里云RDS和Redis服务
- **Nginx**: 1.18+ (可选)

**注意**: MySQL和Redis使用阿里云托管服务

## 构建流程

### 1. 完整构建

```bash
# 执行完整构建（推荐）
./build.sh

# 查看构建选项
./build.sh help
```

### 2. 分步构建

```bash
# 仅构建Go后端
./build.sh go

# 仅构建Vue前端
./build.sh vue

# 清理构建目录
./build.sh clean

# 验证构建结果
./build.sh verify
```

### 3. 构建配置

通过环境变量自定义构建：

```bash
# 设置构建环境
export BUILD_ENV=production
export BUILD_VERSION=v1.0.0

# 设置Go构建参数
export GOOS=linux
export GOARCH=amd64
export CGO_ENABLED=0

# 执行构建
./build.sh
```

## 构建产物

构建完成后，所有文件将位于 `deployer/build/` 目录：

```
deployer/
├── build/                   # 构建产物
│   ├── app/                 # Go后端
│   │   ├── bin/fincore      # 主程序
│   │   ├── config/          # 配置文件
│   │   ├── logs/            # 日志目录
│   │   └── runtime/         # 运行时目录
│   ├── business/            # Business前端
│   ├── uniapp/              # UniApp前端
│   └── build_info.txt       # 构建信息
├── build.sh                 # 构建脚本
└── watchdog/                # 监控系统
```

## 部署流程

### 1. 准备部署环境

```bash
# 复制配置文件
cp deploy.env.example deploy.env

# 编辑部署配置
vim deploy.env
```

配置示例：
```bash
DEPLOY_HOST=*************
DEPLOY_USER=fincore
DEPLOY_PORT=22
DEPLOY_PATH=/opt/fincore
```

### 2. 执行部署

```bash
# 1. 上传部署包到服务器
scp -r deployer/ user@*************:/tmp/

# 2. 在服务器上执行部署
ssh user@*************
cd /tmp/deployer
sudo ./deploy.sh --start

# 查看部署选项
./deploy.sh --help
```

### 3. 部署选项

```bash
# 常用部署命令（在服务器上执行）
sudo ./deploy.sh --start --check     # 部署+启动+检查
sudo ./deploy.sh --restart           # 部署+重启服务
sudo ./deploy.sh -d /opt/fincore      # 指定部署路径
```

## 服务管理

### 1. 启动服务

```bash
# 在服务器上启动
ssh fincore@server 'cd /opt/fincore/deployer/watchdog && ./scripts/start.sh'

# 或使用systemd
ssh fincore@server 'sudo systemctl start fincore-watchdog'
```

### 2. 查看状态

```bash
# 查看服务状态
ssh fincore@server 'cd /opt/fincore/deployer/watchdog && ./scripts/status.sh'

# 查看日志
ssh fincore@server 'tail -f /opt/fincore/deployer/watchdog/logs/watchdog.log'
```

### 3. 停止服务

```bash
# 优雅停止
ssh fincore@server 'cd /opt/fincore/deployer/watchdog && ./scripts/stop.sh'

# 强制停止
ssh fincore@server 'cd /opt/fincore/deployer/watchdog && ./scripts/stop.sh force'
```

## 配置管理

### 1. 应用配置

主要配置文件位置：
- Go配置: `/opt/fincore/deployer/bin/app/resource/config.yml`
- Watchdog配置: `/opt/fincore/deployer/watchdog/config/`

### 2. 环境配置

不同环境的配置管理：

```bash
# 开发环境
BUILD_ENV=development ./build.sh

# 测试环境  
BUILD_ENV=testing ./build.sh

# 生产环境
BUILD_ENV=production ./build.sh
```

### 3. 配置更新

```bash
# 更新配置后重启服务
ssh fincore@server 'sudo systemctl restart fincore'
ssh fincore@server 'sudo systemctl restart fincore-watchdog'
```

## 监控和日志

### 1. 服务监控

```bash
# 健康检查
ssh fincore@server '/opt/fincore/deployer/watchdog/bin/health-check'

# 测试告警
ssh fincore@server '/opt/fincore/deployer/watchdog/bin/alert-handler test'
```

### 2. 日志查看

```bash
# 应用日志
ssh fincore@server 'tail -f /opt/fincore/deployer/bin/app/runtime/app/app.log'

# 监控日志
ssh fincore@server 'tail -f /opt/fincore/deployer/watchdog/logs/watchdog.log'

# 系统日志
ssh fincore@server 'sudo journalctl -u fincore -f'
```

## 故障排除

### 1. 构建问题

```bash
# Go编译失败
cd go/src && go mod tidy && go build

# 前端构建失败
cd vue/business && yarn install && yarn build
cd vue/uniapp && npm install && npm run build:h5
```

### 2. 部署问题

```bash
# SSH连接失败
ssh -v fincore@server  # 详细连接信息

# 权限问题
ssh fincore@server 'ls -la /opt/fincore/deployer/bin/app/fincore'

# 服务启动失败
ssh fincore@server 'sudo journalctl -u fincore -n 50'
```

### 3. 运行时问题

```bash
# 端口占用
ssh fincore@server 'sudo netstat -tuln | grep 8080'

# 进程状态
ssh fincore@server 'ps aux | grep fincore'

# 资源使用
ssh fincore@server 'top -p $(pgrep fincore)'
```

## 最佳实践

### 1. 构建最佳实践

- 构建前清理旧文件：`./build.sh clean`
- 使用版本标签：`BUILD_VERSION=v1.0.0 ./build.sh`
- 验证构建结果：`./build.sh verify`

### 2. 部署最佳实践

- 部署前备份：自动创建备份
- 分步部署：先部署到测试环境
- 监控部署：使用 `--check` 选项

### 3. 运维最佳实践

- 定期检查服务状态
- 监控日志文件大小
- 定期更新依赖版本

## 自动化部署

### 1. CI/CD集成

```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy
        run: |
          ./build.sh
          ./deploy.sh --build --restart ${{ secrets.DEPLOY_HOST }}
```

### 2. 定时部署

```bash
# crontab示例
0 2 * * * cd /path/to/fincore && ./deploy.sh --check $DEPLOY_HOST
```

## 安全考虑

### 1. SSH安全

- 使用SSH密钥认证
- 禁用密码登录
- 更改默认SSH端口

### 2. 应用安全

- 定期更新依赖
- 使用非root用户运行
- 配置防火墙规则

### 3. 数据安全

- 定期备份数据库
- 加密敏感配置
- 监控异常访问

## 版本管理

### 1. 版本号规则

- 格式：`v主版本.次版本.修订版本`
- 示例：`v1.0.0`, `v1.1.0`, `v1.1.1`

### 2. 发布流程

1. 更新版本号
2. 执行构建测试
3. 创建Git标签
4. 部署到生产环境
5. 验证部署结果

## 联系支持

如有问题，请联系：
- 技术支持：<EMAIL>
- 运维团队：<EMAIL>
- 项目文档：内部Wiki
