<template>
  <div class="container" >
    <Breadcrumb :items="['menu.suggestions', 'menu.suggestions.complaint']" />
    <a-card class="general-card onelineCard" style="height: calc(100% - 50px);">
      <a-row style="margin-bottom: 10px">
        <a-col :span="16">
          <a-space>
            <!-- 规则名称搜索框 -->
            <a-input :style="{width:'220px'}" v-model="formModel.content" placeholder="投诉内容" allow-clear />
            
            <a-button type="primary" @click="search">
              <template #icon>
                <icon-search />
              </template>
              查询
            </a-button>
            <a-button @click="reset">
              重置
            </a-button>
          </a-space>
        </a-col>
        <a-col
          :span="8"
           style="text-align: right;"
        >
        <a-space>
          <a-tooltip :content="$t('searchTable.actions.refresh')">
            <div class="action-icon" @click="search"
              ><icon-refresh size="18"
            /></div>
          </a-tooltip>
          <a-dropdown @select="handleSelectDensity">
            <a-tooltip :content="$t('searchTable.actions.density')">
              <div class="action-icon"><icon-line-height size="18" /></div>
            </a-tooltip>
            <template #content>
              <a-doption
                v-for="item in densityList"
                :key="item.value"
                :value="item.value"
                :class="{ active: item.value === size }"
              >
                <span>{{ item.name }}</span>
              </a-doption>
            </template>
          </a-dropdown>
          </a-space>
        </a-col>
      </a-row>
      <a-table
         row-key="id"
        :loading="loading"
        :pagination="pagination"
        :columns="(cloneColumns as TableColumnData[])"
        :data="renderData"
        :bordered="{wrapper:true,cell:true}"
        :size="size"
        :default-expand-all-rows="true"
        ref="artable"
        @page-change="handlePaageChange" 
        @page-size-change="handlePaageSizeChange" 
      >
        <!-- 序号列 -->
        <template #index="{ rowIndex }">
            {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
        </template>

        <!-- 订单状态列 -->
        <template #order_status="{ record }">
          {{ record.order_status }}
        </template>
        
        <!-- 手机号列 -->
        <template #mobile="{ record }">
          {{ record.mobile }}
        </template>
        <!-- 投诉内容列 -->
        <template #content="{ record }">
          {{ record.content }}
        </template>
        <!-- 备注列 -->
        <template #remark="{ record }">
          {{ record.remark }}
        </template>
        <!-- 创建时间列 -->
        <template #complaintTime="{ record }">
          {{ record.complaintTime }}
        </template>
        
        <!-- 操作列 -->
        <template #operations="{ record }">
          <a @click="handleProcess(record)" class="operation-link">处理</a>
          <a-divider direction="vertical" />
          <a @click="handleDetail(record)" class="operation-link">详情</a>
        </template>
      </a-table>
    </a-card>
    
    <!-- 处理弹窗 -->
    <a-modal
      v-model:visible="showProcessModal"
      title="处理"
      @ok="handleProcessOk"
      @cancel="() => (showProcessModal = false)"
      :destroyOnClose="true"
      :okButtonProps="{ disabled: isProcessFormInvalid }"
    >
      <a-form :model="processForm" :rules="processRules" ref="processFormRef" layout="vertical">
        <a-form-item label="备注" name="remark" :rules="[{ required: true, message: '请输入备注' }]">
          <a-input v-model="processForm.remark" placeholder="请输入备注（备注不会在客户端显示）" />
        </a-form-item>
        <a-form-item label="反馈" name="feedback" :rules="[{ required: true, message: '请输入反馈' }]">
          <a-input v-model="processForm.feedback" placeholder="请输入反馈（反馈会在客户端显示）" />
        </a-form-item>
      </a-form>
    </a-modal>
    <!-- 详情弹窗 -->
    <a-modal
      v-model:visible="showDetailModal"
      title="投诉详情"
      :footer="null"
      @cancel="() => (showDetailModal = false)"
      :destroyOnClose="true"
    >
      <a-descriptions bordered column="1">
        <a-descriptions-item label="订单状态">{{ currentRecord?.order_status }}</a-descriptions-item>
        <a-descriptions-item label="手机号">{{ currentRecord?.mobile }}</a-descriptions-item>
        <a-descriptions-item label="姓名">{{ currentRecord?.name }}</a-descriptions-item>
        <a-descriptions-item label="身份证">{{ currentRecord?.id_card }}</a-descriptions-item>
        <a-descriptions-item label="投诉内容">{{ currentRecord?.content }}</a-descriptions-item>
        <a-descriptions-item label="备注">{{ currentRecord?.remark }}</a-descriptions-item>
        <a-descriptions-item label="反馈">{{ currentRecord?.feedback }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ currentRecord?.complaintTime }}</a-descriptions-item>
        <a-descriptions-item label="投诉图片">
          <div v-if="currentRecord?.photoUrls" class="photo-container">
            <Image
              :src="currentRecord.photoUrls"
              :preview="true"
              :preview-props="{
                mask: true
              }"
              class="complaint-thumbnail"
              :style="{
                width: '120px',
                height: '120px',
                maxWidth: '120px',
                maxHeight: '120px',
                objectFit: 'cover',
                borderRadius: '4px',
                border: '1px solid #d9d9d9'
              }"
              alt="投诉图片"
            />
          </div>
          <span v-else>暂无图片</span>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, reactive, watch } from 'vue';
  import useLoading from '@/hooks/loading';
  import { getComplaint, handleComplaint } from '@/api/suggestions/complaint';
  import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface';
  import cloneDeep from 'lodash/cloneDeep';
  import dayjs from 'dayjs';
  // 数据
  import { columns } from './data';
  // 表单
  import { useModal } from '/@/components/Modal';
  import { useI18n } from 'vue-i18n';
  import { Icon } from '@/components/Icon';
  import { Message, Image } from '@arco-design/web-vue';
  import { Pagination } from '@/types/global';
  
  const { t } = useI18n();
  const [registerModal, { openModal }] = useModal();
  
  // 配置项
  const densityList = computed(() => [
    { name: t('searchTable.size.mini'), value: 'mini' },
    { name: t('searchTable.size.small'), value: 'small' },
    { name: t('searchTable.size.medium'), value: 'medium' },
    { name: t('searchTable.size.large'), value: 'large' },
  ]);
  
  // 分页配置
  const basePagination: Pagination = {
    current: 1,
    pageSize: 10,
  };
  const pagination = reactive({
    ...basePagination,
    showTotal: true,
    showPageSize: true,
  });
  
  type SizeProps = 'mini' | 'small' | 'medium' | 'large';
  type Column = TableColumnData & { checked?: true };
  const { loading, setLoading } = useLoading(true);
  const renderData = ref([]);
  const cloneColumns = ref<Column[]>([]);
  const showColumns = ref<Column[]>([]);
  const size = ref<SizeProps>('large');

  const showProcessModal = ref(false);
  const showDetailModal = ref(false);
  const currentRecord = ref<any>(null);
  const processForm = ref({ remark: '', feedback: '' });
  const processFormRef = ref<any>();
  const processRules = {
    remark: [{ required: true, message: '请输入备注' }],
    feedback: [{ required: true, message: '请输入反馈' }],
  };
  
  // 新增：确定按钮禁用逻辑
  const isProcessFormInvalid = computed(() => {
    return !processForm.value.remark || !processForm.value.feedback;
  });
  
  // 查询条件
  const generateFormModel = () => {
    return {
      content: '',
    };
  };
  
  const formModel = ref(generateFormModel());
  
  
  // 修改 fetchData 函数
const fetchData = async () => {
    setLoading(true);
    try {
        const params = {
            page: pagination.current,
            limit: pagination.pageSize,
            ...formModel.value
        };

        // 直接获取响应数据
        const responseData = await getComplaint(params);

        // 根据控制台输出，responseData 直接是分页数据对象
        renderData.value = responseData.items;
        pagination.total = responseData.total;

        // 如果需要，可以更新分页信息
        if (responseData.page) {
            pagination.current = responseData.page;
        }
        if (responseData.pageSize) {
            pagination.pageSize = responseData.pageSize;
        }

    } catch (err) {
        // 处理网络错误或异常
        Message.error('获取投诉列表失败: ' + (err.message || '未知错误'));
        console.error('API 请求错误详情:', err);
    } finally {
        setLoading(false);
    }
};

  // 搜索
  const search = () => {
    pagination.current = 1;
    fetchData();
  };
  
  // 重置
  const reset = () => {
    formModel.value = generateFormModel();
    search();
  };
  
  // 初始化加载数据
  fetchData();
  
  // 表格密度设置
  const handleSelectDensity = (
    val: string | number | Record<string, any> | undefined,
    e: Event
  ) => {
    size.value = val as SizeProps;
  };

  // 监听列变化
  watch(
    () => columns.value,
    (val) => {
      cloneColumns.value = cloneDeep(val);
      cloneColumns.value.forEach((item, index) => {
        item.checked = true;
      });
      showColumns.value = cloneDeep(cloneColumns.value);
    },
    { deep: true, immediate: true }
  );
  
  // 分页变化
  const handlePaageChange = (page: number) => {
    pagination.current = page;
    fetchData();
  };
  
  // 页面大小变化
  const handlePaageSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.current = 1; // 重置到第一页
    fetchData();
  };

  function handleProcess(record:any) {
    currentRecord.value = record;
    processForm.value = { remark: '', feedback: '' };
    showProcessModal.value = true;
  }
  function handleDetail(record:any) {
    currentRecord.value = record;
    showDetailModal.value = true;
  }
  function handleProcessOk() {
    processFormRef.value.validate()
      .then(() => {
        // 校验通过才会走这里
        handleComplaint({
          id: currentRecord.value.id,
          remark: processForm.value.remark,
          feedback: processForm.value.feedback,
        }).then(() => {
          showProcessModal.value = false;
          fetchData();
          Message.success('处理成功');
        });
      })
      .catch(() => {
        // 校验失败，不提交，可选提示
        Message.error('请填写完整备注和反馈');
      });
  }
</script>

<script lang="ts">
  export default {
    name: 'Rule',
  };
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    height: 100%;
  }
  :deep(.arco-table-th) {
    &:last-child {
      .arco-table-th-item-title {
        margin-left: 16px;
      }
    }
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }
  .setting {
    display: flex;
    align-items: center;
    width: 200px;
    .title {
      margin-left: 12px;
      cursor: pointer;
    }
  }
  :deep(.general-card > .arco-card-header){
    padding: 10px 16px;
  }
  .iconbtn{
    user-select: none;
    cursor: pointer;
    opacity: .8;
    &:hover{
      opacity: 1;
    }
  }
  .operation-link {
    color: #1890ff;         /* 蓝色 */
    cursor: pointer;        /* 小手 */
    transition: color 0.2s;
    text-decoration: none;
  }
  .operation-link:hover {
    color: #40a9ff;         /* 更亮的蓝色 */
    text-decoration: underline;
  }
  
  /* 新增：图片相关样式 */
  .photo-container {
    display: flex;
    align-items: center;
    height: 100%;
    width: 100%;
  }
  
  :deep(.complaint-photo) {
    width: 40px !important;
    height: 40px !important;
    max-width: 40px !important;
    max-height: 40px !important;
    object-fit: cover !important;
    border-radius: 4px;
    border: 1px solid #d9d9d9;
    cursor: pointer;
    transition: all 0.2s;
    display: block;
  }
  
  .complaint-photo:hover {
    border-color: #1890ff;
    transform: scale(1.05);
  }

  .complaint-thumbnail {
    width: 120px !important;
    height: 120px !important;
    cursor: pointer;
    border-radius: 4px !important;
    border: 1px solid #d9d9d9 !important;
    transition: all 0.3s;
    object-fit: cover !important;
  }

  .complaint-thumbnail:hover {
    border-color: #1890ff !important;
    transform: scale(1.05);
  }
  
  /* 使用:deep()确保样式穿透到Arco Design组件内部 */
  :deep(.complaint-thumbnail) {
    width: 120px !important;
    height: 120px !important;
    max-width: 120px !important;
    max-height: 120px !important;
    cursor: pointer;
    border-radius: 4px !important;
    border: 1px solid #d9d9d9 !important;
    transition: all 0.3s;
    object-fit: cover !important;
  }

  :deep(.complaint-thumbnail:hover) {
    border-color: #ffffff !important;
    transform: scale(1.05);
  }
  
  /* 针对Arco Design Image组件的特殊处理 */
  :deep(.arco-image) {
    width: 120px !important;
    height: 120px !important;
    max-width: 120px !important;
    max-height: 120px !important;
  }
  
  :deep(.arco-image-img) {
    width: 120px !important;
    height: 120px !important;
    max-width: 120px !important;
    max-height: 120px !important;
    object-fit: cover !important;
    border-radius: 4px !important;
    border: 1px solid #d9d9d9 !important;
  }
  
  /* 自定义图片预览遮罩样式 */
  :deep(.custom-mask) {
    background-color: rgba(0, 0, 0, 0.8);
  }
  
  :deep(.arco-image-preview-img) {
    max-width: 60vw !important;
    max-height: 60vh !important;
    object-fit: contain !important;
  }
</style>
