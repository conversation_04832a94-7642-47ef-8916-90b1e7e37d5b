package oss

import (
	"bytes"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"fincore/global"
	"fincore/utils/config"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

type OSSClient struct {
	client *oss.Client
	bucket *oss.Bucket
	config config.OSS
}

// NewOSSClient 创建OSS客户端
func NewOSSClient(ossConfig config.OSS) (*OSSClient, error) {
	if !ossConfig.Enabled {
		return nil, fmt.Errorf("OSS is disabled")
	}

	client, err := oss.New(ossConfig.Endpoint, ossConfig.AccessKeyId, ossConfig.AccessKeySecret)
	if err != nil {
		return nil, fmt.Errorf("failed to create OSS client: %v", err)
	}

	bucket, err := client.Bucket(ossConfig.BucketName)
	if err != nil {
		return nil, fmt.Errorf("failed to get bucket: %v", err)
	}

	return &OSSClient{
		client: client,
		bucket: bucket,
		config: ossConfig,
	}, nil
}

// UploadFile 上传文件到OSS
func (o *OSSClient) UploadFile(reader io.Reader, filename string) (string, error) {
	// 生成OSS对象键
	objectKey := o.generateObjectKey(filename)

	// 上传文件
	err := o.bucket.PutObject(objectKey, reader)
	if err != nil {
		return "", fmt.Errorf("failed to upload file to OSS: %v", err)
	}

	// 返回文件URL
	fileURL := o.getFileURL(objectKey)
	return fileURL, nil
}

// generateObjectKey 生成OSS对象键
func (o *OSSClient) generateObjectKey(filename string) string {
	// 获取文件扩展名
	ext := filepath.Ext(filename)
	// 生成时间戳
	timestamp := time.Now().Format("20060102150405")
	// 生成随机文件名
	newFilename := fmt.Sprintf("%s_%s%s", strings.TrimSuffix(filename, ext), timestamp, ext)

	// 组合完整路径
	if o.config.BasePath != "" {
		return fmt.Sprintf("%s/%s", strings.Trim(o.config.BasePath, "/"), newFilename)
	}
	return newFilename
}

// getFileURL 获取文件访问URL， 外部访问请使用singedurl，这个有时效性
func (o *OSSClient) getFileURL(objectKey string) string {
	if o.config.Domain != "" {
		return fmt.Sprintf("%s/%s", strings.TrimRight(o.config.Domain, "/"), objectKey)
	}
	// 使用默认的OSS域名
	return fmt.Sprintf("https://%s.%s/%s", o.config.BucketName, o.config.Endpoint, objectKey)
}

// GetSignedURL 生成带过期时间的签名URL
func (o *OSSClient) GetSignedURL(objectKey string, expireTime time.Duration) (string, error) {
	signedURL, err := o.bucket.SignURL(objectKey, oss.HTTPGet, int64(expireTime.Seconds()))
	if err != nil {
		return "", fmt.Errorf("failed to generate signed URL: %v", err)
	}
	return signedURL, nil
}

// GetObjectKey 从完整URL中提取ObjectKey
func GetObjectKeyFromURL(fileURL string) string {
	// 从URL中提取ObjectKey
	parts := strings.Split(fileURL, "/")
	if len(parts) >= 4 {
		return strings.Join(parts[3:], "/")
	}
	return fileURL
}

// IsOSSEnabled 检查OSS是否启用
func IsOSSEnabled() bool {
	return global.App.Config.OSS.Enabled
}

// GetOSSClient 获取OSS客户端实例
func GetOSSClient() (*OSSClient, error) {
	return NewOSSClient(global.App.Config.OSS)
}

// GetFileBase64 获取文件的Base64编码，兼容OSS和本地存储
func GetFileBase64(filePath string) (string, error) {
	// 判断是否为OSS文件（通过URL格式判断）
	if IsOSSFile(filePath) {
		// OSS文件：下载后编码
		return GetOSSFileBase64(filePath)
	} else {
		// 本地文件：直接读取编码
		return getLocalFileBase64(filePath), nil
	}
}

// IsOSSFile 判断是否为OSS文件
func IsOSSFile(filePath string) bool {
	// OSS文件通常包含域名，如：https://bucket.oss-region.aliyuncs.com/path/file.jpg
	return strings.HasPrefix(filePath, "http://") || strings.HasPrefix(filePath, "https://")
}

// GetOSSFileBase64 获取OSS文件的Base64编码
func GetOSSFileBase64(fileURL string) (string, error) {
	if !IsOSSEnabled() {
		return "", fmt.Errorf("OSS未启用，无法处理OSS文件")
	}

	// 获取OSS客户端
	ossClient, err := GetOSSClient()
	if err != nil {
		return "", fmt.Errorf("获取OSS客户端失败: %v", err)
	}

	// 从URL中提取ObjectKey
	objectKey := GetObjectKeyFromURL(fileURL)
	if objectKey == "" {
		return "", fmt.Errorf("无法从URL中提取ObjectKey: %s", fileURL)
	}

	// 生成临时签名URL（1小时有效期）
	signedURL, err := ossClient.GetSignedURL(objectKey, time.Hour)
	if err != nil {
		return "", fmt.Errorf("生成签名URL失败: %v", err)
	}

	// 下载文件内容
	resp, err := http.Get(signedURL)
	if err != nil {
		return "", fmt.Errorf("下载OSS文件失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("下载OSS文件失败，状态码: %d", resp.StatusCode)
	}

	// 读取文件内容
	fileBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取OSS文件内容失败: %v", err)
	}

	// 进行Base64编码
	encodeString := base64.StdEncoding.EncodeToString(fileBytes)
	return encodeString, nil
}

// getLocalFileBase64 获取本地文件的Base64编码
func getLocalFileBase64(filePath string) string {
	file, err := os.Open(filePath)
	if err != nil {
		return ""
	}
	defer file.Close()

	fileBytes, err := io.ReadAll(file)
	if err != nil {
		return ""
	}

	return base64.StdEncoding.EncodeToString(fileBytes)
}

// SaveImageWithOSS 保存图片，兼容OSS和本地存储
func SaveImageWithOSS(photoData, serialNo string) (string, error) {
	if IsOSSEnabled() {
		return SaveImageToOSS(photoData, serialNo)
	}
	return SaveImageToLocal(photoData, serialNo)
}

// SaveImageToOSS 保存图片到OSS
func SaveImageToOSS(photoData, serialNo string) (string, error) {
	// 将BASE64字符串解码为字节数组
	imageBytes, err := base64.StdEncoding.DecodeString(photoData)
	if err != nil || len(imageBytes) == 0 {
		return "", fmt.Errorf("图片解码失败: %v", err)
	}

	// 获取OSS客户端
	ossClient, err := GetOSSClient()
	if err != nil {
		return "", fmt.Errorf("获取OSS客户端失败: %v", err)
	}

	// 生成文件名
	fileName := fmt.Sprintf("%s.jpg", serialNo)

	// 上传到OSS
	filePath, err := ossClient.UploadFile(bytes.NewReader(imageBytes), fileName)
	if err != nil {
		return "", fmt.Errorf("上传图片到OSS失败: %v", err)
	}

	return filePath, nil
}

// SaveImageToLocal 保存图片到本地
func SaveImageToLocal(photoData, serialNo string) (string, error) {
	// 将BASE64字符串解码为字节数组
	imageBytes, err := base64.StdEncoding.DecodeString(photoData)
	if err != nil || len(imageBytes) == 0 {
		return "", fmt.Errorf("图片解码失败: %v", err)
	}

	// 生成本地文件路径
	currentDate := time.Now().Format("20060102")
	uploadDir := fmt.Sprintf("resource/uploads/%s", currentDate)

	// 创建目录（如果不存在）
	if err := os.MkdirAll(uploadDir, os.ModePerm); err != nil {
		return "", fmt.Errorf("创建上传目录失败: %v", err)
	}

	// 生成文件名和完整路径
	fileName := fmt.Sprintf("%s.jpg", serialNo)
	filePath := filepath.Join(uploadDir, fileName)

	// 保存文件
	err = os.WriteFile(filePath, imageBytes, 0644)
	if err != nil {
		return "", fmt.Errorf("保存图片文件失败: %v", err)
	}

	return filePath, nil
}

// VerifyMD5WithPath 验证MD5值，兼容OSS和本地存储
func VerifyMD5WithPath(filePath, expectedMd5 string) bool {
	var fileBytes []byte
	var err error

	// 检查是否为OSS路径
	if IsOSSFile(filePath) {
		// OSS路径，使用OSS工具包获取文件内容
		base64Data, err := GetOSSFileBase64(filePath)
		if err != nil {
			return false
		}
		// 将base64解码为字节数组
		fileBytes, err = base64.StdEncoding.DecodeString(base64Data)
		if err != nil || len(fileBytes) == 0 {
			return false
		}
	} else {
		// 本地路径，直接读取文件
		fileBytes, err = os.ReadFile(filePath)
		if err != nil {
			return false
		}
	}

	// 计算MD5
	hash := md5.New()
	hash.Write(fileBytes)
	calculatedMd5 := hex.EncodeToString(hash.Sum(nil))

	return calculatedMd5 == expectedMd5
}

// RemoveFile 删除文件，兼容OSS和本地存储
func RemoveFile(filePath string) {
	if IsOSSFile(filePath) {
		// OSS文件，暂时不删除，目前不放开删除接口
	} else {
		// 本地文件，直接删除
		os.Remove(filePath)
	}
}
