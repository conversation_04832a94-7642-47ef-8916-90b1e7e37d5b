package blacklist

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"fincore/global"
	"fincore/model"

	"go.uber.org/zap"
)

// BlacklistType 黑名单类型
type BlacklistType string

const (
	BlacklistTypeA BlacklistType = "A" // A类黑名单
	BlacklistTypeB BlacklistType = "B" // B类黑名单
)

// BlacklistReason 黑名单原因
type BlacklistReason string

const (
	ReasonOverdue     BlacklistReason = "overdue"       // 逾期
	ReasonIDCard      BlacklistReason = "id_card"       // 身份证风险
	ReasonAddress     BlacklistReason = "address"       // 地址风险
	ReasonCardBin     BlacklistReason = "card_bin"      // 卡Bin风险
	ReasonMobile      BlacklistReason = "mobile"        // 手机号码风险
	ReasonBlacklist   BlacklistReason = "blacklist"     // 用户状态为手动拉黑黑名单
	ReasonNoChannelID BlacklistReason = "no_channel_id" // 用户没有渠道id

)

// BlacklistResult 黑名单检查结果
type BlacklistResult struct {
	IsBlacklisted bool              `json:"is_blacklisted"` // 是否在黑名单中
	BlacklistType BlacklistType     `json:"blacklist_type"` // 黑名单类型
	Reasons       []BlacklistReason `json:"reasons"`        // 黑名单原因列表
	Details       map[string]string `json:"details"`        // 详细信息
}

// BlacklistService 黑名单服务
type BlacklistService struct {
	billService     *model.BusinessRepaymentBillsService
	ocrService      *model.OCRIdentityRecordService
	accountService  *model.BusinessAppAccountService
	bankCardService *model.BusinessBankCardsService
	ctx             context.Context
}

// NewBlacklistService 创建黑名单服务实例
func NewBlacklistService(ctx context.Context) *BlacklistService {
	return &BlacklistService{
		billService:     model.NewBusinessRepaymentBillsService(ctx),
		ocrService:      model.NewOCRIdentityRecordService(),
		accountService:  model.NewBusinessAppAccountService(),
		bankCardService: model.NewBusinessBankCardsService(ctx),
	}
}

// CheckUserBlacklist 检查用户是否在黑名单中
// BlacklistData 预加载的黑名单检查数据
type BlacklistData struct {
	CustomerInfo *model.BusinessAppAccount
	Bills        []model.BusinessRepaymentBills
	OCRRecord    *model.OCRIdentityRecord
	BankCards    []model.BusinessBankCards
}

func (s *BlacklistService) CheckUserBlacklist(ctx context.Context, customerID uint64, customerInfo *model.BusinessAppAccount) (*BlacklistResult, error) {
	result := &BlacklistResult{
		IsBlacklisted: false,
		Reasons:       []BlacklistReason{},
		Details:       make(map[string]string),
	}

	// 预加载所有需要的数据，减少数据库查询次数
	data, err := s.preloadBlacklistData(ctx, customerID, customerInfo)
	if err != nil {
		global.App.Log.Error("预加载黑名单数据失败", zap.Error(err))
		// 即使预加载失败，也继续进行基本检查
		data = &BlacklistData{CustomerInfo: customerInfo}
	}

	// 2. 检查逾期情况
	if err := s.checkOverdueStatusOptimized(ctx, data, result); err != nil {
		global.App.Log.Error("检查逾期状态失败", zap.Error(err))
	}

	// 3. 检查身份证相关风险
	if err := s.checkIDCardRiskOptimized(ctx, data, result); err != nil {
		global.App.Log.Error("检查身份证风险失败", zap.Error(err))
	}

	// 4. 检查申请地址风险
	if err := s.checkAddressRiskOptimized(ctx, data, result); err != nil {
		global.App.Log.Error("检查地址风险失败", zap.Error(err))
	}

	// 5. 检查卡Bin风险
	if err := s.checkCardBinRiskOptimized(ctx, data, result); err != nil {
		global.App.Log.Error("检查卡Bin风险失败", zap.Error(err))
	}

	// 6. 检查手机号码风险
	if err := s.checkMobileRiskOptimized(ctx, data, result); err != nil {
		global.App.Log.Error("检查手机号码风险失败", zap.Error(err))
	}

	return result, nil
}

// preloadBlacklistData 预加载所有黑名单检查需要的数据
func (s *BlacklistService) preloadBlacklistData(ctx context.Context, customerID uint64, customerInfo *model.BusinessAppAccount) (*BlacklistData, error) {
	data := &BlacklistData{
		CustomerInfo: customerInfo,
	}

	// 并发加载数据以提高性能
	var wg sync.WaitGroup
	var billsErr, ocrErr, bankCardsErr error

	// 加载账单数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		data.Bills, billsErr = s.billService.GetBillsByUserID(int(customerID))
	}()

	// 加载OCR记录
	wg.Add(1)
	go func() {
		defer wg.Done()
		data.OCRRecord, ocrErr = s.ocrService.GetOCRRecordByUserID(uint32(customerID))
	}()

	// 加载银行卡数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		data.BankCards, bankCardsErr = s.bankCardService.GetBankCardsByUserID(int(customerID))
	}()

	wg.Wait()

	// 记录加载错误但不中断流程
	if billsErr != nil {
		global.App.Log.Warn("加载账单数据失败", zap.Error(billsErr))
	}
	if ocrErr != nil {
		global.App.Log.Warn("加载OCR记录失败", zap.Error(ocrErr))
	}
	if bankCardsErr != nil {
		global.App.Log.Warn("加载银行卡数据失败", zap.Error(bankCardsErr))
	}

	return data, nil
}

// checkOverdueStatusOptimized 检查逾期状态（优化版本）
func (s *BlacklistService) checkOverdueStatusOptimized(ctx context.Context, data *BlacklistData, result *BlacklistResult) error {
	if data.Bills == nil {
		return nil
	}

	now := time.Now()
	for _, bill := range data.Bills {
		// 获取到期日期（现在已经是time.Time类型）
		dueDate := bill.DueDate
		// 检查日期是否为零值
		if dueDate.IsZero() {
			// 记录日期为空的错误，但继续处理其他账单
			result.Details[fmt.Sprintf("empty_date_bill_%d", bill.ID)] = fmt.Sprintf("账单ID %d 的到期日期为空", bill.ID)
			continue
		}

		if bill.PaidAt == nil {
			// 未还款，检查是否逾期
			if now.After(dueDate) {
				// 已逾期未还款，属于A类黑名单
				overdueDays := int(now.Sub(dueDate).Hours() / 24)
				result.IsBlacklisted = true
				result.BlacklistType = BlacklistTypeA
				result.Reasons = append(result.Reasons, ReasonOverdue)
				result.Details["overdue_type"] = fmt.Sprintf("未还款且已逾期%d天", overdueDays)
				return nil
			}
		} else {
			// 已还款，检查是否逾期超过7天
			paidTime := *bill.PaidAt
			if paidTime.After(dueDate) {
				overdueDays := int(paidTime.Sub(dueDate).Hours() / 24)
				if overdueDays > 7 {
					// 逾期超过7天，属于B类黑名单
					result.IsBlacklisted = true
					result.BlacklistType = BlacklistTypeB
					result.Reasons = append(result.Reasons, ReasonOverdue)
					result.Details["overdue_days"] = fmt.Sprintf("%d天", overdueDays)
					return nil
				}
			}
		}
	}

	return nil
}

// checkOverdueStatus 检查逾期状态
func (s *BlacklistService) checkOverdueStatus(ctx context.Context, customerID uint64, result *BlacklistResult) error {
	// 获取用户的还款账单
	bills, err := s.billService.GetBillsByUserID(int(customerID))
	if err != nil {
		return fmt.Errorf("获取还款账单失败: %v", err)
	}

	now := time.Now()
	for _, bill := range bills {
		// 获取到期日期（现在已经是time.Time类型）
		dueDate := bill.DueDate
		// 检查日期是否为零值
		if dueDate.IsZero() {
			// 记录日期为空的错误，但继续处理其他账单
			result.Details[fmt.Sprintf("empty_date_bill_%d", bill.ID)] = fmt.Sprintf("账单ID %d 的到期日期为空", bill.ID)
			continue
		}

		if bill.PaidAt == nil {
			// 未还款，检查是否逾期
			if now.After(dueDate) {
				// 已逾期未还款，属于A类黑名单
				overdueDays := int(now.Sub(dueDate).Hours() / 24)
				result.IsBlacklisted = true
				result.BlacklistType = BlacklistTypeA
				result.Reasons = append(result.Reasons, ReasonOverdue)
				result.Details["overdue_type"] = fmt.Sprintf("未还款且已逾期%d天", overdueDays)
				return nil
			}
		} else {
			// 已还款，检查是否逾期超过7天
			paidTime := *bill.PaidAt
			if paidTime.After(dueDate) {
				overdueDays := int(paidTime.Sub(dueDate).Hours() / 24)
				if overdueDays > 7 {
					// 逾期超过7天，属于B类黑名单
					result.IsBlacklisted = true
					result.BlacklistType = BlacklistTypeB
					result.Reasons = append(result.Reasons, ReasonOverdue)
					result.Details["overdue_days"] = fmt.Sprintf("%d天", overdueDays)
					return nil
				}
			}
		}
	}

	return nil
}

// checkIDCardRiskOptimized 检查身份证相关风险（优化版本）
func (s *BlacklistService) checkIDCardRiskOptimized(ctx context.Context, data *BlacklistData, result *BlacklistResult) error {
	if data.OCRRecord == nil {
		return nil
	}

	// 检查身份证号码格式、年龄等
	if s.isIDCardRisky(data.OCRRecord.IDCard) {
		result.IsBlacklisted = true
		if result.BlacklistType == "" {
			result.BlacklistType = BlacklistTypeB
		}
		result.Reasons = append(result.Reasons, ReasonIDCard)
		result.Details["id_card_risk"] = "身份证存在风险"
	}

	// 检查地址省份是否合法
	if s.isAddressRisky(data.OCRRecord.Address) {
		result.IsBlacklisted = true
		if result.BlacklistType == "" {
			result.BlacklistType = BlacklistTypeB
		}
		result.Reasons = append(result.Reasons, ReasonAddress)
		result.Details["address_risk"] = "地址省份不在合法范围内"
	}

	return nil
}

// checkAddressRiskOptimized 检查申请地址风险（优化版本）
func (s *BlacklistService) checkAddressRiskOptimized(ctx context.Context, data *BlacklistData, result *BlacklistResult) error {
	if data.CustomerInfo == nil {
		return nil
	}

	// 检查省份是否在风险地区列表中
	if s.isProvinceRisky(data.CustomerInfo.Province) {
		result.IsBlacklisted = true
		if result.BlacklistType == "" {
			result.BlacklistType = BlacklistTypeB
		}
		result.Reasons = append(result.Reasons, ReasonAddress)
		result.Details["risky_province"] = data.CustomerInfo.Province
	}

	return nil
}

// checkCardBinRiskOptimized 检查卡Bin风险（优化版本）
func (s *BlacklistService) checkCardBinRiskOptimized(ctx context.Context, data *BlacklistData, result *BlacklistResult) error {
	if data.BankCards == nil {
		return nil
	}

	for _, card := range data.BankCards {
		if s.isCardBinRisky(card.BankCardNo) {
			result.IsBlacklisted = true
			if result.BlacklistType == "" {
				result.BlacklistType = BlacklistTypeB
			}
			result.Reasons = append(result.Reasons, ReasonCardBin)
			result.Details["risky_card_bin"] = card.BankCardNo[:6] // 只显示前6位
			break
		}
	}

	return nil
}

// checkMobileRiskOptimized 检查手机号码风险（优化版本）
func (s *BlacklistService) checkMobileRiskOptimized(ctx context.Context, data *BlacklistData, result *BlacklistResult) error {
	// 检查账户表中的手机号码
	if data.CustomerInfo != nil && s.isMobileRisky(data.CustomerInfo.Mobile) {
		result.IsBlacklisted = true
		if result.BlacklistType == "" {
			result.BlacklistType = BlacklistTypeB
		}
		result.Reasons = append(result.Reasons, ReasonMobile)
		result.Details["risky_mobile"] = "账户手机号码前三位不在合法范围内"
	}

	// 检查银行卡表中的手机号码
	if data.BankCards != nil {
		for _, card := range data.BankCards {
			if s.isMobileRisky(card.BankPhone) {
				result.IsBlacklisted = true
				if result.BlacklistType == "" {
					result.BlacklistType = BlacklistTypeB
				}
				result.Reasons = append(result.Reasons, ReasonMobile)
				result.Details["risky_bank_phone"] = "银行卡手机号码前三位不在合法范围内"
				break
			}
		}
	}

	return nil
}

// checkIDCardRisk 检查身份证相关风险
func (s *BlacklistService) checkIDCardRisk(ctx context.Context, customerID uint64, result *BlacklistResult) error {
	// 获取OCR身份证记录
	ocrRecord, err := s.ocrService.GetOCRRecordByUserID(uint32(customerID))
	if err != nil {
		return fmt.Errorf("获取OCR记录失败: %v", err)
	}

	if ocrRecord == nil {
		return nil
	}

	// 检查身份证号码格式、年龄等
	if s.isIDCardRisky(ocrRecord.IDCard) {
		result.IsBlacklisted = true
		if result.BlacklistType == "" {
			result.BlacklistType = BlacklistTypeB
		}
		result.Reasons = append(result.Reasons, ReasonIDCard)
		result.Details["id_card_risk"] = "身份证存在风险"
	}

	// 检查地址省份是否合法
	if s.isAddressRisky(ocrRecord.Address) {
		result.IsBlacklisted = true
		if result.BlacklistType == "" {
			result.BlacklistType = BlacklistTypeB
		}
		result.Reasons = append(result.Reasons, ReasonAddress)
		result.Details["address_risk"] = "地址省份不在合法范围内"
	}

	return nil
}

// checkAddressRisk 检查申请地址风险
func (s *BlacklistService) checkAddressRisk(ctx context.Context, customerID uint64, result *BlacklistResult) error {
	// 获取用户申请账户信息
	account, err := s.accountService.GetBusinessAppAccountByID(int64(customerID))
	if err != nil {
		return fmt.Errorf("获取账户信息失败: %v", err)
	}

	if account == nil {
		return nil
	}

	// 检查省份是否在风险地区列表中
	if s.isProvinceRisky(account.Province) {
		result.IsBlacklisted = true
		if result.BlacklistType == "" {
			result.BlacklistType = BlacklistTypeB
		}
		result.Reasons = append(result.Reasons, ReasonAddress)
		result.Details["risky_province"] = account.Province
	}

	return nil
}

// checkCardBinRisk 检查卡Bin风险
func (s *BlacklistService) checkCardBinRisk(ctx context.Context, customerID uint64, result *BlacklistResult) error {
	// 获取用户银行卡信息
	bankCards, err := s.bankCardService.GetBankCardsByUserID(int(customerID))
	if err != nil {
		return fmt.Errorf("获取银行卡信息失败: %v", err)
	}

	for _, card := range bankCards {
		if s.isCardBinRisky(card.BankCardNo) {
			result.IsBlacklisted = true
			if result.BlacklistType == "" {
				result.BlacklistType = BlacklistTypeB
			}
			result.Reasons = append(result.Reasons, ReasonCardBin)
			result.Details["risky_card_bin"] = card.BankCardNo[:6] // 只显示前6位
			break
		}
	}

	return nil
}

// isIDCardRisky 判断身份证是否存在风险
func (s *BlacklistService) isIDCardRisky(idCard string) bool {
	// 基本格式检查
	if len(idCard) != 18 {
		return true
	}

	// 检查年龄（出生年份）
	birthYear, err := strconv.Atoi(idCard[6:10])
	if err != nil {
		return true
	}

	currentYear := time.Now().Year()
	age := currentYear - birthYear

	// 年龄风险检查（18岁以下或70岁以上）
	if age < 18 || age > 70 {
		return true
	}

	return false
}

// isAddressRisky 判断地址是否存在风险（从OCR记录的address字段验证省份）
func (s *BlacklistService) isAddressRisky(address string) bool {
	// 合法省份列表
	validProvinces := []string{
		"河北省", "山西省", "辽宁省", "吉林省", "黑龙江省",
		"江苏省", "浙江省", "安徽省", "福建省", "江西省", "山东省", "河南省", "湖北省", "湖南省", "广东省",
		"海南省", "四川省", "贵州省", "云南省", "陕西省",
		"甘肃省", "青海省",
		"内蒙古自治区", "广西壮族自治区", "宁夏回族自治区",
		"北京市", "上海市", "天津市", "重庆市",
	}

	// 从地址中提取省份信息
	for _, province := range validProvinces {
		if strings.HasPrefix(address, province) {
			return false // 找到合法省份，不是风险地址
		}
	}

	return true // 没有找到合法省份，认为是风险地址
}

// isProvinceRisky 判断省份是否存在风险
func (s *BlacklistService) isProvinceRisky(province string) bool {
	// 风险省份列表（根据Excel表格策略配置）
	riskyProvinces := []string{
		"河北省", "山西省", "辽宁省", "吉林省", "黑龙江省",
		"江苏省", "浙江省", "安徽省", "福建省", "江西省", "山东省", "河南省", "湖北省", "湖南省", "广东省",
		"海南省", "四川省", "贵州省", "云南省", "陕西省",
		"甘肃省", "青海省",
		"内蒙古自治区", "广西壮族自治区", "宁夏回族自治区",
		"北京市", "上海市", "天津市", "重庆市",
	}

	for _, risky := range riskyProvinces {
		if strings.Contains(province, risky) {
			return true
		}
	}

	return false
}

// isCardBinRisky 判断卡Bin是否存在风险
func (s *BlacklistService) isCardBinRisky(cardNo string) bool {
	if len(cardNo) < 6 {
		return true
	}

	cardBin := cardNo[:6]

	// 风险卡Bin列表（根据Excel表格策略配置）
	riskyBins := []string{
		"622415", // 示例风险卡Bin
		"623098", // 示例风险卡Bin
		"621766",
		"623139",
		"625052",
	}

	for _, risky := range riskyBins {
		if cardBin == risky {
			return true
		}
	}

	return false
}

// parseDueDate 解析到期日期，支持多种格式
func (s *BlacklistService) parseDueDate(dateStr string) (time.Time, error) {
	// 尝试多种日期格式
	dateFormats := []string{
		"2006-01-02",          // 标准日期格式
		"2006-01-02 15:04:05", // 日期时间格式
		"2006/01/02",          // 斜杠分隔格式
		"01/02/2006",          // 美式日期格式
		"02-01-2006",          // 欧式日期格式
		time.RFC3339,          // RFC3339格式
		"2006-1-2",            // 无前导零格式
	}

	for _, format := range dateFormats {
		if t, err := time.Parse(format, dateStr); err == nil {
			return t, nil
		}
	}

	return time.Time{}, fmt.Errorf("无法解析日期格式: %s", dateStr)
}

// checkMobileRisk 检查手机号码风险
func (s *BlacklistService) checkMobileRisk(ctx context.Context, customerID uint64, result *BlacklistResult) error {
	// 检查账户表中的手机号码
	account, err := s.accountService.GetBusinessAppAccountByID(int64(customerID))
	if err != nil {
		return fmt.Errorf("获取账户信息失败: %v", err)
	}

	if account != nil && s.isMobileRisky(account.Mobile) {
		result.IsBlacklisted = true
		if result.BlacklistType == "" {
			result.BlacklistType = BlacklistTypeB
		}
		result.Reasons = append(result.Reasons, ReasonMobile)
		result.Details["risky_mobile"] = "账户手机号码前三位不在合法范围内"
	}

	// 检查银行卡表中的手机号码
	bankCards, err := s.bankCardService.GetBankCardsByUserID(int(customerID))
	if err != nil {
		return fmt.Errorf("获取银行卡信息失败: %v", err)
	}

	for _, card := range bankCards {
		if s.isMobileRisky(card.BankPhone) {
			result.IsBlacklisted = true
			if result.BlacklistType == "" {
				result.BlacklistType = BlacklistTypeB
			}
			result.Reasons = append(result.Reasons, ReasonMobile)
			result.Details["risky_bank_phone"] = "银行卡手机号码前三位不在合法范围内"
			break
		}
	}

	return nil
}

// isMobileRisky 判断手机号码是否存在风险
func (s *BlacklistService) isMobileRisky(mobile string) bool {
	if len(mobile) < 3 {
		return true
	}

	// 合法手机号码前三位列表
	validPrefixes := []string{
		"130", "131", "132", "133", "134", "135", "136", "137", "138", "139",
		"145", "146", "147", "149",
		"150", "151", "152", "153", "155", "156", "157", "158", "159",
		"162", "165", "166", "167",
		"171", "172", "173", "174", "175", "176", "177", "178",
		"180", "181", "182", "183", "184", "185", "186", "187", "188", "189",
		"191", "192", "193", "195", "196", "197", "198", "199",
	}

	mobilePrefix := mobile[:3]
	for _, validPrefix := range validPrefixes {
		if mobilePrefix == validPrefix {
			return false // 找到合法前缀，不是风险手机号
		}
	}

	return true // 没有找到合法前缀，认为是风险手机号
}

// GetBlacklistSummary 获取黑名单统计信息
func (s *BlacklistService) GetBlacklistSummary(ctx context.Context) (map[string]interface{}, error) {
	// 这里可以实现黑名单统计功能
	return map[string]interface{}{
		"total_checked": 0,
		"a_type_count":  0,
		"b_type_count":  0,
		"reasons_stats": map[string]int{
			"overdue":   0,
			"id_card":   0,
			"address":   0,
			"card_bin":  0,
			"mobile":    0,
			"blacklist": 0,
		},
	}, nil
}
