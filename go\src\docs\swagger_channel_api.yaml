openapi: 3.0.0
info:
  title: FinCore 渠道管理 API
  description: 渠道管理控制器的完整API接口文档
  version: 1.0.0
  contact:
    name: FinCore Team
    email: <EMAIL>

servers:
  - url: http://localhost:8080
    description: 渠道管理API服务器（开发环境）
  - url: https://api.fincore.com
    description: 渠道管理API服务器（生产环境）

components:
  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码，0为成功，1为失败
          example: 0
        message:
          type: string
          description: 响应消息
          example: "操作成功"
        data:
          type: object
          description: 响应数据
        exdata:
          type: object
          description: 扩展数据
        token:
          type: string
          description: 刷新后的token（如果有）
        time:
          type: integer
          description: 响应时间戳
          example: 1701234567
      required:
        - code
        - message
        - time

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误状态码
          example: 1
        message:
          type: string
          description: 错误消息
          example: "操作失败"
        data:
          type: object
          description: 错误详情
        time:
          type: integer
          description: 响应时间戳
          example: 1701234567

    LoanRule:
      type: object
      description: 放款规则
      properties:
        rule_id:
          type: string
          description: 规则ID
          example: "RULE_001"
        min_risk_score:
          type: number
          description: 风控阈值下限（0-100）
          minimum: 0
          maximum: 100
          example: 60.0
        max_risk_score:
          type: number
          description: 风控阈值上限（0-100）
          minimum: 0
          maximum: 100
          example: 85.0
      required:
        - rule_id
        - min_risk_score
        - max_risk_score

    ChannelItem:
      type: object
      description: 渠道信息
      properties:
        id:
          type: integer
          description: 渠道ID
          example: 1
        channel_name:
          type: string
          description: 渠道名称
          example: "宏盛渠道"
        channel_code:
          type: string
          description: 渠道编码
          example: "HS001"
        domain:
          type: string
          description: 渠道域名
          example: "https://hs.example.com"
        mobile:
          type: string
          description: 联系手机号
          example: "***********"
        password:
          type: string
          description: 渠道密码
          example: "******"
        channel_status:
          type: integer
          description: "渠道状态：0-禁用，1-启用"
          example: 1
        channel_usage:
          type: integer
          description: "渠道用途：0-推广，1-测试"
          example: 0
        uv:
          type: integer
          description: 独立访客数
          example: 1000
        register_count:
          type: integer
          description: 注册用户数
          example: 500
        real_name_count:
          type: integer
          description: 实名认证用户数
          example: 300
        download_count:
          type: integer
          description: 下载次数
          example: 800
        machine_review_count:
          type: integer
          description: 机审通过数
          example: 200
        transaction_success_count:
          type: integer
          description: 交易成功数
          example: 150
        retention_rate:
          type: number
          description: 留存率（百分比）
          example: 75.5
        operation:
          type: string
          description: 运营人员
          example: "张三"
        loan_limits:
          type: string
          description: 放款规则JSON字符串
          example: '[{"rule_id":"RULE_001","min_risk_score":60,"max_risk_score":85}]'
        auto_disbursement:
          type: integer
          description: "是否自动放款：0-否(人工审核)，1-是(自动放款)"
          example: 0
        risk_control_1_limit:
          type: number
          description: 风控1下限
          example: 1000.00
        risk_control_1_upper:
          type: number
          description: 风控1上限
          example: 5000.00
        risk_control_2_limit:
          type: number
          description: 风控2下限
          example: 5000.00
        risk_control_2_upper:
          type: number
          description: 风控2上限
          example: 10000.00
        point_amount_1:
          type: number
          description: 积分金额1
          example: 100.00
        point_amount_2:
          type: number
          description: 积分金额2
          example: 200.00
        total_amount:
          type: number
          description: 总金额
          example: 50000.00
        remark:
          type: string
          description: 备注信息
          example: "测试渠道"
        create_time:
          type: string
          description: 创建时间
          example: "2024-12-01 10:00:00"
        update_time:
          type: string
          description: 更新时间
          example: "2024-12-01 10:00:00"

    ChannelListRequest:
      type: object
      description: 渠道列表查询参数
      properties:
        channel_name:
          type: string
          description: 渠道名称（模糊查询）
          maxLength: 100
          example: "宏盛"
        channel_code:
          type: string
          description: 渠道编码
          maxLength: 50
          example: "HS001"
        channel_status:
          type: integer
          description: "渠道状态：0-禁用，1-启用"
          enum: [0, 1]
          example: 1
        channel_usage:
          type: integer
          description: "渠道用途：0-推广，1-测试"
          enum: [0, 1]
          example: 0
        mobile:
          type: string
          description: 手机号（模糊查询）
          maxLength: 20
          example: "138"
        page:
          type: integer
          description: 页码
          minimum: 1
          default: 1
          example: 1
        pageSize:
          type: integer
          description: 每页数量
          minimum: 1
          default: 10
          example: 20
        start_time:
          type: string
          description: 开始时间（YYYY-MM-DD）
          pattern: '^\d{4}-\d{2}-\d{2}$'
          example: "2024-01-01"
        end_time:
          type: string
          description: 结束时间（YYYY-MM-DD）
          pattern: '^\d{4}-\d{2}-\d{2}$'
          example: "2024-12-31"

    ChannelCreateRequest:
      type: object
      description: 创建渠道请求参数
      properties:
        channel_name:
          type: string
          description: 渠道名称
          minLength: 1
          maxLength: 100
          example: "新渠道"
        channel_code:
          type: string
          description: 渠道编码
          minLength: 1
          maxLength: 50
          example: "NEW001"
        domain:
          type: string
          description: 域名
          maxLength: 255
          example: "https://new.example.com"
        mobile:
          type: string
          description: 手机号
          maxLength: 20
          pattern: '^1[3-9]\d{9}$'
          example: "***********"
        password:
          type: string
          description: 密码
          maxLength: 255
          example: "password123"
        channel_status:
          type: integer
          description: "渠道状态：0-禁用，1-启用"
          enum: [0, 1]
          default: 1
          example: 1
        channel_usage:
          type: integer
          description: "渠道用途：0-推广，1-测试"
          enum: [0, 1]
          default: 0
          example: 0
        uv:
          type: integer
          description: 独立访客数
          minimum: 0
          default: 0
          example: 0
        register_count:
          type: integer
          description: 注册用户数
          minimum: 0
          default: 0
          example: 0
        real_name_count:
          type: integer
          description: 实名认证用户数
          minimum: 0
          default: 0
          example: 0
        download_count:
          type: integer
          description: 下载次数
          minimum: 0
          default: 0
          example: 0
        machine_review_count:
          type: integer
          description: 机审通过数
          minimum: 0
          default: 0
          example: 0
        transaction_success_count:
          type: integer
          description: 交易成功数
          minimum: 0
          default: 0
          example: 0
        retention_rate:
          type: number
          description: 留存率（百分比）
          minimum: 0
          maximum: 100
          default: 0
          example: 0
        operation:
          type: string
          description: 运营人员
          maxLength: 255
          example: "张三"
        loan_limits:
          type: string
          description: 放款规则JSON
          example: '[]'
        auto_disbursement:
          type: integer
          description: "是否自动放款：0-否(人工审核)，1-是(自动放款)"
          enum: [0, 1]
          default: 0
          example: 0
        risk_control_1_limit:
          type: number
          description: 风控1下限
          minimum: 0
          maximum: 999999999.99
          default: 0
          example: 1000.00
        risk_control_1_upper:
          type: number
          description: 风控1上限
          minimum: 0
          maximum: 999999999.99
          default: 0
          example: 5000.00
        risk_control_2_limit:
          type: number
          description: 风控2下限
          minimum: 0
          maximum: 999999999.99
          default: 0
          example: 5000.00
        risk_control_2_upper:
          type: number
          description: 风控2上限
          minimum: 0
          maximum: 999999999.99
          default: 0
          example: 10000.00
        point_amount_1:
          type: number
          description: 积分金额1
          minimum: 0
          maximum: 999999999.99
          default: 0
          example: 100.00
        point_amount_2:
          type: number
          description: 积分金额2
          minimum: 0
          maximum: 999999999.99
          default: 0
          example: 200.00
        total_amount:
          type: number
          description: 总金额
          minimum: 0
          maximum: 999999999.99
          default: 0
          example: 50000.00
        remark:
          type: string
          description: 备注信息
          maxLength: 500
          example: "备注信息"
      required:
        - channel_name
        - channel_code

    ChannelUpdateRequest:
      type: object
      description: 更新渠道请求参数（所有字段都是可选的）
      properties:
        channel_name:
          type: string
          description: 渠道名称
          minLength: 1
          maxLength: 100
          example: "更新后的渠道名称"
        channel_code:
          type: string
          description: 渠道编码
          minLength: 1
          maxLength: 50
          example: "UPD001"
        domain:
          type: string
          description: 域名
          maxLength: 255
          example: "https://updated.example.com"
        mobile:
          type: string
          description: 手机号
          maxLength: 20
          pattern: '^1[3-9]\d{9}$'
          example: "***********"
        password:
          type: string
          description: 密码
          maxLength: 255
          example: "newpassword123"
        channel_status:
          type: integer
          description: "渠道状态：0-禁用，1-启用"
          enum: [0, 1]
          example: 1
        channel_usage:
          type: integer
          description: "渠道用途：0-推广，1-测试"
          enum: [0, 1]
          example: 0
        uv:
          type: integer
          description: 独立访客数
          minimum: 0
          example: 1500
        register_count:
          type: integer
          description: 注册用户数
          minimum: 0
          example: 800
        real_name_count:
          type: integer
          description: 实名认证用户数
          minimum: 0
          example: 600
        download_count:
          type: integer
          description: 下载次数
          minimum: 0
          example: 1200
        machine_review_count:
          type: integer
          description: 机审通过数
          minimum: 0
          example: 400
        transaction_success_count:
          type: integer
          description: 交易成功数
          minimum: 0
          example: 300
        retention_rate:
          type: number
          description: 留存率（百分比）
          minimum: 0
          maximum: 100
          example: 80.5
        operation:
          type: string
          description: 运营人员
          maxLength: 255
          example: "李四"
        loan_limits:
          type: string
          description: 放款规则JSON
          example: '[{"rule_id":"RULE_002","min_risk_score":65,"max_risk_score":90}]'
        auto_disbursement:
          type: integer
          description: "是否自动放款：0-否(人工审核)，1-是(自动放款)"
          enum: [0, 1]
          example: 1
        risk_control_1_limit:
          type: number
          description: 风控1下限
          minimum: 0
          maximum: 999999999.99
          example: 2000.00
        risk_control_1_upper:
          type: number
          description: 风控1上限
          minimum: 0
          maximum: 999999999.99
          example: 8000.00
        risk_control_2_limit:
          type: number
          description: 风控2下限
          minimum: 0
          maximum: 999999999.99
          example: 8000.00
        risk_control_2_upper:
          type: number
          description: 风控2上限
          minimum: 0
          maximum: 999999999.99
          example: 15000.00
        point_amount_1:
          type: number
          description: 积分金额1
          minimum: 0
          maximum: 999999999.99
          example: 150.00
        point_amount_2:
          type: number
          description: 积分金额2
          minimum: 0
          maximum: 999999999.99
          example: 300.00
        total_amount:
          type: number
          description: 总金额
          minimum: 0
          maximum: 999999999.99
          example: 80000.00
        remark:
          type: string
          description: 备注信息
          maxLength: 500
          example: "更新后的备注信息"

    ChannelOption:
      type: object
      description: 渠道选项（用于下拉框）
      properties:
        value:
          type: integer
          description: 渠道ID
          example: 1
        label:
          type: string
          description: 渠道名称
          example: "宏盛渠道"
        id:
          type: integer
          description: 渠道ID
          example: 1
        name:
          type: string
          description: 渠道名称
          example: "宏盛渠道"

    LoanRulesUpdateRequest:
      type: object
      description: 更新放款规则请求参数
      properties:
        loan_rules:
          type: array
          description: 放款规则数组
          items:
            $ref: '#/components/schemas/LoanRule'
          example:
            - rule_id: "RULE_001"
              min_risk_score: 60.0
              max_risk_score: 85.0
            - rule_id: "RULE_002"
              min_risk_score: 85.0
              max_risk_score: 95.0
      required:
        - loan_rules

    ChannelCodeResponse:
      type: object
      description: 生成渠道编码响应
      properties:
        channel_code:
          type: string
          description: 生成的渠道编码
          example: "CH20241201001"

    PaginationResponse:
      type: object
      description: 分页响应
      properties:
        total:
          type: integer
          description: 总记录数
          example: 100
        page:
          type: integer
          description: 当前页码
          example: 1
        pageSize:
          type: integer
          description: 每页数量
          example: 20
        totalPages:
          type: integer
          description: 总页数
          example: 5
        hasNext:
          type: boolean
          description: 是否有下一页
          example: true
        hasPrev:
          type: boolean
          description: 是否有上一页
          example: false
        data:
          type: array
          description: 数据列表
          items:
            $ref: '#/components/schemas/ChannelItem'

paths:
  /business/channel/channelcontroller/listChannels:
    post:
      summary: 获取渠道列表
      description: 支持分页和多条件筛选的渠道列表查询
      tags:
        - 渠道管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChannelListRequest'
            example:
              channel_name: "宏盛"
              channel_status: 1
              page: 1
              pageSize: 20
      responses:
        '200':
          description: 获取渠道列表成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/PaginationResponse'
              example:
                code: 0
                message: "获取渠道列表成功"
                data:
                  total: 50
                  page: 1
                  pageSize: 20
                  totalPages: 3
                  hasNext: true
                  hasPrev: false
                  data:
                    - id: 1
                      channel_name: "宏盛渠道"
                      channel_code: "HS001"
                      domain: "https://hs.example.com"
                      mobile: "***********"
                      channel_status: 1
                      channel_usage: 0
                      uv: 1000
                      register_count: 500
                      real_name_count: 300
                      download_count: 800
                      machine_review_count: 200
                      transaction_success_count: 150
                      retention_rate: 75.5
                      operation: "张三"
                      create_time: "2024-12-01 10:00:00"
                      update_time: "2024-12-01 10:00:00"
                exdata: null
                token: ""
                time: 1701234567
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/channel/channelcontroller/getChannelDetail/{id}:
    get:
      summary: 获取渠道详情
      description: 根据渠道ID获取渠道的详细信息
      tags:
        - 渠道管理
      parameters:
        - name: id
          in: path
          required: true
          description: 渠道ID
          schema:
            type: integer
            minimum: 1
          example: 1
      responses:
        '200':
          description: 获取渠道详情成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ChannelItem'
              example:
                code: 0
                message: "获取渠道详情成功"
                data:
                  id: 1
                  channel_name: "宏盛渠道"
                  channel_code: "HS001"
                  domain: "https://hs.example.com"
                  mobile: "***********"
                  password: "******"
                  channel_status: 1
                  channel_usage: 0
                  uv: 1000
                  register_count: 500
                  real_name_count: 300
                  download_count: 800
                  machine_review_count: 200
                  transaction_success_count: 150
                  retention_rate: 75.5
                  operation: "张三"
                  loan_limits: '[{"rule_id":"RULE_001","min_risk_score":60,"max_risk_score":85}]'
                  auto_disbursement: 0
                  risk_control_1_limit: 1000.00
                  risk_control_1_upper: 5000.00
                  risk_control_2_limit: 5000.00
                  risk_control_2_upper: 10000.00
                  point_amount_1: 100.00
                  point_amount_2: 200.00
                  total_amount: 50000.00
                  remark: "测试渠道"
                  create_time: "2024-12-01 10:00:00"
                  update_time: "2024-12-01 10:00:00"
                exdata: null
                token: ""
                time: 1701234567
        '400':
          description: 参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 1
                message: "渠道ID格式无效"
                data: null
                time: 1701234567
        '404':
          description: 渠道不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 1
                message: "获取渠道详情失败"
                data: "渠道不存在"
                time: 1701234567
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/channel/channelcontroller/createChannel:
    post:
      summary: 创建渠道
      description: 创建新的渠道，渠道编码和渠道名称为必填项
      tags:
        - 渠道管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChannelCreateRequest'
            example:
              channel_name: "新渠道"
              channel_code: "NEW001"
              domain: "https://new.example.com"
              mobile: "***********"
              password: "password123"
              channel_status: 1
              channel_usage: 0
              operation: "张三"
              remark: "新创建的渠道"
      responses:
        '200':
          description: 创建渠道成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ChannelItem'
              example:
                code: 0
                message: "创建渠道成功"
                data:
                  id: 10
                  channel_name: "新渠道"
                  channel_code: "NEW001"
                  domain: "https://new.example.com"
                  mobile: "***********"
                  password: "******"
                  channel_status: 1
                  channel_usage: 0
                  uv: 0
                  register_count: 0
                  real_name_count: 0
                  download_count: 0
                  machine_review_count: 0
                  transaction_success_count: 0
                  retention_rate: 0
                  operation: "张三"
                  loan_limits: "[]"
                  auto_disbursement: 0
                  risk_control_1_limit: 0
                  risk_control_1_upper: 0
                  risk_control_2_limit: 0
                  risk_control_2_upper: 0
                  point_amount_1: 0
                  point_amount_2: 0
                  total_amount: 0
                  remark: "新创建的渠道"
                  create_time: "2024-12-01 15:00:00"
                  update_time: "2024-12-01 15:00:00"
                exdata: null
                token: ""
                time: 1701234567
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 1
                message: "参数验证失败"
                data:
                  errors:
                    - field: "channel_name"
                      message: "渠道名称不能为空"
                    - field: "channel_code"
                      message: "渠道编码不能为空"
                time: 1701234567
        '409':
          description: 渠道编码已存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 1
                message: "创建渠道失败"
                data: "渠道编码已存在"
                time: 1701234567
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/channel/channelcontroller/updateChannel/{id}:
    put:
      summary: 更新渠道
      description: 根据渠道ID更新渠道信息，所有字段都是可选的
      tags:
        - 渠道管理
      parameters:
        - name: id
          in: path
          required: true
          description: 渠道ID
          schema:
            type: integer
            minimum: 1
          example: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChannelUpdateRequest'
            example:
              channel_name: "更新后的渠道名称"
              domain: "https://updated.example.com"
              mobile: "***********"
              channel_status: 1
              operation: "李四"
              remark: "更新后的备注信息"
      responses:
        '200':
          description: 更新渠道成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ChannelItem'
              example:
                code: 0
                message: "更新渠道成功"
                data:
                  id: 1
                  channel_name: "更新后的渠道名称"
                  channel_code: "HS001"
                  domain: "https://updated.example.com"
                  mobile: "***********"
                  password: "******"
                  channel_status: 1
                  channel_usage: 0
                  uv: 1000
                  register_count: 500
                  real_name_count: 300
                  download_count: 800
                  machine_review_count: 200
                  transaction_success_count: 150
                  retention_rate: 75.5
                  operation: "李四"
                  loan_limits: '[{"rule_id":"RULE_001","min_risk_score":60,"max_risk_score":85}]'
                  auto_disbursement: 0
                  risk_control_1_limit: 1000.00
                  risk_control_1_upper: 5000.00
                  risk_control_2_limit: 5000.00
                  risk_control_2_upper: 10000.00
                  point_amount_1: 100.00
                  point_amount_2: 200.00
                  total_amount: 50000.00
                  remark: "更新后的备注信息"
                  create_time: "2024-12-01 10:00:00"
                  update_time: "2024-12-01 16:00:00"
                exdata: null
                token: ""
                time: 1701234567
        '400':
          description: 参数验证失败或渠道ID无效
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 渠道不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/channel/channelcontroller/deleteChannel/{id}:
    delete:
      summary: 删除渠道
      description: 根据渠道ID删除渠道（软删除或硬删除）
      tags:
        - 渠道管理
      parameters:
        - name: id
          in: path
          required: true
          description: 渠道ID
          schema:
            type: integer
            minimum: 1
          example: 1
      responses:
        '200':
          description: 删除渠道成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        nullable: true
              example:
                code: 0
                message: "删除渠道成功"
                data: null
                exdata: null
                token: ""
                time: 1701234567
        '400':
          description: 参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 1
                message: "渠道ID格式无效"
                data: null
                time: 1701234567
        '404':
          description: 渠道不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 1
                message: "删除渠道失败"
                data: "渠道不存在"
                time: 1701234567
        '409':
          description: 渠道正在使用中，无法删除
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 1
                message: "删除渠道失败"
                data: "渠道正在使用中，无法删除"
                time: 1701234567
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/channel/channelcontroller/getChannelOptions:
    get:
      summary: 获取渠道选项
      description: 获取所有启用状态的渠道选项，用于下拉框等组件
      tags:
        - 渠道管理
      responses:
        '200':
          description: 获取渠道选项成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/ChannelOption'
              example:
                code: 0
                message: "获取渠道选项成功"
                data:
                  - value: 1
                    label: "宏盛渠道"
                    id: 1
                    name: "宏盛渠道"
                  - value: 2
                    label: "公众号渠道"
                    id: 2
                    name: "公众号渠道"
                  - value: 3
                    label: "APP推广渠道"
                    id: 3
                    name: "APP推广渠道"
                exdata: null
                token: ""
                time: 1701234567
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/channel/channelcontroller/getChannelLoanRules/{id}:
    get:
      summary: 获取渠道放款规则
      description: 根据渠道ID获取该渠道的放款规则列表
      tags:
        - 渠道管理
      parameters:
        - name: id
          in: path
          required: true
          description: 渠道ID
          schema:
            type: integer
            minimum: 1
          example: 1
      responses:
        '200':
          description: 获取渠道放款规则成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/LoanRule'
              example:
                code: 0
                message: "获取渠道放款规则成功"
                data:
                  - rule_id: "RULE_001"
                    min_risk_score: 60.0
                    max_risk_score: 85.0
                  - rule_id: "RULE_002"
                    min_risk_score: 85.0
                    max_risk_score: 95.0
                exdata: null
                token: ""
                time: 1701234567
        '400':
          description: 参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 渠道不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/channel/channelcontroller/updateChannelLoanRules/{id}:
    put:
      summary: 更新渠道放款规则
      description: 根据渠道ID更新该渠道的放款规则列表
      tags:
        - 渠道管理
      parameters:
        - name: id
          in: path
          required: true
          description: 渠道ID
          schema:
            type: integer
            minimum: 1
          example: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoanRulesUpdateRequest'
            example:
              loan_rules:
                - rule_id: "RULE_001"
                  min_risk_score: 60.0
                  max_risk_score: 85.0
                - rule_id: "RULE_002"
                  min_risk_score: 85.0
                  max_risk_score: 95.0
      responses:
        '200':
          description: 更新渠道放款规则成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        nullable: true
              example:
                code: 0
                message: "更新放款规则成功"
                data: null
                exdata: null
                token: ""
                time: 1701234567
        '400':
          description: 参数验证失败或渠道ID无效
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 1
                message: "参数验证失败"
                data:
                  errors:
                    - field: "loan_rules"
                      message: "放款规则不能为空"
                time: 1701234567
        '404':
          description: 渠道不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/channel/channelcontroller/generateChannelCode:
    post:
      summary: 生成渠道编码
      description: 自动生成一个新的唯一渠道编码
      tags:
        - 渠道管理
      responses:
        '200':
          description: 生成渠道编码成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ChannelCodeResponse'
              example:
                code: 0
                message: "生成渠道编码成功"
                data:
                  channel_code: "CH20241201001"
                exdata: null
                token: ""
                time: 1701234567
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

tags:
  - name: 渠道管理
    description: 渠道管理相关的所有API接口，包括渠道的增删改查、放款规则管理、渠道选项获取等功能
