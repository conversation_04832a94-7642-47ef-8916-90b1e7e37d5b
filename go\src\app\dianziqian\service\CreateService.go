package service

import (
	"fincore/app/dianziqian/config"
	"fincore/app/dianziqian/httpUtils"
	"fincore/utils/log"
)

// 创建待签署文件
func CreateFile(dataJson string, fileConfig *config.FileConfig) []byte {
	apiUrl := "/contract/createContract"
	log.Info("创建待签署文件")
	response, err := httpUtils.SendRequest(apiUrl, dataJson, fileConfig)
	if err != nil {
		log.Info("创建待签署文件失败")

	}
	return response
}

// 添加签署方，入参为数组
func AddSigner(dataJson string) []byte {
	apiUrl := "/contract/addSigner"
	log.Info("添加签署方")
	response, err := httpUtils.SendRequest2(apiUrl, dataJson, nil)
	if err != nil {
		log.Error("添加签署方失败")

	}
	return response
}

// 下载合同
func DownloadFile(dataJson string) []byte {
	apiUrl := "/contract/downloadContract"
	log.Info("下载合同")
	response, err := httpUtils.SendRequest(apiUrl, dataJson, nil)
	if err != nil {
		log.Info("下载合同失败")

	}
	return response
}

func GetContract(dataJson string) []byte {
	apiUrl := "/contract/getContract"
	log.Info("获取合同状态")
	response, err := httpUtils.SendRequest(apiUrl, dataJson, nil)
	if err != nil {
		log.Info("获取合同状态失败")

	}
	return response
}
