// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameBusinessRepaymentBill = "business_repayment_bills"

// BusinessRepaymentBill 还款计划账单表
type BusinessRepaymentBill struct {
	ID                   uint32     `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true;index:idx_bill_income_query,priority:1" json:"id"`
	OrderID              uint32     `gorm:"column:order_id;type:int unsigned;not null;index:idx_bill_income_query,priority:3;index:idx_bills_order_due_period,priority:1;index:idx_order_id_period,priority:1;index:idx_order_id_status,priority:2;comment:订单ID" json:"order_id"`                           // 订单ID
	UserID               uint32     `gorm:"column:user_id;type:int unsigned;not null;index:idx_user_id,priority:1;comment:用户ID" json:"user_id"`                                                                                                                                                             // 用户ID
	PeriodNumber         uint32     `gorm:"column:period_number;type:int unsigned;not null;index:idx_bills_order_due_period,priority:3;index:idx_order_id_period,priority:2;comment:期数 (从1开始)" json:"period_number"`                                                                                        // 期数 (从1开始)
	DuePrincipal         float64    `gorm:"column:due_principal;type:decimal(10,2);not null;default:0.00;comment:应还本金，单位：元" json:"due_principal"`                                                                                                                                                           // 应还本金，单位：元
	DueInterest          float64    `gorm:"column:due_interest;type:decimal(15,2);not null;comment:当期应还利息" json:"due_interest"`                                                                                                                                                                             // 当期应还利息
	DueGuaranteeFee      float64    `gorm:"column:due_guarantee_fee;type:decimal(15,2);not null;comment:当期应还担保费" json:"due_guarantee_fee"`                                                                                                                                                                  // 当期应还担保费
	DueOtherFees         float64    `gorm:"column:due_other_fees;type:decimal(15,2);not null;comment:当期应还其他费用" json:"due_other_fees"`                                                                                                                                                                       // 当期应还其他费用
	LateFee              float64    `gorm:"column:late_fee;type:decimal(15,2);not null;default:0.00;comment:逾期罚息" json:"late_fee"`                                                                                                                                                                          // 逾期罚息
	PaidAmount           float64    `gorm:"column:paid_amount;type:decimal(15,2);not null;default:0.00;comment:当期已还金额" json:"paid_amount"`                                                                                                                                                                  // 当期已还金额
	Status               int32      `gorm:"column:status;type:tinyint;not null;index:idx_due_date_status,priority:1;index:idx_order_id_status,priority:1;comment:账单状态: 0-待支付；1-已支付；2-逾期已支付；3-逾期待支付；4-已取消；5-已结算；6-已退款；7-部分还款；8-提前结清；9-逾期部分支付" json:"status"`                                                 // 账单状态: 0-待支付；1-已支付；2-逾期已支付；3-逾期待支付；4-已取消；5-已结算；6-已退款；7-部分还款；8-提前结清；9-逾期部分支付
	DueDate              time.Time  `gorm:"column:due_date;type:date;not null;index:idx_bill_income_query,priority:2;index:idx_bills_due_date,priority:1;index:idx_bills_order_due_period,priority:2;index:idx_due_date_only,priority:1;index:idx_due_date_status,priority:2;comment:应还款日" json:"due_date"` // 应还款日
	DeductRetryCount     uint32     `gorm:"column:deduct_retry_count;type:tinyint unsigned;not null;comment:自动扣款重试次数" json:"deduct_retry_count"`                                                                                                                                                            // 自动扣款重试次数
	LastDeductAttemptAt  *time.Time `gorm:"column:last_deduct_attempt_at;type:timestamp;comment:上次自动扣款尝试时间" json:"last_deduct_attempt_at"`                                                                                                                                                                  // 上次自动扣款尝试时间
	PaidAt               *time.Time `gorm:"column:paid_at;type:timestamp;comment:当期结清时间" json:"paid_at"`                                                                                                                                                                                                    // 当期结清时间
	CreatedAt            *time.Time `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt            *time.Time `gorm:"column:updated_at;type:timestamp;default:CURRENT_TIMESTAMP" json:"updated_at"`
	TotalDueAmount       float64    `gorm:"column:total_due_amount;type:decimal(15,2);not null;default:0.00;comment:当期应还总额(担保费+资管费+逾期费)" json:"total_due_amount"`            // 当期应还总额(担保费+资管费+逾期费)
	AssetManagementEntry float64    `gorm:"column:asset_management_entry;type:decimal(15,2);not null;default:0.00;comment:当期资管费用(本金+利息+其他费用)" json:"asset_management_entry"` // 当期资管费用(本金+利息+其他费用)
	TotalRefundAmount    float64    `gorm:"column:total_refund_amount;type:decimal(15,2);not null;default:0.00;comment:累计退款金额" json:"total_refund_amount"`                   // 累计退款金额
	TotalWaiveAmount     float64    `gorm:"column:total_waive_amount;type:decimal(15,2);not null;default:0.00;comment:累计减免金额" json:"total_waive_amount"`                     // 累计减免金额
	CollectionAssigneeID *int32     `gorm:"column:collection_assignee_id;type:int;comment:催收人id" json:"collection_assignee_id"`                                              // 催收人id
	LastCollectionTime   *time.Time `gorm:"column:last_collection_time;type:datetime;comment:最近催收时间" json:"last_collection_time"`                                            // 最近催收时间
}

// TableName BusinessRepaymentBill's table name
func (*BusinessRepaymentBill) TableName() string {
	return TableNameBusinessRepaymentBill
}
