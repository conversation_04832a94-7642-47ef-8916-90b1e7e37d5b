package error_handler

import "fincore/thirdparty/types"

// ErrorMapper 错误映射器接口
type ErrorMapper interface {
	MapError(response *types.Response) *types.Response
}

// StandardErrorMapper 标准错误映射器
type StandardErrorMapper struct {
	errorCodeMap map[string]ErrorInfo
}

// ErrorInfo 错误信息
type ErrorInfo struct {
	Code        string `json:"code"`
	Message     string `json:"message"`
	Description string `json:"description"`
	Retryable   bool   `json:"retryable"`
}

// NewStandardErrorMapper 创建标准错误映射器
func NewStandardErrorMapper() *StandardErrorMapper {
	return &StandardErrorMapper{
		errorCodeMap: map[string]ErrorInfo{
			// 通用错误码
			"SUCCESS":          {Code: "0000", Message: "成功", Retryable: false},
			"100000":           {Code: "0000", Message: "成功", Retryable: false}, // 电子签成功码
			"SYSTEM_ERROR":     {Code: "9999", Message: "系统错误", Retryable: true},
			"PARAM_ERROR":      {Code: "1001", Message: "参数错误", Retryable: false},
			"AUTH_ERROR":       {Code: "1002", Message: "认证失败", Retryable: false},
			"PERMISSION_ERROR": {Code: "1003", Message: "权限不足", Retryable: false},
			"RATE_LIMIT":       {Code: "1004", Message: "请求频率超限", Retryable: true},
			"SERVICE_UNAVAIL":  {Code: "1005", Message: "服务不可用", Retryable: true},
			"TIMEOUT":          {Code: "1006", Message: "请求超时", Retryable: true},
			"NETWORK_ERROR":    {Code: "1007", Message: "网络错误", Retryable: true},
		},
	}
}

// AddErrorMapping 添加错误映射
func (m *StandardErrorMapper) AddErrorMapping(originalCode string, errorInfo ErrorInfo) {
	m.errorCodeMap[originalCode] = errorInfo
}

// MapError 映射错误
func (m *StandardErrorMapper) MapError(response *types.Response) *types.Response {
	if response.Success {
		return response
	}

	// 优先使用响应中的错误码
	originalCode := response.Code
	if originalCode == "" {
		// 如果没有错误码，根据HTTP状态码映射
		originalCode = m.mapHTTPStatusToCode(response.StatusCode)
	}

	if errorInfo, exists := m.errorCodeMap[originalCode]; exists {
		response.Code = errorInfo.Code
		if response.Message == "" {
			response.Message = errorInfo.Message
		}
	} else {
		// 未知错误
		response.Code = "9999"
		if response.Message == "" {
			response.Message = "未知错误"
		}
	}

	return response
}

// mapHTTPStatusToCode 将HTTP状态码映射为错误码
func (m *StandardErrorMapper) mapHTTPStatusToCode(statusCode int) string {
	switch statusCode {
	case 400:
		return "PARAM_ERROR"
	case 401, 403:
		return "AUTH_ERROR"
	case 404:
		return "SERVICE_UNAVAIL"
	case 429:
		return "RATE_LIMIT"
	case 500, 502, 503:
		return "SERVICE_UNAVAIL"
	case 504:
		return "TIMEOUT"
	default:
		if statusCode >= 500 {
			return "SYSTEM_ERROR"
		}
		return "SYSTEM_ERROR"
	}
}

// GetErrorInfo 获取错误信息
func (m *StandardErrorMapper) GetErrorInfo(code string) (ErrorInfo, bool) {
	info, exists := m.errorCodeMap[code]
	return info, exists
}
