package model

import (
	"context"
	"database/sql"
	"fincore/global"
	"fmt"
	"time"

	"fincore/utils/gform" //数据库操作

	_ "github.com/go-sql-driver/mysql"
)

type Decimal float64

var err error
var engin *gform.Engin

// 取得数据库连接实例
func MyInit(starType interface{}) {
	global.App.Log.Info(fmt.Sprintf("连接数据库中:%v", starType))
	// 配置已在main.go中初始化，这里不需要重复初始化
	dsbSource := fmt.Sprintf("%v:%v@tcp(%v:%v)/%v?charset=utf8&parseTime=True&loc=Local&timeout=1000ms", global.App.Config.DBconf.Username, global.App.Config.DBconf.Password, global.App.Config.DBconf.Hostname, global.App.Config.DBconf.Hostport, global.App.Config.DBconf.Database)
	engin, err = gform.Open(&gform.Config{Driver: global.App.Config.DBconf.Driver, Dsn: dsbSource, Prefix: global.App.Config.DBconf.Prefix})
	if err != nil {
		global.App.Log.Info(fmt.Sprintf("数据库连接实例错误: %v", err))
	} else {
		global.App.Log.Info(fmt.Sprintf("连接数据库成功:%v", starType))

		// 设置连接池参数
		engin.GetExecuteDB().SetMaxIdleConns(10)                  //连接池最大空闲连接数,不设置, 默认无
		engin.GetExecuteDB().SetMaxOpenConns(50)                  // 连接池最大连接数,不设置, 默认无限
		engin.GetExecuteDB().SetConnMaxLifetime(59 * time.Second) //时间比超时时间短
		engin.GetQueryDB().Exec("SET @@sql_mode='NO_ENGINE_SUBSTITUTION';")

		// 使用新的SQL日志器替换默认日志器
		engin.Use(gform.DefaultSQLLogger())
	}
}

// DBOption 数据库选项函数类型
type DBOption func(*gform.Engin)

// WithContext 设置context选项
func WithContext(ctx context.Context) DBOption {
	return func(e *gform.Engin) {
		// 使用带context的SQL日志器
		e.Use(gform.SQLLoggerWithContext(ctx))
	}
}

// controller层调用
func DB(opts ...DBOption) gform.IOrm {
	// 如果有选项，创建新的引擎实例并应用选项
	if len(opts) > 0 {
		// 创建新的ORM实例
		orm := engin.NewOrm()

		// 应用选项到引擎
		for _, opt := range opts {
			opt(engin)
		}

		return orm
	}

	// 默认行为，保持向后兼容
	return engin.NewOrm()
}
func DBEV() *gform.Engin {
	return engin
}

// 新建数据库
func CreateDataBase(Username, Password, Hostname, Hostport, Database interface{}) {
	// 配置已在main.go中初始化，这里不需要重复初始化
	dsbSource := fmt.Sprintf("%v:%v@tcp(%v:%v)/%v?charset=utf8&parseTime=True&loc=Local&timeout=1000ms", Username, Password, Hostname, Hostport, "")
	engin, err = gform.Open(&gform.Config{Driver: global.App.Config.DBconf.Driver, Dsn: dsbSource})
	if err != nil {
		global.App.Log.Info(fmt.Sprintf("创建时，数据库连接实例错误: %v", err))
	} else {
		engin.GetQueryDB().Exec(fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %v DEFAULT CHARACTER SET utf8mb4 DEFAULT COLLATE utf8mb4_general_ci", Database))
	}
}

// 导入数据库文件
func ExecSql(rows string) (sql.Result, error) {
	Result, error := engin.GetExecuteDB().Exec(rows)
	if error != nil {
		global.App.Log.Info(fmt.Sprintf("导入数据失败:%v。%v", error, Result))
		return nil, error
	}
	return Result, nil
}

// 取得总行数
func GetTotal(tablename string, wheres map[string]interface{}) int64 {
	total, _ := DB().Table(tablename).Where(wheres).Count()
	return total
}
