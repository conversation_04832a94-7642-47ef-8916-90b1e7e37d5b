<template>
	<view class="no-data">
		<image src="/static/image/noData.png" mode="widthFix"></image>
		<text>{{ name }}</text>
	</view>
</template>

<script>
	export default {
		props: {
			name: {
				type: String,
				default: '暂无数据'
			}
		}
	}
	
</script>

<style lang="scss" scoped>
	.no-data{
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		padding: 50rpx;
		image{
			width: 200rpx;
			height: 200rpx;
		}
		text{
			color: #444;
			font-size: 32rpx;
			margin-top: 30rpx;
		}
	}
</style>