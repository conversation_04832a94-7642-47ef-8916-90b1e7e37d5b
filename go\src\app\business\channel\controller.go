package channel

import (
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/log"
	"fincore/utils/results"
	"fmt"
	"reflect"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ChannelController struct{}

func init() {
	controller := ChannelController{}
	gf.Register(&controller, reflect.TypeOf(controller).PkgPath())
}

// ListChannels 获取渠道列表
func (c *ChannelController) ListChannels(ctx *gin.Context) {
	// 1. 参数校验
	schema := GetChannelListSchema()
	validator := jsonschema.NewValidator(schema)

	// 从请求体获取数据（POST请求）
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		// 如果JSON解析失败，尝试从查询参数获取（兼容GET请求）
		requestData = map[string]interface{}{
			"channel_name":   ctx.Query("channel_name"),
			"channel_code":   ctx.Query("channel_code"),
			"channel_status": ctx.Query("channel_status"),
			"channel_usage":  ctx.Query("channel_usage"),
			"mobile":         ctx.Query("mobile"),
			"page":           ctx.Query("page"),
			"pageSize":       ctx.Query("pageSize"),
			"start_time":     ctx.Query("start_time"),
			"end_time":       ctx.Query("end_time"),
		}
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		switch val := v.(type) {
		case string:
			if val != "" {
				cleanData[k] = val
			}
		case float64:
			cleanData[k] = val
		case int:
			cleanData[k] = val
		case bool:
			cleanData[k] = val
		}
	}

	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := NewChannelService()
	result, err := service.GetChannelListByParams(cleanData)
	if err != nil {
		results.Failed(ctx, "获取渠道列表失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取渠道列表成功", result, nil)
}

// GetChannelDetail 获取渠道详情
func (c *ChannelController) GetChannelDetail(ctx *gin.Context) {
	// 1. 参数校验
	idStr := ctx.Param("id")
	if idStr == "" {
		results.Failed(ctx, "参数错误", "渠道ID不能为空")
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		results.Failed(ctx, "参数错误", "渠道ID格式无效")
		return
	}

	// 2. 业务逻辑处理
	service := NewChannelService()
	result, err := service.GetChannelDetail(id)
	if err != nil {
		results.Failed(ctx, "获取渠道详情失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取渠道详情成功", result, nil)
}

// CreateChannel 创建渠道
func (c *ChannelController) CreateChannel(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetChannelCreateSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := NewChannelService()
	result, err := service.CreateChannel(validationResult.Data)
	if err != nil {
		results.Failed(ctx, "创建渠道失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "创建渠道成功", result, nil)
}

// UpdateChannel 更新渠道
func (c *ChannelController) UpdateChannel(ctx *gin.Context) {
	// 1. 参数解析
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	// 2. 参数校验（包含id）
	schema := GetChannelUpdateSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 3. 提取ID
	idInterface, exists := requestData["id"]
	if !exists {
		results.Failed(ctx, "参数错误", "渠道ID不能为空")
		return
	}

	id, ok := idInterface.(float64)
	if !ok {
		results.Failed(ctx, "参数错误", "渠道ID格式无效")
		return
	}

	// 4. 业务逻辑处理
	service := NewChannelService()
	result, err := service.UpdateChannel(int(id), validationResult.Data)
	if err != nil {
		results.Failed(ctx, "更新渠道失败", err.Error())
		return
	}

	// 5. 返回结果
	results.Success(ctx, "更新渠道成功", result, nil)
}

// DeleteChannel 删除渠道
func (c *ChannelController) DeleteChannel(ctx *gin.Context) {
	// 1. 参数解析
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	// 2. 提取ID
	idInterface, exists := requestData["id"]
	if !exists {
		results.Failed(ctx, "参数错误", "渠道ID不能为空")
		return
	}

	id, ok := idInterface.(float64)
	if !ok {
		results.Failed(ctx, "参数错误", "渠道ID格式无效")
		return
	}

	// 3. 业务逻辑处理
	service := NewChannelService()
	err := service.DeleteChannel(int(id))
	if err != nil {
		results.Failed(ctx, "删除渠道失败", err.Error())
		return
	}

	// 4. 返回结果
	results.Success(ctx, "删除渠道成功", nil, nil)
}

// GetChannelOptions 获取渠道选项
func (c *ChannelController) GetChannelOptions(ctx *gin.Context) {
	// 业务逻辑处理
	service := NewChannelService()
	result, err := service.GetChannelOptions()
	if err != nil {
		results.Failed(ctx, "获取渠道选项失败", err.Error())
		return
	}

	// 返回结果
	results.Success(ctx, "获取渠道选项成功", result, nil)
}

// GetChannelLoanRules 获取渠道放款规则
func (c *ChannelController) GetChannelLoanRules(ctx *gin.Context) {
	// 1. 参数校验
	idStr := ctx.Param("id")
	if idStr == "" {
		results.Failed(ctx, "参数错误", "渠道ID不能为空")
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		results.Failed(ctx, "参数错误", "渠道ID格式无效")
		return
	}

	// 2. 业务逻辑处理
	service := NewChannelService()
	result, err := service.GetChannelLoanRules(id)
	if err != nil {
		results.Failed(ctx, "获取渠道放款规则失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取渠道放款规则成功", result, nil)
}

// UpdateChannelLoanRules 更新渠道放款规则
func (c *ChannelController) UpdateChannelLoanRules(ctx *gin.Context) {
	// 1. 参数校验
	idStr := ctx.Param("id")
	if idStr == "" {
		results.Failed(ctx, "参数错误", "渠道ID不能为空")
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		results.Failed(ctx, "参数错误", "渠道ID格式无效")
		return
	}

	// 2. 请求体解析
	var requestBody map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestBody); err != nil {
		results.Failed(ctx, "参数错误", "请求体格式错误")
		return
	}

	// 3. 验证规则数据
	rulesData, ok := requestBody["rules"].([]interface{})
	if !ok {
		results.Failed(ctx, "参数错误", "放款规则数据格式错误")
		return
	}

	// 4. 验证每个规则
	schema := GetLoanRuleSchema()
	validator := jsonschema.NewValidator(schema)

	for i, ruleData := range rulesData {
		if ruleMap, ok := ruleData.(map[string]interface{}); ok {
			result := validator.Validate(ruleMap)
			if !result.Valid {
				errorMsg := "验证失败"
				if len(result.Errors) > 0 {
					errorMsg = result.Errors[0].Message
				}
				results.Failed(ctx, "参数错误", fmt.Sprintf("规则 %d %s", i+1, errorMsg))
				return
			}
		} else {
			results.Failed(ctx, "参数错误", fmt.Sprintf("规则 %d 数据格式错误", i+1))
			return
		}
	}

	// 5. 业务逻辑处理
	service := NewChannelService()
	err = service.UpdateChannelLoanRules(id, rulesData)
	if err != nil {
		results.Failed(ctx, "更新渠道放款规则失败", err.Error())
		return
	}

	// 6. 返回结果
	results.Success(ctx, "更新渠道放款规则成功", nil, nil)
}

// GenerateChannelCode 生成新的渠道编码
func (c *ChannelController) GenerateChannelCode(ctx *gin.Context) {
	// 业务逻辑处理
	service := NewChannelService()
	result, err := service.GenerateNewChannelCode()
	if err != nil {
		results.Failed(ctx, "生成渠道编码失败", err.Error())
		return
	}

	// 返回结果
	results.Success(ctx, "生成渠道编码成功", map[string]string{"channel_code": result}, nil)
}

// GenerateChannelInvitation 生成渠道邀请链接和二维码
func (c *ChannelController) GenerateChannelInvitation(ctx *gin.Context) {
	log.Info("开始生成渠道邀请链接")

	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		log.Error("生成邀请链接参数解析失败: %v", err)
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetChannelInvitationSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		log.Error("生成邀请链接参数验证失败: %v", validationResult.Errors)
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := NewChannelService()
	result, err := service.GenerateInvitation(validationResult.Data)
	if err != nil {
		log.Error("生成邀请链接失败: %v", err)
		results.Failed(ctx, "生成邀请链接失败", err.Error())
		return
	}

	// 3. 返回结果
	log.Info("生成邀请链接成功，渠道编码: %s", result.ChannelCode)
	results.Success(ctx, "生成邀请链接成功", result, nil)
}

// CheckChannelStatus 检查渠道状态
func (c *ChannelController) CheckChannelStatus(ctx *gin.Context) {
	channelCode := ctx.Param("channel_code")
	log.Info("开始检查渠道状态，渠道编码: %s", channelCode)

	if channelCode == "" {
		log.Error("检查渠道状态失败: 渠道编码为空")
		results.Failed(ctx, "参数错误", "渠道编码不能为空")
		return
	}

	// 参数验证
	requestData := map[string]interface{}{
		"channel_code": channelCode,
	}

	schema := GetChannelStatusSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		log.Error("检查渠道状态参数验证失败: %v", validationResult.Errors)
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 业务逻辑处理
	service := NewChannelService()
	result, err := service.CheckChannelStatus(channelCode)
	if err != nil {
		log.Error("检查渠道状态失败: %v", err)
		results.Failed(ctx, "检查渠道状态失败", err.Error())
		return
	}

	log.Info("检查渠道状态成功，渠道编码: %s, 状态: %d", channelCode, result.ChannelStatus)
	results.Success(ctx, "获取渠道状态成功", result, nil)
}

// GetProductRules 获取产品规则列表
func (c *ChannelController) GetProductRules(ctx *gin.Context) {
	// 1. 业务逻辑处理
	service := NewChannelService()
	result, err := service.GetProductRules()
	if err != nil {
		results.Failed(ctx, "获取产品规则列表失败", err.Error())
		return
	}

	// 2. 返回结果
	results.Success(ctx, "获取产品规则列表成功", result, nil)
}
