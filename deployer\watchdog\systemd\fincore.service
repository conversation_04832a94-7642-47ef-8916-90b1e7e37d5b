[Unit]
Description=Fincore Application Service
Documentation=https://github.com/fincore/fincore
After=network.target network-online.target
Wants=network-online.target

[Service]
Type=forking
User=fincore
Group=fincore
WorkingDirectory=/opt/fincore/build/app
ExecStart=/opt/fincore/build/app/bin/fincore
PIDFile=/opt/fincore/build/app/logs/fincore.pid
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID

# 注意：如果应用程序支持forking模式，可以改为Type=forking并启用PIDFile
# PIDFile=/opt/fincore/build/app/logs/fincore.pid

# 重启策略
Restart=on-failure
RestartSec=5
StartLimitInterval=300
StartLimitBurst=3

# 资源限制
LimitNOFILE=65536
LimitNPROC=32768
LimitCORE=0

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/fincore/build/app/logs /opt/fincore/build/app/runtime /opt/fincore/build/app/static /var/log/fincore

# 环境变量
Environment=PATH=/usr/local/bin:/usr/bin:/bin
Environment=FINCORE_ENV=production
Environment=USE_OSS_CONFIG=true

# 日志设置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=fincore

# 进程管理
KillMode=mixed
KillSignal=SIGTERM
TimeoutStartSec=60
TimeoutStopSec=30

# 启动后等待
ExecStartPost=/bin/sleep 5

[Install]
WantedBy=multi-user.target
