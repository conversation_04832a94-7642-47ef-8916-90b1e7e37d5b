package bankcard

import "fincore/utils/jsonschema"

// GetUserInfoSchema 用户信息绑定的参数验证规则
func GetUserInfoSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "用户信息绑定参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"marry": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				Description: "婚姻状况",
			},
			"address": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				Description: "详细地址",
			},
			"occupation": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				Description: "职业",
			},
			"yearrevenue": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				Description: "年收入（数字）",
			},
			"purposeofborrowing": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				Description: "借款用途",
			},
			"emergencycontact0relation": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				Description: "联系人1关系",
			},
			"emergencycontact0name": {
				Type:        "string",
				Required:    true,
				Pattern:     "^[\\p{Han}]+$",
				Description: "联系人1姓名（中文）",
			},
			"emergencycontact0phone": {
				Type:        "string",
				Required:    true,
				Pattern:     "^1[3-9]\\d{9}$",
				Description: "联系人1手机号",
			},
			"emergencycontact1relation": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				Description: "联系人2关系",
			},
			"emergencycontact1name": {
				Type:        "string",
				Required:    true,
				Pattern:     "^[\\p{Han}]+$",
				Description: "联系人2姓名（中文）",
			},
			"emergencycontact1phone": {
				Type:        "string",
				Required:    true,
				Pattern:     "^1[3-9]\\d{9}$",
				Description: "联系人2手机号",
			},
			"degree": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				MaxLength:   30,
				Description: "学历",
			},
		},
		Required: []string{
			"marry", "address", "occupation", "yearrevenue", "purposeofborrowing",
			"emergencycontact0relation", "emergencycontact0name", "emergencycontact0phone",
			"emergencycontact1relation", "emergencycontact1name", "emergencycontact1phone",
			"degree",
		},
	}
}

// GetBankCardInfoSchema 银行卡信息的参数验证规则
func GetBankCardInfoSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "银行卡信息参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"mobile": {
				Type:        "string",
				Required:    true,
				Pattern:     "^1[3-9]\\d{9}$",
				Description: "银行预留手机号",
			},
			"banknumber": {
				Type:        "string",
				Required:    true,
				Pattern:     "^[0-9]{12,19}$",
				Description: "银行卡号（12-19位数字）",
			},
			"bankCode": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				Description: "银行代码",
			},
			"bankName": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				Description: "银行名称",
			},
			"cardType": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				Description: "卡片类型",
			},
			"cardTypeName": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				Description: "卡片类型名称",
			},
		},
		Required: []string{"mobile", "banknumber", "bankCode", "bankName", "cardType", "cardTypeName"},
	}
}

// GetBankCardBindSchema 银行卡绑定的参数验证规则
func GetBankCardBindSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "银行卡绑定参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"mobile": {
				Type:        "string",
				Required:    true,
				Pattern:     "^1[3-9]\\d{9}$",
				Description: "银行预留手机号",
			},
			"banknumber": {
				Type:        "string",
				Required:    true,
				Pattern:     "^[0-9]{12,19}$",
				Description: "银行卡号（12-19位数字）",
			},
			"code": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				Description: "短信验证码",
			},
			"thirdPartyOrderNo": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				Description: "订单号",
			},
		},
		Required: []string{"mobile", "banknumber", "code", "thirdPartyOrderNo"},
	}
}

// GetBankCardListSchema 银行卡列表查询的参数验证规则
func GetBankCardListSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "银行卡列表查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"customer_id": {
				Type:        "string",
				Required:    true,
				Pattern:     "^[0-9]+$",
				Description: "客户ID（数字字符串）",
			},
		},
		Required: []string{"customer_id"},
	}
}
