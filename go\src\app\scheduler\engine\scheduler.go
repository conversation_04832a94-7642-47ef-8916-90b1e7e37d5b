package engine

import (
	"fincore/app/scheduler/config"
	"fincore/app/scheduler/registry"
	"fincore/app/scheduler/tasks"
	"fincore/utils/log"
	"fmt"
	"sync"
	"time"

	"github.com/go-co-op/gocron/v2"
	"go.uber.org/zap"
)

// ScheduleEngine 调度引擎
type ScheduleEngine struct {
	scheduler    gocron.Scheduler        // gocron调度器
	registry     *registry.TaskRegistry  // 任务注册中心
	executor     *TaskExecutor           // 任务执行器
	config       *config.SchedulerConfig // 配置
	logger       *log.Logger             // 日志器
	isRunning    bool                    // 是否运行中
	mutex        sync.RWMutex            // 读写锁
	jobs         map[string]gocron.Job   // 任务作业映射
	runningTasks map[string]int          // 正在运行的任务计数
}

// NewScheduleEngine 创建调度引擎
func NewScheduleEngine(
	registry *registry.TaskRegistry,
	executor *TaskExecutor,
	config *config.SchedulerConfig,
	logger *log.Logger,
) *ScheduleEngine {
	// 创建gocron调度器
	location, err := time.LoadLocation(config.Scheduler.Timezone)
	if err != nil {
		logger.Warn("加载时区失败，使用默认时区",
			zap.String("timezone", config.Scheduler.Timezone),
			zap.Error(err),
		)
		location = time.Local
	}

	scheduler, err := gocron.NewScheduler(gocron.WithLocation(location))
	if err != nil {
		logger.Error("创建调度器失败", zap.Error(err))
		panic(fmt.Sprintf("创建调度器失败: %v", err))
	}

	return &ScheduleEngine{
		scheduler:    scheduler,
		registry:     registry,
		executor:     executor,
		config:       config,
		logger:       logger,
		jobs:         make(map[string]gocron.Job),
		runningTasks: make(map[string]int),
	}
}

// Start 启动调度引擎
func (e *ScheduleEngine) Start() error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	if e.isRunning {
		return fmt.Errorf("调度引擎已经启动")
	}

	e.logger.Info("正在启动调度引擎")

	// 注册所有任务
	if err := e.registerAllTasks(); err != nil {
		return fmt.Errorf("注册任务失败: %w", err)
	}

	// 启动gocron调度器
	e.scheduler.Start()
	e.isRunning = true

	e.logger.Info("调度引擎启动成功",
		zap.Int("registered_jobs", len(e.jobs)),
		zap.String("timezone", e.config.Scheduler.Timezone),
	)

	return nil
}

// Stop 停止调度引擎
func (e *ScheduleEngine) Stop() error {
	e.mutex.Lock()
	if !e.isRunning {
		e.mutex.Unlock()
		return fmt.Errorf("调度引擎未启动")
	}

	e.logger.Info("正在停止调度引擎")

	// 停止gocron调度器
	if err := e.scheduler.Shutdown(); err != nil {
		e.mutex.Unlock()
		e.logger.Error("停止调度器失败", zap.Error(err))
		return err
	}

	// 释放锁，然后等待正在运行的任务完成
	e.mutex.Unlock()
	e.waitForRunningTasks()

	// 重新获取锁来设置状态
	e.mutex.Lock()
	e.isRunning = false
	e.mutex.Unlock()

	e.logger.Info("调度引擎已停止")
	return nil
}

// IsRunning 检查是否运行中
func (e *ScheduleEngine) IsRunning() bool {
	e.mutex.RLock()
	defer e.mutex.RUnlock()
	return e.isRunning
}

// registerAllTasks 注册所有任务
func (e *ScheduleEngine) registerAllTasks() error {
	allTasks := e.registry.GetAllTasks()

	for taskName, task := range allTasks {
		if err := e.registerTask(taskName, task); err != nil {
			e.logger.Error("注册任务失败",
				zap.String("task_name", taskName),
				zap.Error(err),
			)
			return err
		}
	}

	return nil
}

// registerTask 注册单个任务
func (e *ScheduleEngine) registerTask(taskName string, task tasks.TaskInterface) error {
	// 创建任务执行函数
	jobFunc := e.createJobFunc(taskName, task)

	// 根据调度规则创建作业
	job, err := e.scheduler.NewJob(
		gocron.CronJob(task.GetSchedule(), true),
		gocron.NewTask(jobFunc),
		gocron.WithTags(taskName),
	)
	if err != nil {
		return fmt.Errorf("创建调度作业失败: %w", err)
	}

	// 保存作业引用
	e.jobs[taskName] = job

	e.logger.Info("任务调度注册成功",
		zap.String("task_name", taskName),
		zap.String("schedule", task.GetSchedule()),
		zap.String("concurrency_mode", task.GetConcurrencyMode().String()),
	)

	return nil
}

// createJobFunc 创建任务执行函数
func (e *ScheduleEngine) createJobFunc(taskName string, task tasks.TaskInterface) func() {
	return func() {
		// 检查并发控制
		if !e.canExecuteTask(taskName, task) {
			e.logger.Warn("任务跳过执行（并发控制）",
				zap.String("task_name", taskName),
				zap.String("concurrency_mode", task.GetConcurrencyMode().String()),
				zap.Int("running_count", e.getRunningCount(taskName)),
			)
			return
		}

		// 增加运行计数
		e.incrementRunningCount(taskName)
		defer e.decrementRunningCount(taskName)

		// 执行任务
		e.logger.Info("开始执行任务",
			zap.String("task_name", taskName),
		)

		if err := e.executor.ExecuteTask(task); err != nil {
			e.logger.Error("任务执行失败",
				zap.String("task_name", taskName),
				zap.Error(err),
			)
		} else {
			e.logger.Info("任务执行成功",
				zap.String("task_name", taskName),
			)
		}
	}
}

// canExecuteTask 检查是否可以执行任务
func (e *ScheduleEngine) canExecuteTask(taskName string, task tasks.TaskInterface) bool {
	switch task.GetConcurrencyMode() {
	case tasks.ConcurrencyModeSingleton:
		// 单例模式：不允许并发执行
		return e.getRunningCount(taskName) == 0

	case tasks.ConcurrencyModeParallel:
		// 并行模式：检查是否超过最大并行数
		maxParallel := e.config.Scheduler.Concurrency.MaxParallelPerTask
		if maxParallel <= 0 {
			maxParallel = 3 // 默认最大并行数
		}
		return e.getRunningCount(taskName) < maxParallel

	default:
		// 默认不允许执行
		return false
	}
}

// getRunningCount 获取正在运行的任务数量
func (e *ScheduleEngine) getRunningCount(taskName string) int {
	e.mutex.RLock()
	defer e.mutex.RUnlock()
	return e.runningTasks[taskName]
}

// incrementRunningCount 增加运行计数
func (e *ScheduleEngine) incrementRunningCount(taskName string) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	e.runningTasks[taskName]++
}

// decrementRunningCount 减少运行计数
func (e *ScheduleEngine) decrementRunningCount(taskName string) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	if e.runningTasks[taskName] > 0 {
		e.runningTasks[taskName]--
	}
	if e.runningTasks[taskName] == 0 {
		delete(e.runningTasks, taskName)
	}
}

// waitForRunningTasks 等待正在运行的任务完成
func (e *ScheduleEngine) waitForRunningTasks() {
	maxWait := 30 * time.Second // 最大等待时间
	checkInterval := 1 * time.Second

	start := time.Now()
	for {
		e.mutex.RLock()
		runningCount := len(e.runningTasks)
		e.mutex.RUnlock()

		if runningCount == 0 {
			break
		}

		if time.Since(start) > maxWait {
			e.logger.Warn("等待任务完成超时，强制停止",
				zap.Int("remaining_tasks", runningCount),
			)
			break
		}

		e.logger.Info("等待正在运行的任务完成",
			zap.Int("running_tasks", runningCount),
		)

		time.Sleep(checkInterval)
	}
}

// GetJobStatus 获取作业状态
func (e *ScheduleEngine) GetJobStatus(taskName string) (map[string]interface{}, error) {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	job, exists := e.jobs[taskName]
	if !exists {
		return nil, fmt.Errorf("任务 %s 未找到", taskName)
	}

	nextRun, err := job.NextRun()
	if err != nil {
		e.logger.Warn("获取下次运行时间失败", zap.Error(err))
	}

	lastRun, err := job.LastRun()
	if err != nil {
		e.logger.Warn("获取上次运行时间失败", zap.Error(err))
	}

	return map[string]interface{}{
		"task_name":     taskName,
		"next_run_time": nextRun,
		"last_run_time": lastRun,
		"is_running":    e.getRunningCount(taskName) > 0,
		"running_count": e.getRunningCount(taskName),
	}, nil
}

// GetAllJobsStatus 获取所有作业状态
func (e *ScheduleEngine) GetAllJobsStatus() map[string]interface{} {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	jobsStatus := make(map[string]interface{})

	for taskName := range e.jobs {
		if status, err := e.GetJobStatus(taskName); err == nil {
			jobsStatus[taskName] = status
		}
	}

	return map[string]interface{}{
		"total_jobs":    len(e.jobs),
		"running_jobs":  len(e.runningTasks),
		"engine_status": e.isRunning,
		"jobs":          jobsStatus,
	}
}

// RemoveJob 移除作业
func (e *ScheduleEngine) RemoveJob(taskName string) error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	job, exists := e.jobs[taskName]
	if !exists {
		return fmt.Errorf("任务 %s 未找到", taskName)
	}

	// 从调度器中移除
	if err := e.scheduler.RemoveJob(job.ID()); err != nil {
		e.logger.Error("从调度器移除任务失败", zap.Error(err))
		return err
	}

	// 从映射中删除
	delete(e.jobs, taskName)

	e.logger.Info("任务调度已移除",
		zap.String("task_name", taskName),
	)

	return nil
}

// AddJob 添加新作业
func (e *ScheduleEngine) AddJob(taskName string, task tasks.TaskInterface) error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	// 检查是否已存在
	if _, exists := e.jobs[taskName]; exists {
		return fmt.Errorf("任务 %s 已存在", taskName)
	}

	// 注册任务
	return e.registerTask(taskName, task)
}
