<template>
  <a-modal
    v-model:visible="modalVisible"
    :title="modalTitle"
    :width="800"
    :footer="mode === 'view' ? null : undefined"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      :rules="formRules"
      layout="horizontal"
    >
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="渠道名称" name="channel_name">
            <a-input 
              v-model:value="formData.channel_name" 
              placeholder="请输入渠道名称"
              :disabled="mode === 'view'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="渠道编码" name="channel_code">
            <a-input-group compact>
              <a-input 
                v-model:value="formData.channel_code" 
                placeholder="请输入渠道编码或自动生成"
                :disabled="mode === 'view'"
                style="width: calc(100% - 100px)"
              />
              <a-button 
                v-if="mode !== 'view'"
                @click="generateChannelCode"
                :loading="generateLoading"
                style="width: 100px"
              >
                自动生成
              </a-button>
            </a-input-group>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="访问域名" name="domain">
            <a-input 
              v-model:value="formData.domain" 
              placeholder="请输入访问域名"
              :disabled="mode === 'view'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="联系手机" name="mobile">
            <a-input 
              v-model:value="formData.mobile" 
              placeholder="请输入联系手机"
              :disabled="mode === 'view'"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="接入密码" name="password">
            <a-input 
              v-model:value="formData.password" 
              placeholder="请输入接入密码"
              type="password"
              :disabled="mode === 'view'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="渠道状态" name="channel_status">
            <a-select 
              v-model:value="formData.channel_status" 
              placeholder="请选择渠道状态"
              :disabled="mode === 'view'"
            >
              <a-option :value="0">禁用</a-option>
              <a-option :value="1">启用</a-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="使用状态" name="channel_usage">
            <a-select 
              v-model:value="formData.channel_usage" 
              placeholder="请选择使用状态"
              :disabled="mode === 'view'"
            >
              <a-option :value="0">未使用</a-option>
              <a-option :value="1">使用中</a-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="限额节点" name="limitation_node">
            <a-input-number 
              v-model:value="formData.limitation_node" 
              placeholder="请输入限额节点"
              :min="0"
              :max="100"
              :disabled="mode === 'view'"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="下单节点" name="order_node">
            <a-input-number 
              v-model:value="formData.order_node" 
              placeholder="请输入下单节点"
              :min="0"
              :max="100"
              :disabled="mode === 'view'"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="发送节点" name="send_node">
            <a-input-number 
              v-model:value="formData.send_node" 
              placeholder="请输入发送节点"
              :min="0"
              :max="100"
              :disabled="mode === 'view'"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="成功节点" name="success_node">
            <a-input-number 
              v-model:value="formData.success_node" 
              placeholder="请输入成功节点"
              :min="0"
              :max="100"
              :disabled="mode === 'view'"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="失败节点" name="failure_node">
            <a-input-number 
              v-model:value="formData.failure_node" 
              placeholder="请输入失败节点"
              :min="0"
              :max="100"
              :disabled="mode === 'view'"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="回调节点" name="callback_node">
            <a-input-number 
              v-model:value="formData.callback_node" 
              placeholder="请输入回调节点"
              :min="0"
              :max="100"
              :disabled="mode === 'view'"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="单笔限额" name="quota_order">
            <a-input-number 
              v-model:value="formData.quota_order" 
              placeholder="请输入单笔限额"
              :min="0"
              :precision="2"
              :disabled="mode === 'view'"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="审核费率" name="audit_fee_rate">
            <a-input-number 
              v-model:value="formData.audit_fee_rate" 
              placeholder="请输入审核费率"
              :min="0"
              :max="1"
              :step="0.01"
              :precision="4"
              :disabled="mode === 'view'"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="审核费用" name="audit_fee">
            <a-input-number 
              v-model:value="formData.audit_fee" 
              placeholder="请输入审核费用"
              :min="0"
              :precision="2"
              :disabled="mode === 'view'"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="下单费率" name="order_fee_rate">
            <a-input-number 
              v-model:value="formData.order_fee_rate" 
              placeholder="请输入下单费率"
              :min="0"
              :max="1"
              :step="0.01"
              :precision="4"
              :disabled="mode === 'view'"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="下单费用" name="order_fee">
            <a-input-number 
              v-model:value="formData.order_fee" 
              placeholder="请输入下单费用"
              :min="0"
              :precision="2"
              :disabled="mode === 'view'"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="成功费率" name="success_fee_rate">
            <a-input-number 
              v-model:value="formData.success_fee_rate" 
              placeholder="请输入成功费率"
              :min="0"
              :max="1"
              :step="0.01"
              :precision="4"
              :disabled="mode === 'view'"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="成功费用" name="success_fee">
            <a-input-number 
              v-model:value="formData.success_fee" 
              placeholder="请输入成功费用"
              :min="0"
              :precision="2"
              :disabled="mode === 'view'"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="回调费率" name="callback_fee_rate">
            <a-input-number 
              v-model:value="formData.callback_fee_rate" 
              placeholder="请输入回调费率"
              :min="0"
              :max="1"
              :step="0.01"
              :precision="4"
              :disabled="mode === 'view'"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="回调费用" name="callback_fee">
            <a-input-number 
              v-model:value="formData.callback_fee" 
              placeholder="请输入回调费用"
              :min="0"
              :precision="2"
              :disabled="mode === 'view'"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 放款规则配置 -->
      <a-form-item label="放款规则" name="loan_rule_id">
        <a-select 
          v-model:value="formData.loan_rule_id" 
          placeholder="暂无放款规则111111，请选择添加"
          :disabled="mode === 'view'"
          style="width: 100%"
          allow-clear
        >
          <a-option
            v-for="productRule in productRules" 
            :key="productRule.id" 
            :value="productRule.id"
          >
            {{ productRule.rule_name }}
          </a-option>
        </a-select>
      </a-form-item>

      <a-form-item label="备注信息" name="remark">
        <a-textarea 
          v-model:value="formData.remark" 
          placeholder="请输入备注信息"
          :rows="3"
          :disabled="mode === 'view'"
        />
      </a-form-item>
    </a-form>

    <template #footer v-if="mode !== 'view'">
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ mode === 'create' ? '创建' : '更新' }}
        </a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue';
import {Message} from "@arco-design/web-vue";
import { 
  createChannel, 
  updateChannel, 
  generateChannelCode as apiGenerateChannelCode,
  getProductRules,
  type ChannelFormData, 
  type ChannelItem,
  type LoanRule,
  type ProductRule
} from '@/api/channel';

interface Props {
  visible: boolean;
  mode: 'create' | 'edit' | 'view';
  channelData?: ChannelItem | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  mode: 'create',
  channelData: null,
});

const emit = defineEmits<Emits>();

// 弹窗显示状态
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 弹窗标题
const modalTitle = computed(() => {
  switch (props.mode) {
    case 'create':
      return '新增渠道';
    case 'edit':
      return '编辑渠道';
    case 'view':
      return '查看渠道';
    default:
      return '渠道管理';
  }
});

// 表单数据
const formData = reactive<ChannelFormData>({
  channel_name: '',
  channel_code: '',
  domain: '',
  mobile: '',
  password: '',
  channel_status: 1,
  channel_usage: 0,
  limitation_node: 0,
  order_node: 0,
  send_node: 0,
  success_node: 0,
  failure_node: 0,
  callback_node: 0,
  quota_order: 0,
  audit_fee_rate: 0,
  audit_fee: 0,
  order_fee_rate: 0,
  order_fee: 0,
  success_fee_rate: 0,
  success_fee: 0,
  callback_fee_rate: 0,
  callback_fee: 0,
  loan_rule_id: undefined,
  remark: '',
});

// 表单验证规则
const formRules = {
  channel_name: [
    { required: true, message: '请输入渠道名称', trigger: 'blur' },
    { max: 100, message: '渠道名称不能超过100个字符', trigger: 'blur' },
  ],
  channel_code: [
    { max: 50, message: '渠道编码不能超过50个字符', trigger: 'blur' },
  ],
  domain: [
    { max: 255, message: '域名不能超过255个字符', trigger: 'blur' },
    { pattern: /^https?:\/\/.+/, message: '请输入有效的域名地址', trigger: 'blur' },
  ],
  mobile: [
    { required: true, message: '请输入联系手机', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur' },
  ],
  password: [
    { min: 6, message: '密码不能少于6个字符', trigger: 'blur' },
  ],
  channel_status: [
    { required: true, message: '请选择渠道状态', trigger: 'change' },
  ],
  remark: [
    { max: 1000, message: '备注不能超过1000个字符', trigger: 'blur' },
  ],
};

// 加载状态
const submitLoading = ref(false);
const generateLoading = ref(false);

// 表单引用
const formRef = ref();

// 产品规则列表
const productRules = ref<ProductRule[]>([]);

// 获取产品规则列表
const fetchProductRules = async () => {
  try {
    const response = await getProductRules();
    productRules.value = (response as any) || [];
  } catch (error) {
    console.error('获取产品规则失败:', error);
    Message.error('获取产品规则失败');
  }
};



// 监听渠道数据变化，初始化表单
watch(
  () => props.channelData,
  (newData) => {
    if (newData) {
      Object.keys(formData).forEach(key => {
        if (key in newData) {
          (formData as any)[key] = (newData as any)[key];
        }
      });
      
      // 设置放款规则ID
      formData.loan_rule_id = (newData as any).loan_rule_id || undefined;
    } else {
      resetForm();
    }
  },
  { immediate: true }
);

// 监听弹窗打开状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      // 每次打开弹窗时都获取最新的产品规则
      fetchProductRules();
      
      if (props.mode === 'create') {
        resetForm();
      }
    }
  }
);

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    channel_name: '',
    channel_code: '',
    domain: '',
    mobile: '',
    password: '',
    channel_status: 1,
    channel_usage: 0,
    limitation_node: 0,
    order_node: 0,
    send_node: 0,
    success_node: 0,
    failure_node: 0,
    callback_node: 0,
    quota_order: 0,
    audit_fee_rate: 0,
    audit_fee: 0,
    order_fee_rate: 0,
    order_fee: 0,
    success_fee_rate: 0,
    success_fee: 0,
    callback_fee_rate: 0,
    callback_fee: 0,
    loan_rule_id: undefined,
    remark: '',
  });
  
  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

// 生成渠道编码
const generateChannelCode = async () => {
  try {
    generateLoading.value = true;
    const response = await apiGenerateChannelCode();
    
    // HTTP拦截器成功时直接返回数据，失败时抛出异常
    formData.channel_code = (response as any).channel_code;
    Message.success('渠道编码生成成功');
  } catch (error) {
    console.error('生成渠道编码失败:', error);
    Message.error('生成渠道编码失败');
  } finally {
    generateLoading.value = false;
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate();
    
    submitLoading.value = true;
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 处理 loan_rules 字段
    if (submitData.loan_rule_id !== undefined && submitData.loan_rule_id !== null) {
      // 将 loan_rule_id 转换为 loan_rules JSON字符串
      submitData.loan_rules = JSON.stringify([submitData.loan_rule_id]);
    } else {
      submitData.loan_rules = '';
    }
    
    // 删除 loan_rule_id 字段
    delete submitData.loan_rule_id;
    
    // 确保必填字段不为空
    if (!submitData.channel_name) {
      submitData.channel_name = '';
    }
    if (!submitData.mobile) {
      submitData.mobile = '';
    }
    if (submitData.channel_status === null || submitData.channel_status === undefined) {
      submitData.channel_status = 1;
    }
    
    // 清理其他字段的空值
    Object.keys(submitData).forEach(key => {
      const value = (submitData as any)[key];
      if (value === null || value === undefined) {
        if (key === 'channel_code' || key === 'domain' || key === 'password' || key === 'remark') {
          (submitData as any)[key] = '';
        } else if (typeof (formData as any)[key] === 'number') {
          (submitData as any)[key] = 0;
        }
      }
    });
    
    console.log('提交数据:', submitData);
    
    if (props.mode === 'create') {
      await createChannel(submitData);
      Message.success('创建渠道成功');
    } else {
      await updateChannel(props.channelData!.id, submitData);
      Message.success('更新渠道成功');
    }
    
    emit('success');
    handleCancel();
  } catch (error: any) {
    console.error('提交失败:', error);
    Message.error(error.message || '操作失败');
  } finally {
    submitLoading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  modalVisible.value = false;
  resetForm();
};

// 组件挂载时不再自动获取产品规则，改为在弹窗打开时获取
// onMounted(() => {
//   fetchProductRules();
// });
</script>

<style scoped lang="less">
.ant-modal {
  .ant-form {
    .ant-form-item {
      margin-bottom: 16px;
    }
  }
}


</style>