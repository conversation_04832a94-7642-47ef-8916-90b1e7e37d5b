package repayment

import (
	"errors"
	"math"
	"time"

	"fincore/model"
	"fincore/utils/shopspringutils"
)

// roundToTwoDecimal 保留两位小数
func roundToTwoDecimal(value float64) float64 {
	return math.Ceil(value*100) / 100
}

// RepaymentPeriod 单期还款计划
type RepaymentPeriod struct {
	PeriodNumber       int     `json:"period_number"`        // 期数
	DuePrincipal       float64 `json:"due_principal"`        // 应还本金（实际是展示用的放款金额明细）
	DueInterest        float64 `json:"due_interest"`         // 应还利息
	DueGuaranteeFee    float64 `json:"due_guarantee_fee"`    // 应还担保费（进入担保账户）
	DueOtherFees       float64 `json:"due_other_fees"`       // 应还其他费用
	AssetManagementFee float64 `json:"asset_management_fee"` // 资管费（进入资管账户）
	LateFee            float64 `json:"late_fee"`             // 逾期费
	TotalDueAmount     float64 `json:"total_due_amount"`     // 当期应还总额 = 担保费 + 资管费
	DueDate            string  `json:"due_date"`             // 到期日期，格式：YYYY-MM-DD
}

// RepaymentSchedule 完整还款计划
type RepaymentSchedule struct {
	TotalPeriods            int               `json:"total_periods"`              // 总期数
	Periods                 []RepaymentPeriod `json:"periods"`                    // 各期还款计划
	TotalPrincipal          float64           `json:"total_principal"`            // 总本金
	TotalInterest           float64           `json:"total_interest"`             // 总利息
	TotalGuaranteeFee       float64           `json:"total_guarantee_fee"`        // 总担保费
	TotalOtherFees          float64           `json:"total_other_fees"`           // 总其他费用
	TotalAssetManagementFee float64           `json:"total_asset_management_fee"` // 总资管费
	TotalRepayableAmount    float64           `json:"total_repayable_amount"`     // 总应还金额
	DisbursementAmount      float64           `json:"disbursement_amount"`        // 实际放款金额
	IsPrePayment            bool              `json:"is_pre_payment"`             // 是否为前置还款
}

// CalculateRepaymentSchedule 计算还款计划
// 这是一个纯计算方法，不涉及数据库操作，可被多个场景复用
func CalculateRepaymentSchedule(productRule *model.ProductRules) (*RepaymentSchedule, error) {
	// 参数验证
	if productRule == nil {
		return nil, errors.New("产品规则不能为空")
	}
	if productRule.LoanAmount <= 0 {
		return nil, errors.New("产品规则中的贷款金额必须大于0")
	}

	// 使用当前时间作为基准日期
	startDate := time.Now()

	// 计算基础金额
	amounts := CalculateOrderAmounts(productRule.LoanAmount, productRule)

	// 获取期数
	totalPeriods := productRule.TotalPeriods
	if totalPeriods <= 0 {
		totalPeriods = 1 // 默认一期
	}

	// 计算资管费总额（本金 + 利息 + 其他费用）
	assetManagementFeeTotal := shopspringutils.AddAmountsWithDecimal(amounts.Principal, shopspringutils.AddAmountsWithDecimal(amounts.TotalInterest, amounts.TotalOtherFees))

	// 按期数均摊各项费用，先计算精确值再保留两位小数
	guaranteeFeePerPeriod := shopspringutils.DivideAmountsWithDecimal(amounts.TotalGuaranteeFee, float64(totalPeriods))
	guaranteeFeePerPeriod = shopspringutils.CeilToTwoDecimal(guaranteeFeePerPeriod)
	assetManagementFeePerPeriod := shopspringutils.DivideAmountsWithDecimal(assetManagementFeeTotal, float64(totalPeriods))
	assetManagementFeePerPeriod = shopspringutils.CeilToTwoDecimal(assetManagementFeePerPeriod)
	actualDisbursementPerPeriod := shopspringutils.DivideAmountsWithDecimal(amounts.Principal, float64(totalPeriods))
	actualDisbursementPerPeriod = shopspringutils.CeilToTwoDecimal(actualDisbursementPerPeriod)
	interestPerPeriod := shopspringutils.DivideAmountsWithDecimal(amounts.TotalInterest, float64(totalPeriods))
	interestPerPeriod = shopspringutils.CeilToTwoDecimal(interestPerPeriod)
	otherFeesPerPeriod := shopspringutils.DivideAmountsWithDecimal(amounts.TotalOtherFees, float64(totalPeriods))
	otherFeesPerPeriod = shopspringutils.CeilToTwoDecimal(otherFeesPerPeriod)

	// 计算期间间隔天数：每期间隔等于借款周期天数
	// 总还款天数 = LoanPeriod * TotalPeriods
	periodDays := productRule.LoanPeriod

	// 累加每一期的还款金额，后续与总还款金额做对比
	var comparisonAmount float64
	// 累加每一期的还款本金，后续与总还款金额做对比
	var comparisonPrincipal float64
	// 累加每一期的还款利息，后续与总还款金额做对比
	var comparisonInterest float64
	// 累加每一期的还款担保费，后续与总还款金额做对比
	var comparisonGuaranteeFee float64
	// 生成各期还款计划
	periods := make([]RepaymentPeriod, 0, totalPeriods)
	for i := 1; i <= totalPeriods; i++ {
		dueDate := startDate.AddDate(0, 0, periodDays*i)

		// 计算当期应还总额 = 担保费 + 资管费，保留两位小数
		totalDueAmount := shopspringutils.CeilToTwoDecimal(shopspringutils.AddAmountsWithDecimal(guaranteeFeePerPeriod, assetManagementFeePerPeriod))

		comparisonAmount = shopspringutils.AddAmountsWithDecimal(comparisonAmount, totalDueAmount)
		comparisonAmount = shopspringutils.CeilToTwoDecimal(comparisonAmount)
		comparisonPrincipal = shopspringutils.AddAmountsWithDecimal(comparisonPrincipal, actualDisbursementPerPeriod)
		comparisonPrincipal = shopspringutils.CeilToTwoDecimal(comparisonPrincipal)
		comparisonInterest = shopspringutils.AddAmountsWithDecimal(comparisonInterest, interestPerPeriod)
		comparisonInterest = shopspringutils.CeilToTwoDecimal(comparisonInterest)
		comparisonGuaranteeFee = shopspringutils.AddAmountsWithDecimal(comparisonGuaranteeFee, guaranteeFeePerPeriod)
		comparisonGuaranteeFee = shopspringutils.CeilToTwoDecimal(comparisonGuaranteeFee)

		if i == totalPeriods {
			diffAmount := shopspringutils.SubtractAmountsWithDecimal(amounts.TotalRepayableAmount, comparisonAmount)
			if diffAmount != 0 {
				// 将差额放到最后一期
				totalDueAmount = shopspringutils.AddAmountsWithDecimal(totalDueAmount, diffAmount)

			}

			diffPrincipal := shopspringutils.SubtractAmountsWithDecimal(amounts.Principal, comparisonPrincipal)
			if diffPrincipal != 0 {
				// 将差额放到最后一期
				actualDisbursementPerPeriod = shopspringutils.AddAmountsWithDecimal(actualDisbursementPerPeriod, diffPrincipal)
			}

			diffInterest := shopspringutils.SubtractAmountsWithDecimal(amounts.TotalInterest, comparisonInterest)
			if diffInterest != 0 {
				// 将差额放到最后一期
				interestPerPeriod = shopspringutils.AddAmountsWithDecimal(interestPerPeriod, diffInterest)
			}

			diffGuaranteeFee := shopspringutils.SubtractAmountsWithDecimal(amounts.TotalGuaranteeFee, comparisonGuaranteeFee)
			if diffGuaranteeFee != 0 {
				// 将差额放到最后一期

				guaranteeFeePerPeriod = shopspringutils.AddAmountsWithDecimal(guaranteeFeePerPeriod, diffGuaranteeFee)

			}
		}

		// 当期的本金 + 利息 + 担保与总金额对比
		tmpAmount := shopspringutils.AddAmountsWithDecimal(actualDisbursementPerPeriod, shopspringutils.AddAmountsWithDecimal(interestPerPeriod, guaranteeFeePerPeriod))
		diffTmpAmount := shopspringutils.SubtractAmountsWithDecimal(totalDueAmount, tmpAmount)
		// 差额在利息中体现
		if diffTmpAmount != 0 {
			interestPerPeriod = shopspringutils.AddAmountsWithDecimal(interestPerPeriod, diffTmpAmount)
			interestPerPeriod = shopspringutils.CeilToTwoDecimal(interestPerPeriod)
		}

		period := RepaymentPeriod{
			PeriodNumber:       i,
			DuePrincipal:       actualDisbursementPerPeriod, // 实际放款金额明细（用于展示）
			DueInterest:        interestPerPeriod,           // 利息明细（用于展示）
			DueGuaranteeFee:    guaranteeFeePerPeriod,       // 担保费（进入担保账户）
			DueOtherFees:       otherFeesPerPeriod,          // 其他费用明细
			AssetManagementFee: assetManagementFeePerPeriod, // 资管费（进入资管账户）
			LateFee:            0,                           // 逾期费
			TotalDueAmount:     totalDueAmount,              // 当期应还总额
			DueDate:            dueDate.Format("2006-01-02"),
		}

		periods = append(periods, period)
	}

	// 构建完整还款计划，所有金额保留两位小数
	schedule := &RepaymentSchedule{
		TotalPeriods:            totalPeriods,
		Periods:                 periods,
		TotalPrincipal:          amounts.Principal,
		TotalInterest:           amounts.TotalInterest,
		TotalGuaranteeFee:       amounts.TotalGuaranteeFee,
		TotalOtherFees:          amounts.TotalOtherFees,
		TotalAssetManagementFee: assetManagementFeeTotal,
		TotalRepayableAmount:    amounts.TotalRepayableAmount,
		DisbursementAmount:      amounts.DisbursementAmount,
		IsPrePayment:            amounts.IsPrePayment,
	}

	return schedule, nil
}

// OrderAmounts 订单金额结构
type OrderAmounts struct {
	Principal            float64 // 本金（申请金额）
	TotalInterest        float64 // 总利息
	TotalGuaranteeFee    float64 // 总担保费
	TotalOtherFees       float64 // 总其他费用
	TotalRepayableAmount float64 // 总还款金额
	DisbursementAmount   float64 // 实际放款金额（前置还款时会扣除费用）
	IsPrePayment         bool    // 是否为前置还款
}

// CalculateOrderAmounts 计算产品规则金额
func CalculateOrderAmounts(loanAmount float64, productRule *model.ProductRules) OrderAmounts {
	if loanAmount <= 0 || productRule == nil {
		return OrderAmounts{}
	}

	principal := loanAmount

	// 计算利息: 本金 * 年利率 * (天数/365) * 期数，使用 math 库进行精确计算
	interestRate := shopspringutils.DivideAmountsWithDecimal(productRule.AnnualInterestRate, 100)

	if interestRate < 0 {
		interestRate = 0
	}

	totalInterest := shopspringutils.MultiplyAmountsWithDecimal(loanAmount, interestRate)
	totalInterest = shopspringutils.MultiplyAmountsWithDecimal(totalInterest, float64(productRule.LoanPeriod))
	totalInterest = shopspringutils.MultiplyAmountsWithDecimal(totalInterest, float64(productRule.TotalPeriods))
	totalInterest = shopspringutils.DivideAmountsWithDecimal(totalInterest, 365)
	totalInterest = shopspringutils.CeilToTwoDecimal(totalInterest)

	// 获取费用（确保为正数），保留两位小数
	totalGuaranteeFee := shopspringutils.CeilToTwoDecimal(math.Max(0, productRule.GuaranteeFee))
	totalOtherFees := shopspringutils.CeilToTwoDecimal(math.Max(0, productRule.OtherFees))

	// 计算总还款金额，保留两位小数
	totalRepayableAmount := shopspringutils.AddAmountsWithDecimal(principal, totalInterest)
	totalRepayableAmount = shopspringutils.AddAmountsWithDecimal(totalRepayableAmount, totalGuaranteeFee)
	totalRepayableAmount = shopspringutils.AddAmountsWithDecimal(totalRepayableAmount, totalOtherFees)
	totalRepayableAmount = shopspringutils.CeilToTwoDecimal(totalRepayableAmount)

	// 计算实际放款金额，保留两位小数
	disbursementAmount := productRule.CalculateDisbursementAmount(loanAmount)
	disbursementAmount = shopspringutils.CeilToTwoDecimal(disbursementAmount)

	return OrderAmounts{
		Principal:            principal,
		TotalInterest:        totalInterest,
		TotalGuaranteeFee:    totalGuaranteeFee,
		TotalOtherFees:       totalOtherFees,
		TotalRepayableAmount: totalRepayableAmount,
		DisbursementAmount:   disbursementAmount,
		IsPrePayment:         productRule.IsPrePayment(),
	}
}

// ConvertToRepaymentBillMaps 将还款计划转换为数据库插入用的 map 数据
func ConvertToRepaymentBillMaps(schedule *RepaymentSchedule, orderID, userID int) []map[string]interface{} {
	if schedule == nil {
		return nil
	}

	now := time.Now()
	billMaps := make([]map[string]interface{}, 0, len(schedule.Periods))

	for _, period := range schedule.Periods {
		billMap := map[string]interface{}{
			"order_id":               orderID,
			"user_id":                userID,
			"period_number":          period.PeriodNumber,
			"due_principal":          period.DuePrincipal,
			"due_interest":           period.DueInterest,
			"due_guarantee_fee":      period.DueGuaranteeFee,
			"due_other_fees":         period.DueOtherFees,
			"asset_management_entry": period.AssetManagementFee,
			"late_fee":               period.LateFee,
			"total_due_amount":       period.TotalDueAmount,
			"paid_amount":            0,
			"status":                 0, // 0-待还款
			"due_date":               period.DueDate,
			"deduct_retry_count":     0,
			"created_at":             now,
			"updated_at":             now,
		}
		billMaps = append(billMaps, billMap)
	}

	return billMaps
}
