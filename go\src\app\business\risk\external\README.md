# 第三方风控系统集成模块

本模块实现了第三方风控系统的接入，支持与现有风控系统的双服务调用机制。

## 功能特性

- **双服务调用机制**: 支持原有风控服务和第三方风控服务的切换
- **配置化管理**: 通过配置文件控制第三方风控服务的启用状态
- **数据存储**: 支持三种模型数据的存储（雷达v4、探针C、Zwsc）
- **结果映射**: 将第三方风控结果映射到标准评估结果
- **错误处理**: 完善的错误处理和日志记录

## 目录结构

```
external/
├── config.go          # 配置管理
├── models.go          # 数据模型定义
├── utils.go           # 工具函数（AES加密、HTTP请求）
├── third_service.go   # 第三方风控服务实现
├── test_integration.go # 集成测试
└── README.md          # 说明文档
```

## 配置说明

在 `config.yml` 文件中添加第三方风控配置：

```yaml
third_risk:
  enabled: true                           # 是否启用第三方风控
  url: "https://api.example.com/risk"      # 第三方风控API地址
  app_id: "your_app_id"                   # 应用ID
  aes_key: "your_aes_key"                 # AES加密密钥
  timeout: 30                             # 请求超时时间（秒）
  service_code: "RISK_EVAL"               # 服务代码
  data_source: "third_party"              # 数据源标识
```

## 使用方法

### 1. 通过API参数控制

在调用风控评估接口时，可以通过 `use_third` 参数控制是否使用第三方风控：

```bash
GET /business/risk/riskcontroller/getEvaluate?customer_id=123&use_third=true
```

### 2. 通过配置文件控制

如果没有传递 `use_third` 参数，系统会根据配置文件中的 `enabled` 字段决定是否使用第三方风控。

### 3. 编程方式调用

```go
// 创建第三方风控服务实例
service := external.NewGoDemoRiskService()

// 构建请求数据
data := map[string]interface{}{
    "customer_id": "12345",
    "user_name":   "张三",
    "id_card":     "110101199001011234",
    "telephone":   "***********",
}

// 调用风控评估
result, err := service.EvaluateRisk(ctx, data)
if err != nil {
    // 处理错误
}

// 映射结果
riskResult, riskScore, failureReason := service.MapToRiskResult(result)
```

## 数据流程

1. **参数验证**: 验证必要的客户信息（姓名、身份证、手机号）
2. **配置检查**: 检查第三方风控服务是否启用
3. **客户信息获取**: 从数据库获取完整的客户信息
4. **请求构建**: 构建第三方风控API请求
5. **数据加密**: 使用AES加密敏感数据
6. **HTTP请求**: 发送POST请求到第三方API
7. **响应解析**: 解析API响应数据
8. **结果映射**: 将第三方结果映射到标准格式
9. **数据存储**: 存储原始数据和评估结果

## 结果映射规则

| 第三方结果 | 映射结果 | 风险分数 | 说明 |
|-----------|---------|---------|------|
| OK        | 0 (通过) | 800-1000 | 风控通过 |
| MANUAL    | 1 (审核) | 400-799  | 需要人工审核 |
| DENY      | 2 (拒绝) | 0-399    | 风控拒绝 |

## 数据存储

### risk_evaluations 表
存储风控评估结果：
- evaluation_id: 评估ID
- customer_id: 客户ID
- risk_score: 风险分数
- risk_result: 风险结果（0/1/2）
- failure_type: 失败类型
- failure_reason: 失败原因
- evaluation_time: 评估时间

### risk_raw_data 表
存储原始风控数据：
- evaluation_id: 评估ID
- leida_v4_response: 雷达v4响应数据
- tan_zhen_c_response: 探针C响应数据
- zwsc_response: Zwsc响应数据
- data_source: 数据源（"third_party"）

## 测试

运行集成测试：

```bash
go test -v ./external/
```

运行性能测试：

```bash
go test -bench=. ./external/
```

## 错误处理

系统包含完善的错误处理机制：

- **配置错误**: 配置文件缺失或格式错误
- **网络错误**: API请求超时或连接失败
- **数据错误**: 响应数据格式错误或解析失败
- **业务错误**: 第三方服务返回的业务错误

所有错误都会记录到日志中，便于问题排查。

## 注意事项

1. **安全性**: AES密钥需要妥善保管，不要提交到版本控制系统
2. **性能**: 第三方API调用会增加响应时间，建议设置合理的超时时间
3. **容错性**: 当第三方服务不可用时，可以降级到原有风控服务
4. **监控**: 建议对第三方API调用进行监控和告警
5. **数据合规**: 确保数据传输和存储符合相关法规要求

## 版本历史

- v1.0.0: 初始版本，实现基本的第三方风控接入功能