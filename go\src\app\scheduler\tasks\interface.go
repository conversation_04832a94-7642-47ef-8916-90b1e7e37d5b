package tasks

import (
	"context"
	"time"
)

// ConcurrencyMode 并发模式
type ConcurrencyMode int

const (
	// ConcurrencyModeParallel 并行模式 - 允许同一任务多个实例并行执行
	ConcurrencyModeParallel ConcurrencyMode = iota
	// ConcurrencyModeSingleton 单例模式 - 同一任务同时只能有一个实例执行
	ConcurrencyModeSingleton
)

// String 返回并发模式的字符串表示
func (c ConcurrencyMode) String() string {
	switch c {
	case ConcurrencyModeParallel:
		return "parallel"
	case ConcurrencyModeSingleton:
		return "singleton"
	default:
		return "unknown"
	}
}

// TaskInterface 定时任务接口
// 所有定时任务都必须实现此接口
type TaskInterface interface {
	// GetName 获取任务名称（唯一标识）
	GetName() string

	// GetDescription 获取任务描述
	GetDescription() string

	// GetSchedule 获取调度规则（Cron表达式）
	GetSchedule() string

	// GetTimeout 获取任务超时时间
	GetTimeout() time.Duration

	// GetRetryCount 获取重试次数
	GetRetryCount() int

	// GetRetryInterval 获取重试间隔
	GetRetryInterval() time.Duration

	// GetConcurrencyMode 获取并发模式
	GetConcurrencyMode() ConcurrencyMode

	// Execute 执行任务
	// ctx: 上下文，包含超时控制和取消信号
	// 返回 error: 任务执行错误，nil表示成功
	Execute(ctx context.Context) error
}

// TaskLifecycleInterface 任务生命周期接口（可选实现）
// 任务可以选择性实现此接口来处理生命周期事件
type TaskLifecycleInterface interface {
	// OnStart 任务开始执行前调用
	OnStart(ctx context.Context) error

	// OnSuccess 任务执行成功后调用
	OnSuccess(ctx context.Context) error

	// OnError 任务执行失败后调用
	OnError(ctx context.Context, err error) error

	// OnComplete 任务执行完成后调用（无论成功失败）
	OnComplete(ctx context.Context) error
}

// TaskMetadata 任务元数据
type TaskMetadata struct {
	Name            string          `json:"name"`
	Description     string          `json:"description"`
	Schedule        string          `json:"schedule"`
	Timeout         time.Duration   `json:"timeout"`
	RetryCount      int             `json:"retry_count"`
	RetryInterval   time.Duration   `json:"retry_interval"`
	ConcurrencyMode ConcurrencyMode `json:"concurrency_mode"`

	// 运行时信息
	IsRunning    bool      `json:"is_running"`
	LastRunTime  time.Time `json:"last_run_time"`
	NextRunTime  time.Time `json:"next_run_time"`
	RunCount     int64     `json:"run_count"`
	SuccessCount int64     `json:"success_count"`
	FailureCount int64     `json:"failure_count"`
	LastError    string    `json:"last_error,omitempty"`
}

// GetMetadata 从任务接口获取元数据
func GetMetadata(task TaskInterface) TaskMetadata {
	return TaskMetadata{
		Name:            task.GetName(),
		Description:     task.GetDescription(),
		Schedule:        task.GetSchedule(),
		Timeout:         task.GetTimeout(),
		RetryCount:      task.GetRetryCount(),
		RetryInterval:   task.GetRetryInterval(),
		ConcurrencyMode: task.GetConcurrencyMode(),
	}
}

// ValidateTask 验证任务接口实现
func ValidateTask(task TaskInterface) error {
	if task.GetName() == "" {
		return ErrTaskNameEmpty
	}

	if task.GetSchedule() == "" {
		return ErrTaskScheduleEmpty
	}

	if task.GetTimeout() <= 0 {
		return ErrTaskTimeoutInvalid
	}

	if task.GetRetryCount() < 0 {
		return ErrTaskRetryCountInvalid
	}

	if task.GetRetryInterval() < 0 {
		return ErrTaskRetryIntervalInvalid
	}

	return nil
}

// BaseTask 基础任务结构体
// 提供任务接口的默认实现，业务任务可以嵌入此结构体
type BaseTask struct {
	name            string
	description     string
	schedule        string
	timeout         time.Duration
	retryCount      int
	retryInterval   time.Duration
	concurrencyMode ConcurrencyMode
}

// NewBaseTask 创建基础任务
func NewBaseTask(name, description, schedule string, timeout time.Duration) *BaseTask {
	return &BaseTask{
		name:            name,
		description:     description,
		schedule:        schedule,
		timeout:         timeout,
		retryCount:      3,                        // 默认重试3次
		retryInterval:   30 * time.Second,         // 默认重试间隔30秒
		concurrencyMode: ConcurrencyModeSingleton, // 默认单例模式
	}
}

// GetName 获取任务名称
func (b *BaseTask) GetName() string {
	return b.name
}

// GetDescription 获取任务描述
func (b *BaseTask) GetDescription() string {
	return b.description
}

// GetSchedule 获取调度规则
func (b *BaseTask) GetSchedule() string {
	return b.schedule
}

// GetTimeout 获取任务超时时间
func (b *BaseTask) GetTimeout() time.Duration {
	return b.timeout
}

// GetRetryCount 获取重试次数
func (b *BaseTask) GetRetryCount() int {
	return b.retryCount
}

// GetRetryInterval 获取重试间隔
func (b *BaseTask) GetRetryInterval() time.Duration {
	return b.retryInterval
}

// GetConcurrencyMode 获取并发模式
func (b *BaseTask) GetConcurrencyMode() ConcurrencyMode {
	return b.concurrencyMode
}

// SetRetryCount 设置重试次数
func (b *BaseTask) SetRetryCount(count int) *BaseTask {
	b.retryCount = count
	return b
}

// SetRetryInterval 设置重试间隔
func (b *BaseTask) SetRetryInterval(interval time.Duration) *BaseTask {
	b.retryInterval = interval
	return b
}

// SetConcurrencyMode 设置并发模式
func (b *BaseTask) SetConcurrencyMode(mode ConcurrencyMode) *BaseTask {
	b.concurrencyMode = mode
	return b
}

// Execute 默认执行方法（需要业务任务重写）
func (b *BaseTask) Execute(ctx context.Context) error {
	return ErrTaskNotImplemented
}
