export default {
  'menu.system.rule': 'Rule',
  // columns
  'searchTable.columns.index': '#',
  'searchTable.columns.number': 'Set Number',
  'searchTable.columns.name': 'Set Name',
  'searchTable.columns.contentType': 'Content Type',
  'searchTable.columns.filterType': 'Filter Type',
  'searchTable.columns.count': 'Count',
  'searchTable.columns.createdTime': 'CreatedTime',
  'searchTable.columns.status': 'Status',
  'searchTable.columns.operations': 'Operations',
  'searchTable.columns.operations.view': 'View',
  // size
  'searchTable.size.mini': 'mini',
  'searchTable.size.small': 'small',
  'searchTable.size.medium': 'middle',
  'searchTable.size.large': 'large',
  // actions
  'searchTable.actions.refresh': 'refresh',
  'searchTable.actions.density': 'density',
  'searchTable.actions.columnSetting': 'columnSetting',
};
