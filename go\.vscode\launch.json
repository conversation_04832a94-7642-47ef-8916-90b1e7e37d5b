{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug Main",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/src",
            "cwd": "${workspaceFolder}/src",
            "args": [],
            // "env": {
            //     "ENV": "dev"
            // }
        },
        {
            "name": "Debug Cron",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/src/app/cron/main.go",
            "cwd": "${workspaceFolder}/src/app/cron",
            "args": [],
            "env": {
                "ENV": "dev"
            }
        },
        {
            "name": "Debug Current File",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${fileDirname}"
        },
        {
            "name": "Debug Tests",
            "type": "go",
            "request": "launch",
            "mode": "test",
            "program": "${fileDirname}",
            "args": []
        },
        {
            "name": "Debug with Dev Config",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/src",
            "cwd": "${workspaceFolder}",
            "env": {
                "GIN_MODE": "debug",
                "CONFIG_PATH": "${workspaceFolder}/src/resource/config.yml"
            },
            "args": []
        }
    ]
}