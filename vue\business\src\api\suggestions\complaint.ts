import { defHttp } from '@/utils/http';

export interface ComplaintListParams {
  content?: string;
  status?: number;
  phone?: string;
}

export interface ComplaintItem {
  id: number;
  status: string;
  phone: string;
  content: string;
  remark: string;
  feedback: string;
  createTime: string;
}

enum Api {
  getComplaint = '/suggestions/complaint/getComplaintContentList',
  handleComplaint = '/suggestions/complaint/handleComplaint',
}

export function getComplaint(params: object) {
  return defHttp.get({ url: Api.getComplaint, params }, { errorMessageMode: 'none' });
}

export function handleComplaint(params: object) {
  return defHttp.post({ url: Api.handleComplaint, params }, { errorMessageMode: 'message' });
} 