package home

import (
	"fincore/utils/gf"
	"fincore/utils/results"
	"reflect"

	"github.com/gin-gonic/gin"
)

//首页接口
/**
*使用 Index 是省略路径中的index
*本路径为： /admin/user/login -省去了index
 */
type Index struct {
	NoNeedLogin []string //忽略登录接口配置-忽略全部传[*]
	NoNeedAuths []string //忽略RBAC权限认证接口配置-忽略全部传[*]
}

func init() {
	fpath := Index{NoNeedLogin: []string{"GetList"}, NoNeedAuths: []string{"*"}}
	gf.Register(&fpath, reflect.TypeOf(fpath).PkgPath())
}

// 获取数据列表
func (api *Index) GetList(c *gin.Context) {

	list := map[string]interface{}{
		"lastid":   1111,
		"pageSize": 2,
		"items":    1,
	}
	results.Success(c, "获取数据列表", list, nil)
}
