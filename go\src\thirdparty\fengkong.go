package thirdparty

import (
	"context"
	"fmt"

	"fincore/thirdparty/auth"
	"fincore/thirdparty/client"
	"fincore/thirdparty/error_handler"
	"fincore/thirdparty/service"
	"fincore/thirdparty/types"
)

// FengkongService 风控服务
type FengkongService struct {
	*service.BaseService
}

// NewFengkongService 创建风控服务
func NewFengkongService(config types.ServiceConfig) (*FengkongService, error) {
	// 创建加密认证器
	authenticator := auth.NewEncryptAuth(
		config.GetString("machine_id"),
		config.GetString("secret_key"),
		config.GetString("algorithm"),
	)

	// 创建错误映射器
	errorMapper := error_handler.NewStandardErrorMapper()
	// 添加风控服务特有的错误映射
	errorMapper.AddErrorMapping("RISK001", error_handler.ErrorInfo{
		Code: "4001", Message: "风险等级过高", Retryable: false,
	})
	errorMapper.AddErrorMapping("RISK002", error_handler.ErrorInfo{
		Code: "4002", Message: "数据解密失败", Retryable: false,
	})

	// 创建HTTP客户端
	clientConfig := &client.ClientConfig{
		BaseURL:       config.GetString("base_url"),
		Timeout:       config.GetDuration("timeout"),
		Authenticator: authenticator,
		ErrorMapper:   errorMapper,
	}

	httpClient := client.NewHTTPClient(clientConfig)

	// 创建基础服务
	baseService := service.NewBaseService("fengkong", "1.0.0", httpClient, config)

	fengkongService := &FengkongService{
		BaseService: baseService,
	}

	// 添加支持的方法
	fengkongService.AddMethod("RiskCheck")
	fengkongService.AddMethod("FraudDetection")
	fengkongService.AddMethod("BlacklistCheck")

	return fengkongService, nil
}

// Initialize 初始化服务
func (s *FengkongService) Initialize(config types.ServiceConfig) error {
	// 验证必要的配置项
	requiredFields := []string{"base_url", "machine_id", "secret_key", "algorithm"}
	for _, field := range requiredFields {
		if config.GetString(field) == "" {
			return fmt.Errorf("缺少必要配置项: %s", field)
		}
	}
	return nil
}

// Call 调用服务
func (s *FengkongService) Call(ctx context.Context, method string, params map[string]interface{}) (*types.Response, error) {
	switch method {
	case "RiskCheck":
		return s.riskCheck(ctx, params)
	case "FraudDetection":
		return s.fraudDetection(ctx, params)
	case "BlacklistCheck":
		return s.blacklistCheck(ctx, params)
	default:
		return nil, fmt.Errorf("不支持的方法: %s", method)
	}
}

// riskCheck 风险检测
func (s *FengkongService) riskCheck(ctx context.Context, params map[string]interface{}) (*types.Response, error) {
	req := &types.Request{
		Method: "POST",
		Path:   "/risk/check",
		Data:   params,
	}

	return s.GetClient().Do(ctx, req)
}

// fraudDetection 欺诈检测
func (s *FengkongService) fraudDetection(ctx context.Context, params map[string]interface{}) (*types.Response, error) {
	req := &types.Request{
		Method: "POST",
		Path:   "/fraud/detect",
		Data:   params,
	}

	return s.GetClient().Do(ctx, req)
}

// blacklistCheck 黑名单检查
func (s *FengkongService) blacklistCheck(ctx context.Context, params map[string]interface{}) (*types.Response, error) {
	req := &types.Request{
		Method: "POST",
		Path:   "/blacklist/check",
		Data:   params,
	}

	return s.GetClient().Do(ctx, req)
}
