package order

import (
	"context"
	"testing"
	"time"

	"fincore/utils/pagination"
)

// TestOrderListPerformance 测试订单列表查询性能
func TestOrderListPerformance(t *testing.T) {
	repository := NewRepository(context.Background())

	// 测试参数
	params := map[string]interface{}{
		"page":     1,
		"pageSize": 20,
	}

	paginationReq := pagination.PaginationRequest{
		Page:     1,
		PageSize: 20,
	}
	paginationReq.ValidateAndNormalize()

	// 预热
	_, _ = repository.GetOrderListWithFilters(params, paginationReq)
	_, _ = repository.GetOrderListOptimized(params, paginationReq)

	// 测试原始方法性能
	t.Run("Original Method", func(t *testing.T) {
		start := time.Now()
		for i := 0; i < 10; i++ {
			result, err := repository.GetOrderListWithFilters(params, paginationReq)
			if err != nil {
				t.<PERSON><PERSON><PERSON>("原始方法查询失败: %v", err)
				return
			}
			if result == nil {
				t.Error("原始方法返回结果为空")
				return
			}
		}
		duration := time.Since(start)
		t.Logf("原始方法 10次查询耗时: %v, 平均: %v", duration, duration/10)
	})

	// 测试优化方法性能
	t.Run("Optimized Method", func(t *testing.T) {
		start := time.Now()
		for i := 0; i < 10; i++ {
			result, err := repository.GetOrderListOptimized(params, paginationReq)
			if err != nil {
				t.Errorf("优化方法查询失败: %v", err)
				return
			}
			if result == nil {
				t.Error("优化方法返回结果为空")
				return
			}
		}
		duration := time.Since(start)
		t.Logf("优化方法 10次查询耗时: %v, 平均: %v", duration, duration/10)
	})
}

// TestOrderListConsistency 测试查询结果一致性
func TestOrderListConsistency(t *testing.T) {
	repository := NewRepository(context.Background())

	params := map[string]interface{}{
		"page":     1,
		"pageSize": 5, // 使用较小的页面大小便于比较
	}

	paginationReq := pagination.PaginationRequest{
		Page:     1,
		PageSize: 5,
	}
	paginationReq.ValidateAndNormalize()

	// 获取原始方法结果
	originalResult, err := repository.GetOrderListWithFilters(params, paginationReq)
	if err != nil {
		t.Fatalf("原始方法查询失败: %v", err)
	}

	// 获取优化方法结果
	optimizedResult, err := repository.GetOrderListOptimized(params, paginationReq)
	if err != nil {
		t.Fatalf("优化方法查询失败: %v", err)
	}

	// 比较基本信息
	if originalResult.Total != optimizedResult.Total {
		t.Errorf("总数不一致: 原始=%d, 优化=%d", originalResult.Total, optimizedResult.Total)
	}

	if originalResult.Page != optimizedResult.Page {
		t.Errorf("页码不一致: 原始=%d, 优化=%d", originalResult.Page, optimizedResult.Page)
	}

	if originalResult.PageSize != optimizedResult.PageSize {
		t.Errorf("页面大小不一致: 原始=%d, 优化=%d", originalResult.PageSize, optimizedResult.PageSize)
	}

	// 比较数据长度
	originalData := originalResult.Data.([]interface{})
	optimizedData := optimizedResult.Data.([]interface{})

	if len(originalData) != len(optimizedData) {
		t.Errorf("数据长度不一致: 原始=%d, 优化=%d", len(originalData), len(optimizedData))
		return
	}

	t.Logf("一致性测试通过: 总数=%d, 数据条数=%d", originalResult.Total, len(originalData))
}

// BenchmarkOrderListOriginal 基准测试原始方法
func BenchmarkOrderListOriginal(b *testing.B) {
	repository := NewRepository(context.Background())
	params := map[string]interface{}{
		"page":     1,
		"pageSize": 20,
	}

	paginationReq := pagination.PaginationRequest{
		Page:     1,
		PageSize: 20,
	}
	paginationReq.ValidateAndNormalize()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := repository.GetOrderListWithFilters(params, paginationReq)
		if err != nil {
			b.Fatalf("查询失败: %v", err)
		}
	}
}

// BenchmarkOrderListOptimized 基准测试优化方法
func BenchmarkOrderListOptimized(b *testing.B) {
	repository := NewRepository(context.Background())
	params := map[string]interface{}{
		"page":     1,
		"pageSize": 20,
	}

	paginationReq := pagination.PaginationRequest{
		Page:     1,
		PageSize: 20,
	}
	paginationReq.ValidateAndNormalize()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := repository.GetOrderListOptimized(params, paginationReq)
		if err != nil {
			b.Fatalf("查询失败: %v", err)
		}
	}
}
