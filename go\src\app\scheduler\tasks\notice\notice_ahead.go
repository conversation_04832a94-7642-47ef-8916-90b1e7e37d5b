package notice

import (
	"context"
	"fmt"
	"time"

	"fincore/app/scheduler/tasks"
	"fincore/model"
	"fincore/thirdparty/sms"
	"fincore/utils/convert"
	"fincore/utils/gform"
	"fincore/utils/log"

	"github.com/shopspring/decimal"
)

// BillNoticeAheadTask 账单到期提前通知任务
// 用于处理账单到期前一天，短信通知客户
type BillNoticeAheadTask struct {
	*tasks.BaseTask
	logger *log.Logger
	ctx    context.Context
}

// NewBillNoticeAheadTask 创建账单到期提前通知任务
func NewBillNoticeAheadTask() *BillNoticeAheadTask {
	baseTask := tasks.NewBaseTask(
		"bill-notice-ahead",
		"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户",
		"0 0 9 * * *", // 每天9点执行一次
		1*time.Hour,   // 超时时间1小时
	)

	// 设置为单例模式，避免重复执行
	baseTask.SetConcurrencyMode(tasks.ConcurrencyModeSingleton)
	// 设置重试次数和间隔
	baseTask.SetRetryCount(3).SetRetryInterval(30 * time.Second)

	logger := log.RegisterModule("bill_notice_ahead_task", "账单到期提前通知任务")
	ctx := context.Background()
	return &BillNoticeAheadTask{
		BaseTask: baseTask,
		logger:   logger,
		ctx:      ctx,
	}
}

// Execute 执行账单到期提前通知任务
func (t *BillNoticeAheadTask) Execute(ctx context.Context) error {

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "start_execution"),
	).Info("开始执行账单到期提前通知任务")

	startTime := time.Now()
	var processedCount, successCount, failureCount int

	toNotices, err := t.getExpiringSoonBills()
	if err != nil {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "query_bills"),
			log.String("error", err.Error()),
		).Error("查询即将到期订单失败")
		return fmt.Errorf("查询即将到期订单失败: %v", err)
	}

	if len(toNotices) == 0 {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "no_notices_found"),
		).Info("未找到需要通知的订单")
		return nil
	}
	processedCount = len(toNotices)

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "sms_bill_notices"),
		log.Int("notice_count", processedCount),
	).Info("找到需要通知的订单")
	tomorrow := time.Now().Add(24 * time.Hour).Format("2006-01-02")
	// 分批处理
	const batchSize = 100
	for i := 0; i < len(toNotices); i += batchSize {
		end := i + batchSize
		if end > len(toNotices) {
			end = len(toNotices)
		}
		batch := toNotices[i:end]
		batchLen := len(batch)
		notices := make([]sms.NoticeAheadParams, batchLen)
		// 遍历处理每条通知
		for j := range batchLen {

			// 检查上下文是否被取消
			select {
			case <-ctx.Done():
				t.logger.WithFields(
					log.String("task", t.GetName()),
					log.String("operation", "context_cancelled"),
					log.Int("processed_count", processedCount-1),
				).Warn("任务被取消，停止处理")
				return ctx.Err()
			default:
			}
			total := decimal.NewFromFloat(convert.GetFloatFromMap(batch[j], "total_due_amount", 0))
			paid := decimal.NewFromFloat(convert.GetFloatFromMap(batch[j], "paid_amount", 0))
			amount := decimal.NewFromFloat(0)
			status := convert.GetIntFromMap(batch[j], "status", model.RepaymentBillStatusUnpaid)
			if status == model.RepaymentBillStatusUnpaid {
				amount = total
			} else {
				amount = total.Sub(paid)
			}
			notices[j] = sms.NoticeAheadParams{
				Username: convert.GetStringFromMap(batch[j], "username"),
				Amount:   amount.String(),
				DueDate:  tomorrow,
				Mobile:   convert.GetStringFromMap(batch[j], "mobile"),
				URL:      "",
			}
		}
		// 发送短信
		err := sms.SendBillNoticeAheadSms(notices)
		if err != nil {
			t.logger.WithFields(
				log.String("task", t.GetName()),
				log.String("operation", "send_sms"),
				log.String("error", err.Error()),
			).Error("批量发送短信失败")
			failureCount += batchLen
			continue
		}
		successCount += batchLen
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.Int("batchLen", batchLen),
			log.String("operation", "batch_send_success"),
		).Info("批量短信通知成功")
	}
	// 记录执行统计
	duration := time.Since(startTime)
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "execution_completed"),
		log.Int("processed_count", processedCount),
		log.Int("success_count", successCount),
		log.Int("failure_count", failureCount),
		log.String("duration", duration.String()),
	).Info("到期订单通知任务执行完成")

	return nil
}

// 查询所有到期日前一天的订单，并关联查询用户名称
func (t *BillNoticeAheadTask) getExpiringSoonBills() ([]gform.Data, error) {
	// 明天到期的待支付或部分支付订单
	query := `
		SELECT 
		    o.status AS status,
			o.paid_amount as paid_amount,
			o.total_due_amount AS total_due_amount,
			u.name AS username,
			u.mobile AS mobile
		FROM business_repayment_bills o
		JOIN business_app_account u ON o.user_id = u.id
		WHERE (o.status = ? OR o.status = ?)
		  AND o.due_date = ?
		ORDER BY o.created_at ASC
	`

	tomorrow := time.Now().Add(24 * time.Hour).Format("2006-01-02")

	result, err := model.DB(model.WithContext(t.ctx)).Query(query,
		model.RepaymentBillStatusUnpaid, model.RepaymentBillStatusPartialPaid,
		tomorrow,
	)
	if err != nil {
		return nil, fmt.Errorf("查询即将到期订单失败: %v", err)
	}
	return result, nil
}

// OnStart 任务开始执行前的回调
func (t *BillNoticeAheadTask) OnStart(ctx context.Context) error {

	// task_ 开头，记录整个周期所有 sql 执行日志
	requestID := "task_" + t.GetName() + "_" + time.Now().Format("**************")
	t.ctx = context.WithValue(t.ctx, log.RequestIDKey, requestID)
	t.logger = t.logger.WithRequestID(requestID)

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_starting"),
	).Info("到期订单通知任务即将开始")
	return nil
}

// OnSuccess 任务执行成功后的回调
func (t *BillNoticeAheadTask) OnSuccess(ctx context.Context) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_success"),
	).Info("到期订单通知任务执行成功")
	return nil
}

// OnError 任务执行失败后的回调
func (t *BillNoticeAheadTask) OnError(ctx context.Context, err error) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_error"),
		log.String("error", err.Error()),
	).Error("到期订单通知任务执行失败")
	return nil
}
