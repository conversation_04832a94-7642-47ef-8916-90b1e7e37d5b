#!/bin/bash

# Fincore Build Wrapper Script
# 调用deployer目录中的构建脚本
# Author: System Administrator
# Version: 1.0.0

set -euo pipefail

# 颜色定义
BLUE='\033[0;34m'
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOYER_DIR="$PROJECT_ROOT/deployer"
BUILD_SCRIPT="$DEPLOYER_DIR/build.sh"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

# 检查构建脚本是否存在
if [[ ! -f "$BUILD_SCRIPT" ]]; then
    log_error "构建脚本不存在: $BUILD_SCRIPT"
    exit 1
fi

# 检查构建脚本是否可执行
if [[ ! -x "$BUILD_SCRIPT" ]]; then
    log_error "构建脚本不可执行: $BUILD_SCRIPT"
    log_info "请运行: chmod +x $BUILD_SCRIPT"
    exit 1
fi

log_info "调用deployer构建脚本..."
log_info "构建脚本位置: $BUILD_SCRIPT"

# 切换到deployer目录并执行构建脚本
cd "$DEPLOYER_DIR"

# 检查是否需要跳过UniApp构建
if [[ $# -gt 0 && "$1" == "--skip-uniapp" ]]; then
    export SKIP_UNIAPP=true
    shift  # 移除这个参数
    log_info "将跳过UniApp构建"
fi

# 传递所有参数给构建脚本
if ./build.sh "$@"; then
    log_success "构建完成！"
    echo ""
    echo "构建产物位置: $DEPLOYER_DIR/build/"
    echo "部署说明:"
    echo "1. 将整个deployer目录拷贝到目标服务器"
    echo "2. 在目标服务器上执行: sudo ./deploy.sh --start"
    echo ""
    echo "快速命令:"
    echo "  ./build.sh --skip-uniapp    # 跳过UniApp构建"
    echo "  SKIP_UNIAPP=true ./build.sh # 环境变量方式跳过"
else
    log_error "构建失败！"
    echo ""
    echo "UniApp处理说明:"
    echo "1. 当前使用手动编译方式，请先手动编译UniApp"
    echo "2. 手动编译指南: vue/uniapp/MANUAL_BUILD_GUIDE.md"
    echo "3. 如果不需要UniApp: ./build.sh --skip-uniapp"
    echo ""
    echo "故障排除:"
    echo "1. 查看构建日志中的详细错误信息"
    echo "2. 检查各组件的构建状态"
    echo "3. 参考相关文档进行问题排查"
    exit 1
fi