<template>
  <div class="container">
    <a-card class="general-card">
      <template #title>
        <a-space>
          <a-button type="text" @click="goBack">
            <template #icon>
              <icon-arrow-left />
            </template>
            {{ t('ordermanagement.detail.backToList') }}
          </a-button>
          <a-divider direction="vertical" />
          {{ t('ordermanagement.detail.title') }}
        </a-space>
      </template>
      <a-spin :loading="loading">
        <div v-if="orderDetail">
          <!-- 下单人信息 -->
          <div class="info-section">
            <div class="section-title">
              <span>下单人信息</span>
            </div>

            <!-- 个人信息卡片 -->
            <div class="user-info-cards">
              <a-row :gutter="[16, 16]">
                <a-col :span="6">
                  <div class="info-card-shadow">
                    <a-descriptions :column="1" size="small" title="个人资料" layout="vertical" :title-style="{ fontWeight: 'bold', fontSize: '14px', marginBottom: '16px', borderBottom: 'none' }">
                      <a-descriptions-item label="姓名">{{ safeGetNestedValue(customerInfo, 'name') || orderDetail?.user_name }}</a-descriptions-item>
                      <a-descriptions-item label="性别">{{ safeGetNestedValue(customerInfo, 'gender') || getGenderFromIdCard(orderDetail?.user_id_card) }}</a-descriptions-item>
                      <a-descriptions-item label="年龄">{{ safeGetNestedValue(customerInfo, 'age') || '-' }}</a-descriptions-item>
                      <a-descriptions-item label="手机号">{{ safeGetNestedValue(customerInfo, 'mobile') || orderDetail?.user_mobile }}</a-descriptions-item>
                      <a-descriptions-item label="身份证号">{{ safeGetNestedValue(customerInfo, 'id_card_no') || orderDetail?.user_id_card }}</a-descriptions-item>
                      <a-descriptions-item label="所在位置">{{ safeGetNestedValue(customerInfo, 'location') || '-' }}</a-descriptions-item>
                    </a-descriptions>
                  </div>
                </a-col>
                <a-col :span="6">
                  <div class="info-card-shadow">
                    <a-descriptions :column="1" size="small" title="订单信息" layout="vertical" :title-style="{ fontWeight: 'bold', fontSize: '14px', marginBottom: '16px', borderBottom: 'none' }">
                      <a-descriptions-item label="订单状态">
                        {{ safeGetNestedValue(customerInfo, 'order_status') !== undefined ? getCustomerOrderStatusText(safeGetNestedValue(customerInfo, 'order_status')) : getOrderStatusText(orderDetail?.status || 0) }}
                      </a-descriptions-item>
                      <a-descriptions-item label="下单时间">{{ safeGetNestedValue(customerInfo, 'order_time') || orderDetail?.created_at }}</a-descriptions-item>
                      <a-descriptions-item label="申请金额">{{ formatCurrency(safeGetNestedValue(customerInfo, 'loan_amount') || orderDetail?.loan_amount) }}</a-descriptions-item>
                      <a-descriptions-item label="订单归属">{{ safeGetNestedValue(customerInfo, 'payment_channel') || orderDetail?.payment_channel_name }}</a-descriptions-item>
                    </a-descriptions>
                  </div>
                </a-col>
                <a-col :span="6">
                  <div class="info-card-shadow">
                    <a-descriptions :column="1" size="small" title="用户统计" layout="vertical" :title-style="{ fontWeight: 'bold', fontSize: '14px', marginBottom: '16px', borderBottom: 'none' }">
                      <a-descriptions-item label="用户状态">{{ safeGetNestedValue(customerInfo, 'user_status') !== undefined ? getUserStatusText(safeGetNestedValue(customerInfo, 'user_status')) : '-' }}</a-descriptions-item>
                      <a-descriptions-item label="注册时间">{{ safeGetNestedValue(customerInfo, 'register_time') || '-' }}</a-descriptions-item>
                      <a-descriptions-item label="复购次数">{{ safeGetNestedValue(customerInfo, 'repeat_buy_count') || '-' }}</a-descriptions-item>
                      <a-descriptions-item label="在途订单数">{{ safeGetNestedValue(customerInfo, 'in_progress_orders') || '-' }}</a-descriptions-item>
                      <a-descriptions-item label="完结订单数">{{ safeGetNestedValue(customerInfo, 'completed_orders') || '-' }}</a-descriptions-item>
                      <a-descriptions-item label="全部订单数">
                        <a-space>
                          <span>{{ safeGetNestedValue(customerInfo, 'total_orders') || '-' }}</span>
                          <a-button type="text" size="mini" @click="goToOrderList">查看</a-button>
                        </a-space>
                      </a-descriptions-item>
                    </a-descriptions>
                  </div>
                </a-col>
                <a-col :span="6">
                  <div class="info-card-shadow">
                    <a-descriptions :column="1" size="small" title="额度信息" layout="vertical" :title-style="{ fontWeight: 'bold', fontSize: '14px', marginBottom: '16px', borderBottom: 'none' }">
                      <a-descriptions-item label="风控评分">{{ safeGetNestedValue(customerInfo, 'risk_score') || orderDetail?.risk_score || '-' }}</a-descriptions-item>
                      <a-descriptions-item label="历史额度">{{ safeGetNestedValue(customerInfo, 'past_quota') ? formatCurrency(safeGetNestedValue(customerInfo, 'past_quota')) : '-' }}</a-descriptions-item>
                      <a-descriptions-item label="总额度">{{ safeGetNestedValue(customerInfo, 'all_quota') ? formatCurrency(safeGetNestedValue(customerInfo, 'all_quota')) : '-' }}</a-descriptions-item>
                      <a-descriptions-item label="剩余额度">{{ safeGetNestedValue(customerInfo, 'reminder_quota') ? formatCurrency(safeGetNestedValue(customerInfo, 'reminder_quota')) : '-' }}</a-descriptions-item>
                      <a-descriptions-item label="最后登录IP地址">{{ safeGetNestedValue(customerInfo, 'last_login_location') || '-' }}</a-descriptions-item>
                    </a-descriptions>
                  </div>
                </a-col>
              </a-row>
            </div>

            <!-- 身份证照片 -->
            <a-row :gutter="16" v-if="safeGetNestedValue(customerInfo, 'id_card_front_url') || safeGetNestedValue(customerInfo, 'id_card_back_url')">
              <a-col :span="24">
                <div class="id-card-images">
                  <div class="id-card-title">身份证照片</div>
                  <div class="id-card-container">
                    <el-image style="width: 150px; height: 100px" :src="getFullImageUrl(safeGetNestedValue(customerInfo, 'id_card_front_url'))" :preview-src-list="[getFullImageUrl(safeGetNestedValue(customerInfo, 'id_card_front_url'))]" fit="cover" />
                    <el-image style="width: 150px; height: 100px" :src="getFullImageUrl(safeGetNestedValue(customerInfo, 'id_card_back_url'))" :preview-src-list="[getFullImageUrl(safeGetNestedValue(customerInfo, 'id_card_back_url'))]" fit="cover" />

<!--                    <div class="id-card-item" v-if="safeGetNestedValue(customerInfo, 'id_card_front_url')">
                      <img :src="getFullImageUrl(safeGetNestedValue(customerInfo, 'id_card_front_url'))" alt="身份证正面" class="id-card-image" />
                      <div class="id-card-label">正面</div>
                    </div>
                    <div class="id-card-item" v-if="safeGetNestedValue(customerInfo, 'id_card_back_url')">
                      <img :src="getFullImageUrl(safeGetNestedValue(customerInfo, 'id_card_back_url'))" alt="身份证反面" class="id-card-image" />
                      <div class="id-card-label">反面</div>
                    </div>-->
                  </div>
                </div>
              </a-col>
            </a-row>

            <!-- 操作按钮 -->
            <div class="action-buttons">
              <a-space>
                <!-- 移除:disabled属性，保留点击条件判断 -->
                <a-button
                  type="primary"
                  @click="orderDetail.status === 0 ? handleProcessDisbursement() : undefined"
                >放款</a-button>

                <a-button type="primary" @click="handleRefreshDisbursement">放款刷新</a-button>

                <!-- 移除:disabled属性，保留点击条件判断 -->
                <a-button
                  type="primary"
                  @click="handleEarlySettlementButtonClick"
                >提前结清</a-button>

                <!-- 移除:disabled属性，保留点击条件判断 -->
                <a-button
                  type="primary"
                  @click="handleCloseOrderButtonClick"
                >关闭订单</a-button>

                <a-button type="primary" @click="openRemarkDialog">备注</a-button>
                <!-- 移除:disabled属性，保留点击条件判断 -->
                <a-button
                  type="primary"
                  @click="handleCancelClaimButtonClick"
                >取消认领</a-button>
              </a-space>
            </div>
          </div>

          <!-- 标签页 -->
          <a-tabs class="detail-tabs">
            <a-tab-pane key="orderInfo" title="订单信息">
              <!-- 订单信息内容 -->
              <a-descriptions bordered :column="3">
                <a-descriptions-item label="订单ID">{{ orderDetail?.id || '-' }}</a-descriptions-item>
                <a-descriptions-item label="订单编号">{{ orderDetail?.order_no || '-' }}</a-descriptions-item>
                <a-descriptions-item label="创建时间">{{ orderDetail?.created_at || '-' }}</a-descriptions-item>
                <a-descriptions-item label="更新时间">{{ orderDetail?.updated_at || '-' }}</a-descriptions-item>
                <a-descriptions-item label="借款金额">{{ formatCurrency(orderDetail?.loan_amount) }}</a-descriptions-item>
                <a-descriptions-item label="借款期限">{{ orderDetail?.loan_period ? `${orderDetail.loan_period}天` : '-' }}</a-descriptions-item>
              </a-descriptions>

              <!-- 紧急联系人信息 -->
              <div class="info-section contact-section">
                <div class="section-title">紧急联系人</div>
                <a-row :gutter="16">
                  <a-col :span="24">
                    <div class="content-wrapper">
                      <a-row :gutter="[16, 16]" v-if="safeGetNestedValue(customerInfo, 'emergency_contact_0') || safeGetNestedValue(customerInfo, 'emergency_contact_1')">
                        <!-- 联系人1 -->
                        <a-col :span="24" v-if="safeGetNestedValue(customerInfo, 'emergency_contact_0')">
                          <div class="contact-row">
                            <div class="contact-header">联系人1</div>
                            <div class="contact-info">
                              <div class="contact-item">
                                <span class="contact-label">姓名：</span>
                                <span class="contact-value">{{ safeGetNestedValue(customerInfo, 'emergency_contact_0.name') || '-' }}</span>
                              </div>
                              <div class="contact-item">
                                <span class="contact-label">关系：</span>
                                <span class="contact-value">{{ safeGetNestedValue(customerInfo, 'emergency_contact_0.relation') || '-' }}</span>
                              </div>
                              <div class="contact-item">
                                <span class="contact-label">电话：</span>
                                <span class="contact-value">{{ safeGetNestedValue(customerInfo, 'emergency_contact_0.phone') || '-' }}</span>
                              </div>
                            </div>
                          </div>
                        </a-col>
                        <!-- 联系人2 -->
                        <a-col :span="24" v-if="safeGetNestedValue(customerInfo, 'emergency_contact_1')">
                          <div class="contact-row">
                            <div class="contact-header">联系人2</div>
                            <div class="contact-info">
                              <div class="contact-item">
                                <span class="contact-label">姓名：</span>
                                <span class="contact-value">{{ safeGetNestedValue(customerInfo, 'emergency_contact_1.name') || '-' }}</span>
                              </div>
                              <div class="contact-item">
                                <span class="contact-label">关系：</span>
                                <span class="contact-value">{{ safeGetNestedValue(customerInfo, 'emergency_contact_1.relation') || '-' }}</span>
                              </div>
                              <div class="contact-item">
                                <span class="contact-label">电话：</span>
                                <span class="contact-value">{{ safeGetNestedValue(customerInfo, 'emergency_contact_1.phone') || '-' }}</span>
                              </div>
                            </div>
                          </div>
                        </a-col>
                      </a-row>
                      <a-empty v-else description="暂无紧急联系人信息" />
                    </div>
                  </a-col>
                </a-row>
              </div>

              <!-- 复审信息 -->
              <div class="info-section">
                <div class="section-title">复审信息</div>
                <div class="content-wrapper">
                  <div v-if="safeGetNestedValue(customerInfo, 'review_status') !== undefined">
                    <a-descriptions :column="1" bordered>
                      <a-descriptions-item label="复审状态">
                        <a-tag :color="getReviewStatusColor(safeGetNestedValue(customerInfo, 'review_status'))">
                          {{ getReviewStatusText(safeGetNestedValue(customerInfo, 'review_status')) }}
                        </a-tag>
                      </a-descriptions-item>
                      <a-descriptions-item label="复审备注">
                        {{ safeGetNestedValue(customerInfo, 'review_remark') || '无' }}
                      </a-descriptions-item>
                    </a-descriptions>
                  </div>
                  <a-empty v-else description="暂无复审信息" />
                </div>
              </div>

              <!-- 备注列表 -->
              <div class="info-section">
                <div class="section-title">备注列表</div>
                <div class="content-wrapper">
                  <a-table
                    v-if="getRemarksData().length > 0"
                    :data="getRemarksData()"
                    :pagination="false"
                    :bordered="true"
                    size="small"
                    row-key="id"
                  >
                    <template #columns>
                      <a-table-column title="序号" align="center" width="80px">
                        <template #cell="{ rowIndex }">
                          {{ rowIndex + 1 }}
                        </template>
                      </a-table-column>
                      <a-table-column title="备注人" data-index="user_name">
                        <template #cell="{ record }">
                          {{ record.user_name || '-' }}
                        </template>
                      </a-table-column>
                      <a-table-column title="备注时间" data-index="create_time">
                        <template #cell="{ record }">
                          {{ record.create_time || '-' }}
                        </template>
                      </a-table-column>
                      <a-table-column title="备注内容" data-index="content">
                        <template #cell="{ record }">
                          {{ record.content || '-' }}
                        </template>
                      </a-table-column>
                    </template>
                  </a-table>
                  <a-empty v-else description="暂无备注信息" />
                </div>
              </div>
            </a-tab-pane>

            <a-tab-pane key="riskReport" title="风控报告">
              <div class="tab-content">
                <div v-if="customerInfo">
                  <a-descriptions bordered :column="2">
                    <a-descriptions-item label="风控评分">{{ safeGetNestedValue(customerInfo, 'risk_score') }}</a-descriptions-item>
                    <a-descriptions-item label="用户状态">{{ getUserStatusText(safeGetNestedValue(customerInfo, 'user_status')) }}</a-descriptions-item>
                    <a-descriptions-item label="历史额度">{{ formatCurrency(safeGetNestedValue(customerInfo, 'past_quota')) }}</a-descriptions-item>
                    <a-descriptions-item label="总额度">{{ formatCurrency(safeGetNestedValue(customerInfo, 'all_quota')) }}</a-descriptions-item>
                    <a-descriptions-item label="剩余额度">{{ formatCurrency(safeGetNestedValue(customerInfo, 'reminder_quota')) }}</a-descriptions-item>
                    <a-descriptions-item label="复购次数">{{ safeGetNestedValue(customerInfo, 'repeat_buy_count') }}</a-descriptions-item>
                  </a-descriptions>
                </div>
                <div v-else>
                  <a-empty description="暂无风控报告数据" />
                </div>
              </div>
            </a-tab-pane>

            <a-tab-pane key="billInfo" title="账单信息">
              <div>
                <billInfo/>
              </div>
            </a-tab-pane>

            <a-tab-pane key="collectionRecord" title="催收记录">
              <div class="tab-content">催收记录内容</div>
            </a-tab-pane>

            <a-tab-pane key="processProgress" title="流程进度">
              <div class="tab-content">流程进度内容</div>
            </a-tab-pane>
          </a-tabs>
        </div>
        <a-result
          v-else-if="!loading"
          status="error"
          :title="t('ordermanagement.message.getDetailFailed')"
        >
          <template #extra>
            <a-button type="primary" @click="goBack">
              {{ t('ordermanagement.detail.backToList') }}
            </a-button>
          </template>
        </a-result>
      </a-spin>
    </a-card>

    <!-- 备注对话框 -->
    <a-modal
      v-model:visible="remarkDialogVisible"
      :title="t('ordermanagement.detail.addRemark')"
      :on-before-cancel="handleBeforeCancelRemark"
      @before-ok="handleSubmitRemark"
      :mask-closable="false"
      :closable="false"
      ok-text="提交"
      cancel-text="取消"
    >
      <div class="remark-textarea-container">
        <a-textarea
          v-model="remarkContent"
          :placeholder="t('ordermanagement.detail.remarkPlaceholder')"
          allow-clear
          :auto-size="{minRows: 5, maxRows: 8}"
          show-word-limit
          :max-length="1000"
        />
      </div>
    </a-modal>

    <!-- 关闭订单对话框 -->
    <a-modal
      v-model:visible="closeOrderDialogVisible"
      :title="t('ordermanagement.detail.closeOrder')"
      @before-ok="handleCloseOrder"
      :mask-closable="false"
      :width="500"
      ok-text="确定"
      cancel-text="取消"
    >
      <div class="close-order-form">
        <a-form :model="closeOrderForm" layout="vertical">
          <a-form-item field="reasonType" :label="t('ordermanagement.detail.closeReasonType')" required>
            <a-select v-model="closeOrderForm.reasonType" :placeholder="t('ordermanagement.detail.selectCloseReasonType')">
              <a-option :value="0">终审拒绝</a-option>
              <a-option :value="1">法院涉案</a-option>
              <a-option :value="2">纯白户</a-option>
              <a-option :value="3">客户失联</a-option>
              <a-option :value="4">不提供资料</a-option>
              <a-option :value="5">多余订单</a-option>
              <a-option :value="6">重新下单</a-option>
              <a-option :value="7">客户不同意方案</a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="reason" :label="t('ordermanagement.detail.closeReason')">
            <a-textarea
              v-model="closeOrderForm.reason"
              :placeholder="t('ordermanagement.detail.closeReasonPlaceholder')"
              allow-clear
              :auto-size="{minRows: 4, maxRows: 6}"
              show-word-limit
              :max-length="1000"
            />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 取消确认对话框 -->
    <a-modal
      v-model:visible="cancelConfirmVisible"
      :title="t('ordermanagement.detail.confirmCancel')"
      @cancel="cancelConfirmVisible = false"
      @before-ok="confirmCancelEditing"
      :mask-closable="false"
      :width="400"
      ok-text="确认离开"
      cancel-text="返回编辑"
    >
      <p class="confirm-message">{{ t('ordermanagement.detail.dataWillNotBeSaved') }}</p>
    </a-modal>

    <!-- 提前结清确认对话框 -->
    <a-modal
      v-model:visible="earlySettlementDialogVisible"
      title="提前结清确认"
      @before-ok="handleEarlySettlementOK"
      :mask-closable="false"
      :width="400"
      ok-text="确认结清"
      cancel-text="取消"
      @ok="handleEarlySettlementOK"
      @cancel="handleEarlySettlementCancel"
    >
      <p class="confirm-message">确定要对订单进行提前结清操作吗？此操作不可撤销。</p>
    </a-modal>

    <!-- 放款确认对话框 -->
    <a-modal
      v-model:visible="disbursementDialogVisible"
      title="确认放款"
      @before-ok="confirmProcessDisbursement"
      :mask-closable="false"
      :width="400"
      ok-text="确认放款"
      cancel-text="取消"
    >
      <p class="confirm-message">确定要对订单进行放款操作吗？此操作不可撤销。</p>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import {
  IconArrowLeft,
} from '@arco-design/web-vue/es/icon';
import type { OrderListItem, OrderCustomerInfo } from '@/api/ordermanagement';
import { getOrderList, getOrderCustomerInfo, createOrderRemark, closeOrder, earlySettlement } from '@/api/ordermanagement';
import { defHttp } from '@/utils/http';
import { isUrl } from '@/utils/is';
import { useI18n } from 'vue-i18n';
import { useUserStore } from '@/store';

import billInfo from './components/billInfo.vue';

const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const userStore = useUserStore();
const loading = ref(true);
const orderDetail = ref<OrderListItem | null>(null);
const customerInfo = ref<OrderCustomerInfo | null>(null);



// 获取当前用户ID
const getCurrentUserId = (): number => {
  // 从localStorage获取userInfo中的uid
  const userInfoStr = localStorage.getItem('userInfo');
  if (!userInfoStr) return 0;
  
  try {
    const userInfo = JSON.parse(userInfoStr);
    const userId = Number(userInfo.uid) || 0;
    return userId;
  } catch (e) {
    console.error('解析userInfo失败:', e);
    return 0;
  }
};

// 检查当前用户是否有权限操作订单
const hasOrderPermission = (): boolean => {
  console.log('------权限检查开始------');
  
  if (!orderDetail.value) {
    console.log('订单详情不存在');
    return false;
  }
  
  // 获取当前用户ID
  const userInfoStr = localStorage.getItem('userInfo');
  if (!userInfoStr) {
    console.log('userInfo不存在');
    return false;
  }
  
  try {
    const userInfo = JSON.parse(userInfoStr);
    console.log('userInfo:', userInfo);
    
    // 获取当前用户ID
    const currentUserId = Number(userInfo.uid || 0);
    console.log('当前用户ID:', currentUserId, '类型:', typeof currentUserId);
    
    // 获取订单分配的业务员ID
    const salesAssigneeId = Number(orderDetail.value.sales_assignee_id || 0);
    console.log('订单业务员ID:', salesAssigneeId, '类型:', typeof salesAssigneeId);
    
    // 判断用户是否有权限操作 - 必须是订单的分配业务员
    const hasPermission = currentUserId > 0 && currentUserId === salesAssigneeId;
    console.log('权限判断结果:', hasPermission);
    
    console.log('------权限检查结束------');
    return hasPermission;
  } catch (e) {
    console.error('解析userInfo失败:', e);
    return false;
  }
};

// 检查权限并提示
const checkPermissionAndTip = (): boolean => {
  if (!hasOrderPermission()) {
    Message.warning('没有操作当前订单的权限，请在订单分配后操作');
    return false;
  }
  return true;
};

// 备注相关
const remarkDialogVisible = ref(false);
const cancelConfirmVisible = ref(false);
const remarkContent = ref('');

// 关闭订单相关
const closeOrderDialogVisible = ref(false);
const closeOrderForm = ref({
  reasonType: undefined,
  reason: '',
});

// 提前结清相关
const earlySettlementDialogVisible = ref(false);

// 放款相关
const disbursementDialogVisible = ref(false);

onMounted(async () => {
  await fetchOrderDetail();
});

// 处理取消认领
const handleCancelClaim = async () => {
  try {
    // 先检查业务员权限
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      return;
    }
    
    if (!orderDetail.value?.id) {
      throw new Error('订单ID不存在');
    }
    
    // 获取当前用户ID作为操作人ID
    const operatorId = getCurrentUserId();
    
    // 构造请求参数
    const requestData = {
      order_id: orderDetail.value.id,
      operatorId: operatorId || 0 // 如果获取不到ID则使用0
    };
    
    // 调用取消认领API
    const response = await defHttp.post<any>({
      url: '/order/manager/cancelClaimOrder',
      data: requestData
    }, {
      isTransformResponse: false  // 不转换响应，获取原始数据
    });
    
    if (response && response.code === 0) {
      Message.success('取消认领成功');
      
      // 刷新订单详情
      await fetchOrderDetail();
    } else {
      throw new Error(response?.message || '取消认领失败');
    }
  } catch (error) {
    console.error('取消认领失败:', error);
    Message.error(error instanceof Error ? error.message : '取消认领失败');
  }
};

// 处理放款刷新
const handleRefreshDisbursement = async () => {
  try {
    if (!orderDetail.value?.order_no) {
      Message.warning('订单编号不存在');
      return;
    }

    loading.value = true;
    // 调用getDisbursementStatus API
    const response = await defHttp.get<any>(
      {
        url: '/order/manager/getDisbursementStatus',
        params: { order_no: orderDetail.value.order_no }
      },
      {
        isTransformResponse: false // 不转换响应，获取原始数据
      }
    );

    console.log('放款刷新响应:', response);

    if (response && response.code === 0) {
      Message.success('放款状态刷新成功');
      // 刷新订单详情，更新页面状态
      await fetchOrderDetail();
    } else {
      // 显示接口返回的错误信息
      Message.error(response?.data || '放款状态刷新失败');
    }
  } catch (error) {
    console.error('放款状态刷新失败:', error);
    Message.error(error instanceof Error ? error.message : '放款状态刷新失败');
  } finally {
    loading.value = false;
  }
};

const fetchOrderDetail = async () => {
  try {
    loading.value = true;
    const orderId = Number(route.params.id);
    const orderNo = route.query.orderNo as string;

    if (!orderId || isNaN(orderId)) {
      throw new Error('无效的订单ID');
    }

    // 获取订单基础信息
    const response = await getOrderList({
      order_no: orderNo,
      page: 1,
      page_size: 1
    });

    if (response && response.data && response.data.length > 0) {
      orderDetail.value = response.data[0];

      // 获取下单人详细信息
      try {
        const customerResponse = await getOrderCustomerInfo(orderId);

        // 处理API响应，适应不同的数据结构
        if (customerResponse) {
          // 使用类型断言处理类型问题
          customerInfo.value = customerResponse as unknown as OrderCustomerInfo;
        }
      } catch (customerError) {
        console.error('获取下单人信息失败:', customerError);
        Message.warning('获取下单人详细信息失败，显示基础信息');
      }
    } else {
      throw new Error('找不到订单详情');
    }
  } catch (error) {
    console.error('获取订单详情失败:', error);
    Message.error('获取订单详情失败');
    orderDetail.value = null;
    customerInfo.value = null;
  } finally {
    loading.value = false;
  }
};

const goBack = () => {
  router.push('/ordermanagement/Orderlist');
};

// 获取订单状态文本
const getOrderStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '待放款',
    1: '放款中',
    2: '交易关闭',
    3: '交易完成',
  };
  return statusMap[status] || '未知状态';
};

// 获取CustomerInfo中订单状态文本
const getCustomerOrderStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '待放款',
    1: '放款中',
    2: '交易关闭',
    3: '交易完成',
  };
  return statusMap[status] || '未知状态';
};

// 获取用户状态文本
const getUserStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '正常',
    1: '白名单',
    2: '黑名单',
    4: '风控拦截',
  };
  return statusMap[status] || '未知状态';
};

// 格式化货币
const formatCurrency = (amount?: number): string => {
  if (amount === undefined || amount === null) return '¥0.00';
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
  }).format(amount);
};

// 从身份证号获取性别
const getGenderFromIdCard = (idCard?: string): string => {
  if (!idCard || idCard.length < 18) return '未知';
  // 身份证第17位，奇数为男，偶数为女
  const genderCode = parseInt(idCard.charAt(16));
  return genderCode % 2 === 1 ? '男' : '女';
};

// 获取图片完整URL
const getFullImageUrl = (url?: string): string => {
  if (!url) return '';

  // 如果已经是完整URL（以http或https开头），则直接返回
  if (isUrl(url)) {
    return url;
  }

  // 判断环境并获取对应的域名前缀
  const domainPrefix = import.meta.env.VITE_APP_ENV === "production"
    ? window?.globalConfig?.Root_url
    : window?.globalConfig?.Root_url_dev;

  // 如果URL已经包含域名前缀，则直接返回
  if (url.startsWith(domainPrefix)) {
    return url;
  }

  // 如果URL以/开头，则直接拼接域名
  if (url.startsWith('/')) {
    return `${domainPrefix}${url}`;
  }

  // 否则添加/再拼接
  return `${domainPrefix}/${url}`;
};

// 获取复审状态文本
const getReviewStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '未复审',
    1: '已通过',
    2: '未通过',
  };
  return statusMap[status] || '未知状态';
};

// 获取复审状态颜色
const getReviewStatusColor = (status: number): string => {
  const colorMap: Record<number, string> = {
    0: 'default',
    1: 'success',
    2: 'error',
  };
  return colorMap[status] || 'default';
};

// 安全获取嵌套值
const safeGetNestedValue = (obj: any, path: string, defaultValue?: any): any => {
  const keys = path.split('.');
  let result = obj;
  for (const key of keys) {
    if (result && typeof result === 'object' && key in result) {
      result = result[key];
    } else {
      return defaultValue;
    }
  }
  return result;
};

// 获取备注数据
const getRemarksData = () => {
  // 尝试多种可能的数据路径
  let remarks: any[] = [];

  // 尝试直接访问customerInfo.remarks
  if (Array.isArray(safeGetNestedValue(customerInfo.value, 'remarks'))) {
    remarks = safeGetNestedValue(customerInfo.value, 'remarks');
  }
  // 尝试访问customerInfo.data.remarks
  else if (Array.isArray(safeGetNestedValue(customerInfo.value, 'data.remarks'))) {
    remarks = safeGetNestedValue(customerInfo.value, 'data.remarks');
  }
  // 从响应对象中寻找 remarks 字段
  else if (customerInfo.value && typeof customerInfo.value === 'object') {
    // 在对象的任何层级查找名为remarks的数组
    const findRemarks = (obj: any, depth = 0): any[] | null => {
      if (!obj || depth > 3 || typeof obj !== 'object') return null;

      for (const key in obj) {
        if (key === 'remarks' && Array.isArray(obj[key])) {
          return obj[key];
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          const found: any[] | null = findRemarks(obj[key], depth + 1);
          if (found) return found;
        }
      }
      return null;
    };

    const foundRemarks = findRemarks(customerInfo.value);
    if (foundRemarks) {
      remarks = foundRemarks;
    }
  }

  return remarks || [];
};

// 打开备注对话框
const openRemarkDialog = () => {
  // 先检查业务员权限
  if (!hasOrderPermission()) {
    Message.warning('没有操作当前订单的权限，请在订单分配后操作');
    return;
  }

  remarkDialogVisible.value = true;
  remarkContent.value = '';
};

// 打开关闭订单对话框
const openCloseOrderDialog = () => {
  // 先检查业务员权限
  if (!hasOrderPermission()) {
    Message.warning('没有操作当前订单的权限，请在订单分配后操作');
    return;
  }

  // 检查订单状态
  if (!orderDetail.value || orderDetail.value.status !== 0) {
    Message.warning('只有待放款状态的订单才能进行关闭操作');
    return;
  }

  closeOrderDialogVisible.value = true;
  closeOrderForm.value = {
    reasonType: undefined,
    reason: '',
  };
};

// 检查提前结清权限
const checkEarlySettlement = () => {
  // 先检查业务员权限
  if (!hasOrderPermission()) {
    Message.warning('没有操作当前订单的权限，请在订单分配后操作');
    return;
  }

  // 再次检查订单状态
  if (!orderDetail.value || orderDetail.value.status !== 1) {
    Message.warning('只有放款中状态的订单才能进行提前结清操作');
    return;
  }

  // 通过权限检查，打开提前结清对话框
  openEarlySettlementDialog();
};

// 打开提前结清对话框
const openEarlySettlementDialog = () => {
  earlySettlementDialogVisible.value = true;
};

// 提前结清按钮点击处理 - 入口函数
const handleEarlySettlementButtonClick = () => {
  // 先检查权限，没有权限则直接提示，不进行后续操作
  if (!hasOrderPermission()) {
    Message.warning('没有操作当前订单的权限，请在订单分配后操作');
    return;
  }

  // 检查订单状态
  if (!orderDetail.value) {
    Message.warning('订单信息不存在');
    return;
  }

  // 检查订单状态是否为放款中(1)
  if (orderDetail.value.status !== 1) {
    Message.warning(`只有放款中状态的订单才能进行提前结清操作，当前状态: ${getOrderStatusText(orderDetail.value.status)}`);
    return;
  }

  // 权限和状态验证通过，打开确认对话框
  earlySettlementDialogVisible.value = true;
};

// 提前结清OK按钮点击
const handleEarlySettlementOK = () => {
  // 提前结清处理放在这里确保一定会被执行
  handleEarlySettlementConfirm();
};

// 提前结清取消按钮点击
const handleEarlySettlementCancel = () => {
  earlySettlementDialogVisible.value = false;
};

// 提前结清确认处理
const handleEarlySettlementConfirm = async () => {
  // 显示加载提示
  const loadingMessage = Message.loading('正在提交提前结清请求...');

  try {
    if (!orderDetail.value?.order_no) {
      Message.error('订单编号不存在');
      loadingMessage.close();
      return;
    }

    // 再次检查权限
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      loadingMessage.close();
      return;
    }

    // 再次检查订单状态
    if (!orderDetail.value || orderDetail.value.status !== 1) {
      Message.warning(`只有放款中状态的订单才能进行提前结清操作，当前状态: ${getOrderStatusText(orderDetail.value?.status || 0)}`);
      loadingMessage.close();
      return;
    }

    // 获取当前用户ID作为操作人ID
    const operatorId = getCurrentUserId();

    // 构造请求参数
    const requestData = {
      orderNo: orderDetail.value.order_no,
      operatorId: operatorId || 0 // 如果获取不到ID则使用0
    };

    // 发送请求
    const response = await defHttp.post<any>({
      url: '/order/manager/earlySettlement',
      data: requestData
    }, {
      isTransformResponse: false  // 不转换响应，获取原始数据
    });

    // 关闭加载提示
    loadingMessage.close();

    // 根据API文档，code=0表示成功
    if (response && response.code === 0) {
      // 关闭对话框
      earlySettlementDialogVisible.value = false;

      Message.success('提前结清成功');

      // 刷新订单详情
      fetchOrderDetail();
    } else {
      throw new Error(response?.message || '提前结清失败');
    }
  } catch (error) {
    // 关闭加载提示
    loadingMessage.close();

    console.error('提前结清失败:', error);
    Message.error(error instanceof Error ? error.message : '提前结清失败');
  }
};

// 打开放款确认对话框
const handleProcessDisbursement = () => {
  // 先检查业务员权限
  if (!hasOrderPermission()) {
    Message.warning('没有操作当前订单的权限，请在订单分配后操作');
    return;
  }

  // 再检查订单状态
  if (!orderDetail.value || orderDetail.value.status !== 0) {
    Message.warning('只有待放款状态的订单才能进行放款操作');
    return;
  }

  disbursementDialogVisible.value = true;
};

// 确认处理放款
const confirmProcessDisbursement = async (done: Function) => {
  try {
    if (!orderDetail.value?.order_no) {
      throw new Error('订单编号不存在');
    }

    // 再次检查权限
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      done(false);
      return;
    }

    // 获取当前用户ID作为操作人ID
    const operatorId = getCurrentUserId();

    // 构造请求参数，operatorId为可选参数，不传
    const requestData = {
      orderNo: orderDetail.value.order_no
    };

    console.log('放款处理请求参数:', requestData);

    // 调用放款处理API
    const response = await defHttp.post<any>({
      url: '/order/manager/processDisbursement',
      data: requestData
    }, {
      isTransformResponse: false  // 不转换响应，获取原始数据
    });

    console.log('放款处理API响应:', response);

    if (response && response.code === 0) {
      Message.success('放款请求已提交请刷新放款状态');
      // 自动关闭确认放款弹窗
      disbursementDialogVisible.value = false;
      done(true);

      // 刷新订单详情
      await fetchOrderDetail();
    } else {
      // 直接显示接口返回的错误信
      Message.error(response?.data || '放款处理失败');
      // 关闭放款确认对话框
      disbursementDialogVisible.value = false;
      done(false);
      return;
    }
  } catch (error) {
    console.error('放款处理失败:', error);
    Message.error(error instanceof Error ? error.message : '放款处理失败');
    done(false);
  }
};

// 跳转到订单列表并按用户姓名筛选
const goToOrderList = () => {
  const userName = safeGetNestedValue(customerInfo.value, 'name') || orderDetail.value?.user_name;
  
  // 确保参数名与API文档匹配，使用user_name作为筛选参数
  const query: Record<string, string> = {};
  if (userName) {
    query.user_name = userName;
  }
  
  // 使用replace而不是push，以确保用户可以返回到详情页
  router.push({
    path: '/ordermanagement/Orderlist',
    query
  });
};

// 处理关闭订单
const handleCloseOrder = async (done: Function) => {
  console.log('handleCloseOrder被调用');
  try {
    // 表单验证
    if (closeOrderForm.value.reasonType === undefined) {
      Message.warning(t('ordermanagement.detail.selectCloseReasonType'));
      done(false);
      return;
    }
    
    // 备注是可选的，不需要验证非空
    
    if (!orderDetail.value?.order_no) {
      throw new Error('订单编号不存在');
    }
    
    // 再次检查权限
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      done(false);
      return;
    }
    
    // 获取当前用户ID作为操作人ID
    const operatorId = getCurrentUserId();
    
    // 按照swagger规范构造请求参数
    const requestData = {
      orderNo: orderDetail.value.order_no,
      reason_for_closure: closeOrderForm.value.reasonType,
      closure_remarks: closeOrderForm.value.reason?.trim() || '', // 可选参数
      operatorId: operatorId || 0 // 如果获取不到ID则使用0
    };
    
    console.log('关闭订单请求参数:', requestData);
    
    // 使用defHttp直接调用API，不需要包含/business前缀，因为defHttp已经配置了基础URL
    // 关键是设置isTransformResponse为false，直接获取原始响应
    const response = await defHttp.post<any>({
      url: '/order/manager/closeOrder',
      data: requestData
    }, {
      isTransformResponse: false
    });
    
    console.log('关闭订单API响应:', response);
    
    if (response && response.code === 0) {
      Message.success(t('ordermanagement.detail.closeOrderSuccess'));
      done(true);
      // 关闭成功后返回列表页
      setTimeout(() => {
        goBack();
      }, 1000);
    } else {
      throw new Error(response?.message || '关闭订单失败');
    }
  } catch (error) {
    console.error('关闭订单失败:', error);
    Message.error(error instanceof Error ? error.message : t('ordermanagement.detail.closeOrderFailed'));
    done(false);
  }
};

// 获取关闭原因类型文本
const getCloseReasonTypeText = (type?: number): string => {
  if (type === undefined) return '未知原因';
  
  const reasonMap: Record<number, string> = {
    0: '终审拒绝',
    1: '法院涉案',
    2: '纯白户',
    3: '客户失联',
    4: '不提供资料',
    5: '多余订单',
    6: '重新下单',
    7: '客户不同意方案',
  };
  
  return reasonMap[type] || '未知原因';
};

// 处理取消前确认
const handleBeforeCancelRemark = () => {
  // 如果有内容，则显示确认对话框
  if (remarkContent.value && remarkContent.value.trim()) {
    cancelConfirmVisible.value = true;
    // 阻止主对话框关闭
    return false;
  }
  // 如果没有内容，允许关闭
  return true;
};

// 确认取消备注 (二次确认"确认"按钮)
const confirmCancelEditing = (done: Function) => {
  // 先关闭确认对话框
  cancelConfirmVisible.value = false;
  // 再关闭主对话框
  remarkDialogVisible.value = false;
  // 清空内容
  remarkContent.value = '';
  done(true);
};

// 处理提交备注
const handleSubmitRemark = async (done: Function) => {
  try {
    if (!remarkContent.value || !remarkContent.value.trim()) {
      Message.warning(t('ordermanagement.detail.remarkEmpty'));
      done(false);
      return;
    }

    const orderId = Number(route.params.id);
    if (!orderId || isNaN(orderId)) {
      throw new Error('无效的订单ID');
    }
    
    // 再次检查权限
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      done(false);
      return;
    }
    
    // 获取当前用户ID作为操作人ID
    const operatorId = getCurrentUserId();
    
    // 构造请求参数
    const requestData = {
      order_id: orderId,
      content: remarkContent.value,
    };
    
    console.log('添加备注请求参数:', requestData);

    // 调用添加备注API
    const response = await defHttp.post<any>({
      url: '/order/manager/createOrderRemark',
      data: requestData
    }, {
      isTransformResponse: false  // 不转换响应，获取原始数据
    });
    
    console.log('添加备注API响应:', response);
    
    if (response && response.code === 0) {
      Message.success(t('ordermanagement.detail.remarkSuccess'));
      
      // 重新获取订单详情，刷新备注列表
      await fetchOrderDetail();
      
      done(true);
      remarkDialogVisible.value = false;
    } else {
      throw new Error(response?.message || t('ordermanagement.detail.remarkFailed'));
    }
  } catch (error) {
    console.error('添加备注失败:', error);
    Message.error(error instanceof Error ? error.message : t('ordermanagement.detail.remarkFailed'));
    done(false);
  }
};

// 处理关闭订单按钮点击
const handleCloseOrderButtonClick = () => {
  // 检查订单状态
  if (!orderDetail.value || orderDetail.value.status !== 0) {
    Message.warning('只有待放款状态的订单才能进行关闭操作');
    return;
  }
  
  // 检查权限
  if (!hasOrderPermission()) {
    Message.warning('没有操作当前订单的权限，请在订单分配后操作');
    return;
  }
  
  // 权限验证通过，打开关闭订单对话框
  openCloseOrderDialog();
};

// 处理取消认领按钮点击
const handleCancelClaimButtonClick = () => {
  // 检查是否有销售分配ID
  if (!orderDetail.value?.sales_assignee_id || orderDetail.value.sales_assignee_id === 0) {
    Message.warning('订单未分配业务员，无法取消认领');
    return;
  }
  
  // 检查权限
  if (!hasOrderPermission()) {
    Message.warning('没有操作当前订单的权限，请在订单分配后操作');
    return;
  }
  
  // 权限验证通过，执行取消认领
  handleCancelClaim();
};
</script>

<style scoped>
.container {
  padding: 0 20px 20px 20px;
}

.general-card {
  margin-bottom: 24px;
}

.info-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  border-left: 4px solid #165DFF;
  padding-left: 12px;
}

.info-card-shadow {
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  padding: 16px;
  height: 100%;
  background-color: #ffffff;
}

.user-info-cards {
  margin-bottom: 24px;
}

.action-buttons {
  margin-top: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e9e9e9;
}

.detail-tabs {
  margin-top: 24px;
}

.tab-content {
  padding: 24px;
  min-height: 200px;
}

.id-card-images {
  margin-top: 20px;
}

.id-card-title {
  font-weight: bold;
  margin-bottom: 16px;
}

.id-card-container {
  display: flex;
  gap: 24px;
}

.id-card-item {
  width: 240px;
  text-align: center;
}

.id-card-image {
  width: 100%;
  height: auto;
  border: 1px solid #e9e9e9;
  border-radius: 4px;
}

.id-card-label {
  margin-top: 8px;
  color: #86909c;
}

.content-wrapper {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.contact-section {
  margin-top: 36px;
}

.contact-row {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.contact-header {
  font-weight: bold;
  margin-bottom: 16px;
  font-size: 15px;
  color: #1D2129;
  border-bottom: 1px solid #e9e9e9;
  padding-bottom: 8px;
}

.contact-info {
  display: flex;
  gap: 32px;
}

.contact-item {
  display: flex;
  align-items: center;
}

.contact-label {
  font-weight: 500;
  color: #4E5969;
  margin-right: 4px;
}

.contact-value {
  color: #1D2129;
}

:deep(.arco-descriptions-title) {
  color: #1D2129;
  border-bottom: none !important;
}

.remark-textarea-container {
  width: 100%;
}

.remark-textarea-container :deep(.arco-textarea) {
  min-height: 120px;
  resize: vertical;
}

.confirm-message {
  text-align: center;
  padding: 10px 0;
}
</style> 