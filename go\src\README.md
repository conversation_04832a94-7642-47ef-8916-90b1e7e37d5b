# FinCore快速开发后台管理系统介绍（基于GoFly框架）
## 一、框架介绍
框架采用前后端分离，将Go与Vue结合开发中后台系统，Go作为一种高效、安全的编程语言，可以帮助开发者快速构建高效、可靠、安全的应用，Vue作为前端优秀框架，可以快速搭建漂亮，高体验，稳定前端页面。能让开发者开发时顺手，客户使用时满意，性能与颜值并存，让开每一个项目交付都能让您和您的客户双方都满意。Go 开发业务接口，vue 开发前端界面。后台管理系统从业务上分为：
总管理系统（admin 端简称 A 端）和业务端管理系统（专门编写业务的，方便系统做出 saas 形系统，
减少后期需要多个应用重构成本，遇到买系统时不要单独重新部署直接再 A 端开一个账号就可以，
业务端 business 简称 B 端）。天生自带SAAS多账号数据分离，可以做到不用重新部署，即可单独拉出新的一套。

## 二、优势简介
1. 基于优秀成熟框架集成，保证系统可靠性。集成的主要有 Gin、Arco Design 、Mysql 等主流框架技术《我们不生产框架，我们是优秀框架的搬运工》。
2. 系统已集成开发常用基础功能，开箱即用，快速开始您业务开发，快人一步，比同行节省成本，降本增效首选。
3. 框架根据app目录下文件成交自动生成路由，无需手动添加，这种生成方式会避免路由重复，也减少手动添加麻烦。
4. 框架提供其他开发者开发的插件，可快速安装或卸载，让开个资源共享，同意功能无需重复造车，一键安装即可使用。 框架搭建了一键 CRUD 生成前后端代码，建数据库一键生成，节省您的复制粘贴时间，进一步为您节省时间。
5. 框架自带 API 接口文档管理，接口带有请求 token 等配置，添加接口只需配置路径和数据库或者备注，其部分信息如数据字段，系统自动根据数据库字段补齐，开发配套接口文档尽可能的为您节省一点时间。不需要其他接口文档工具复制粘贴，登录注册等时间。还有一个重点！接口文档可以一键生成接口 CRUD 的代码和通用的操作数据的 CRUD 接口，根据您的业务选择自己写接口代码、一键生成接口代码、不用写和生成代码调用通用接口。让写接口工作节省更多时间。
6. 前后端分离解耦业务，让前段人员与后端人协调开发，提高项目交付，并且可以开发出功能复杂度高的项目。
7. 前端用 Vue3+TypeScript 的 UI 框架 [Arco Design](https://arco.design/vue/component/button)，好用的 UI 框架前端可以设计出优秀且交互不错的界面，完善的大厂 UI 支持，前端开发效率也很高！ 以上只是框架比较明显优势点，还有很多优势等你自己体验，我们从各个开发环节，努力为您节省每一分时间。
8. 集成操作简单的 ORM 框架，操作数据非常简单，就像使用php的Laravel一样，您可以去文档看看 [框架的ROM数据库操作文档](https://doc.goflys.cn/docview?id=25&fid=289)
   例如下面语句就可以查找一条数据：
 ```
  db.Table("users").Fields("uid,name,age").First()
```

## 三、目录结构

```
├── app                     # 应用目录
│   ├── admin               # 后台管理应用模块
│   ├── business            # 业务端应用模块
│   ├── common              # 公共应用模块
│   ├── wxapp               # Uniapp H5后台api模块
│   └── controller.go       # 应用控制器
├── bootstrap               # 工具方法
├── global                  # 全局变量
├── model                   # 数据模型
├── resource                # 静态资源和config配置文件
├── route                   # 路由
├── runtime                 # 运行日志文件
├── tmp                     # 开发是使用fresh热编译 产生临时文件
├── utils                   # 工具包
├── go.mod                  # 依赖包管理工具
├── go.sum         
├── main.go                 # main函数        
└── README.md               # 项目介绍
```
开发时仅需在app目录下添加你新的需求，app外部文件建议不要改动，除了config配置需要改，其他不要修改，
框架已经为您封装好，你只需在app应用目录书写你的业务，路由、访问权限、跨域、限流、Token验证、ORM等
框架已集成好，开发只需添加新的方法或者新增一个文件即可。

## 四、安装及部署打包说明

### 1. 本地电脑安装好vmware，并安装好ubuntu24

### 2. 提前安装好软件
go、mysql、redis、nginx、nodejs、yarn、git、npm、docker

### 3. 拉取代码到/data/xxxx(你自己的名字)/

### 4. 后端代码
#### 初始化mod
go mod tidy

#### 执行下面的脚本初始化数据库
脚本目录：go\src\resource\developer\template\install_db.sh
bash install_db.sh

#### 修改ip
将项目中所有的的***********改为自己虚拟机的端口

#### 打包
bash build.sh
打包完后会在go/bin目录下生成fincore可执行文件

#### 拉起后端服务
cd go/bin
./fincore
