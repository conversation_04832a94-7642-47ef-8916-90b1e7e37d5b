package compression

import (
	"fincore/utils/log"
	"fmt"
	"image"
	"image/jpeg"
	"os"
	"path/filepath"

	"github.com/nfnt/resize"
)

/***
* 压缩图片
InplaceCompressImage(filePath string, width uint, quality int)可以原地压缩
* filePath: 图片文件路径
* width: 目标宽度，0表示保持原始比例
* quality: JPEG质量 (1-100)，（数值越大，画质越好，文件越大）。推荐区间：70-85（平衡画质和文件大小的黄金区间）
* 注意：压缩后会覆盖原始文件
*/

// compressImage 压缩图片
func compressImage(inputPath, outputPath string, width uint, quality int) error {
	// 打开原始图片文件
	file, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("Error opening file: %v", err)
	}
	defer file.Close()

	// 解码图片
	img, _, err := image.Decode(file)
	if err != nil {
		return fmt.Errorf("Error decoding image: %v", err)
	}

	// 调整图片大小
	height := uint(0)
	newImg := resize.Resize(width, height, img, resize.Lanczos3)

	// 创建输出文件
	outFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("Error creating output file: %v", err)
	}
	defer outFile.Close()

	// 保存压缩后的图片
	err = jpeg.Encode(outFile, newImg, &jpeg.Options{Quality: quality})
	if err != nil {
		return fmt.Errorf("Error encoding image: %v", err)
	}

	return nil
}

// InplaceCompressImage 原地压缩图片
// 参数：
//   - filePath: 图片文件路径
//   - width: 目标宽度，0表示保持原始比例
//   - quality: JPEG质量 (1-100)
func InplaceCompressImage(filePath string, width uint, quality int) error {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("文件不存在: %s", filePath)
	}

	// 获取文件扩展名和临时文件名
	ext := filepath.Ext(filePath)
	tempFile, err := os.CreateTemp(filepath.Dir(filePath),
		fmt.Sprintf("temp-%s-*.%s",
			filepath.Base(filePath),
			ext[1:])) // 移除扩展名前的点
	if err != nil {
		return fmt.Errorf("创建临时文件失败: %v", err)
	}
	tempPath := tempFile.Name()
	tempFile.Close()

	// 调用压缩函数
	err = compressImage(filePath, tempPath, width, quality)
	if err != nil {
		os.Remove(tempPath) // 清理临时文件
		return fmt.Errorf("压缩图片失败: %v", err)
	}

	// 获取原始文件权限
	info, err := os.Stat(filePath)
	if err != nil {
		os.Remove(tempPath) // 清理临时文件
		return fmt.Errorf("获取文件权限失败: %v", err)
	}

	// 替换原始文件
	err = os.Rename(tempPath, filePath)
	if err != nil {
		os.Remove(tempPath) // 清理临时文件
		return fmt.Errorf("替换原始文件失败: %v", err)
	}

	// 恢复原始文件权限
	if err := os.Chmod(filePath, info.Mode()); err != nil {
		return fmt.Errorf("恢复文件权限失败: %v", err)
	}

	log.Info("压缩成功！")
	return nil
}
