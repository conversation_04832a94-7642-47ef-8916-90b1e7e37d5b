package system

import (
	"fincore/route/middleware"
	"fincore/utils/gf"
	"fincore/utils/results"
	"reflect"

	"github.com/gin-gonic/gin"
)

// Member 成员管理控制器
type Member struct{}

// init 初始化路由注册
func init() {
	controller := Member{}
	gf.Register(&controller, reflect.TypeOf(controller).PkgPath())
}

// Get_list 获取成员列表
// @Summary 获取成员列表
// @Description 获取所有成员信息，不分页返回
// @Tags 系统管理-成员管理
// @Accept json
// @Produce json
// @Success 200 {object} results.Response
// @Router /business/system/member/get_list [get]
func (c *Member) Get_list(ctx *gin.Context) {
	// 1. 获取当前登录用户信息
	getuser, exists := ctx.Get("user")
	if !exists {
		results.Failed(ctx, "用户信息获取失败", "未找到用户登录信息")
		return
	}
	user := getuser.(*middleware.UserClaims)

	// 2. 调用服务层获取成员列表
	service := MemberService{}
	memberList, err := service.GetMemberList(user.BusinessID)
	if err != nil {
		results.Failed(ctx, "获取成员列表失败", err.Error())
		return
	}

	// 3. 返回成功结果
	results.Success(ctx, "获取成员列表成功", memberList, nil)
}
