package repayment

// PaymentResult 支付结果
type PaymentResult struct {
	TransactionNo string  `json:"transaction_no"` // 交易流水号
	Amount        float64 `json:"amount"`         // 支付金额（元）
	Status        string  `json:"status"`         // 支付状态
	Message       string  `json:"message"`        // 状态描述
}

// CreateRepaymentResponse 创建还款支付响应
type CreateRepaymentResponse struct {
	GuaranteePayment *PaymentResult `json:"guarantee_payment,omitempty"` // 担保费支付结果
	AssetPayment     *PaymentResult `json:"asset_payment,omitempty"`     // 资管费支付结果
}

// ManualWithholdResponse 手动代扣响应
type ManualWithholdResponse struct {
	TransactionNo string  `json:"transaction_no"` // 交易流水号
	Amount        float64 `json:"amount"`         // 代扣金额（元）
	Status        string  `json:"status"`         // 代扣状态
	Message       string  `json:"message"`        // 状态描述
	BillID        int     `json:"bill_id"`        // 账单ID
	OperatorID    int     `json:"operator_id"`    // 操作员ID
}

// QueryPaymentStatusResponse 查询支付状态响应
type QueryPaymentStatusResponse struct {
	TransactionNo        string  `json:"transaction_no"`          // 交易流水号
	Status               string  `json:"status"`                  // 支付状态
	StatusRaw            int     `json:"status_raw"`              // 支付状态原始值
	Amount               float64 `json:"amount"`                  // 支付金额（元）
	ChannelTransactionNo string  `json:"channel_transaction_no"`  // 第三方交易号
	ErrorCode            string  `json:"error_code,omitempty"`    // 错误码
	ErrorMessage         string  `json:"error_message,omitempty"` // 错误信息
	UpdatedAt            string  `json:"updated_at"`              // 更新时间
}

// SubmittedAmountsResponse 已提交金额查询响应
type SubmittedAmountsResponse struct {
	BillID                int     `json:"bill_id"`                 // 账单ID
	GuaranteeAmount       float64 `json:"guarantee_amount"`        // 已提交担保费（元）
	AssetAmount           float64 `json:"asset_amount"`            // 已提交资管费（元）
	TotalAmount           float64 `json:"total_amount"`            // 已提交总金额（元）
	RemainingGuaranteeFee float64 `json:"remaining_guarantee_fee"` // 剩余担保费（元）
	RemainingAssetFee     float64 `json:"remaining_asset_fee"`     // 剩余资管费（元）
}

// SystemAutoWithholdBatchResponse 系统自动批量代扣响应
type SystemAutoWithholdBatchResponse struct {
	TotalCount   int                        `json:"total_count"`   // 总处理数量
	SuccessCount int                        `json:"success_count"` // 成功数量
	FailureCount int                        `json:"failure_count"` // 失败数量
	Results      []SystemAutoWithholdResult `json:"results"`       // 详细结果
}

// SystemAutoWithholdResult 系统自动代扣单个结果
type SystemAutoWithholdResult struct {
	BillID            int                      `json:"bill_id"`            // 账单ID
	UserID            int                      `json:"user_id"`            // 用户ID
	OrderID           int                      `json:"order_id"`           // 订单ID
	BankCardID        int                      `json:"bank_card_id"`       // 银行卡ID
	Success           bool                     `json:"success"`            // 是否成功
	ErrorMessage      string                   `json:"error_message"`      // 错误信息
	RepaymentResponse *CreateRepaymentResponse `json:"repayment_response"` // 还款响应
}
