<template>
	<view class="tousu">
		<!-- 顶部 banner 区域 -->
		<view class="tousu-top">
			<image src="/static/image/toushubg-Bsap913V.png" mode="widthFix"></image>
			<!-- 标题与电话 -->
			<view class="banner-info">
				<text class="title">我要投诉</text>
				<text class="phone">************</text>
			</view>
		</view>

		<!-- 投诉人信息区域 -->
		<view class="form-section">
			<text class="section-title">* 投诉人信息</text>
			<view class="form-item">
				<text class="label">姓名：</text>
				<input class="input-field" v-model="formData.name" placeholder="请输入您的姓名" />
			</view>
			<view class="form-item">
				<text class="label">身份证号：</text>
				<input class="input-field" v-model="formData.id_card" placeholder="请输入您的身份证号" maxlength="18"/>
			</view>
			<view class="form-item">
				<text class="label">手机号：</text>
				<input class="input-field" v-model="formData.mobile" placeholder="请输入您的手机号"  maxlength="11"/>
			</view>
			<!-- <view class="form-item">
				<text class="label">详细地址：</text>
				<input class="input-field" v-model="formData.address" placeholder="请输入您的详细地址 (选填)" />
			</view> -->
		</view>

		<!-- 投诉内容区域 -->
		<view class="form-section">
			<text class="section-title">* 投诉内容</text>
			<textarea class="complain-text" placeholder="请描述投诉内容..." v-model="complainContent"></textarea>
		</view>

		<!-- 上传照片区域 -->
		<view class="form-section">
			<text class="section-title">上传照片</text>
			<view class="upload-list">
				<view v-for="(img, idx) in imageList" :key="img" class="upload-box">
					<image :src="img" class="upload-img" @click="previewImage(idx)" />
					<view class="remove-btn" @click.stop="removeImage(idx)">×</view>
				</view>
				<view v-if="imageList.length < maxImages" class="upload-box add" @click="chooseImage">
					<text class="upload-placeholder">+</text>
				</view>
			</view>
		</view>
		<!-- 提交按钮 -->
		<button class="submit-btn" :class="{ disabled: !canSubmit }" :disabled="!canSubmit" @click="handleSubmit">提交</button>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		reactive
	} from 'vue';
	import {
		onShow
	} from '@dcloudio/uni-app';
	import {
		storeToRefs
	} from 'pinia';
	import user from '@/store/user.js';
	import upload from '@/api/upload.js'
	import complaintApi from '@/api/complaint.js'
	const userStore = user();
	const {
		userInfo
	} = storeToRefs(userStore);

	const formData = reactive({
		name: '',
		id_card: '',
		mobile: '',
		address: ''
	});

	// 在页面显示时获取最新的用户信息
	onShow(async () => {
		console.log('投诉页面 userInfo:', userInfo.value);
		if (!userStore.isLogin) {
			uni.showToast({ title: '请先登录', icon: 'none' });
			setTimeout(() => {
				uni.reLaunch({ url: '/pages/login/login' });
			}, 2000);
			return;
		}
		if (!userInfo.value.uid) {
			// 主动从本地恢复
			const stored = uni.getStorageSync('userInfo');
			if (stored) {
				const parsed = typeof stored === 'string' ? JSON.parse(stored) : stored;
				userInfo.value = parsed;
				console.log('投诉页面本地恢复userInfo:', userInfo.value);
			}
		}
		if (!userInfo.value.uid) {
			await userStore.getInfo();
		}
		if (!userInfo.value.uid) {
			uni.showToast({ title: '用户信息获取失败', icon: 'none' });
			return;
		}
		formData.name = userInfo.value.name || '';
		formData.id_card = userInfo.value.idCard || '';
		formData.mobile = userInfo.value.mobile || '';
		formData.address = userInfo.value.address || '';
		
		// try {
		// 	const res = await complaintApi.getComplaintUserInfo(userInfo.value.uid);
		// 	if (res.code === 0 && res.data) {
		// 		formData.name = res.data.name || '';
		// 		formData.id_card = res.data.idCard || '';
		// 		formData.mobile = res.data.telephone || '';
		// 		formData.address = res.data.address || '';
		// 	} else {
		// 		uni.showToast({ title: '请先实名认证', icon: 'none' });
		// 	}
		// } catch (e) {
		// 	uni.showToast({ title: '获取实名信息失败', icon: 'none' });
		// }
	});

	// 投诉内容
	const complainContent = ref('');
	// 图片上传
	const imageList = ref([]);
	const maxImages = 3;

	// 提交按钮可用性
	const canSubmit = computed(() => {
		return formData.name.trim() !== '' &&
			formData.id_card.trim() !== '' &&
			formData.mobile.trim() !== '' &&
			complainContent.value.trim() !== ''
	});

	// 上传图片
	function chooseImage() {
		if (imageList.value.length >= maxImages) return;
		uni.chooseImage({
			count: maxImages - imageList.value.length,
			success: (res) => {
				imageList.value.push(...res.tempFilePaths);
			},
			fail: (err) => {
				console.log('chooseImage fail', err)
			}
		});
	}
	// 删除图片
	function removeImage(idx) {
		imageList.value.splice(idx, 1);
	}
	// 预览图片
	function previewImage(idx) {
		uni.previewImage({
			current: imageList.value[idx],
			urls: imageList.value
		});
	}
	// 提交逻辑
	const handleSubmit = async () => {
		if (!userInfo.value.uid) {
			uni.showToast({ title: '用户信息不完整', icon: 'none' });
			return;
		}
		if (!canSubmit.value) {
			uni.showToast({ title: '请完善所有必填信息后再提交', icon: 'none' });
			return;
		}
		uni.showLoading({ title: '提交中...' });
		try {
			const uploadResults = await Promise.all(imageList.value.map(filePath => upload.uploadFile(filePath)));
			const imgUrls = uploadResults.map(result => result.data.url);

			const complaintData = {
				uid: userInfo.value.uid,
				complaintContent: complainContent.value,
				imgUrls: imgUrls.join(','),
				userName: formData.name,
				telephone: formData.mobile,
				idCard: formData.id_card,
				address: formData.address
			};

			console.log('投诉提交数据:', complaintData);

			const complaintResult = await complaintApi.uploadComplaintContent(complaintData);
			uni.hideLoading();
			uni.showToast({ title: '提交成功', icon: 'success' });
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		} catch (error) {
			uni.hideLoading();
			uni.showToast({ title: error.message || '提交失败', icon: 'none' });
		}
	};
</script>

<style scoped>
	/* 页面基础样式 */
	.tousu {
		width: 100%;
		min-height: 100vh;
		background-color: #eff2f7;
		font-size: 14px;
		color: #333;
		overflow: hidden;
	}

	/* 顶部 banner */
	.tousu-top {
		position: relative;
		width: 100%;
	}

	.tousu-top image {
		width: 100%;
		height: 150px !important;
		display: block;
	}

	/* banner 标题与电话 */
	.banner-info {
		position: absolute;
		left: 20px;
		top: 15px;
		font-size: 20px;
		font-weight: 600;
		color: #fff;
	}

	.banner-info .title {
		font-size: 26px;
		font-weight: bold;
		margin-bottom: 15px;
		display: block;
	}

	.banner-info .phone {
		font-size: 20px;
		color: #000;
		opacity: 1.9;
	}

	/* 表单板块通用样式 */
	.form-section {
		background-color: #fff;
		margin: 12px;
		padding: 16px;
		border-radius: 8px;
		box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
	}

	.section-title {
		font-size: 16px;
		font-weight: 500;
		color: #333;
		margin-bottom: 20px;
		display: block;
	}

	/* 表单项 */
	.form-item {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
		border-bottom: 1px solid #f0f0f0;
		padding-bottom: 10px;
	}

	.form-item:last-child {
		border-bottom: none;
	}

	.form-item .label {
		width: 85px;
		color: #666;
		flex-shrink: 0;
	}

	.input-field {
		flex: 1;
		font-size: 15px;
		color: #333;
	}

	.form-item .value.placeholder {
		color: #bbb;
		text-align: right;
		font-style: italic;
	}

	/* 投诉内容输入框 */
	.complain-text {
		width: 100%;
		min-height: 100px;
		padding: 10px;
		border: 1px solid #eee;
		border-radius: 4px;
		line-height: 1.6;
		color: #333;
		box-sizing: border-box;
	}

	/* 图片上传 */
	.upload-list {
		display: flex;
		gap: 12px;
		flex-wrap: wrap;
		margin-top: 8px;
	}

	.upload-box {
		width: 80px;
		height: 80px;
		border: 1px dashed #ddd;
		border-radius: 8px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #fafafa;
		position: relative;
		overflow: hidden;
	}

	.upload-img {
		width: 100%;
		height: 100%;
		border-radius: 8px;
	}

	.upload-placeholder {
		font-size: 36px;
		color: #ccc;
	}

	.upload-box.add {
		cursor: pointer;
		background: #f0f4fa;
		border: 1px dashed #b3c0d1;
		color: #b3c0d1;
		justify-content: center;
		align-items: center;
	}

	.remove-btn {
		position: absolute;
		top: 2px;
		right: 6px;
		color: #fff;
		background: #f56c6c;
		border-radius: 50%;
		width: 18px;
		height: 18px;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 16px;
		cursor: pointer;
		z-index: 2;
	}

	/* 提交按钮 */
	.submit-btn {
		width: calc(100% - 24px);
		height: 44px;
		line-height: 44px;
		background-color: #007aff;
		color: #fff;
		font-size: 16px;
		border-radius: 22px;
		margin: 12px auto 30px;
		display: block;
		border: none;
		transition: background 0.2s;
	}

	.submit-btn.disabled {
		background: #ccc;
		color: #fff;
	}

	.submit-btn::after {
		border: none;
	}
</style>
