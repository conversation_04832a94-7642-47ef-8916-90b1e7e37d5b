dbconf:
     # 数据库类型 mysql, sqlite3, post<PERSON><PERSON>, sqlserver
     driver: mysql
     #服务器地址 本地建议 127.0.0.1
     hostname: 127.0.0.1
     #端口 默认3306
     hostport: 3306
     #用户名
     username: root
     #密码
     password: root1234
     #数据库名
     database: fincore
     #数据表前缀
     prefix: 
redis:
     host: ************ # 连接地址
     port: 6379         # 端口
     password:          # 密码
     db: 0              # 数据库编号
     timeout: 15        # 链接超时 单位秒
jwt:
     secret: 3Bde3BGEbYqtqyEUzW3ry8jKFcaPH17fRmTmqE7MDr05Lwj95uruRKrrkb44TJ4s
     jwt_ttl: 43200
app:
     #版本号
     version: 1.3.0
     #环境状态：dev=开发，pro=生产
     env: dev
     #运行服务端口（根据您的实际情况修改）
     port: 8109
     #运行H5服务的端口（根据您的实际情况修改）
     h5port: 6020
     #运行服务器的IP地址
     hostname: localhost
     #接口合法性验证
     apisecret: gofly@888
     #接口JWT验证、跨域域名-不添加请求时报403 (开发、部署必改)
     allowurl: http://************:9105,http://************:9106,http://************:444,http://localhost:6020,http://localhost:9106,http://localhost:*,http://************:9106
     #token超时时间单位分钟 
     tokenouttime: 10 
     #调用cpu个数
     cpunum: 3
     # Gin 框架在运行的时候默认是debug模式 有： 开发：debug，生产：release，测试模式：test
     runlogtype: debug
     # 配置代码生成时-前端代码根目录位置(开发必改)
     vueobjroot: D:/Project/develop/vue/gofly_enterprise/business
     #配置企业私有仓网址
     companyPrivateHouse: 
     # 配置根域名访问重定向路径,默认是业务端后台
     rootview: webbusiness
     #不需要token-根模块
     noVerifyTokenRoot: resource,webbusiness,webadmin
     #不需要api接口合法性验证-根模块md5加密
     noVerifyAPIRoot: resource,webbusiness,webadmin,uniapp
     #不需要验证token-具体请求路径
     noVerifyToken: /common/uploadfile/get_image,/common/install/index,/common/install/save,/admin/user/login,/admin/user/logout,/admin/user/refreshtoken,/admin/user/get_code,/admin/user/resetPassword,/business/user/login,/business/user/logout,/business/user/refreshtoken,/business/user/get_code,/business/user/resetPassword,/admin/user/get_logininfo,/business/user/get_logininfo,/uniapp/user/loginBySms,/uniapp/captcha/getCaptcha,/uniapp/user/postSms,/uniapp/user/postBySms,/uniapp/user/postBySms,/uniapp/user/getUserInfo,
     #不需要接口合法性-具体请求路径
     noVerifyAPI: /common/install/index,/common/install/save
     #邀请页面路径
     invitationPage: /#/pages/login/login
     #前端请求协议
     h5Protocol: http 
     # 文件服务地址，后续要改成 oss
     fileServer: http://************:8109

# ==================== 开发环境日志系统配置 ====================
# 开发环境专用配置，启用详细的调试信息和SQL日志
log:
     # 开发环境使用debug等级，记录详细的调试信息
     # debug: 包含所有调试信息，便于开发调试
     level: debug

     # 日志根目录，所有日志文件都存放在此目录下
     # 目录结构: ./log/YYYY-MM/YYYY-MM-DD_模块名.log
     root_dir: ./log

     # 开发环境使用JSON格式，便于日志分析工具处理
     format: json

     # 日志输出方式，both: 同时输出到文件和控制台，file: 只输出到文件，console: 只输出到控制台
     output: both

     # 时区设置，支持标准时区名称如：Asia/Shanghai, UTC, America/New_York, Europe/London 等
     timezone: Asia/Shanghai

     # 开发环境显示代码行号，便于快速定位问题
     show_line: true

     # 开发环境日志轮转配置（相对宽松）
     max_backups: 10    # 保留的旧日志文件数量
     max_size: 100      # 单个日志文件最大大小（MB）
     max_age: 30        # 旧日志文件保留天数
     compress: true     # 压缩旧日志文件节省空间


sms:
     # 短信平台
     platform: dahantc
     # 短信平台账号
     account: dh48364
     # 短信平台密码
     password: c48b94fef1b0e9bd4f08d0ef5657dc60
     # 短信平台url
     apiurl: http://www.dh3t.com/json/sms/BatchSubmit
     # 短信平台签名
     sign:
     #是否启用第三方接口 T启用 F不启用
     isused: T
vbank:
     # 四要素验证第三方API
     apiurl: "http://v.juhe.cn/verifybankcard4/query"
     # 四要素验证第三方API key (购买后获取)
     apikey: ""



# 爱签的基本配置信息
aiqian_envs:
     app_id: "*********"
     # pro_host: "https://oapi.asign.cn"
     pro_host: "https://prev.asign.cn"
     port: 443
     PrivateKey: "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCUQi9WkiINWvg2alKlQDwT8TTk0dsRC66gmqZYzGPdQUCAPPc2XGM9vrUTJBWiyBCliEHPRXm+M5rV0e3xWyzCkYb6hS0Am0B9pdl8vNMgQxhayktWVz3j0UkJgB9SaRmgQ9oFf5ZSUoqUtKe8p4dFHoSCTjSxebJJvAfYhj1IU/VirwMoBNMvIIHTyzAMx+Brgxnm/JcoUpxfY3q+kCJOGl0LdieU1LXd/6MkMyb9dVV7s/BApr5AqtJ4yThTZWlTuu01QOxzZyEhgft59YVYXutMXYXCp6qpYT9yPD/woWpZFRU8sRnvwKe/8i3ewc9+WY9oLapTXwP0iguNNjQRAgMBAAECggEAHRlMb0JpWpOzSgLeS77qy8M47Xxe8d6IHM+dvKoJI8EN5t2Qa+QAOns20RXxBS0dT1AKEqOBPJqmVjdI62lIxLuJcZw38/mE0+R8ZmYtThS9L/yqgrzG0fMe0bWK8ELem70ViqS7HVdECeHXVkjPXPqbaXPuFbNcEerM42udlZdOPWVmV/3cH19STcG2twpEpesVX1aRcpLFaNGEiYSgUVbCtyDETuaToC7e6W+INFN47/3u818gNj8lspHhM2sp0Igz3SOx/v+8ibTm59M5kbAbYjE2xBXf8xR3TYa48EXzQa7NyHMkB88cJ8sFI1tn5MqmWRhtXF/WJlU5mU5wzQKBgQD4lKUMHsAk0if6G6UkXa3RLSJnxOA8dxd4Tyz78ehlc1M12HD8VbTyaS/oATP0BroiCGN4oQiyl3keDb3ir0ddEovLTgENYalazdZ+dFc6wqstPXfsBF+oRwOkRc/01JUsysfX+c6nOlMBfNaugdtwp6xEeQ6WfJsZWQcM83YLEwKBgQCYrv+4ee4BY87r4n/0JQ/BqWqPu+9R+O8JKfeygRV7GVQZK6qvgJWNoESDqeJUEE/S/vMboVPRt/lnKf4vEKLPTPxVpE7LkkYnGgZZUrdcLg8IG5UeFf8qyUpuz+DFU/6jBhdGjOvzopki2dJ1WDtjEuPDBy/8MwHhh10F1ZhkywKBgCpD49gutk7MaL2uy6JmRzKEsQ/hupqtSRFMrQJdfD0boa9LENLmo7B/0ARrh/Da3/T/ZiJRhxAOjypb/cg4MzMQ/zHfHnCvBUOKMgkoMNit1rsHc4duqvEKugTJVfVz27VU+SuwrsFiOlDLcBkVvUDnUXQG/UL7BsgFihxanNx/AoGAZTqpaurURPGikDxEr18fGjwz3YYxvrx35zB+/EefMVVyr5mG+gMbpKGcq1qoJyoFcpP8JNaKVyJN5fsufo/eEekt564fW9LoViZoDTLNRP14k9yNujvFSN3aTqvej5YH3pg8OFdYXNjyBOw8H/bWsPImNfj+9Ivw9FEl+KdL+Y8CgYEAnDoYk+7MBBuCSS9UzIo7WB47uAdPI/qsVVYPCf9UoIFbVSe5V8fss8CYO3BO36BV+BC5I9eLOxkLQtLmf1NJXy96fQprJbKhcnBu1IBp6GHOAZbBsM8KBPTehsMA+b1w8IghNIMOcWiLIyguLgSpnS4AUgrA9PZm1Cc3YjAlpR4="
# 接口配置示例
aiqian_apis:
     idcard_verification_path: "/verify/person/idcard2"  # "个人身份证二要素比对"

# 商盟 统统付
sumpay:
     environment: "test"  # test, prod
     test_url: "https://test.sumpay.cn/entrance/gateway.htm"
     prod_url: "https://entrance.sumpay.cn/gateway.htm"
     mer_no: "s100000040" # 资管账户
     mer_no_guarantee: "200105383508" # 担保账户
     app_id: "s100000040"
     domain: "www.baidu.com"
     notify_url: "http://www.baidu.com"
     skip_tls_verify: true      # 是否跳过TLS验证（测试环境建议true）
     request_timeout: 60        # 请求超时时间（秒）
     retry_count: 3             # 重试次数
     retry_wait_time: 5         # 重试等待时间（秒）
     cert:
          private_key_path: "resource/cert/yixuntiankong.pfx"
          public_key_path: "resource/cert/yixun.cer"
          password: "sumpay"