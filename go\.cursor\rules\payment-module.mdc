---
description: src/*
alwaysApply: false
---
# 支付模块开发规范

## 模块文件
- [src/app/business/payment/controller.go](mdc:src/app/business/payment/controller.go) - 支付控制器
- [src/app/business/payment/schema.go](mdc:src/app/business/payment/schema.go) - 支付数据结构
- [src/app/business/payment/service.go](mdc:src/app/business/payment/service.go) - 支付业务逻辑
- [src/thirdparty/payment/sumpay/service.go](mdc:src/thirdparty/payment/sumpay/service.go) - 第三方支付服务
- [src/model/business_payment_channels.go](mdc:src/model/business_payment_channels.go) - 支付渠道模型
- [src/model/business_payment_transactions.go](mdc:src/model/business_payment_transactions.go) - 支付交易模型

## 支付处理流程
- 所有支付请求必须经过签名验证
- 支付接口应有幂等性处理，防止重复支付
- 支付结果通知应有重试机制
- 所有金额计算必须使用 decimal 包

## 支付安全规范
- 敏感信息（如银行卡号）必须加密存储
- 接口参数必须进行严格校验
- 支付请求必须记录详细日志，便于追踪问题
- 第三方支付接口调用必须有超时处理

## 第三方支付对接规范
- 对接不同渠道时，应使用统一的接口抽象
- 渠道配置应存储在配置文件中，不应硬编码
- 应实现完整的日志记录，包括请求和响应数据
# 支付模块开发规范

## 模块文件
- [src/app/business/payment/controller.go](mdc:src/app/business/payment/controller.go) - 支付控制器
- [src/app/business/payment/schema.go](mdc:src/app/business/payment/schema.go) - 支付数据结构
- [src/app/business/payment/service.go](mdc:src/app/business/payment/service.go) - 支付业务逻辑
- [src/thirdparty/payment/sumpay/service.go](mdc:src/thirdparty/payment/sumpay/service.go) - 第三方支付服务
- [src/model/business_payment_channels.go](mdc:src/model/business_payment_channels.go) - 支付渠道模型
- [src/model/business_payment_transactions.go](mdc:src/model/business_payment_transactions.go) - 支付交易模型

## 支付处理流程
- 所有支付请求必须经过签名验证
- 支付接口应有幂等性处理，防止重复支付
- 支付结果通知应有重试机制
- 所有金额计算必须使用 decimal 包

## 支付安全规范
- 敏感信息（如银行卡号）必须加密存储
- 接口参数必须进行严格校验
- 支付请求必须记录详细日志，便于追踪问题
- 第三方支付接口调用必须有超时处理

## 第三方支付对接规范
- 对接不同渠道时，应使用统一的接口抽象
- 渠道配置应存储在配置文件中，不应硬编码
- 应实现完整的日志记录，包括请求和响应数据
