package bankcard

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"fincore/model"
	"fincore/thirdparty/payment/sumpay"
	"fincore/utils/log"
)

// UserInfoRequest 用户信息请求
type UserInfoRequest struct {
	Marry                     string `json:"marry"`                     // 婚姻状况
	Address                   string `json:"address"`                   // 详细地址
	Occupation                string `json:"occupation"`                // 职业
	Yearrevenue               string `json:"yearrevenue"`               // 年收入
	Purposeofborrowing        string `json:"purposeofborrowing"`        // 借款用途
	Emergencycontact0relation string `json:"emergencycontact0relation"` // 与联系人1的关系
	Emergencycontact0name     string `json:"emergencycontact0name"`     // 联系人1的姓名
	Emergencycontact0phone    string `json:"emergencycontact0phone"`    // 联系人1的手机号
	Emergencycontact1relation string `json:"emergencycontact1relation"` // 与联系人2的关系
	Emergencycontact1name     string `json:"emergencycontact1name"`     // 联系人2的姓名
	Emergencycontact1phone    string `json:"emergencycontact1phone"`    // 联系人2的手机号
	Degree                    string `json:"degree"`                    // 学历
}

// BankCardInfoRequest 银行卡信息请求
type BankCardInfoRequest struct {
	Mobile     string `json:"mobile"`     // 银行预留手机号
	Banknumber string `json:"banknumber"` // 银行卡号
	SmsCode    string `json:"smsCode"`    // 短信验证码
}

// BankCardRequest 银行卡绑定请求
type BankCardRequest struct {
	Mobile            string `json:"mobile"`               // 银行预留手机号
	Banknumber        string `json:"banknumber"`           // 银行卡号
	Code              string `json:"code"`                 // 短信验证码
	ThirdPartyOrderNo string `json:"third_party_order_no"` // 订单号
}

// BankCardSmsResponse 银行卡短信响应
type BankCardSmsResponse struct {
	ThirdPartyOrderNo string `json:"third_party_order_no"` // 订单号
	NeedSms           bool   `json:"need_sms"`             // 是否需要短信验证
}

// BankCardBindResponse 银行卡绑定响应
type BankCardBindResponse struct {
	ThirdPartyOrderNo string `json:"third_party_order_no"` // 订单号
	BindCardId        string `json:"bind_card_id"`         // 绑卡ID
	BankCardNo        string `json:"bank_card_no"`         // 银行卡号
}

// BankCardListResponse 银行卡列表响应
type BankCardListResponse struct {
	Total int                      `json:"total"` // 银行卡总数
	List  []map[string]interface{} `json:"list"`  // 银行卡列表
}

// BankCardService 银行卡服务
type BankCardService struct {
	sumpayService sumpay.SumpayServiceInterface
	bankCardModel *model.BusinessBankCardsService
	logger        *log.Logger
}

// NewBankCardService 创建银行卡服务实例
func NewBankCardService(ctx context.Context) *BankCardService {
	sumpayService, err := sumpay.NewSumpayService()
	if err != nil {
		return nil
	}

	return &BankCardService{
		sumpayService: sumpayService,
		bankCardModel: model.NewBusinessBankCardsService(ctx),
		logger:        log.BankCard().WithContext(ctx),
	}
}

// BindUserInfo 绑定用户信息
func (s *BankCardService) BindUserInfo(ctx context.Context, userID uint, data map[string]interface{}) error {
	// 更新用户信息到数据库
	err := s.bankCardModel.UpdateUserInfo(int(userID), data)
	if err != nil {
		return fmt.Errorf("绑定信息至数据库失败: %v", err)
	}

	return nil
}

// SendBankCardSms 发送银行卡绑定短信验证码
func (s *BankCardService) SendBankCardSms(ctx context.Context, userID uint, data map[string]interface{}) (*BankCardSmsResponse, error) {

	mobile := data["mobile"].(string)
	banknumber := data["banknumber"].(string)
	bankCode := data["bankCode"].(string)
	bankName := data["bankName"].(string)
	cardType := data["cardType"].(string)
	cardTypeName := data["cardTypeName"].(string)

	s.logger.WithFields(
		log.Uint("user_id", userID),
		log.String("bank_name", bankName),
		log.String("card_type", cardTypeName),
		log.String("action", "send_bankcard_sms_start"),
	).Info("开始发送银行卡绑定短信验证码")

	// 获取用户信息
	userInfo, err := s.getUserInfo(ctx, userID)
	if err != nil {
		return nil, err
	}

	name, ok := userInfo["name"].(string)
	if !ok || name == "" {
		return nil, fmt.Errorf("用户姓名不能为空")
	}

	idCard, ok := userInfo["idCard"].(string)
	if !ok || idCard == "" {
		return nil, fmt.Errorf("用户身份证号不能为空")
	}

	// 生成唯一订单号（时间戳+用户ID）
	orderNo := fmt.Sprintf("%d%d", time.Now().Unix(), userID)

	// 调用商盟支付签约接口
	signRequest := sumpay.NewSignRequest(orderNo, orderNo, banknumber, mobile, name, idCard)
	signResp, err := s.sumpayService.Sign(signRequest)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.Uint("user_id", userID),
			log.String("order_no", orderNo),
			log.String("action", "sumpay_sign_failed"),
		).Error("银行卡签约失败")
		return nil, fmt.Errorf("银行卡签约失败: %v", err)
	}

	s.logger.WithFields(
		log.Uint("user_id", userID),
		log.String("order_no", orderNo),
		log.String("resp_code", signResp.RespCode),
		log.String("action", "sumpay_sign_success"),
	).Info("银行卡签约请求成功")

	// 保存银行卡信息到数据库
	cardStatus := model.CardStatusPending // 待验证
	if signResp.RespCode == "000000" {
		cardStatus = model.CardStatusPending // 待验证
	} else {
		cardStatus = model.CardStatusFailed // 绑定失败
	}

	bankCard := &model.BusinessBankCards{
		UserID:            int(userID),
		ThirdPartyOrderNo: orderNo,
		BankCardNo:        banknumber,
		BankPhone:         mobile,
		CardStatus:        cardStatus,
		RespCode:          signResp.RespCode,
		RespMsg:           signResp.RespMsg,
		BankCode:          bankCode,
		BankName:          bankName,
		CardType:          cardType,
		CardTypeName:      cardTypeName,
	}
	// 检查是否已经绑定过该银行卡
	existCard, err := s.bankCardModel.GetBankCardByUserIDAndBankCardNo(int(userID), banknumber)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.Uint("user_id", userID),
			log.String("bank_card_no", s.maskBankCardNo(banknumber)),
			log.String("action", "check_existing_bankcard_failed"),
		).Error("检查已存在银行卡失败")
		return nil, err
	}

	if existCard != nil {
		// 如果银行卡已存在
		if existCard.CardStatus == model.CardStatusBound {
			// 如果是已绑定状态，直接返回成功
			s.logger.WithFields(
				log.Uint("user_id", userID),
				log.String("bank_card_no", s.maskBankCardNo(banknumber)),
				log.String("action", "bankcard_already_bound"),
			).Info("银行卡已绑定，无需重复绑定")
			return &BankCardSmsResponse{
				ThirdPartyOrderNo: existCard.ThirdPartyOrderNo,
				NeedSms:           false, // 已绑定，不需要短信验证
			}, nil
		} else {
			// 其他状态（待验证、绑定失败），更新银行卡信息
			updateData := map[string]interface{}{
				"third_party_order_no": orderNo,
				"bank_phone":           mobile,
				"card_status":          cardStatus,
				"resp_code":            signResp.RespCode,
				"resp_msg":             signResp.RespMsg,
				"bank_code":            bankCode,
				"bank_name":            bankName,
				"card_type":            cardType,
				"card_type_name":       cardTypeName,
			}
			err = s.bankCardModel.UpdateBankCardInfo(existCard.ID, updateData)
			if err != nil {
				s.logger.WithError(err).WithFields(
					log.Uint("user_id", userID),
					log.Uint("card_id", uint(existCard.ID)),
					log.String("action", "update_bankcard_failed"),
				).Error("更新银行卡信息失败")
				return nil, err
			}
			s.logger.WithFields(
				log.Uint("user_id", userID),
				log.Uint("card_id", uint(existCard.ID)),
				log.String("action", "update_bankcard_success"),
			).Info("更新银行卡信息成功")
		}
	} else {
		// 银行卡不存在，创建新记录
		err = s.bankCardModel.CreateBankCard(bankCard)
		if err != nil {
			s.logger.WithError(err).WithFields(
				log.Uint("user_id", userID),
				log.String("bank_card_no", s.maskBankCardNo(banknumber)),
				log.String("action", "create_bankcard_failed"),
			).Error("创建银行卡记录失败")
			return nil, err
		}
		s.logger.WithFields(
			log.Uint("user_id", userID),
			log.String("bank_card_no", s.maskBankCardNo(banknumber)),
			log.String("action", "create_bankcard_success"),
		).Info("创建银行卡记录成功")
	}

	// 处理响应
	if signResp.RespCode == "000000" {
		s.logger.WithFields(
			log.Uint("user_id", userID),
			log.String("order_no", orderNo),
			log.String("action", "send_bankcard_sms_success"),
		).Info("发送银行卡绑定短信验证码成功")
		return &BankCardSmsResponse{
			ThirdPartyOrderNo: orderNo,
			NeedSms:           true,
		}, nil
	} else {
		// 根据错误码返回具体错误信息
		errorMsg := sumpay.GetErrorMessage(signResp.RespCode)
		if signResp.RespMsg != "" {
			errorMsg = signResp.RespMsg
		}

		s.logger.WithFields(
			log.Uint("user_id", userID),
			log.String("order_no", orderNo),
			log.String("resp_code", signResp.RespCode),
			log.String("error_msg", errorMsg),
			log.String("action", "send_bankcard_sms_failed"),
		).Error("发送银行卡绑定短信验证码失败")
		return nil, fmt.Errorf("%s", errorMsg)
	}
}

// BindBankCard 绑定银行卡
func (s *BankCardService) BindBankCard(ctx context.Context, userID uint, data map[string]interface{}) (*BankCardBindResponse, error) {
	// 检查是否为测试环境
	if s.sumpayService == nil {
		return nil, fmt.Errorf("测试环境下无法调用第三方服务")
	}

	banknumber := data["banknumber"].(string)
	code := data["code"].(string)
	thirdPartyOrderNo := data["thirdPartyOrderNo"].(string)

	s.logger.WithFields(
		log.Uint("user_id", userID),
		log.String("order_no", thirdPartyOrderNo),
		log.String("bank_card_no", s.maskBankCardNo(banknumber)),
		log.String("action", "bind_bankcard_start"),
	).Info("开始绑定银行卡")

	// 查询银行卡记录
	bankCard, err := s.bankCardModel.GetBankCardByUserIDAndOrderNo(int(userID), thirdPartyOrderNo)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.Uint("user_id", userID),
			log.String("order_no", thirdPartyOrderNo),
			log.String("action", "get_bankcard_record_failed"),
		).Error("查询银行卡记录失败")
		return nil, fmt.Errorf("银行卡记录不存在: %v", err)
	}

	// 检查银行卡状态
	if bankCard.CardStatus == model.CardStatusBound {
		return nil, fmt.Errorf("银行卡已绑定，无需重复操作")
	}
	if bankCard.CardStatus == model.CardStatusFailed {
		return nil, fmt.Errorf("银行卡绑定失败，请重新发起绑卡")
	}

	// 验证短信验证码
	validSMSRequest := sumpay.NewValidSMSRequest(thirdPartyOrderNo, code)
	validSMSResp, err := s.sumpayService.ValidSMS(validSMSRequest)
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.Uint("user_id", userID),
			log.String("order_no", thirdPartyOrderNo),
			log.String("action", "sms_validation_failed"),
		).Error("短信验证失败")
		return nil, fmt.Errorf("短信验证失败: %v", err)
	}

	// 更新银行卡状态
	newCardStatus := model.CardStatusFailed // 默认绑定失败
	if validSMSResp.RespCode == "000000" && validSMSResp.GetBindCardId() != "" {
		newCardStatus = model.CardStatusBound // 绑定成功
	}

	// 更新数据库记录
	err = s.bankCardModel.UpdateBankCardStatus(bankCard.ID, newCardStatus, validSMSResp.RespCode, validSMSResp.RespMsg, validSMSResp.GetBindCardId())
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.Uint("user_id", userID),
			log.Uint("card_id", uint(bankCard.ID)),
			log.String("action", "update_bankcard_status_failed"),
		).Error("更新银行卡状态失败")
		return nil, fmt.Errorf("更新银行卡状态失败: %v", err)
	}

	// 处理响应
	if validSMSResp.RespCode == "000000" {
		s.logger.WithFields(
			log.Uint("user_id", userID),
			log.String("order_no", thirdPartyOrderNo),
			log.String("bind_card_id", validSMSResp.GetBindCardId()),
			log.String("action", "bind_bankcard_success"),
		).Info("银行卡绑定成功")
		return &BankCardBindResponse{
			ThirdPartyOrderNo: thirdPartyOrderNo,
			BindCardId:        validSMSResp.GetBindCardId(),
			BankCardNo:        banknumber,
		}, nil
	} else {
		// 根据错误码返回具体错误信息
		errorMsg := sumpay.GetErrorMessage(validSMSResp.RespCode)
		if validSMSResp.RespMsg != "" {
			errorMsg = validSMSResp.RespMsg
		}
		s.logger.WithFields(
			log.Uint("user_id", userID),
			log.String("order_no", thirdPartyOrderNo),
			log.String("resp_code", validSMSResp.RespCode),
			log.String("error_msg", errorMsg),
			log.String("action", "bind_bankcard_failed"),
		).Error("银行卡绑定失败")
		return nil, fmt.Errorf("%s", errorMsg)
	}
}

// GetBankCardList 获取用户银行卡列表
func (s *BankCardService) GetBankCardList(ctx context.Context, userID uint) (*BankCardListResponse, error) {
	s.logger.WithFields(
		log.Uint("user_id", userID),
		log.String("action", "get_bankcard_list_start"),
	).Info("开始获取用户银行卡列表")

	// 查询用户的银行卡列表
	bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
	if err != nil {
		s.logger.WithError(err).WithFields(
			log.Uint("user_id", userID),
			log.String("action", "get_bankcard_list_failed"),
		).Error("查询银行卡列表失败")
		return nil, fmt.Errorf("查询银行卡列表失败: %v", err)
	}

	// 处理银行卡数据，隐藏敏感信息
	var cardList []map[string]interface{}
	for _, card := range bankCards {
		cardInfo := map[string]interface{}{
			"id":                   card.ID,
			"third_party_order_no": card.ThirdPartyOrderNo,
			"bank_card_no":         s.maskBankCardNo(card.BankCardNo),
			"bank_phone":           s.maskPhoneNumber(card.BankPhone),
			"bind_card_id":         card.BindCardId,
			"card_status":          card.CardStatus,
			"bank_code":            card.BankCode,
			"bank_name":            card.BankName,
			"card_type":            card.CardType,
			"card_type_name":       card.CardTypeName,
			"created_at":           card.CreatedAt.Format("2006-01-02 15:04:05"),
			"updated_at":           card.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
		cardList = append(cardList, cardInfo)
	}

	s.logger.WithFields(
		log.Uint("user_id", userID),
		log.Int("count", len(cardList)),
		log.String("action", "get_bankcard_list_success"),
	).Info("获取用户银行卡列表成功")

	return &BankCardListResponse{
		Total: len(cardList),
		List:  cardList,
	}, nil
}

// getUserInfo 获取用户信息
func (s *BankCardService) getUserInfo(ctx context.Context, userID uint) (map[string]interface{}, error) {
	// 直接查询数据库获取用户信息
	userInfo, err := s.bankCardModel.GetUserInfo(int(userID))
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}
	return userInfo, nil
}

// maskBankCardNo 掩码银行卡号，只显示前4位和后4位
func (s *BankCardService) maskBankCardNo(cardNo string) string {
	if len(cardNo) <= 8 {
		return cardNo
	}
	return cardNo[:4] + "****" + cardNo[len(cardNo)-4:]
}

// maskPhoneNumber 掩码手机号，只显示前3位和后4位
func (s *BankCardService) maskPhoneNumber(phone string) string {
	if len(phone) != 11 {
		return phone
	}
	return phone[:3] + "****" + phone[7:]
}

// getUserIDFromData 从数据中获取用户ID
func (s *BankCardService) getUserIDFromData(data map[string]interface{}) (uint, error) {
	if userIDVal, ok := data["user_id"]; ok {
		switch v := userIDVal.(type) {
		case string:
			if userID, err := strconv.ParseUint(v, 10, 32); err == nil {
				return uint(userID), nil
			}
		case float64:
			return uint(v), nil
		case int:
			return uint(v), nil
		case uint:
			return v, nil
		}
	}
	return 0, fmt.Errorf("用户ID不能为空或格式错误")
}
