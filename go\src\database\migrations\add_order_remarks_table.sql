-- 创建订单备注表
-- 用于存储订单的详细备注信息，替代原有的单一备注字段

-- 创建订单备注表
CREATE TABLE IF NOT EXISTS `business_order_remarks` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '备注ID，主键',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID，关联business_loan_orders表的id字段',
  `content` text NOT NULL COMMENT '备注内容，支持长文本',
  `user_id` bigint(20) NOT NULL COMMENT '备注人用户ID，关联business_account表的id字段',
  `create_time` bigint(20) NOT NULL COMMENT '备注创建时间，Unix时间戳',
  `update_time` bigint(20) NOT NULL COMMENT '备注更新时间，Unix时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`) COMMENT '订单ID索引，用于快速查询某订单的所有备注',
  KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引，用于查询某用户的所有备注',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引，用于按时间排序',
  KEY `idx_order_create` (`order_id`, `create_time`) COMMENT '复合索引，优化按订单ID和时间查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单备注表，存储订单的详细备注信息';

-- 为现有订单迁移备注数据（如果review_remarks字段有数据）
-- 注意：这个脚本需要根据实际情况调整，可能需要手动执行
/*
INSERT INTO business_order_remarks (order_id, content, user_id, create_time, update_time)
SELECT 
    id as order_id,
    review_remarks as content,
    COALESCE(reviewer_id, 1) as user_id,  -- 如果没有reviewer_id，默认使用用户ID 1
    COALESCE(update_time, create_time, UNIX_TIMESTAMP()) as create_time,
    COALESCE(update_time, create_time, UNIX_TIMESTAMP()) as update_time
FROM business_loan_orders 
WHERE review_remarks IS NOT NULL 
  AND review_remarks != '' 
  AND review_remarks != '0';
*/

-- 备份原有的review_remarks字段（可选）
-- ALTER TABLE business_loan_orders ADD COLUMN review_remarks_backup text COMMENT '原备注字段备份';
-- UPDATE business_loan_orders SET review_remarks_backup = review_remarks WHERE review_remarks IS NOT NULL;

-- 修改business_loan_orders表的review_remarks字段类型
-- 将其改为bigint类型，用于存储备注表的ID
-- 注意：执行前请确保已经迁移了现有数据
/*
ALTER TABLE business_loan_orders 
MODIFY COLUMN review_remarks bigint(20) DEFAULT NULL COMMENT '关联备注表ID，指向business_order_remarks表的主键';
*/

-- 添加外键约束（可选，根据实际需求决定是否添加）
/*
ALTER TABLE business_order_remarks 
ADD CONSTRAINT fk_order_remarks_order_id 
FOREIGN KEY (order_id) REFERENCES business_loan_orders(id) ON DELETE CASCADE;

ALTER TABLE business_order_remarks 
ADD CONSTRAINT fk_order_remarks_user_id 
FOREIGN KEY (user_id) REFERENCES business_account(id) ON DELETE SET NULL;
*/
