---
alwaysApply: true
---
# 项目结构指南

## 项目概览

该项目是一个多应用前端系统，包含以下三个主要子项目：

- [admin](mdc:admin) - 管理后台系统
- [business](mdc:business) - 业务系统
- [uniapp](mdc:uniapp) - 移动端应用

## 技术栈

该项目基于以下技术栈：

- Vue.js - 前端框架
- Vite - 构建工具
- Arco Design - UI组件库 (admin和business子项目)
- uni-app - 移动端跨平台框架 (uniapp子项目)

## 项目通用结构

每个子项目(admin和business)具有类似的目录结构：

```
子项目/
  ├── src/               # 源代码目录
  │   ├── api/           # API接口定义
  │   ├── assets/        # 静态资源
  │   ├── components/    # 公共组件
  │   ├── config/        # 配置文件
  │   ├── directive/     # Vue指令
  │   ├── hooks/         # 自定义hooks
  │   ├── layout/        # 布局组件
  │   ├── locale/        # 国际化资源
  │   ├── router/        # 路由配置
  │   ├── store/         # 状态管理
  │   ├── types/         # 类型定义
  │   ├── utils/         # 工具函数
  │   └── views/         # 页面视图
  ├── public/            # 公共资源
  ├── config/            # 构建配置
  └── types/             # 全局类型定义
```

## 主要配置文件

- [business/public/config.js](mdc:business/public/config.js) - 业务系统配置
- [admin/public/config.js](mdc:admin/public/config.js) - 管理系统配置

## 关键API目录

- [business/src/api](mdc:business/src/api) - 业务系统API
- [admin/src/api](mdc:admin/src/api) - 管理系统API
- [uniapp/api](mdc:uniapp/api) - 移动端API

## 工具函数

- [business/src/utils](mdc:business/src/utils) - 业务系统工具函数
- [admin/src/utils](mdc:admin/src/utils) - 管理系统工具函数
- [uniapp/utils](mdc:uniapp/utils) - 移动端工具函数

