import request from '@/utils/request'

export default {

	postBindBankCard: (params) => request({
		url: '/user/postBindBankCard',
		method: 'POST',
		data: params,
		custom: {
			loadingMsg: '银行卡绑定中...',
			showSuccess: true,
			auth: true
		}
	}),

	postBindBankCardSms: (params) => request({
		url: '/user/postBindBankCardSms',
		method: 'POST',
		data: params,
		custom: {
			loadingMsg: '验证码发送中...',
			auth: true
		}
	})
}