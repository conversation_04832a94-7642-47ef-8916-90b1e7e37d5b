<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=0.3,minimum-scale=0.3,maximum-scale=1,viewport-fit=cover">
    <link rel="shortcut icon" type="image/x-icon" href="https://doc.goflys.cn/logo.png">
    <title>安装GoFly快速开发</title>
	<style >
		 body {
            background: #E8F7FF;
            margin: 0;
            padding: 0;
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        body, input, button {
            font-family: 'Source Sans Pro', 'Helvetica Neue', Helvetica, 'Microsoft Yahei', Arial, sans-serif;
            font-size: 14px;
            color: #4e5969;
        }
		a {
            color: #4e73df;
            text-decoration: none;
        }

        a:hover {
			 font-weight: 600;
        }
		.install{
			width: 100%;
		}
		.install .formbox{
			max-width: 600px;
            margin: 0 auto;
			margin-top: 68px;
		}
		.install .formbox .logo{
			text-align: center;
		}
		.install .formbox .title{
			text-align: center;
		}
		.install .formbox .content{
		   margin-top: 50px;
		}
		.content .tig{
            padding: 15px 20px;
            border-radius: 4px;
            margin-bottom: 20px;
			background: #FF7D00;
            font-size: 16px;
            color: #ffffff;
		}

	</style>
  </head>
  <body>
	 <div class="install">
        <div class="formbox">
			<div class="logo">
				<img src="https://doc.goflys.cn/logo.png">
			</div>
			<div class="title">
				<h1>GoFly快速开发已安装成功</h1>
			</div>
			<div class="content">
				<div class="tig">如果需要重新安装请删除“resource/staticfile/template/”目录下的“install.lock”文件，重新刷新页面，即可重新安装</div>
			</div>
		</div>
	 </div>
  </body>
</html>

