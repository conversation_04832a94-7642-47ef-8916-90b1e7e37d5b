package engine

import (
	"context"
	"fincore/app/scheduler/config"
	"fincore/app/scheduler/tasks"
	"fincore/utils/log"
	"fmt"
	"runtime/debug"
	"sync"
	"time"

	"go.uber.org/zap"
)

// TaskExecutor 任务执行器
type TaskExecutor struct {
	config       *config.SchedulerConfig  // 配置
	logger       *log.Logger              // 日志器
	activeJobs   map[string]*JobExecution // 活跃的任务执行
	mutex        sync.RWMutex             // 读写锁
	jobCounter   int64                    // 任务执行计数器
	counterMutex sync.Mutex               // 计数器锁
}

// JobExecution 任务执行信息
type JobExecution struct {
	JobID      int64               `json:"job_id"`
	TaskName   string              `json:"task_name"`
	StartTime  time.Time           `json:"start_time"`
	Context    context.Context     `json:"-"`
	Cancel     context.CancelFunc  `json:"-"`
	Task       tasks.TaskInterface `json:"-"`
	RetryCount int                 `json:"retry_count"`
	MaxRetries int                 `json:"max_retries"`
	LastError  error               `json:"-"`
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	JobID      int64         `json:"job_id"`
	TaskName   string        `json:"task_name"`
	Success    bool          `json:"success"`
	StartTime  time.Time     `json:"start_time"`
	EndTime    time.Time     `json:"end_time"`
	Duration   time.Duration `json:"duration"`
	RetryCount int           `json:"retry_count"`
	Error      string        `json:"error,omitempty"`
	PanicInfo  string        `json:"panic_info,omitempty"`
}

// NewTaskExecutor 创建任务执行器
func NewTaskExecutor(config *config.SchedulerConfig, logger *log.Logger) *TaskExecutor {
	return &TaskExecutor{
		config:     config,
		logger:     logger,
		activeJobs: make(map[string]*JobExecution),
	}
}

// ExecuteTask 执行任务
func (e *TaskExecutor) ExecuteTask(task tasks.TaskInterface) error {
	// 生成任务执行ID
	jobID := e.generateJobID()
	taskName := task.GetName()

	// 创建执行上下文
	timeout := task.GetTimeout()
	if timeout <= 0 {
		timeout = e.config.Scheduler.DefaultTimeout
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 创建任务执行信息
	execution := &JobExecution{
		JobID:      jobID,
		TaskName:   taskName,
		StartTime:  time.Now(),
		Context:    ctx,
		Cancel:     cancel,
		Task:       task,
		MaxRetries: task.GetRetryCount(),
	}

	// 注册活跃任务
	e.registerActiveJob(taskName, execution)
	defer e.unregisterActiveJob(taskName)

	// 执行任务（带重试）
	result := e.executeWithRetry(execution)

	// 记录执行结果
	e.logExecutionResult(result)

	if !result.Success {
		return fmt.Errorf("任务执行失败: %s", result.Error)
	}

	return nil
}

// executeWithRetry 带重试的执行任务
func (e *TaskExecutor) executeWithRetry(execution *JobExecution) *ExecutionResult {

	var (
		lastError error
		panicInfo string
	)

retryLoop:
	for attempt := 0; attempt <= execution.MaxRetries; attempt++ {
		execution.RetryCount = attempt

		// 检查上下文是否已取消
		select {
		case <-execution.Context.Done():
			lastError = execution.Context.Err()
			break retryLoop
		default:
		}

		// 如果不是第一次尝试，等待重试间隔
		if attempt > 0 {
			retryInterval := execution.Task.GetRetryInterval()
			if retryInterval > 0 {
				e.logger.Info("等待重试",
					zap.String("task_name", execution.TaskName),
					zap.Int("attempt", attempt),
					zap.Duration("retry_interval", retryInterval),
				)

				select {
				case <-time.After(retryInterval):
				case <-execution.Context.Done():
					lastError = execution.Context.Err()
					break retryLoop
				}
			}
		}

		// 执行任务
		panic, err := e.executeSingleAttempt(execution)
		if panic != "" {
			panicInfo = panic
			lastError = fmt.Errorf("任务执行发生panic: %s", panic)
			break // panic不重试
		}

		if err == nil {
			// 执行成功
			return &ExecutionResult{
				JobID:      execution.JobID,
				TaskName:   execution.TaskName,
				Success:    true,
				StartTime:  execution.StartTime,
				EndTime:    time.Now(),
				Duration:   time.Since(execution.StartTime),
				RetryCount: attempt,
			}
		}

		lastError = err

		e.logger.Warn("任务执行失败，准备重试",
			zap.String("task_name", execution.TaskName),
			zap.Int("attempt", attempt),
			zap.Int("max_retries", execution.MaxRetries),
			zap.Error(err),
		)
	}

	// 所有重试都失败了
	errorMsg := ""
	if lastError != nil {
		errorMsg = lastError.Error()
	}

	return &ExecutionResult{
		JobID:      execution.JobID,
		TaskName:   execution.TaskName,
		Success:    false,
		StartTime:  execution.StartTime,
		EndTime:    time.Now(),
		Duration:   time.Since(execution.StartTime),
		RetryCount: execution.RetryCount,
		Error:      errorMsg,
		PanicInfo:  panicInfo,
	}
}

// executeSingleAttempt 执行单次尝试
func (e *TaskExecutor) executeSingleAttempt(execution *JobExecution) (string, error) {
	var err error
	var panicInfo string

	// 捕获panic
	defer func() {
		if r := recover(); r != nil {
			panicInfo = fmt.Sprintf("%v\n%s", r, debug.Stack())
			e.logger.Error("任务执行发生panic",
				zap.String("task_name", execution.TaskName),
				zap.Any("panic", r),
				zap.String("stack", string(debug.Stack())),
			)
		}
	}()

	// 执行生命周期钩子：OnStart
	if lifecycle, ok := execution.Task.(tasks.TaskLifecycleInterface); ok {
		if err = lifecycle.OnStart(execution.Context); err != nil {
			e.logger.Error("任务OnStart钩子执行失败",
				zap.String("task_name", execution.TaskName),
				zap.Error(err),
			)
			return "", err
		}
	}

	// 执行任务主逻辑
	err = execution.Task.Execute(execution.Context)

	// 执行生命周期钩子：OnComplete（无论成功失败都执行）
	if lifecycle, ok := execution.Task.(tasks.TaskLifecycleInterface); ok {
		if completeErr := lifecycle.OnComplete(execution.Context); completeErr != nil {
			e.logger.Error("任务OnComplete钩子执行失败",
				zap.String("task_name", execution.TaskName),
				zap.Error(completeErr),
			)
		}
	}

	// 根据执行结果调用相应的钩子
	if lifecycle, ok := execution.Task.(tasks.TaskLifecycleInterface); ok {
		if err != nil {
			// 执行失败钩子
			if errorErr := lifecycle.OnError(execution.Context, err); errorErr != nil {
				e.logger.Error("任务OnError钩子执行失败",
					zap.String("task_name", execution.TaskName),
					zap.Error(errorErr),
				)
			}
		} else {
			// 执行成功钩子
			if successErr := lifecycle.OnSuccess(execution.Context); successErr != nil {
				e.logger.Error("任务OnSuccess钩子执行失败",
					zap.String("task_name", execution.TaskName),
					zap.Error(successErr),
				)
			}
		}
	}

	return panicInfo, err
}

// generateJobID 生成任务执行ID
func (e *TaskExecutor) generateJobID() int64 {
	e.counterMutex.Lock()
	defer e.counterMutex.Unlock()
	e.jobCounter++
	return e.jobCounter
}

// registerActiveJob 注册活跃任务
func (e *TaskExecutor) registerActiveJob(taskName string, execution *JobExecution) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	e.activeJobs[taskName] = execution
}

// unregisterActiveJob 注销活跃任务
func (e *TaskExecutor) unregisterActiveJob(taskName string) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	delete(e.activeJobs, taskName)
}

// GetActiveJobs 获取活跃任务
func (e *TaskExecutor) GetActiveJobs() map[string]*JobExecution {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	// 创建副本
	result := make(map[string]*JobExecution)
	for name, execution := range e.activeJobs {
		result[name] = execution
	}

	return result
}

// IsTaskRunning 检查任务是否正在运行
func (e *TaskExecutor) IsTaskRunning(taskName string) bool {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	_, exists := e.activeJobs[taskName]
	return exists
}

// CancelTask 取消任务执行
func (e *TaskExecutor) CancelTask(taskName string) error {
	e.mutex.RLock()
	execution, exists := e.activeJobs[taskName]
	e.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("任务 %s 未在运行", taskName)
	}

	// 取消任务
	execution.Cancel()

	e.logger.Info("任务已被取消",
		zap.String("task_name", taskName),
		zap.Int64("job_id", execution.JobID),
	)

	return nil
}

// ForceKillTask 强制结束任务
func (e *TaskExecutor) ForceKillTask(taskName string) error {
	// 先尝试正常取消
	if err := e.CancelTask(taskName); err != nil {
		return err
	}

	// 等待强制结束超时时间
	forceKillTimeout := e.config.Scheduler.ForceKillTimeout
	if forceKillTimeout <= 0 {
		forceKillTimeout = 30 * time.Second
	}

	// 等待任务结束
	deadline := time.Now().Add(forceKillTimeout)
	for time.Now().Before(deadline) {
		if !e.IsTaskRunning(taskName) {
			return nil
		}
		time.Sleep(100 * time.Millisecond)
	}

	e.logger.Warn("任务强制结束超时",
		zap.String("task_name", taskName),
		zap.Duration("timeout", forceKillTimeout),
	)

	return fmt.Errorf("任务 %s 强制结束超时", taskName)
}

// logExecutionResult 记录执行结果
func (e *TaskExecutor) logExecutionResult(result *ExecutionResult) {
	fields := []zap.Field{
		zap.String("task_name", result.TaskName),
		zap.Int64("job_id", result.JobID),
		zap.Duration("duration", result.Duration),
		zap.Int("retry_count", result.RetryCount),
		zap.Bool("success", result.Success),
	}

	if result.Success {
		e.logger.Info("任务执行成功", fields...)
	} else {
		if result.Error != "" {
			fields = append(fields, zap.String("error", result.Error))
		}
		if result.PanicInfo != "" {
			fields = append(fields, zap.String("panic_info", result.PanicInfo))
		}
		e.logger.Error("任务执行失败", fields...)
	}
}

// GetExecutorStats 获取执行器统计信息
func (e *TaskExecutor) GetExecutorStats() map[string]interface{} {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	return map[string]interface{}{
		"active_jobs_count": len(e.activeJobs),
		"total_jobs_count":  e.jobCounter,
		"active_jobs":       e.getActiveJobsInfo(),
	}
}

// getActiveJobsInfo 获取活跃任务信息
func (e *TaskExecutor) getActiveJobsInfo() map[string]interface{} {
	info := make(map[string]interface{})

	for taskName, execution := range e.activeJobs {
		info[taskName] = map[string]interface{}{
			"job_id":      execution.JobID,
			"start_time":  execution.StartTime,
			"duration":    time.Since(execution.StartTime),
			"retry_count": execution.RetryCount,
			"max_retries": execution.MaxRetries,
		}
	}

	return info
}
