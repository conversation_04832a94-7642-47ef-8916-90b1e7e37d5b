// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameCollectionRecord = "collection_record"

// CollectionRecord mapped from table <collection_record>
type CollectionRecord struct {
	ID        uint32    `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	AdminID   int32     `gorm:"column:admin_id;type:int;not null;index:idx_admin_id,priority:1;comment:管理员id" json:"admin_id"` // 管理员id
	BillID    int32     `gorm:"column:bill_id;type:int;not null;index:idx_bill_id,priority:1;comment:账单id" json:"bill_id"`     // 账单id
	Result    string    `gorm:"column:result;type:varchar(60);not null;comment:催收结果" json:"result"`                            // 催收结果
	Note      string    `gorm:"column:note;type:varchar(512);not null;comment:小记" json:"note"`                                 // 小记
	CreatedAt time.Time `gorm:"column:created_at;type:datetime;not null;comment:创建时间" json:"created_at"`                       // 创建时间
}

// TableName CollectionRecord's table name
func (*CollectionRecord) TableName() string {
	return TableNameCollectionRecord
}
