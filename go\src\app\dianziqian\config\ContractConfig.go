package config

// 创建待签署文件
type CreateFileConfig struct {
	ContractNo   string      `json:"contractNo,omitempty"`
	ContractName string      `json:"contractName,omitempty"`
	ValidityTime int         `json:"validityTime,omitempty"`
	SignOrder    int         `json:"signOrder,omitempty"`
	RedirectUrl  string      `json:"redirectUrl,omitempty"` // 重定向完成签署后的URL
	Templates    []Templates `json:"templates,omitempty"`
}

// type Templates struct {
// 	TemplateNo string            `json:"templateNo,omitempty"`
// 	FillData   map[string]string `json:"fillData,omitempty"`
// }

//表格Template，用于贷款告知书
type Templates struct {
	TemplateNo string            `json:"templateNo,omitempty"`
	FillData   map[string]string `json:"fillData,omitempty"`
	TableDatas []TableData       `json:"tableDatas,omitempty"`
}

type TableData struct {
	Keyword   string     `json:"keyword"`
	RowValues []RowValue `json:"rowValues,omitempty"` // 这里应该是切片类型
}

type RowValue struct {
	InsertRow bool     `json:"insertRow,omitempty"`
	ColValues []string `json:"colValues,omitempty"`
}

// 添加签署方
type AddSignerConfig struct {
	ContractNo       string         `json:"contractNo,omitempty"`
	Account          string         `json:"account,omitempty"`
	SignType         int            `json:"signType,omitempty"`
	ValidateType     int            `json:"validateType,omitempty"`
	SignStrategyList []SignStrategy `json:"signStrategyList,omitempty"`
}

type SignStrategy struct {
	AttachNo     int     `json:"attachNo,omitempty"`
	LocationMode int     `json:"locationMode,omitempty"`
	SignKey      string  `json:"signKey,omitempty"`
	SignPage     int     `json:"signPage,omitempty"`
	SignX        float64 `json:"signX,omitempty"`
	SignY        float64 `json:"signY,omitempty"`
	SignType     int     `json:"signType,omitempty"` // 1=手写签名，2=印章签名
}

// 下载合同
type DownloadFileConfig struct {
	ContractNo string `json:"contractNo,omitempty"`
}
