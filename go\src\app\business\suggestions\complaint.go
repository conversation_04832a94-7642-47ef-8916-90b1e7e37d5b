package suggestions

import (
	"fincore/global"
	"fincore/model"
	"fincore/utils/gf"
	"fincore/utils/log"
	"fincore/utils/results"
	"fmt"
	"reflect"
	"time"

	"github.com/gin-gonic/gin"
)

// 用于自动注册路由
type Complaint struct{}

// 初始化生成路由
func init() {
	fpath := Complaint{}
	gf.Register(&fpath, reflect.TypeOf(fpath).PkgPath())
}

type ComplaintListReq struct {
	PageSize         int    `json:"limit" form:"limit" query:"limit"`
	PageNumber       int    `json:"page" form:"page" query:"page"`
	ComplaintContent string `json:"content" form:"content" query:"content"`
}

// 获取投诉列表
func (api *Complaint) GetComplaintContentList(c *gin.Context) {
	// 获取post传过来的data
	var req ComplaintListReq
	// 这行代码会自动从 query、form、json 里找参数
	if err := c.Should<PERSON>ind(&req); err != nil {
		results.Failed(c, "参数错误", nil)
		return
	}

	// 默认值处理
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageNumber <= 0 {
		req.PageNumber = 1
	}

	// 构建查询
	query := model.DB().Table("user_complaint")

	// 动态添加条件
	if req.ComplaintContent != "" {
		query = query.Where("content", "like", "%"+req.ComplaintContent+"%")
	}

	// 获取总数
	var total int64
	total, _ = query.Count("*")
	log.Info("total: %d", total)

	// 分页查询
	offset := (req.PageNumber - 1) * req.PageSize
	list, err := query.Fields("id, uid, mobile, name, idCard, photoUrls, content, complaintTime, remark, feedback").
		Limit(req.PageSize).Offset(offset).Order("complaintTime desc").Get()

	if err != nil {
		log.Error(fmt.Sprintf("查询投诉列表失败：%s", err))
		results.Failed(c, "查询投诉列表失败", nil)
		return
	} else {
		log.Info("查询投诉列表成功")
	}

	// complaintTime 格式化处理，并组装图片完整URL
	for _, item := range list {
		if t, ok := item["complaintTime"].(time.Time); ok {
			item["complaintTime"] = t.Format("2006-01-02 15:04:05")
		}
		// 组装图片完整URL
		if url, ok := item["photoUrls"].(string); ok && url != "" {
			host := global.App.Config.App.Hostname
			port := global.App.Config.App.Port
			// 如果url本身不是http/https开头，才拼接
			if len(url) < 7 || (url[:7] != "http://" && (len(url) < 8 || url[:8] != "https://")) {
				item["photoUrls"] = fmt.Sprintf("http://%s:%s/%s", host, port, url)
			}
		}
	}

	// 调试信息：打印整个 list
	log.Info("查询结果 list: %+v", list)
	log.Info("list 长度: %d", len(list))
	log.Info("list 类型: %T", list)

	// 返回结果
	results.Success(c, "获取投诉列表成功", map[string]interface{}{
		"page":     req.PageNumber,
		"pageSize": req.PageSize,
		"total":    total,
		"items":    list,
	}, nil)
}

type HandleComplaintReq struct {
	ID       int    `json:"id" form:"id" query:"id"`
	Remark   string `json:"remark" form:"remark" query:"remark"`
	Feedback string `json:"feedback" form:"feedback" query:"feedback"`
}

// 处理投诉
func (api *Complaint) HandleComplaint(c *gin.Context) {
	var req HandleComplaintReq
	if err := c.ShouldBind(&req); err != nil {
		results.Failed(c, "参数错误", nil)
		return
	}

	if req.ID <= 0 {
		results.Failed(c, "请传入有效的投诉ID", nil)
		return
	}

	// 检查投诉是否存在
	existingComplaint, err := model.DB().Table("user_complaint").
		Fields("id, remark, feedback").
		Where("id", req.ID).
		First()

	if err != nil {
		results.Failed(c, "查询投诉记录失败", err)
		return
	}

	if existingComplaint == nil {
		results.Failed(c, "投诉记录不存在", nil)
		return
	}

	// 构建更新数据
	updateData := map[string]interface{}{
		"updatetime": time.Now().Format("2006-01-02 15:04:05"),
	}

	if req.Remark != "" {
		updateData["remark"] = req.Remark
	}
	if req.Feedback != "" {
		updateData["feedback"] = req.Feedback
	}

	// 更新投诉记录
	res, err := model.DB().Table("user_complaint").
		Data(updateData).
		Where("id", req.ID).
		Update()

	if err != nil {
		results.Failed(c, "处理投诉失败", err)
		return
	}

	results.Success(c, "处理投诉成功", map[string]interface{}{
		"affectedRows": res,
	}, nil)
}
