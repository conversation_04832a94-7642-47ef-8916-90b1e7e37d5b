package auth

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
)

// PasswordAuth 账号密码认证器（短信服务）
type PasswordAuth struct {
	username string
	password string
}

// NewPasswordAuth 创建账号密码认证器
func NewPasswordAuth(username, password string) *PasswordAuth {
	return &PasswordAuth{
		username: username,
		password: password,
	}
}

// GetAuthType 获取认证类型
func (p *PasswordAuth) GetAuthType() AuthType {
	return AuthTypePassword
}

// Authenticate 执行认证，在请求参数中添加用户名密码
func (p *PasswordAuth) Authenticate(ctx context.Context, req *http.Request, bodyData []byte) error {
	// 解析请求体，添加认证参数
	var requestData map[string]interface{}
	if len(bodyData) > 0 {
		if err := json.Unmarshal(bodyData, &requestData); err != nil {
			return fmt.Errorf("解析请求数据失败: %v", err)
		}
	} else {
		requestData = make(map[string]interface{})
	}

	// 添加认证信息
	requestData["username"] = p.username
	requestData["password"] = p.password

	// 重新编码请求体
	newData, err := json.Marshal(requestData)
	if err != nil {
		return fmt.Errorf("编码请求数据失败: %v", err)
	}

	// 更新请求体
	req.Body = io.NopCloser(strings.NewReader(string(newData)))
	req.ContentLength = int64(len(newData))

	// 设置Content-Type
	req.Header.Set("Content-Type", "application/json")

	return nil
}

// IsTokenExpired 账号密码认证不需要token刷新
func (p *PasswordAuth) IsTokenExpired(err error) bool {
	return false
}

// RefreshToken 账号密码认证不需要token刷新
func (p *PasswordAuth) RefreshToken(ctx context.Context) error {
	return nil
}
