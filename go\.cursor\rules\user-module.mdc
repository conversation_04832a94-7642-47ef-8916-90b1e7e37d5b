---
description: src/*
alwaysApply: false
---
# 用户模块开发规范

## 模块文件
- [src/app/admin/user/account.go](mdc:src/app/admin/user/account.go) - 管理端用户账号
- [src/app/business/user/account.go](mdc:src/app/business/user/account.go) - 业务端用户账号
- [src/app/business/user/index.go](mdc:src/app/business/user/index.go) - 业务端用户接口
- [src/app/business/usermanage/information.go](mdc:src/app/business/usermanage/information.go) - 用户信息管理

## 用户数据安全
- 用户密码必须加密存储，使用项目封装的加密方法
- 用户敏感信息应加密存储，如身份证号、银行卡号等
- 用户认证应使用 JWT，并合理设置过期时间
- 用户登录应有失败次数限制，防止暴力破解

## 用户权限控制
- 用户权限应基于角色进行控制
- 敏感操作应有额外的权限验证
- API接口应有权限检查中间件

## 注意事项
- 用户注册应有完整的数据验证
- 手机号、邮箱等唯一性信息应有重复检查
- 用户状态变更应记录操作日志
- 敏感操作应发送通知给用户
# 用户模块开发规范

## 模块文件
- [src/app/admin/user/account.go](mdc:src/app/admin/user/account.go) - 管理端用户账号
- [src/app/business/user/account.go](mdc:src/app/business/user/account.go) - 业务端用户账号
- [src/app/business/user/index.go](mdc:src/app/business/user/index.go) - 业务端用户接口
- [src/app/business/usermanage/information.go](mdc:src/app/business/usermanage/information.go) - 用户信息管理

## 用户数据安全
- 用户密码必须加密存储，使用项目封装的加密方法
- 用户敏感信息应加密存储，如身份证号、银行卡号等
- 用户认证应使用 JWT，并合理设置过期时间
- 用户登录应有失败次数限制，防止暴力破解

## 用户权限控制
- 用户权限应基于角色进行控制
- 敏感操作应有额外的权限验证
- API接口应有权限检查中间件

## 注意事项
- 用户注册应有完整的数据验证
- 手机号、邮箱等唯一性信息应有重复检查
- 用户状态变更应记录操作日志
- 敏感操作应发送通知给用户
