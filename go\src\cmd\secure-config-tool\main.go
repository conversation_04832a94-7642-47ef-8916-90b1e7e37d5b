package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"fincore/utils/config"

	"github.com/spf13/viper"
	"gopkg.in/yaml.v2"
)

func main() {
	// 定义命令行参数
	var (
		action     = flag.String("action", "", "操作类型: upload, download, validate, backup, list, encrypt-oss-config, encrypt-sumpay")
		localPath  = flag.String("local", "", "本地配置文件路径")
		ossPath    = flag.String("oss", "", "OSS配置文件路径")
		backupPath = flag.String("backup", "", "备份文件路径")
		prefix     = flag.String("prefix", "config/", "OSS文件前缀")
		help       = flag.Bool("help", false, "显示帮助信息")
	)
	flag.Parse()

	// 显示帮助信息
	if *help || *action == "" {
		showHelp()
		return
	}

	// 创建安全OSS加载器
	loader, err := config.NewSecureOSSLoader()
	if err != nil {
		log.Fatalf("创建安全OSS加载器失败: %v", err)
	}

	// 根据操作类型执行相应功能
	switch *action {
	case "upload":
		handleUpload(loader, *localPath, *ossPath)
	case "download":
		handleDownload(loader, *ossPath, *localPath)
	case "validate":
		handleValidate(loader, *ossPath)
	case "backup":
		handleBackup(loader, *ossPath, *backupPath)
	case "list":
		handleList(loader, *prefix)
	case "encrypt-oss-config":
		handleUploadFincore(*localPath, *ossPath)
	case "encrypt-config":
		handleEncryptConfig(*localPath)
	default:
		log.Fatalf("未知的操作类型: %s", *action)
	}
}

// showHelp 显示帮助信息
func showHelp() {
	fmt.Println(`安全OSS配置管理工具

用法:
  secure-config-tool -action=<操作> [选项]

操作:
  upload         上传本地配置文件到OSS（加密）
  download       从OSS下载配置文件到本地（解密）
  validate       验证OSS配置文件
  backup         备份OSS配置文件到本地
  list           列出OSS中的配置文件
  encrypt-oss-config 硬编码加密本地OSS凭证配置文件
  encrypt-config  加密配置文件中的所有敏感字段

选项:
  -local    本地配置文件路径
  -oss      OSS配置文件路径
  -backup   备份文件路径
  -prefix   OSS文件前缀（默认: config/）
  -help     显示此帮助信息

环境变量:
  OSS_CREDENTIALS_PATH  OSS凭证文件路径（默认: ./resource/fincore.yml）

示例:
  # 上传本地配置到OSS
  secure-config-tool -action=upload -local=config.yml -oss=config/prod/config.yml

  # 从OSS下载配置到本地
  secure-config-tool -action=download -oss=config/prod/config.yml -local=config.yml

  # 验证OSS配置
  secure-config-tool -action=validate -oss=config/prod/config.yml

  # 备份OSS配置
  secure-config-tool -action=backup -oss=config/prod/config.yml -backup=backup.yml

  # 列出OSS配置文件
  secure-config-tool -action=list -prefix=config/

  # 硬编码加密本地OSS凭证配置文件
  secure-config-tool -action=encrypt-oss-config -local=fincore.yml -oss=fincore.yml.encrypted

  # 加密配置文件中的所有敏感字段
  secure-config-tool -action=encrypt-config -local=config.yml

注意:
  1. 确保已正确配置OSS凭证文件
  2. 本地配置文件将被加密后上传到OSS
  3. 从OSS下载的配置文件会自动解密
  4. 建议定期备份重要配置文件`)
}

// handleUpload 处理上传操作
func handleUpload(loader *config.SecureOSSLoader, localPath, ossPath string) {
	if localPath == "" || ossPath == "" {
		log.Fatal("上传操作需要指定 -local 和 -oss 参数")
	}

	// 检查本地文件是否存在
	if _, err := os.Stat(localPath); os.IsNotExist(err) {
		log.Fatalf("本地配置文件不存在: %s", localPath)
	}

	log.Printf("开始上传配置: %s -> OSS:%s", localPath, ossPath)

	// 加载本地配置
	localConfig, err := loadLocalConfigFile(localPath)
	if err != nil {
		log.Fatalf("加载本地配置文件失败: %v", err)
	}

	// 上传到OSS
	if err := loader.UploadConfig(localConfig, ossPath); err != nil {
		log.Fatalf("上传配置到OSS失败: %v", err)
	}

	log.Printf("成功上传配置到OSS: %s", ossPath)
}

// handleDownload 处理下载操作
func handleDownload(loader *config.SecureOSSLoader, ossPath, localPath string) {
	log.Printf("Debug: ossPath='%s', localPath='%s'", ossPath, localPath)
	if ossPath == "" || localPath == "" {
		log.Fatal("下载操作需要指定 -oss 和 -local 参数")
	}

	log.Printf("开始下载配置: OSS:%s -> %s", ossPath, localPath)

	// 从OSS加载配置
	ossConfig, err := loader.LoadConfig(ossPath)
	if err != nil {
		log.Fatalf("从OSS加载配置失败: %v", err)
	}

	// 保存到本地文件
	if err := saveConfigToFile(ossConfig, localPath); err != nil {
		log.Fatalf("保存配置到本地文件失败: %v", err)
	}

	log.Printf("成功下载配置到本地: %s", localPath)
}

// handleValidate 处理验证操作
func handleValidate(loader *config.SecureOSSLoader, ossPath string) {
	if ossPath == "" {
		log.Fatal("验证操作需要指定 -oss 参数")
	}

	log.Printf("开始验证OSS配置: %s", ossPath)

	// 尝试加载配置
	ossConfig, err := loader.LoadConfig(ossPath)
	if err != nil {
		log.Fatalf("验证失败: %v", err)
	}

	// 验证配置完整性
	if err := validateConfig(ossConfig); err != nil {
		log.Fatalf("配置验证失败: %v", err)
	}

	log.Printf("OSS配置验证成功: %s", ossPath)
}

// handleBackup 处理备份操作
func handleBackup(loader *config.SecureOSSLoader, ossPath, backupPath string) {
	if ossPath == "" {
		log.Fatal("备份操作需要指定 -oss 参数")
	}

	// 如果未指定备份路径，自动生成
	if backupPath == "" {
		timestamp := time.Now().Format("20060102-150405")
		backupPath = fmt.Sprintf("backup/config-%s.yml", timestamp)
	}

	log.Printf("开始备份OSS配置: %s -> %s", ossPath, backupPath)

	// 备份配置
	if err := loader.BackupConfig(ossPath, backupPath); err != nil {
		log.Fatalf("备份配置失败: %v", err)
	}

	log.Printf("成功备份配置到: %s", backupPath)
}

// handleList 处理列表操作
func handleList(loader *config.SecureOSSLoader, prefix string) {
	log.Printf("列出OSS配置文件，前缀: %s", prefix)

	// 列出配置文件
	configs, err := loader.ListConfigs(prefix)
	if err != nil {
		log.Fatalf("列出配置文件失败: %v", err)
	}

	if len(configs) == 0 {
		log.Printf("未找到匹配的配置文件")
		return
	}

	fmt.Printf("找到 %d 个配置文件:\n", len(configs))
	for i, configPath := range configs {
		fmt.Printf("%d. %s\n", i+1, configPath)
	}
}

// loadLocalConfigFile 加载本地配置文件
func loadLocalConfigFile(filePath string) (*config.Config, error) {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", filePath)
	}

	// 使用viper解析YAML配置文件
	v := viper.New()
	v.SetConfigFile(filePath)
	v.SetConfigType("yml")

	// 读取配置文件
	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析为Config结构体
	cfg := &config.Config{}
	if err := v.Unmarshal(cfg); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 验证关键配置项
	if err := validateBasicConfig(cfg); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	fmt.Printf("成功加载本地配置文件: %s\n", filePath)
	return cfg, nil
}

// validateBasicConfig 验证基本配置项
func validateBasicConfig(cfg *config.Config) error {
	if cfg.DBconf.Driver == "" {
		return fmt.Errorf("数据库驱动配置为空")
	}
	if cfg.App.Port == "" {
		return fmt.Errorf("应用端口配置为空")
	}
	if cfg.Jwt.Secret == "" {
		return fmt.Errorf("JWT密钥配置为空")
	}
	return nil
}

// handleUploadFincore 处理fincore.yml凭证文件的加密上传
func handleUploadFincore(localPath, ossPath string) {
	if localPath == "" {
		log.Fatal("请指定本地OSS凭证文件路径(-local)")
	}

	// 创建安全OSS加载器
	enhancedLoader, err := config.NewSecureOSSLoader()
	if err != nil {
		log.Fatalf("创建安全OSS加载器失败: %v", err)
	}

	// 读取本地OSS凭证文件
	fileData, err := os.ReadFile(localPath)
	if err != nil {
		log.Fatalf("读取OSS凭证文件失败: %v", err)
	}

	// 使用硬编码密钥加密凭证数据
	encryptedData, err := enhancedLoader.EncryptWithHardcodedKey(fileData)
	if err != nil {
		log.Fatalf("加密OSS凭证文件失败: %v", err)
	}

	// 将加密后的数据写入到指定的输出文件
	outputPath := localPath + ".encrypted"
	if ossPath != "" {
		outputPath = ossPath
	}

	if err := os.WriteFile(outputPath, encryptedData, 0644); err != nil {
		log.Fatalf("保存加密文件失败: %v", err)
	}

	fmt.Printf("OSS凭证文件已硬编码加密保存到: %s\n", outputPath)
	fmt.Println("现在可以安全地上传整个应用配置，加密的凭证文件将被自动解密使用")
}

// handleEncryptConfig 处理配置文件敏感字段加密
func handleEncryptConfig(localPath string) {
	if localPath == "" {
		log.Fatal("请指定本地配置文件路径(-local)")
	}

	// 加载本地配置文件
	cfg, err := loadLocalConfigFile(localPath)
	if err != nil {
		log.Fatalf("加载配置文件失败: %v", err)
	}

	// 创建安全OSS加载器
	loader, err := config.NewSecureOSSLoader()
	if err != nil {
		log.Fatalf("创建安全OSS加载器失败: %v", err)
	}

	// 加密所有敏感字段
	if err := loader.EncryptSensitiveFields(cfg); err != nil {
		log.Fatalf("加密敏感字段失败: %v", err)
	}

	// 保存加密后的配置文件
	if err := saveConfigToFile(cfg, localPath); err != nil {
		log.Fatalf("保存配置文件失败: %v", err)
	}

	fmt.Printf("所有敏感字段已加密并保存到: %s\n", localPath)
}

// loadFincoreCredentials 加载fincore凭证文件
func loadFincoreCredentials(filePath string) (*config.OSSCredentials, error) {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("文件不存在: %s", filePath)
	}

	// 使用viper加载YAML文件
	v := viper.New()
	v.SetConfigFile(filePath)
	v.SetConfigType("yaml")

	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析为OSSCredentials结构
	var credentials config.OSSCredentials
	if err := v.Unmarshal(&credentials); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 验证必要字段
	if credentials.Endpoint == "" || credentials.AccessKeyID == "" ||
		credentials.AccessKeySecret == "" || credentials.BucketName == "" {
		return nil, fmt.Errorf("fincore凭证文件缺少必要字段")
	}

	return &credentials, nil
}

// saveConfigToFile 保存配置到本地文件
func saveConfigToFile(cfg *config.Config, filePath string) error {
	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	// 将Config结构体序列化为YAML格式
	yamlData, err := yaml.Marshal(cfg)
	if err != nil {
		return fmt.Errorf("序列化配置为YAML失败: %v", err)
	}

	// 添加文件头注释
	header := "# 配置文件已从OSS下载并解密\n# 生成时间: " + time.Now().Format("2006-01-02 15:04:05") + "\n\n"
	finalContent := header + string(yamlData)

	// 写入文件
	if err := os.WriteFile(filePath, []byte(finalContent), 0600); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	fmt.Printf("配置文件已成功保存到: %s\n", filePath)
	return nil
}

// validateConfig 验证配置完整性
func validateConfig(cfg *config.Config) error {
	if cfg == nil {
		return fmt.Errorf("配置对象为空")
	}

	// 验证数据库配置
	if cfg.DBconf.Driver == "" {
		return fmt.Errorf("数据库驱动配置为空")
	}
	if cfg.DBconf.Hostname == "" {
		return fmt.Errorf("数据库主机配置为空")
	}
	if cfg.DBconf.Database == "" {
		return fmt.Errorf("数据库名称配置为空")
	}

	// 验证应用配置
	if cfg.App.Port == "" {
		return fmt.Errorf("应用端口配置为空")
	}
	if cfg.App.Version == "" {
		return fmt.Errorf("应用版本配置为空")
	}

	// 验证JWT配置
	if cfg.Jwt.Secret == "" {
		return fmt.Errorf("JWT密钥配置为空")
	}
	if len(cfg.Jwt.Secret) < 32 {
		return fmt.Errorf("JWT密钥长度不足32字符")
	}

	// 验证Redis配置
	if cfg.Redis.Host == "" {
		return fmt.Errorf("Redis主机配置为空")
	}
	if cfg.Redis.Port == "" {
		return fmt.Errorf("Redis端口配置为空")
	}

	// 验证OSS配置（如果启用）
	if cfg.OSS.Enabled {
		if cfg.OSS.Endpoint == "" {
			return fmt.Errorf("OSS端点配置为空")
		}
		if cfg.OSS.AccessKeyId == "" {
			return fmt.Errorf("OSS AccessKeyId配置为空")
		}
		if cfg.OSS.AccessKeySecret == "" {
			return fmt.Errorf("OSS AccessKeySecret配置为空")
		}
		if cfg.OSS.BucketName == "" {
			return fmt.Errorf("OSS存储桶名称配置为空")
		}
	}

	// 验证日志配置
	if cfg.Log.Level == "" {
		cfg.Log.Level = "info" // 设置默认值
	}
	if cfg.Log.RootDir == "" {
		cfg.Log.RootDir = "./log" // 设置默认值
	}

	fmt.Println("配置验证通过")
	return nil
}
