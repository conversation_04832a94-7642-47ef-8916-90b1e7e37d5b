package service

import (
	"fincore/app/dianziqian/httpUtils"
	"fincore/utils/log"
)

// 创建个人账号
func CreatePersonalAccount(dataJson string) []byte {
	apiUrl := "/v2/user/addPersonalUser"
	log.Info("创建个人账号--------")
	response, err := httpUtils.SendRequest(apiUrl, dataJson, nil)
	if err != nil {
		log.Error("请求失败：", err)

	}
	return response
}

// 查询个人账号
func QueryPersonalAccount(dataJson string) []byte {
	apiUrl := "/user/getUser"
	log.Info("查询个人账号--------")
	response, err := httpUtils.SendRequest(apiUrl, dataJson, nil)
	if err != nil {
		log.Error("请求失败：", err)
	}
	return response
}

func CheckPersonalName(dataJson string) []byte {
	apiUrl := "/auth/person/identifyUrl"
	log.Info("进行实名认证--------")
	response, err := httpUtils.SendRequest(apiUrl, dataJson, nil)
	if err != nil {
		log.Error("请求失败：", err)
	}
	return response
}

// CheckPersonalRealName 调用第三方个人实名认证接口的流水号
// 参数:
//   - dataJson: JSON格式的请求数据
//
// 返回值:
//   - []byte: 原始响应数据
func CheckPersonalRealName(dataJson string) []byte {
	apiUrl := "/auth/getSerialNo"
	log.Info("实名认证序列号查询")
	response, err := httpUtils.SendRequest(apiUrl, dataJson, nil)
	if err != nil {
		log.Error("请求失败：", err)
		return nil
	}
	return response
}
