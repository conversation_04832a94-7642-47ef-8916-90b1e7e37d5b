package statistics

import (
	"context"
	"fincore/app/business/statistics"
	"fincore/app/scheduler/tasks"
	"fincore/utils/log"
	"time"

	"github.com/golang-module/carbon/v2"
)

// ChannelStatisticsTask 渠道统计定时任务
// 每天12:00和24:00执行，统计各渠道的新用户数、实名通过数、成交数
type ChannelStatisticsTask struct {
	*tasks.BaseTask
	logger  *log.Logger
	service *statistics.Service
	ctx     context.Context
}

// NewChannelStatisticsTask 创建渠道统计定时任务
func NewChannelStatisticsTask() *ChannelStatisticsTask {
	baseTask := tasks.NewBaseTask(
		"channel_statistics_task", // 任务名称
		"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数", // 任务描述
		"0 0 */2 * * *", // 每两个小时统计一次
		10*time.Minute,  // 超时时间10分钟
	)

	// 设置任务属性
	baseTask.SetRetryCount(2). // 重试2次
					SetRetryInterval(5 * time.Minute).                 // 重试间隔5分钟
					SetConcurrencyMode(tasks.ConcurrencyModeSingleton) // 单例模式，避免重复执行

	return &ChannelStatisticsTask{
		BaseTask: baseTask,
		logger:   log.RegisterModule("channel_statistics_task", "渠道统计定时任务"),
		ctx:      context.Background(),
	}
}

// Execute 执行渠道统计任务
func (t *ChannelStatisticsTask) Execute(ctx context.Context) error {
	// 检查上下文是否被取消
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	t.logger.Info("开始执行渠道统计定时任务")

	// 创建统计服务实例
	t.service = statistics.NewService(ctx)

	// 执行渠道统计
	err := t.service.ExecuteChannelStatistics()
	if err != nil {
		t.logger.Error("渠道统计任务执行失败", log.String("error", err.Error()))
		return err
	}

	// 如果为凌晨2点，统计前一天的数据
	if time.Now().Hour() == 2 {
		subDay := carbon.Now().SubDay()
		err := t.service.ExecuteChannelStatisticsForDate(*subDay)
		if err != nil {
			t.logger.Error("渠道统计任务执行失败", log.String("error", err.Error()))
			return err
		}
	}

	t.logger.Info("渠道统计定时任务执行完成")
	return nil
}

// OnStart 任务开始前的准备工作
func (t *ChannelStatisticsTask) OnStart(ctx context.Context) error {
	requsetID := "task_" + t.GetName() + "_" + time.Now().Format("20060102150405")
	t.ctx = context.WithValue(t.ctx, log.RequestIDKey, requsetID)
	t.logger = t.logger.WithRequestID(requsetID)

	return nil
}

// OnSuccess 任务成功后的处理
func (t *ChannelStatisticsTask) OnSuccess(ctx context.Context) error {
	t.logger.Info("渠道统计任务执行成功",
		log.String("task_name", t.GetName()),
		log.String("execution_time", time.Now().Format("2006-01-02 15:04:05")),
	)

	// 可以在这里添加成功后的通知逻辑，比如发送邮件或消息
	// 暂时只记录日志
	return nil
}

// OnError 任务失败后的处理
func (t *ChannelStatisticsTask) OnError(ctx context.Context, err error) error {
	t.logger.Error("渠道统计任务执行失败",
		log.String("error", err.Error()),
		log.String("task_name", t.GetName()),
		log.String("error_time", time.Now().Format("2006-01-02 15:04:05")),
	)

	// 可以在这里添加失败后的通知逻辑，比如发送告警邮件
	// 暂时只记录错误日志
	return nil
}

// OnComplete 任务完成后的清理工作
func (t *ChannelStatisticsTask) OnComplete(ctx context.Context) error {
	t.logger.Info("渠道统计任务执行完成",
		log.String("task_name", t.GetName()),
		log.String("complete_time", time.Now().Format("2006-01-02 15:04:05")),
	)

	// 清理服务实例
	t.service = nil

	return nil
}

// GetStatisticsSummary 获取统计摘要信息（用于监控和调试）
func (t *ChannelStatisticsTask) GetStatisticsSummary(ctx context.Context) (map[string]interface{}, error) {
	if t.service == nil {
		t.service = statistics.NewService(ctx)
	}

	summary, err := t.service.GetStatisticsSummary()
	if err != nil {
		t.logger.Error("获取统计摘要失败", log.String("error", err.Error()))
		return nil, err
	}

	return summary, nil
}

// ExecuteForDate 执行指定日期的渠道统计（用于补充统计或重新统计）
func (t *ChannelStatisticsTask) ExecuteForDate(ctx context.Context, date time.Time) error {
	t.logger.Info("开始执行指定日期的渠道统计",
		log.String("date", date.Format("2006-01-02")),
	)

	// 创建统计服务实例
	if t.service == nil {
		t.service = statistics.NewService(ctx)
	}

	// 使用carbon包处理日期
	carbonDate := carbon.CreateFromStdTime(date)

	// 执行指定日期的统计
	err := t.service.ExecuteChannelStatisticsForDate(*carbonDate)
	if err != nil {
		t.logger.Error("指定日期的渠道统计执行失败",
			log.String("error", err.Error()),
			log.String("date", date.Format("2006-01-02")),
		)
		return err
	}

	t.logger.Info("指定日期的渠道统计执行完成",
		log.String("date", date.Format("2006-01-02")),
	)

	return nil
}
