package decimal

import (
	"encoding/json"
	"testing"
)

func TestDecimalBasicOperations(t *testing.T) {
	// 测试基本运算
	a := NewDecimal(10.5)
	b := NewDecimal(2.5)

	// 加法
	sum := a.Add(b)
	if sum.Float64() != 13.0 {
		t.<PERSON><PERSON><PERSON>("Add failed: expected 13.0, got %f", sum.Float64())
	}

	// 减法
	diff := a.Sub(b)
	if diff.Float64() != 8.0 {
		t.<PERSON><PERSON>("Sub failed: expected 8.0, got %f", diff.Float64())
	}

	// 乘法
	product := a.Mul(b)
	if product.Float64() != 26.25 {
		t.<PERSON><PERSON><PERSON>("Mul failed: expected 26.25, got %f", product.Float64())
	}

	// 除法
	quotient := a.Div(b)
	if quotient.Float64() != 4.2 {
		t.<PERSON><PERSON><PERSON>("Div failed: expected 4.2, got %f", quotient.Float64())
	}
}

func TestDecimalComparison(t *testing.T) {
	a := NewDecimal(10.5)
	b := NewDecimal(10.5)
	c := NewDecimal(5.0)

	// 相等
	if !a.Equal(b) {
		t.Error("Equal failed: 10.5 should equal 10.5")
	}

	// 大于
	if !a.GreaterThan(c) {
		t.Error("GreaterThan failed: 10.5 should be greater than 5.0")
	}

	// 小于
	if !c.LessThan(a) {
		t.Error("LessThan failed: 5.0 should be less than 10.5")
	}
}

func TestDecimalJSON(t *testing.T) {
	// 测试JSON序列化
	d := NewDecimal(123.45)
	jsonData, err := json.Marshal(d)
	if err != nil {
		t.Errorf("JSON Marshal failed: %v", err)
	}

	expected := "123.45"
	if string(jsonData) != expected {
		t.Errorf("JSON Marshal failed: expected %s, got %s", expected, string(jsonData))
	}

	// 测试JSON反序列化
	var d2 Decimal
	err = json.Unmarshal([]byte(`"123.45"`), &d2)
	if err != nil {
		t.Errorf("JSON Unmarshal failed: %v", err)
	}

	if !d.Equal(d2) {
		t.Errorf("JSON Unmarshal failed: expected %f, got %f", d.Float64(), d2.Float64())
	}

	// 测试数字格式的JSON反序列化
	var d3 Decimal
	err = json.Unmarshal([]byte(`123.45`), &d3)
	if err != nil {
		t.Errorf("JSON Unmarshal (number) failed: %v", err)
	}

	if !d.Equal(d3) {
		t.Errorf("JSON Unmarshal (number) failed: expected %f, got %f", d.Float64(), d3.Float64())
	}
}

func TestDecimalScan(t *testing.T) {
	var d Decimal

	// 测试Scan float64
	err := d.Scan(123.45)
	if err != nil {
		t.Errorf("Scan float64 failed: %v", err)
	}
	if d.Float64() != 123.45 {
		t.Errorf("Scan float64 failed: expected 123.45, got %f", d.Float64())
	}

	// 测试Scan string
	err = d.Scan("678.90")
	if err != nil {
		t.Errorf("Scan string failed: %v", err)
	}
	if d.Float64() != 678.90 {
		t.Errorf("Scan string failed: expected 678.90, got %f", d.Float64())
	}

	// 测试Scan []byte
	err = d.Scan([]byte("999.99"))
	if err != nil {
		t.Errorf("Scan []byte failed: %v", err)
	}
	if d.Float64() != 999.99 {
		t.Errorf("Scan []byte failed: expected 999.99, got %f", d.Float64())
	}

	// 测试Scan nil
	err = d.Scan(nil)
	if err != nil {
		t.Errorf("Scan nil failed: %v", err)
	}
	if !d.IsZero() {
		t.Errorf("Scan nil failed: expected 0, got %f", d.Float64())
	}
}

func TestDecimalString(t *testing.T) {
	d := NewDecimal(123.456)

	// 默认2位小数
	if d.String() != "123.46" {
		t.Errorf("String failed: expected '123.46', got '%s'", d.String())
	}

	// 指定精度
	if d.StringWithPrecision(1) != "123.5" {
		t.Errorf("StringWithPrecision(1) failed: expected '123.5', got '%s'", d.StringWithPrecision(1))
	}

	if d.StringWithPrecision(3) != "123.456" {
		t.Errorf("StringWithPrecision(3) failed: expected '123.456', got '%s'", d.StringWithPrecision(3))
	}
}

func TestDecimalFromString(t *testing.T) {
	// 正常解析
	d, err := NewDecimalFromString("123.45")
	if err != nil {
		t.Errorf("NewDecimalFromString failed: %v", err)
	}
	if d.Float64() != 123.45 {
		t.Errorf("NewDecimalFromString failed: expected 123.45, got %f", d.Float64())
	}

	// 错误格式
	_, err = NewDecimalFromString("invalid")
	if err == nil {
		t.Error("NewDecimalFromString should fail for invalid string")
	}

	// MustNewDecimalFromString
	d2 := MustNewDecimalFromString("678.90")
	if d2.Float64() != 678.90 {
		t.Errorf("MustNewDecimalFromString failed: expected 678.90, got %f", d2.Float64())
	}

	// MustNewDecimalFromString with invalid string
	d3 := MustNewDecimalFromString("invalid")
	if !d3.IsZero() {
		t.Errorf("MustNewDecimalFromString should return zero for invalid string, got %f", d3.Float64())
	}
}

func TestDecimalUtilityFunctions(t *testing.T) {
	// 测试Sum
	decimals := []Decimal{
		NewDecimal(10.5),
		NewDecimal(20.3),
		NewDecimal(5.2),
	}
	sum := Sum(decimals...)
	expected := 36.0
	if sum.Float64() != expected {
		t.Errorf("Sum failed: expected %f, got %f", expected, sum.Float64())
	}

	// 测试Max
	a := NewDecimal(10.5)
	b := NewDecimal(20.3)
	max := Max(a, b)
	if !max.Equal(b) {
		t.Errorf("Max failed: expected %f, got %f", b.Float64(), max.Float64())
	}

	// 测试Min
	min := Min(a, b)
	if !min.Equal(a) {
		t.Errorf("Min failed: expected %f, got %f", a.Float64(), min.Float64())
	}

	// 测试Abs
	negative := NewDecimal(-10.5)
	abs := negative.Abs()
	if abs.Float64() != 10.5 {
		t.Errorf("Abs failed: expected 10.5, got %f", abs.Float64())
	}

	// 测试Round
	d := NewDecimal(123.456)
	rounded := d.Round(2)
	if rounded.Float64() != 123.46 {
		t.Errorf("Round failed: expected 123.46, got %f", rounded.Float64())
	}
}
