package model

import (
	"fmt"
	"time"
)

// BusinessPaymentChannels 支付渠道数据模型
type BusinessPaymentChannels struct {
	ID          int        `json:"id" gorm:"primaryKey;autoIncrement" db:"id"` // 主键ID，自增
	ChannelName string     `json:"channel_name" db:"channel_name"`             // 渠道名称，如：宝付、统统付
	ChannelCode string     `json:"channel_code" db:"channel_code"`             // 渠道代码，唯一标识，如：BAOFU、TTF
	Config      string     `json:"config" db:"config"`                         // 渠道配置，JSON格式，包含API密钥、回调地址等
	Status      int        `json:"status" db:"status"`                         // 渠道状态：1-启用，0-禁用
	CreatedAt   *time.Time `json:"created_at" db:"created_at"`                 // 创建时间，timestamp类型，可为空
	UpdatedAt   *time.Time `json:"updated_at" db:"updated_at"`                 // 更新时间，timestamp类型，可为空
}

const (
	PaymentChannelStatusActive   = 1
	PaymentChannelStatusInactive = 0
)

// TableName 指定表名
func (BusinessPaymentChannels) TableName() string {
	return "business_payment_channels"
}

// BusinessPaymentChannelsService 支付渠道服务
type BusinessPaymentChannelsService struct{}

// NewBusinessPaymentChannelsService 创建支付渠道服务实例
func NewBusinessPaymentChannelsService() *BusinessPaymentChannelsService {
	return &BusinessPaymentChannelsService{}
}

// GetPaymentChannelByID 根据ID获取支付渠道
func (s *BusinessPaymentChannelsService) GetPaymentChannelByID(id int) (*BusinessPaymentChannels, error) {
	data, err := DB().Table("business_payment_channels").Where("id", id).First()
	if err != nil {
		return nil, fmt.Errorf("查询支付渠道失败: %v", err)
	}
	if len(data) == 0 {
		return nil, fmt.Errorf("支付渠道不存在")
	}

	var channel BusinessPaymentChannels
	if err := mapToStruct(data, &channel); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &channel, nil
}

// GetActivePaymentChannels 获取所有可用的支付渠道
func (s *BusinessPaymentChannelsService) GetActivePaymentChannels() ([]BusinessPaymentChannels, error) {
	data, err := DB().Table("business_payment_channels").Where("status", "ACTIVE").OrderBy("id ASC").Get()
	if err != nil {
		return nil, fmt.Errorf("查询支付渠道失败: %v", err)
	}

	var channels []BusinessPaymentChannels
	for _, item := range data {
		var channel BusinessPaymentChannels
		if err := mapToStruct(item, &channel); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		channels = append(channels, channel)
	}

	return channels, nil
}
