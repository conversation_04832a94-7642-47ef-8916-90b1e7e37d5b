package main

import (
	"context"
	"fincore/app/business/risk"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"fincore/global"
	"fincore/model"

	"go.uber.org/zap"
)

func main() {
	// 设置工作目录到项目根目录
	if err := setupWorkingDirectory(); err != nil {
		log.Fatalf("设置工作目录失败: %v", err)
	}

	// 初始化日志系统
	logger, err := zap.NewDevelopment()
	if err != nil {
		log.Fatalf("初始化日志失败: %v", err)
	}
	global.App.Log = logger

	// 初始化数据库连接
	model.MyInit("test")

	// 创建Excel测试器
	tester := risk.NewExcelRiskTester()

	// Excel文件路径
	excelPath := "c:\\Users\\<USER>\\workspace\\fincore\\go\\src\\app\\business\\risk\\测试数据_0717.xlsx"
	outputPath := "c:\\Users\\<USER>\\workspace\\fincore\\go\\src\\app\\business\\risk\\测试结果_" +
		fmt.Sprintf("%d", time.Now().Unix()) + ".xlsx"

	// 检查输入文件是否存在
	if _, err := os.Stat(excelPath); os.IsNotExist(err) {
		log.Fatalf("Excel文件不存在: %s", excelPath)
	}

	ctx := context.Background()

	// 运行测试
	logger.Info("开始运行Excel风险评估测试", zap.String("input_file", excelPath))

	results, err := tester.RunTest(ctx, excelPath)
	if err != nil {
		log.Fatalf("运行测试失败: %v", err)
	}

	// 分析失败类型
	analysis := tester.AnalyzeFailureTypes(results)

	// 打印摘要
	tester.PrintSummary(results, analysis)

	// 保存结果到Excel
	logger.Info("保存测试结果", zap.String("output_file", outputPath))
	if err := tester.SaveResultsToExcel(results, analysis, outputPath); err != nil {
		log.Fatalf("保存结果失败: %v", err)
	}

	logger.Info("测试完成",
		zap.String("output_file", outputPath),
		zap.Int("total_tests", len(results)))

	fmt.Printf("\n测试结果已保存到: %s\n", outputPath)
}

// setupWorkingDirectory 设置工作目录到项目根目录
func setupWorkingDirectory() error {
	// 获取当前可执行文件的目录
	execPath, err := os.Executable()
	if err != nil {
		return fmt.Errorf("获取可执行文件路径失败: %w", err)
	}

	// 获取项目根目录（假设在go/src目录下）
	currentDir := filepath.Dir(execPath)
	for {
		// 检查是否包含go.mod文件
		if _, err := os.Stat(filepath.Join(currentDir, "go.mod")); err == nil {
			break
		}

		// 向上查找
		parentDir := filepath.Dir(currentDir)
		if parentDir == currentDir {
			// 已经到达根目录，使用默认路径
			currentDir = "c:\\Users\\<USER>\\workspace\\fincore\\go\\src"
			break
		}
		currentDir = parentDir
	}

	// 设置工作目录
	if err := os.Chdir(currentDir); err != nil {
		return fmt.Errorf("设置工作目录失败: %w", err)
	}

	fmt.Printf("工作目录设置为: %s\n", currentDir)
	return nil
}
