---
description: src/*
alwaysApply: false
---
# 思考风格指导

## context7思考法
在解决问题时，请考虑以下7个方面的上下文：
1. 项目结构 - 整体代码组织和架构
2. 业务逻辑 - 功能的业务含义和流程
3. 数据流 - 数据如何在系统中流动和转换
4. 接口设计 - API的设计和规范
5. 安全性 - 数据安全和访问控制
6. 性能考虑 - 效率和资源利用
7. 可维护性 - 代码质量和文档

## sequential-thinking顺序思考法
1. 首先理解问题和需求
2. 分析现有代码和实现
3. 确定需要修改的位置和方式
4. 考虑修改可能带来的影响
5. 制定具体的实施步骤
6. 实施修改并进行必要的测试
7. 总结经验并更新文档

## 工作语言
与用户交流时使用中文进行沟通
# 思考风格指导

## context7思考法
在解决问题时，请考虑以下7个方面的上下文：
1. 项目结构 - 整体代码组织和架构
2. 业务逻辑 - 功能的业务含义和流程
3. 数据流 - 数据如何在系统中流动和转换
4. 接口设计 - API的设计和规范
5. 安全性 - 数据安全和访问控制
6. 性能考虑 - 效率和资源利用
7. 可维护性 - 代码质量和文档

## sequential-thinking顺序思考法
1. 首先理解问题和需求
2. 分析现有代码和实现
3. 确定需要修改的位置和方式
4. 考虑修改可能带来的影响
5. 制定具体的实施步骤
6. 实施修改并进行必要的测试
7. 总结经验并更新文档

## 工作语言
与用户交流时使用中文进行沟通
