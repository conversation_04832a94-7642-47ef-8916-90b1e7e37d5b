/**
 * 渠道管理工具类
 */
import utils from '@/utils';

class ChannelUtils {
  constructor() {
    this.STORAGE_KEY = 'channel_info';
    this.SESSION_KEY = 'channel_session';
  }

  /**
   * 解析URL中的渠道参数
   * @param {Object} options - 页面options参数
   * @returns {Object|null} 渠道信息对象
   */
  parseChannelParams(options = {}) {
    try {
      if (!options.cid) {
        return null;
      }

      // URL解码
      const decodedCid = decodeURIComponent(options.cid);
      
      // Base64解码获取原始渠道编码
      const channelCode = this.base64Decode(decodedCid);
      
      if (!channelCode) {
        return null;
      }

      return {
        channelCode: channelCode,
        timestamp: Date.now(),
        isValid: true
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Base64解码
   * @param {string} str - 需要解码的字符串
   * @returns {string} 解码后的字符串
   */
  base64Decode(str) {
    try {
      // 处理URL安全的base64编码
      const base64 = str.replace(/-/g, '+').replace(/_/g, '/');
      const padding = base64.length % 4;
      const paddedBase64 = padding ? base64 + '='.repeat(4 - padding) : base64;
      
      // 使用原生方法或者polyfill
      if (typeof atob !== 'undefined') {
        return atob(paddedBase64);
      } else {
        // UniApp环境的兼容处理
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
        let result = '';
        let i = 0;
        
        while (i < paddedBase64.length) {
          const a = chars.indexOf(paddedBase64.charAt(i++));
          const b = chars.indexOf(paddedBase64.charAt(i++));
          const c = chars.indexOf(paddedBase64.charAt(i++));
          const d = chars.indexOf(paddedBase64.charAt(i++));
          
          const bitmap = (a << 18) | (b << 12) | (c << 6) | d;
          result += String.fromCharCode((bitmap >> 16) & 255);
          if (c !== 64) result += String.fromCharCode((bitmap >> 8) & 255);
          if (d !== 64) result += String.fromCharCode(bitmap & 255);
        }
        
        return result;
      }
    } catch (error) {
      console.error('Base64解码失败:', error);
      return null;
    }
  }

  /**
   * 存储渠道信息到本地存储
   * @param {Object} channelInfo - 渠道信息
   */
  storeChannelInfo(channelInfo) {
    try {
      uni.setStorageSync(this.STORAGE_KEY, JSON.stringify(channelInfo));
    } catch (error) {
      console.error('存储渠道信息失败:', error);
    }
  }

  /**
   * 从本地存储获取渠道信息
   * @returns {Object|null} 渠道信息
   */
  getStoredChannelInfo() {
    try {
      const stored = uni.getStorageSync(this.STORAGE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('获取存储的渠道信息失败:', error);
    }
    return null;
  }

  /**
   * 清除存储的渠道信息
   */
  clearChannelInfo() {
    try {
      uni.removeStorageSync(this.STORAGE_KEY);
      uni.removeStorageSync(this.SESSION_KEY);
    } catch (error) {
      console.error('清除渠道信息失败:', error);
    }
  }





  /**
   * 设置渠道会话信息
   * @param {Object} sessionInfo - 会话信息
   */
  setChannelSession(sessionInfo) {
    try {
      uni.setStorageSync(this.SESSION_KEY, JSON.stringify(sessionInfo));
    } catch (error) {
      console.error('设置渠道会话信息失败:', error);
    }
  }

  /**
   * 获取渠道会话信息
   * @returns {Object|null} 会话信息
   */
  getChannelSession() {
    try {
      const stored = uni.getStorageSync(this.SESSION_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('获取渠道会话信息失败:', error);
    }
    return null;
  }



  /**
   * 获取当前页面的渠道参数
   * @returns {Object|null} 渠道信息
   */
  getCurrentPageChannelInfo() {
    try {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      if (currentPage && currentPage.options) {
        return this.parseChannelParams(currentPage.options);
      }
    } catch (error) {
      console.error('获取当前页面渠道信息失败:', error);
    }
    return null;
  }

  /**
   * 处理渠道信息（简化版本）
   * @param {Object} options - 页面参数
   * @returns {Promise<Object>} 处理结果
   */
  async processChannelInfo(options = {}) {
    try {
      // 1. 尝试从URL参数解析渠道信息
      let channelInfo = this.parseChannelParams(options);
      
      // 2. 检查是否需要清理历史渠道信息
      if (!channelInfo) {
        this.clearChannelInfo();
        return {
          success: true,
          channelInfo: null
        };
      }
      
      // 3. 如果有渠道信息，存储到本地
      if (channelInfo && channelInfo.channelCode) {
        this.storeChannelInfo(channelInfo);
        return {
          success: true,
          channelInfo: channelInfo
        };
      }
      
      return {
        success: true,
        channelInfo: null
      };
    } catch (error) {
      return {
        success: false,
        message: '处理渠道信息失败'
      };
    }
  }
}

// 创建单例实例
const channelUtils = new ChannelUtils();

export default channelUtils; 