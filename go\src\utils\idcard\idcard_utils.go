package idcard

import (
	"strconv"
	"time"
)

// CalculateAge 根据身份证号计算年龄
func CalculateAge(idCard string) int {
	if len(idCard) != 18 {
		return 0
	}

	// 提取出生年月日
	birthYear, err := strconv.Atoi(idCard[6:10])
	if err != nil {
		return 0
	}
	birthMonth, err := strconv.Atoi(idCard[10:12])
	if err != nil {
		return 0
	}
	birthDay, err := strconv.Atoi(idCard[12:14])
	if err != nil {
		return 0
	}

	// 获取当前时间
	now := time.Now()
	currentYear := now.Year()
	currentMonth := int(now.Month())
	currentDay := now.Day()

	// 计算年龄
	age := currentYear - birthYear

	// 如果还没到生日，年龄减1
	if currentMonth < birthMonth || (currentMonth == birthMonth && currentDay < birthDay) {
		age--
	}

	if age < 0 {
		return 0
	}

	return age
}

// GetGenderFromIDCard 根据身份证号获取性别
func GetGenderFromIDCard(idCard string) string {
	if len(idCard) != 18 {
		return "未知"
	}

	// 身份证号第17位（倒数第二位）表示性别，奇数为男，偶数为女
	genderDigit, err := strconv.Atoi(string(idCard[16]))
	if err != nil {
		return "未知"
	}

	if genderDigit%2 == 1 {
		return "男"
	}
	return "女"
}

// MaskIDCard 身份证号脱敏处理
func MaskIDCard(idCard string) string {
	if len(idCard) != 18 {
		return idCard
	}
	return idCard[:6] + "********" + idCard[14:]
}

// MaskMobile 手机号脱敏处理
func MaskMobile(mobile string) string {
	if len(mobile) != 11 {
		return mobile
	}
	return mobile[:3] + "****" + mobile[7:]
}
