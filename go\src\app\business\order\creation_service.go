package order

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"fincore/app/business/contract"
	"fincore/app/business/risk"
	"fincore/model"
	"fincore/thirdparty/payment/sumpay"

	"fincore/utils/convert"
	"fincore/utils/decimal"
	"fincore/utils/gf"
	"fincore/utils/gform"
	"fincore/utils/lock"
	"fincore/utils/log"
	"fincore/utils/repayment"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// SystemOperatorID 系统操作员ID
const SystemOperatorID int = 0

// getPaymentService 获取支付服务实例
func (s *Service) getPaymentService(ctx context.Context) (sumpay.SumpayServiceInterface, error) {
	return sumpay.NewSumpayService(sumpay.WithContext(ctx))
}

type CreateOrderParams struct {
	UserID           int     `json:"user_id"`            // 用户ID
	LoanAmount       float64 `json:"loan_amount"`        // 贷款金额
	ProductRuleID    int     `json:"product_rule_id"`    // 产品规则ID
	ChannelCode      string  `json:"channel_code"`       // 渠道编码
	PaymentChannelID int     `json:"payment_channel_id"` // 支付渠道ID
	CustomerOrigin   string  `json:"customer_origin"`    // 客户来源
	ContractID       int     `json:"contract_id"`        // 合同ID
}

// GenerateCreateOrderLockKey 生成创单锁的key
func GenerateCreateOrderLockKey(userID int) string {
	return fmt.Sprintf("%s_%d", OrderCreationLockKey, userID)
}

// CreateOrder 创建订单
func (s *Service) CreateOrder(ctx *gin.Context, data CreateOrderParams) (order *model.BusinessLoanOrders, err error) {
	// 创建订单前加锁
	key := GenerateCreateOrderLockKey(data.UserID)
	orderLock := lock.GetLockWithLogger(key, s.logger)
	orderLock.Lock()
	defer orderLock.Unlock()

	// 检查合同状态
	contractStatusService := contract.NewContractStatusService(ctx)
	contractResult, err := contractStatusService.CheckContractStatusForOrder(data.UserID, data.ProductRuleID)
	if err != nil {
		s.logger.Errorf("检查合同状态失败: %v", err)
		return nil, errors.New(err.Error())
	}

	if !contractResult.CanCreateOrder {
		s.logger.Infof("合同状态不允许创建订单，用户ID: %d, 产品ID: %d, 原因: %s",
			data.UserID, data.ProductRuleID, contractResult.Message)
		return nil, fmt.Errorf("合同状态为: %s", contractResult.Message)
	}

	s.logger.Infof("合同状态检查通过，用户ID: %d, 产品ID: %d, 合同ID: %d",
		data.UserID, data.ProductRuleID, contractResult.ContractID)

	// 输入参数验证
	if err := s.validateBasicParams(data.UserID, data.LoanAmount, data.ProductRuleID, data.ChannelCode); err != nil {
		return nil, err
	}

	// 创建超时上下文（10秒超时）
	ctx1, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	data.ContractID = contractResult.ContractID

	riskResult := &risk.RiskEvaluationResponse{}

	// 在goroutine中监控超时
	done := make(chan struct{})
	go func() {
		defer close(done)
		order, err = s.createOrderInternal(ctx1, data, riskResult)
	}()

	select {
	case <-done:
		if err != nil {
			return nil, err
		}
	case <-ctx.Done():
		return nil, errors.New("创建订单超时")
	}

	isAutoDisbursement, _, err := CheckChannelAutoDisbursement(order.ChannelID)

	if err != nil {
		return order, fmt.Errorf("检查渠道配置失败: %v", err)
	}

	// 5. 根据风控结果决定后续流程
	if riskResult.RiskResult == model.REJECTED {
		// 风控不通过，关闭订单
		err = s.CloseOrderWithReason(int64(order.ID), SystemOperatorID, "风控未通过，系统关单", fmt.Sprintf("%d", riskResult.RiskResult))
		if err != nil {
			log.Order().WithFields(
				log.OrderID(order.ID),
				log.String("reason", "risk_rejected"),
				log.Int("risk_result", int(riskResult.RiskResult)),
				log.ErrorField(err),
			).Error("关闭订单失败")
		}
		order.Status = model.OrderStatusClosed
		return order, fmt.Errorf("风控检查未通过: %d", riskResult.RiskResult)
	}

	// 6. 风控通过，根据渠道配置决定放款方式
	if isAutoDisbursement && riskResult.RiskResult == model.APPROVED {
		// 自动放款：风控通过直接放款
		operatorName := "系统自动"
		_, err = s.executeDisbursement(order.OrderNo, SystemOperatorID, operatorName, DisbursementTypeAuto)
		if err != nil {
			return order, err
		}
	}

	return order, nil

}

// validateBasicParams 验证基础参数
func (s *Service) validateBasicParams(userID int, loanAmount float64, productRuleID int, channelCode string) error {
	if userID <= 0 {
		return errors.New("用户ID无效")
	}
	if loanAmount <= 0 {
		return errors.New("贷款金额必须大于0")
	}
	if loanAmount > 999999999.99 {
		return errors.New("贷款金额超出限制")
	}

	if productRuleID <= 0 {
		return errors.New("产品规则ID无效")
	}
	if channelCode == "" {
		return errors.New("渠道编码无效")
	}

	return nil
}

// validatePaymentChannel 验证支付渠道
func (s *Service) validatePaymentChannel(paymentChannelID int) error {
	if paymentChannelID <= 0 {
		return errors.New("支付渠道ID无效")
	}

	paymentChannel, err := s.paymentModel.GetPaymentChannelByID(paymentChannelID)
	if err != nil {
		return fmt.Errorf("获取支付渠道失败: %v", err)
	}

	if paymentChannel.Status != model.PaymentChannelStatusActive {
		return fmt.Errorf("支付渠道已停用")
	}

	return nil
}

// getUserChannelID 获取用户的渠道ID
func (s *Service) getUserChannelID(userID int) (int, error) {
	if userID <= 0 {
		return 0, errors.New("用户ID无效")
	}

	// 查询用户的渠道ID
	result, err := model.DB().Table("business_app_account").
		Where("id", userID).
		Fields("channelId").
		First()
	if err != nil {
		return 0, fmt.Errorf("查询用户渠道信息失败: %v", err)
	}

	if result == nil {
		return 0, fmt.Errorf("用户不存在")
	}

	// 获取 channelId 字段值
	channelIDStr := convert.GetStringFromMap(result, "channelId")
	if channelIDStr == "" {
		return 0, nil
	}

	// 将字符串转换为整数
	channelID := convert.MustConvertToInt(channelIDStr, 0)
	if channelID <= 0 {
		return 0, fmt.Errorf("用户渠道ID无效: %s", channelIDStr)
	}

	return channelID, nil
}

// generateOrderNoWithRetry 生成订单编号
func (s *Service) generateOrderNoWithRetry(maxRetries int) (string, error) {
	for i := 0; i < maxRetries; i++ {
		orderNo, err := s.generateOrderNo()
		if err == nil {
			return orderNo, nil
		}
		if i == maxRetries-1 {
			return "", fmt.Errorf("生成订单编号失败，已重试%d次: %v", maxRetries, err)
		}
		// 短暂等待后重试
		time.Sleep(time.Millisecond * 10)
	}
	return "", errors.New("生成订单编号失败")
}

// generateOrderNo 生成订单编号
func (s *Service) generateOrderNo() (string, error) {
	// 格式: LO + YYYYMMDD + 8位随机字符
	dateStr := time.Now().Format("20060102")
	randomStr := strings.ToUpper(gf.RandString(8))
	orderNo := fmt.Sprintf("LO%s%s", dateStr, randomStr)

	// 检查订单号是否已存在
	existingOrder, err := s.orderModel.GetOrderByOrderNo(orderNo)
	if err != nil {
		return "", fmt.Errorf("检查订单号唯一性失败: %v", err)
	}
	if existingOrder != nil {
		return "", errors.New("订单号已存在")
	}

	return orderNo, nil
}

// getProductRuleByAmount 通过额度匹配产品规则
func (s *Service) getProductRuleByAmount(loanAmount float64) (*model.ProductRules, error) {
	s.logger.WithFields(
		log.Amount(loanAmount),
		log.String("operation", "match_product_rule"),
	).Debug("通过额度匹配产品规则")

	// 查询所有有效的产品规则
	productRules, err := s.productRuleModel.GetActiveProductRules()
	if err != nil {
		return nil, fmt.Errorf("查询产品规则失败: %v", err)
	}

	if len(productRules) == 0 {
		return nil, errors.New("没有可用的产品规则")
	}

	// 寻找匹配的产品规则
	for _, rule := range productRules {
		minAmount := rule.LoanAmount
		maxAmount := rule.LoanAmount

		if loanAmount >= minAmount && loanAmount <= maxAmount {
			s.logger.WithFields(
				log.Int("rule_id", int(rule.ID)),
				log.String("rule_name", rule.RuleName),
				log.Float64("standard_amount", rule.LoanAmount),
				log.Amount(loanAmount),
				log.String("match_type", "range_match"),
			).Info("产品规则匹配成功")
			return &rule, nil
		}
	}

	// 如果没有找到匹配的规则，尝试精确匹配
	for _, rule := range productRules {
		if rule.LoanAmount == loanAmount {
			s.logger.WithFields(
				log.Int("rule_id", int(rule.ID)),
				log.String("rule_name", rule.RuleName),
				log.Amount(rule.LoanAmount),
				log.String("match_type", "exact_match"),
			).Info("产品规则精确匹配成功")
			return &rule, nil
		}
	}

	return nil, fmt.Errorf("申请金额%.2f元没有匹配的产品规则", loanAmount)
}

// calculateProductRuleAmountsWithValidation 计算产品规则金额（支持前置还款和后置还款）
func (s *Service) calculateProductRuleAmountsWithValidation(loanAmount float64, productRule *model.ProductRules) (repayment.OrderAmounts, error) {
	var amounts repayment.OrderAmounts

	if loanAmount <= 0 {
		return amounts, errors.New("贷款金额必须大于0")
	}
	if productRule == nil {
		return amounts, errors.New("产品规则不能为空")
	}

	amounts = repayment.CalculateOrderAmounts(loanAmount, productRule)

	// 验证计算结果的合理性
	if amounts.TotalRepayableAmount <= amounts.Principal {
		return amounts, errors.New("总还款金额不能小于等于本金")
	}
	if amounts.TotalRepayableAmount > amounts.Principal*10 { // 防止异常高的费用
		return amounts, errors.New("总还款金额异常，请检查产品规则配置")
	}

	// 前置还款时，验证放款金额不能为负数
	if amounts.IsPrePayment && amounts.DisbursementAmount <= 0 {
		return amounts, errors.New("前置还款模式下，扣除费用后的放款金额不能为负数或零")
	}

	return amounts, nil
}

// createOrderWithTransactionContext 使用事务和上下文创建订单和还款计划
func (s *Service) createOrderWithTransactionContext(
	ctx context.Context,
	order *model.BusinessLoanOrders,
	productRule *model.ProductRules,
	riskResult *risk.RiskEvaluationResponse,
) error {
	// 参数验证
	if order == nil {
		return errors.New("订单对象不能为空")
	}
	if productRule == nil {
		return errors.New("产品规则对象不能为空")
	}

	db := model.DB(model.WithContext(s.ctx))
	return db.Transaction(func(tx gform.IOrm) error {

		// 1. 在事务中创建订单
		err := s.createOrderInTransaction(tx, order)
		if err != nil {
			return fmt.Errorf("创建订单失败: %v", err)
		}

		// 2. 在事务中生成还款计划
		err = s.createRepaymentScheduleInTransaction(tx, int(order.ID), int(order.UserID), productRule)
		if err != nil {
			return fmt.Errorf("生成还款计划失败: %v", err)
		}

		// 执行风控检查
		riskResp, err := risk.NewRiskService(s.ctx).EvaluateRiskWithProductTx(ctx, tx, risk.EvaluateRiskWithProductParams{
			CustomerID: uint64(order.UserID),
			ProductID:  uint64(order.ProductRuleID),
			ChannelID:  uint64(order.ChannelID),
		})

		if err != nil {
			return fmt.Errorf("风控检查失败: %v", err)
		}

		*riskResult = *riskResp

		// 更新订单风控结果
		err = s.updateOrderRiskResult(tx, int64(order.ID), riskResult)
		if err != nil {
			return fmt.Errorf("更新订单风控结果失败: %v", err)
		}

		// 3. 在事务中创建初始操作日志
		err = s.createOperationLogInTransaction(tx, int(order.ID), 0, "系统", "创建订单",
			fmt.Sprintf("订单编号: %s, 申请金额: %.2f", order.OrderNo, float64(order.LoanAmount)))
		if err != nil {
			// 日志创建失败不影响主流程，只记录错误
			s.logger.WithFields(
				log.OrderID(order.ID),
				log.String("order_no", order.OrderNo),
				log.String("operation", "create_operation_log_in_transaction"),
				log.ErrorField(err),
			).Error("创建操作日志失败")
		}

		return nil
	})
}

// createOrderInTransaction 在事务中创建订单
func (s *Service) createOrderInTransaction(tx gform.IOrm, order *model.BusinessLoanOrders) error {
	if order == nil {
		return errors.New("订单对象不能为空")
	}

	// 验证必要字段
	if order.OrderNo == "" {
		return errors.New("订单编号不能为空")
	}
	if order.UserID <= 0 {
		return errors.New("用户ID无效")
	}
	if order.LoanAmount <= 0 {
		return errors.New("贷款金额必须大于0")
	}

	// 设置更新时间
	now := time.Now()
	order.UpdatedAt = &now

	// 构建插入数据
	data := map[string]interface{}{
		"order_no":                 order.OrderNo,
		"user_id":                  order.UserID,
		"product_rule_id":          order.ProductRuleID,
		"loan_amount":              order.LoanAmount,
		"principal":                order.Principal,
		"total_interest":           order.TotalInterest,
		"total_guarantee_fee":      order.TotalGuaranteeFee,
		"total_other_fees":         order.TotalOtherFees,
		"total_repayable_amount":   order.TotalRepayableAmount,
		"payment_channel_id":       *order.PaymentChannelID,
		"amount_paid":              order.AmountPaid,
		"channel_id":               order.ChannelID,
		"customer_origin":          order.CustomerOrigin,
		"initial_order_channel_id": order.InitialOrderChannelID,
		"status":                   order.Status,
		"is_freeze":                order.IsFreeze,
		"is_refund_needed":         order.IsRefundNeeded,
		"review_status":            order.ReviewStatus,
		"contract_id":              order.ContractID,
		"created_at":               now, // 使用 created_at
		"updated_at":               now,
	}

	// 在事务中插入订单数据
	insertID, err := tx.Table("business_loan_orders").InsertGetId(data)
	if err != nil {
		return fmt.Errorf("插入订单数据失败: %v", err)
	}

	// 设置订单ID
	order.ID = uint(insertID)

	return nil
}

// createRepaymentScheduleInTransaction 在事务中创建还款计划
func (s *Service) createRepaymentScheduleInTransaction(tx gform.IOrm, orderID, userID int, productRule *model.ProductRules) error {
	if orderID <= 0 || userID <= 0 {
		return errors.New("订单ID或用户ID无效")
	}
	if productRule == nil {
		return errors.New("产品规则对象不能为空")
	}

	// 使用公共方法计算还款计划
	schedule, err := repayment.CalculateRepaymentSchedule(productRule)
	if err != nil {
		return fmt.Errorf("计算还款计划失败: %v", err)
	}

	// 将还款计划转换为数据库插入用的 map 数据
	billMaps := repayment.ConvertToRepaymentBillMaps(schedule, orderID, userID)

	// 在事务中插入还款账单
	for i, billMap := range billMaps {
		_, err := tx.Table("business_repayment_bills").InsertGetId(billMap)
		if err != nil {
			return fmt.Errorf("创建第%d期还款计划失败: %v", i+1, err)
		}
	}

	return nil
}

// createOrderInternal 内部创建订单方法
func (s *Service) createOrderInternal(ctx context.Context, data CreateOrderParams, riskResult *risk.RiskEvaluationResponse) (*model.BusinessLoanOrders, error) {
	// 1. 获取并验证参数
	userID := data.UserID
	productRuleID := data.ProductRuleID
	loanAmount := data.LoanAmount
	channelCode := data.ChannelCode
	paymentChannelID := data.PaymentChannelID
	customerOrigin := data.CustomerOrigin

	// 设置支付渠道默认值为 1
	if paymentChannelID == 0 {
		paymentChannelID = 1
	}

	// 2. 获取用户的初始渠道ID（从 business_app_account.channelId 字段）
	initialChannelID, err := s.getUserChannelID(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户初始渠道ID失败: %v", err)
	}

	// 创建前并发检查
	var (
		tasks       []func() error
		g           *errgroup.Group
		productRule *model.ProductRules
		orderNo     string
		amounts     repayment.OrderAmounts
		channel     model.Channel
	)

	// 通过额度匹配产品规则（如果未指定产品规则ID）
	if productRuleID == 0 {
		productRule, err = s.getProductRuleByAmount(loanAmount)
		if err != nil {
			return nil, fmt.Errorf("通过额度匹配产品规则失败: %v", err)
		}
	} else {
		productRule, err = s.productRuleModel.GetProductRuleByID(productRuleID)
		if err != nil {
			return nil, fmt.Errorf("获取产品规则失败: %v", err)
		}
	}

	// 验证支付渠道（现在总是有默认值）
	tasks = append(tasks, func() error {
		if err := s.validatePaymentChannel(paymentChannelID); err != nil {
			return fmt.Errorf("支付渠道验证失败: %v", err)
		}
		return nil
	})

	// 生成订单编号
	tasks = append(tasks, func() error {
		orderNo, err = s.generateOrderNoWithRetry(5)
		if err != nil {
			return fmt.Errorf("生成订单编号失败: %v", err)
		}
		return nil
	})

	// 计算订单金额（支持前置还款和后置还款）
	tasks = append(tasks, func() error {
		amounts, err = s.calculateProductRuleAmountsWithValidation(loanAmount, productRule)
		if err != nil {
			return fmt.Errorf("费用计算失败: %v", err)
		}
		return nil
	})

	// 获取渠道信息
	tasks = append(tasks, func() error {
		channel, err = s.channelModel.GetChannelByCode(channelCode)
		if err != nil {
			return fmt.Errorf("获取渠道信息失败: %v", err)
		}
		return nil
	})

	// 检查用户订单限制（检查数量和金额）
	tasks = append(tasks, func() error {
		if err := ValidateUserOrderLimit(userID, loanAmount); err != nil {
			return fmt.Errorf("用户订单限制检查失败: %v", err)
		}
		return nil
	})

	// 检查当前合同是否生成过订单
	tasks = append(tasks, func() error {
		order, err := s.orderModel.GetOrderByContractID(data.ContractID)
		if err != nil {
			return fmt.Errorf("查询订单失败: %v", err)
		}
		if order != nil {
			return fmt.Errorf("当前合同已生成过订单,请稍候查看订单状态")
		}
		return nil
	})

	// 执行并发任务
	if len(tasks) > 0 {
		// 创建新的上下文，避免errgroup取消外部上下文
		ctx1, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		g, _ = errgroup.WithContext(ctx1)

		for _, task := range tasks {
			t := task // 避免闭包问题
			g.Go(t)
		}

		if err := g.Wait(); err != nil {
			return nil, err
		}

		// 检查外部上下文是否被取消
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}
	}

	// 7. 构建订单对象
	order := &model.BusinessLoanOrders{
		OrderNo:               orderNo,
		UserID:                uint(userID),
		ProductRuleID:         uint(productRuleID),
		LoanAmount:            decimal.Decimal(loanAmount),
		Principal:             decimal.Decimal(amounts.Principal),
		TotalInterest:         decimal.Decimal(amounts.TotalInterest),
		TotalGuaranteeFee:     decimal.Decimal(amounts.TotalGuaranteeFee),
		TotalOtherFees:        decimal.Decimal(amounts.TotalOtherFees),
		TotalRepayableAmount:  decimal.Decimal(amounts.TotalRepayableAmount),
		AmountPaid:            0.00,
		ChannelID:             channel.ID, // 渠道ID
		CustomerOrigin:        customerOrigin,
		InitialOrderChannelID: initialChannelID,                           // 初始订单渠道ID
		Status:                int8(model.OrderStatusPendingDisbursement), // 待放款
		IsFreeze:              0,                                          // 是否冻结
		IsRefundNeeded:        0,                                          // 是否需要退款
		ReviewStatus:          0,                                          // 审核状态
		ContractID:            int32(data.ContractID),                     // 合同ID
	}

	// 设置支付渠道ID（现在总是有值，默认为1）
	channelIDUint := uint(paymentChannelID)
	order.PaymentChannelID = &channelIDUint

	// 8. 使用事务创建订单和还款计划
	err = s.createOrderWithTransactionContext(ctx, order, productRule, riskResult)
	if err != nil {
		return nil, fmt.Errorf("创建订单失败: %v", err)
	}

	// 9. 异步记录操作日志（不影响主流程）
	go func() {
		defer func() {
			if r := recover(); r != nil {
				s.logger.WithFields(
					log.OrderID(order.ID),
					log.String("operation", "async_operation_log"),
					log.Any("panic_info", r),
				).Error("异步记录操作日志发生panic")
			}
		}()

		// 记录订单创建信息
		disbursementInfo := fmt.Sprintf("放款金额: %.2f (本金+利息+担保费+其他费用)", amounts.TotalRepayableAmount)
		repaymentInfo := fmt.Sprintf("还款金额: %.2f (资管费+担保费)", amounts.TotalOtherFees+amounts.TotalGuaranteeFee)
		channelInfo := fmt.Sprintf("渠道ID: %d, 初始渠道ID: %d", channel.ID, initialChannelID)
		if amounts.IsPrePayment {
			disbursementInfo = fmt.Sprintf("前置还款，实际放款金额: %.2f", amounts.DisbursementAmount)
		}

		s.createOperationLog(int(order.ID), 0, "系统", "创建订单",
			fmt.Sprintf("订单编号: %s, 产品规则: %s, %s, %s, %s",
				orderNo, productRule.RuleName, disbursementInfo, repaymentInfo, channelInfo))
	}()

	// 10. 返回创建的订单信息
	createdOrder, err := s.orderModel.GetOrderByID(nil, int(order.ID))
	if err != nil {
		// 订单已创建成功，但查询失败，记录错误但不返回失败
		s.logger.WithFields(
			log.OrderID(order.ID),
			log.String("operation", "query_created_order"),
			log.ErrorField(err),
		).Warn("订单创建成功但查询失败")
		return order, nil // 返回原始订单对象
	}

	return createdOrder, nil
}
