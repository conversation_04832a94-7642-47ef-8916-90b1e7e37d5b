package model

import (
	"fincore/utils/convert"
)

// BusinessOrderRemarks 订单备注表模型
type BusinessOrderRemarks struct {
	ID         int64  `json:"id" db:"id"`                   // 备注ID
	OrderID    int64  `json:"order_id" db:"order_id"`       // 订单ID，关联business_loan_orders表
	Content    string `json:"content" db:"content"`         // 备注内容
	UserID     int64  `json:"user_id" db:"user_id"`         // 备注人用户ID，关联business_account表
	CreateTime int64  `json:"create_time" db:"create_time"` // 备注时间（Unix时间戳）
	UpdateTime int64  `json:"update_time" db:"update_time"` // 更新时间（Unix时间戳）
}

// OrderRemarkWithUser 订单备注与用户信息的联合查询结果
type OrderRemarkWithUser struct {
	ID         int64  `json:"id" db:"id"`                   // 备注ID
	OrderID    int64  `json:"order_id" db:"order_id"`       // 订单ID
	Content    string `json:"content" db:"content"`         // 备注内容
	UserID     int64  `json:"user_id" db:"user_id"`         // 备注人用户ID
	UserName   string `json:"user_name" db:"user_name"`     // 备注人姓名
	CreateTime int64  `json:"create_time" db:"create_time"` // 备注时间
}

// BusinessOrderRemarksService 订单备注服务
type BusinessOrderRemarksService struct{}

func NewBusinessOrderRemarksService() *BusinessOrderRemarksService {
	return &BusinessOrderRemarksService{}
}

// GetRemarksByOrderID 根据订单ID获取备注列表
// 使用JOIN查询优化性能，避免N+1查询问题
// 返回按创建时间倒序排列的备注列表
func (s *BusinessOrderRemarksService) GetRemarksByOrderID(orderID int64) ([]OrderRemarkWithUser, error) {
	query := `
		SELECT
			r.id,
			r.order_id,
			r.content,
			r.user_id,
			COALESCE(u.username, '') as user_name,
			r.create_time
		FROM business_order_remarks r
		LEFT JOIN business_account u ON r.user_id = u.id
		WHERE r.order_id = ?
		ORDER BY r.create_time DESC
	`

	results, err := DB().Query(query, orderID)
	if err != nil {
		return nil, err
	}

	var remarks []OrderRemarkWithUser
	for _, result := range results {
		remark := OrderRemarkWithUser{
			ID:         convert.DirectConvertToInt64(result["id"]),
			OrderID:    convert.DirectConvertToInt64(result["order_id"]),
			Content:    convert.DirectConvertToString(result["content"]),
			UserID:     convert.DirectConvertToInt64(result["user_id"]),
			UserName:   convert.DirectConvertToString(result["user_name"]),
			CreateTime: convert.DirectConvertToInt64(result["create_time"]),
		}
		remarks = append(remarks, remark)
	}

	return remarks, nil
}
