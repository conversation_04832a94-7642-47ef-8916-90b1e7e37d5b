package results

import (
	"encoding/json"
	"fincore/route/middleware"
	"fincore/utils/log"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// 人脸识别，由于URL不能转义，所以重新写一个Success方法
// 请求成功的时候 使用该方法返回信息
func SuccessPureJson(ctx *gin.Context, msg string, data interface{}, exdata interface{}) {
	dataJson, _ := json.Marshal(data)
	exdataJson, _ := json.Marshal(exdata)
	log.WithContext(ctx).WithFields(
		log.String("data", string(dataJson)),
		log.String("exdata", string(exdataJson)),
	).Debug(fmt.Sprintf("请求成功: msg: %s", msg))
	token := ctx.Request.Header.Get("Authorization")
	var newtoken string
	if token != "" {
		tockenarr := middleware.Refresh(token)
		if tockenarr != "" {
			newtoken = tockenarr
		}
	}
	ctx.PureJSON(http.StatusOK, gin.H{
		"code":    0,
		"message": msg,
		"data":    data,
		"exdata":  exdata,
		"token":   newtoken,
		"time":    time.Now().Unix(),
	})
}

// 请求成功的时候 使用该方法返回信息
func Success(ctx *gin.Context, msg string, data interface{}, exdata interface{}) {
	dataJson, _ := json.Marshal(data)
	exdataJson, _ := json.Marshal(exdata)
	log.WithContext(ctx).WithFields(
		log.String("data", string(dataJson)),
		log.String("exdata", string(exdataJson)),
	).Debug(fmt.Sprintf("请求成功: msg: %s", msg))
	token := ctx.Request.Header.Get("Authorization")
	var newtoken string
	if token != "" {
		tockenarr := middleware.Refresh(token)
		if tockenarr != "" {
			newtoken = tockenarr
		}
	}
	ctx.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": msg,
		"data":    data,
		"exdata":  exdata,
		"token":   newtoken,
		"time":    time.Now().Unix(),
	})
}

// 请求成功的时候 使用该方法返回信息
func SuccessLogin(ctx *gin.Context, msg string, data interface{}, token, exdata interface{}) {
	dataJson, _ := json.Marshal(data)
	exdataJson, _ := json.Marshal(exdata)
	log.WithContext(ctx).WithFields(
		log.String("data", string(dataJson)),
		log.String("exdata", string(exdataJson)),
	).Debug(fmt.Sprintf("登录成功: msg: %s", msg))
	ctx.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": msg,
		"data":    data,
		"token":   token,
		"exdata":  exdata,
		"time":    time.Now().Unix(),
	})
}

// 请求失败的时候, 使用该方法返回信息
func Failed(ctx *gin.Context, msg string, data interface{}) {
	dataJson, _ := json.Marshal(data)
	log.WithContext(ctx).WithFields(
		log.String("data", string(dataJson)),
	).Info(fmt.Sprintf("请求失败: msg: %s", msg))
	ctx.JSON(http.StatusOK, gin.H{
		"code":    1,
		"message": msg,
		"data":    data,
		"time":    time.Now().Unix(),
	})
}

// 请求失败的时候, 使用该方法返回信息
func FailedWithCode(ctx *gin.Context, msg string, data interface{}, code int) {
	dataJson, _ := json.Marshal(data)
	log.WithContext(ctx).WithFields(
		log.String("data", string(dataJson)),
	).Info(fmt.Sprintf("请求失败: msg: %s", msg))
	ctx.JSON(http.StatusOK, gin.H{
		"code":    code,
		"message": msg,
		"data":    data,
		"time":    time.Now().Unix(),
	})
}

// TtfSuccessCallback 统统付回调接口成功响应
func TtfSuccessCallback(ctx *gin.Context, data interface{}) {
	msg := "处理成功"
	dataJson, _ := json.Marshal(data)
	log.WithContext(ctx).WithFields(
		log.String("data", string(dataJson)),
	).Debug(fmt.Sprintf("统统付回调接口成功: msg: %s", msg))
	ctx.JSON(http.StatusOK, gin.H{
		"resp_code": "000000",
		"resp_msg":  "success",
	})
}

// TtfFailedCallback 统统付回调接口失败响应
func TtfFailedCallback(ctx *gin.Context, err error, data interface{}) {
	msg := "处理失败"
	dataJson, _ := json.Marshal(data)
	log.WithContext(ctx).WithFields(
		log.String("err", err.Error()),
		log.String("data", string(dataJson)),
	).Info(fmt.Sprintf("统统付回调接口失败: msg: %s", msg))
	ctx.JSON(http.StatusOK, gin.H{
		"resp_code": "000001",
		"resp_msg":  msg,
	})
}
