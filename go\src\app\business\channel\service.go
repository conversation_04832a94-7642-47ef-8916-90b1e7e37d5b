package channel

import (
	"encoding/base64"
	"fincore/global"
	"fincore/model"
	"fincore/utils/convert"
	"fincore/utils/log"
	"fincore/utils/pagination"
	"fmt"
	"net/url"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/skip2/go-qrcode"
)

// ChannelService 渠道服务
type ChannelService struct {
	modelService *model.ChannelService
}

// NewChannelService 创建渠道服务实例
func NewChannelService() *ChannelService {
	return &ChannelService{
		modelService: model.NewChannelService(),
	}
}

// GetChannelList 获取渠道列表
func (s *ChannelService) GetChannelList(ctx *gin.Context) (*pagination.PaginationResponse, error) {
	// 解析查询参数
	filters := make(map[string]interface{})

	if channelName := ctx.Query("channel_name"); channelName != "" {
		filters["channel_name"] = channelName
	}
	if channelCode := ctx.Query("channel_code"); channelCode != "" {
		filters["channel_code"] = channelCode
	}
	if channelStatus := ctx.Query("channel_status"); channelStatus != "" {
		filters["channel_status"] = channelStatus
	}
	if channelUsage := ctx.Query("channel_usage"); channelUsage != "" {
		filters["channel_usage"] = channelUsage
	}
	if mobile := ctx.Query("mobile"); mobile != "" {
		filters["mobile"] = mobile
	}

	// 解析分页参数
	page := 1
	pageSize := 10

	if pageStr := ctx.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}
	if pageSizeStr := ctx.Query("pageSize"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 {
			pageSize = ps
		}
	}

	paginationReq := pagination.PaginationRequest{
		Page:     page,
		PageSize: pageSize,
	}

	// 调用Model层方法
	return s.modelService.GetChannelList(filters, paginationReq)
}

// GetChannelDetail 获取渠道详情
func (s *ChannelService) GetChannelDetail(id int) (*model.Channel, error) {
	return s.modelService.GetChannelByID(id)
}

// CreateChannel 创建渠道
func (s *ChannelService) CreateChannel(data map[string]interface{}) (*model.Channel, error) {
	// 构建Channel对象
	channel := &model.Channel{}

	// 基本信息
	if channelName, ok := data["channel_name"].(string); ok {
		channel.ChannelName = channelName
	}

	// 如果没有提供渠道编码，自动生成
	if channelCode, ok := data["channel_code"].(string); ok && channelCode != "" {
		channel.ChannelCode = channelCode
	} else {
		// 自动生成渠道编码
		generatedCode, err := s.modelService.GenerateChannelCode()
		if err != nil {
			return nil, fmt.Errorf("生成渠道编码失败: %v", err)
		}
		channel.ChannelCode = generatedCode
	}

	if mobile, ok := data["mobile"].(string); ok {
		channel.Mobile = mobile
	}

	// 风控级别和自动标签
	if riskLevel, ok := data["risk_level"]; ok {
		if level, ok := riskLevel.(float64); ok {
			channel.RiskLevel = int(level)
		}
	}
	if autoLabel, ok := data["auto_label"]; ok {
		if label, ok := autoLabel.(float64); ok {
			channel.AutoLabel = int(label)
		}
	}

	// 状态字段
	if channelStatus, ok := data["channel_status"]; ok {
		if status, ok := channelStatus.(float64); ok {
			channel.ChannelStatus = int(status)
		}
	}
	if loanRules, ok := data["loan_rules"].(string); ok {
		channel.LoanRules = loanRules
	}

	// 风控规则
	if riskControl1Limit, ok := data["risk_control_1_limit"]; ok {
		if limit, ok := riskControl1Limit.(float64); ok {
			channel.RiskControl1Limit = limit
		}
	}
	if riskControl1Upper, ok := data["risk_control_1_upper"]; ok {
		if upper, ok := riskControl1Upper.(float64); ok {
			channel.RiskControl1Upper = upper
		}
	}
	if riskControl2Limit, ok := data["risk_control_2_limit"]; ok {
		if limit, ok := riskControl2Limit.(float64); ok {
			channel.RiskControl2Limit = limit
		}
	}
	if riskControl2Upper, ok := data["risk_control_2_upper"]; ok {
		if upper, ok := riskControl2Upper.(float64); ok {
			channel.RiskControl2Upper = upper
		}
	}

	// 节点额度
	if pointAmount1, ok := data["point_amount_1"]; ok {
		if amount, ok := pointAmount1.(float64); ok {
			channel.PointAmount1 = amount
		}
	} else {
		channel.PointAmount1 = 1000
	}
	if pointAmount2, ok := data["point_amount_2"]; ok {
		if amount, ok := pointAmount2.(float64); ok {
			channel.PointAmount2 = amount
		}
	} else {
		channel.PointAmount2 = 2000
	}
	if totalAmount, ok := data["total_amount"]; ok {
		if amount, ok := totalAmount.(float64); ok {
			channel.TotalAmount = amount
		}
	}

	// 备注
	if remark, ok := data["remark"].(string); ok {
		channel.Remark = remark
	}

	// 调用Model层创建渠道
	err := s.modelService.CreateChannel(channel)
	if err != nil {
		return nil, err
	}

	// 返回创建的渠道信息
	return s.modelService.GetChannelByCode(channel.ChannelCode)
}

// UpdateChannel 更新渠道
func (s *ChannelService) UpdateChannel(id int, data map[string]interface{}) (*model.Channel, error) {
	// 构建Channel对象
	channel := &model.Channel{}

	// 基本信息
	if channelName, ok := data["channel_name"].(string); ok {
		channel.ChannelName = channelName
	}
	// 不再处理 channel_code
	if mobile, ok := data["mobile"].(string); ok {
		channel.Mobile = mobile
	}

	// 风控级别和自动标签
	if riskLevel, ok := data["risk_level"]; ok {
		if level, ok := riskLevel.(float64); ok {
			channel.RiskLevel = int(level)
		}
	}
	if autoLabel, ok := data["auto_label"]; ok {
		if label, ok := autoLabel.(float64); ok {
			channel.AutoLabel = int(label)
		}
	}

	// 状态字段
	if channelStatus, ok := data["channel_status"]; ok {
		if status, ok := channelStatus.(float64); ok {
			channel.ChannelStatus = int(status)
		}
	}
	if loanRules, ok := data["loan_rules"].(string); ok {
		channel.LoanRules = loanRules
	}

	// 风控规则
	if riskControl1Limit, ok := data["risk_control_1_limit"]; ok {
		if limit, ok := riskControl1Limit.(float64); ok {
			channel.RiskControl1Limit = limit
		}
	}
	if riskControl1Upper, ok := data["risk_control_1_upper"]; ok {
		if upper, ok := riskControl1Upper.(float64); ok {
			channel.RiskControl1Upper = upper
		}
	}
	if riskControl2Limit, ok := data["risk_control_2_limit"]; ok {
		if limit, ok := riskControl2Limit.(float64); ok {
			channel.RiskControl2Limit = limit
		}
	}
	if riskControl2Upper, ok := data["risk_control_2_upper"]; ok {
		if upper, ok := riskControl2Upper.(float64); ok {
			channel.RiskControl2Upper = upper
		}
	}

	// 节点额度
	if pointAmount1, ok := data["point_amount_1"]; ok {
		if amount, ok := pointAmount1.(float64); ok {
			channel.PointAmount1 = amount
		}
	}
	if pointAmount2, ok := data["point_amount_2"]; ok {
		if amount, ok := pointAmount2.(float64); ok {
			channel.PointAmount2 = amount
		}
	}
	if totalAmount, ok := data["total_amount"]; ok {
		if amount, ok := totalAmount.(float64); ok {
			channel.TotalAmount = amount
		}
	}

	// 备注
	if remark, ok := data["remark"].(string); ok {
		channel.Remark = remark
	}

	// 调用Model层更新渠道
	err := s.modelService.UpdateChannel(id, channel)
	if err != nil {
		return nil, err
	}

	// 返回更新后的渠道信息
	return s.modelService.GetChannelByID(id)
}

// DeleteChannel 删除渠道
func (s *ChannelService) DeleteChannel(id int) error {
	return s.modelService.DeleteChannel(id)
}

// GetChannelOptions 获取渠道选项
func (s *ChannelService) GetChannelOptions() ([]map[string]interface{}, error) {
	channelService := model.NewChannelService()
	data, err := channelService.GetChannelOptions()
	if err != nil {
		return nil, fmt.Errorf("获取渠道选项失败: %v", err)
	}

	// 转换为前端需要的格式
	result := make([]map[string]interface{}, len(data))
	for i, item := range data {
		result[i] = map[string]interface{}{
			"value": item.ID,
			"label": item.Name,
			"id":    item.ID,
			"name":  item.Name,
		}
	}

	return result, nil
}

// GetChannelLoanRules 获取渠道放款规则（包含产品规则信息）
func (s *ChannelService) GetChannelLoanRules(channelID int) ([]model.LoanRule, error) {
	// 获取渠道信息
	channel, err := s.modelService.GetChannelByID(channelID)
	if err != nil {
		return nil, fmt.Errorf("获取渠道信息失败: %v", err)
	}

	// 获取放款规则（包含产品规则信息）
	rules, err := channel.GetLoanRules()
	if err != nil {
		return nil, fmt.Errorf("获取放款规则失败: %v", err)
	}

	return rules, nil
}

// UpdateChannelLoanRules 更新渠道放款规则
func (s *ChannelService) UpdateChannelLoanRules(channelID int, rulesData []interface{}) error {
	// 获取渠道信息
	channel, err := s.modelService.GetChannelByID(channelID)
	if err != nil {
		return fmt.Errorf("获取渠道信息失败: %v", err)
	}

	// 转换规则数据
	var rules []model.LoanRule
	for _, ruleData := range rulesData {
		if ruleMap, ok := ruleData.(map[string]interface{}); ok {
			rule := model.LoanRule{}

			// 解析规则ID
			if ruleID, ok := ruleMap["rule_id"].(float64); ok {
				rule.RuleID = int(ruleID)
			}

			// 解析风控阈值下限
			if minRiskScore, ok := ruleMap["min_risk_score"].(float64); ok {
				rule.MinRiskScore = minRiskScore
			}

			// 解析风控阈值上限
			if maxRiskScore, ok := ruleMap["max_risk_score"].(float64); ok {
				rule.MaxRiskScore = maxRiskScore
			}

			rules = append(rules, rule)
		}
	}

	// 设置放款规则
	err = channel.SetLoanRules(rules)
	if err != nil {
		return fmt.Errorf("设置放款规则失败: %v", err)
	}

	// 更新渠道信息
	err = s.modelService.UpdateChannel(channelID, channel)
	if err != nil {
		return fmt.Errorf("更新渠道失败: %v", err)
	}

	return nil
}

// GenerateNewChannelCode 生成新的渠道编码
func (s *ChannelService) GenerateNewChannelCode() (string, error) {
	channelService := model.NewChannelService()
	code, err := channelService.GenerateChannelCode()
	if err != nil {
		return "", fmt.Errorf("生成渠道编码失败: %v", err)
	}
	return code, nil
}

// GetChannelListByParams 根据参数获取渠道列表
func (s *ChannelService) GetChannelListByParams(params map[string]interface{}) (*pagination.PaginationResponse, error) {
	// 构建过滤条件
	filters := make(map[string]interface{})

	if channelName, ok := params["channel_name"]; ok {
		filters["channel_name"] = channelName
	}
	if channelCode, ok := params["channel_code"]; ok {
		filters["channel_code"] = channelCode
	}
	if channelStatus, ok := params["channel_status"]; ok {
		filters["channel_status"] = channelStatus
	}
	if channelUsage, ok := params["channel_usage"]; ok {
		filters["channel_usage"] = channelUsage
	}
	if mobile, ok := params["mobile"]; ok {
		filters["mobile"] = mobile
	}
	if startTime, ok := params["start_time"]; ok {
		filters["start_time"] = startTime
	}
	if endTime, ok := params["end_time"]; ok {
		filters["end_time"] = endTime
	}

	// 解析分页参数
	page := 1
	pageSize := 10

	if pageParam, ok := params["page"]; ok {
		if pageFloat, ok := pageParam.(float64); ok {
			page = int(pageFloat)
		}
	}

	if pageSizeParam, ok := params["pageSize"]; ok {
		if pageSizeFloat, ok := pageSizeParam.(float64); ok {
			pageSize = int(pageSizeFloat)
		}
	}

	paginationReq := pagination.PaginationRequest{
		Page:     page,
		PageSize: pageSize,
	}

	// 调用Model层方法
	return s.modelService.GetChannelList(filters, paginationReq)
}

// InvitationResult 邀请链接生成结果
type InvitationResult struct {
	InvitationURL string `json:"invitation_url"`
	QRCodeBase64  string `json:"qr_code_base64"`
	ChannelCode   string `json:"channel_code"`
}

// ChannelStatusResult 渠道状态结果
type ChannelStatusResult struct {
	ChannelId     int    `json:"channel_id"`
	ChannelCode   string `json:"channel_code"`
	ChannelStatus int    `json:"channel_status"`
	ChannelName   string `json:"channel_name"`
}

// GenerateInvitation 生成邀请链接和二维码
func (s *ChannelService) GenerateInvitation(data map[string]interface{}) (*InvitationResult, error) {
	log.Info("开始生成邀请链接")

	// 1. 从参数中获取数据
	channelID := convert.GetIntFromMap(data, "channel_id", 0)

	if channelID <= 0 {
		log.Error("生成邀请链接失败: 渠道ID无效 %d", channelID)
		return nil, fmt.Errorf("渠道ID无效")
	}

	// 2. 根据channelID查询渠道信息
	channel, err := s.modelService.GetChannelByID(channelID)
	if err != nil {
		log.Error("生成邀请链接失败: 查询渠道信息失败 ID=%d, err=%v", channelID, err)
		return nil, fmt.Errorf("查询渠道信息失败: %v", err)
	}

	if channel == nil {
		log.Error("生成邀请链接失败: 渠道不存在 ID=%d", channelID)
		return nil, fmt.Errorf("渠道不存在")
	}

	// 3. Base64编码渠道编码
	encodedChannelCode := base64.StdEncoding.EncodeToString([]byte(channel.ChannelCode))

	// 4. URL编码
	urlEncodedChannelCode := url.QueryEscape(encodedChannelCode)

	// 5. 从配置中构建H5域名
	config := global.App.Config.App
	h5Protocol := config.H5Protocol
	if h5Protocol == "" {
		h5Protocol = "https" // 默认使用https
	}
	h5Domain := fmt.Sprintf("%s://%s:%s", h5Protocol, config.Hostname, config.H5port)
	log.Info("使用配置的H5域名: %s", h5Domain)

	// 6. 获取邀请页面路径配置
	invitationPage := config.InvitationPage
	if invitationPage == "" {
		invitationPage = "/login" // 默认值
	}

	// 7. 生成完整URL
	invitationURL := fmt.Sprintf("%s%s?cid=%s", h5Domain, invitationPage, urlEncodedChannelCode)
	log.Info("生成邀请URL: %s", invitationURL)

	// 8. 生成二维码
	qrCodeBytes, err := qrcode.Encode(invitationURL, qrcode.Medium, 300)
	if err != nil {
		log.Error("生成二维码失败: %v", err)
		return nil, fmt.Errorf("生成二维码失败: %v", err)
	}

	// 9. 转换为Base64
	qrCodeBase64 := fmt.Sprintf("data:image/png;base64,%s", base64.StdEncoding.EncodeToString(qrCodeBytes))

	result := &InvitationResult{
		InvitationURL: invitationURL,
		QRCodeBase64:  qrCodeBase64,
		ChannelCode:   channel.ChannelCode,
	}

	log.Info("生成邀请链接成功: 渠道编码=%s, H5域名=%s, 页面路径=%s", channel.ChannelCode, h5Domain, invitationPage)
	return result, nil
}

// CheckChannelStatus 检查渠道状态
func (s *ChannelService) CheckChannelStatus(channelCode string) (*ChannelStatusResult, error) {
	log.Info("开始检查渠道状态: %s", channelCode)

	if channelCode == "" {
		log.Error("检查渠道状态失败: 渠道编码为空")
		return nil, fmt.Errorf("渠道编码不能为空")
	}

	// 根据channelCode查询channel表
	channel, err := s.modelService.GetChannelByCode(channelCode)
	if err != nil {
		log.Error("检查渠道状态失败: 查询渠道信息失败 code=%s, err=%v", channelCode, err)
		return nil, fmt.Errorf("查询渠道信息失败: %v", err)
	}

	if channel == nil {
		log.Error("检查渠道状态失败: 渠道不存在 code=%s", channelCode)
		return nil, fmt.Errorf("渠道不存在")
	}

	result := &ChannelStatusResult{
		ChannelId:     channel.ID,
		ChannelCode:   channel.ChannelCode,
		ChannelStatus: channel.ChannelStatus,
		ChannelName:   channel.ChannelName,
	}

	log.Info("检查渠道状态成功: code=%s, status=%d", channelCode, channel.ChannelStatus)
	return result, nil
}

// GetProductRules 获取产品规则列表
func (s *ChannelService) GetProductRules() ([]model.ProductRules, error) {
	productRuleService := model.NewProductRulesService()
	return productRuleService.GetActiveProductRules()
}
