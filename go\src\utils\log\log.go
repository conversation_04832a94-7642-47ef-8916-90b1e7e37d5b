package log

import (
	"context"
	"fincore/global"
	"fmt"
	"sync"

	"github.com/gin-gonic/gin"
)

// 默认日志器实例
var defaultLogger *Logger

// 动态模块注册
var (
	registeredModules = make(map[string]*Logger)
	modulesMutex      sync.RWMutex
)

// getDefaultLogger 获取默认日志器（懒加载）
func getDefaultLogger() *Logger {
	if defaultLogger == nil {
		defaultLogger = NewLogger("app")
	}
	return defaultLogger
}

// 向后兼容的函数 - 保持原有API不变

// Info 打印普通信息日志（兼容原有代码）
func Info(format string, args ...interface{}) {
	msg := fmt.Sprintf(format, args...)
	global.App.Log.Info(msg)
}

// Debug 打印调试日志（兼容原有代码）
func Debug(format string, args ...interface{}) {
	msg := fmt.Sprintf(format, args...)
	global.App.Log.Debug(msg)
}

// Error 打印错误日志（兼容原有代码）
func Error(format string, args ...interface{}) {
	msg := fmt.Sprintf(format, args...)
	global.App.Log.Error(msg)
}

// Warn 打印警告日志（兼容原有代码）
func Warn(format string, args ...interface{}) {
	msg := fmt.Sprintf(format, args...)
	global.App.Log.Warn(msg)
}

// 新的结构化日志API

// WithModule 创建指定模块的日志器
func WithModule(module string) *Logger {
	return NewLogger(module)
}

// WithContext 创建带上下文的日志器
func WithContext(ctx context.Context) *Logger {
	return getDefaultLogger().WithContext(ctx)
}

// WithGinContext 创建带Gin上下文的日志器
func WithGinContext(ctx *gin.Context) *Logger {
	return getDefaultLogger().WithGinContext(ctx)
}

// WithField 创建带字段的日志器
func WithField(key string, value interface{}) *Logger {
	return getDefaultLogger().WithField(key, value)
}

// WithFields 创建带多个字段的日志器
func WithFields(fields ...Field) *Logger {
	return getDefaultLogger().WithFields(fields...)
}

// WithRequestID 创建带请求ID的日志器
func WithRequestID(requestID string) *Logger {
	return getDefaultLogger().WithRequestID(requestID)
}

// WithUserID 创建带用户ID的日志器
func WithUserID(userID interface{}) *Logger {
	return getDefaultLogger().WithUserID(userID)
}

// WithError 创建带错误信息的日志器
func WithError(err error) *Logger {
	return getDefaultLogger().WithError(err)
}

// 动态模块管理

// RegisterModule 注册一个新的业务模块日志器
// 使用示例：log.RegisterModule("payment", "支付模块")
func RegisterModule(moduleName, description string) *Logger {
	modulesMutex.Lock()
	defer modulesMutex.Unlock()

	logger := NewLogger(moduleName)
	registeredModules[moduleName] = logger

	return logger
}

// GetModule 获取已注册的模块日志器，如果不存在则自动创建
// 使用示例：log.GetModule("payment").Info("支付成功")
func GetModule(moduleName string) *Logger {
	modulesMutex.RLock()
	if logger, exists := registeredModules[moduleName]; exists {
		modulesMutex.RUnlock()
		return logger
	}
	modulesMutex.RUnlock()

	// 如果模块不存在，自动创建并注册
	return RegisterModule(moduleName, "自动创建的模块")
}

// ListRegisteredModules 列出所有已注册的模块
func ListRegisteredModules() []string {
	modulesMutex.RLock()
	defer modulesMutex.RUnlock()

	modules := make([]string, 0, len(registeredModules))
	for moduleName := range registeredModules {
		modules = append(modules, moduleName)
	}
	return modules
}

// UnregisterModule 注销模块日志器
func UnregisterModule(moduleName string) {
	modulesMutex.Lock()
	defer modulesMutex.Unlock()

	delete(registeredModules, moduleName)
}

// clearAllModules 清理所有模块日志器缓存（内部函数）
func clearAllModules() {
	modulesMutex.Lock()
	defer modulesMutex.Unlock()

	registeredModules = make(map[string]*Logger)
	defaultLogger = nil // 也清理默认日志器
}

// 便捷的模块日志器

// App 应用日志器
func App() *Logger {
	return NewLogger("app")
}

// HTTP HTTP日志器
func HTTP() *Logger {
	return NewLogger("http")
}

// SQL SQL日志器
func SQL() *Logger {
	return NewLogger("sql")
}

// Access 访问日志器
func Access() *Logger {
	return NewLogger("access")
}

// ErrorLog 错误日志器
func ErrorLog() *Logger {
	return NewLogger("error")
}

// 预置业务模块日志器 - 为了向后兼容保留
// 推荐使用 GetModule("模块名") 或 RegisterModule("模块名", "描述") 的方式

// Channel 渠道管理模块
func Channel() *Logger {
	return GetModule("channel")
}

// Customer 客户管理模块
func Customer() *Logger {
	return GetModule("customer")
}

// Order 订单管理模块
func Order() *Logger {
	return GetModule("order")
}

// Payment 支付模块
func Payment() *Logger {
	return GetModule("payment")
}

// Repayment 还款模块
func Repayment() *Logger {
	return GetModule("repayment")
}

// Risk 风控模块
func Risk() *Logger {
	return GetModule("risk")
}

// BankCard 银行卡模块
func BankCard() *Logger {
	return GetModule("bankcard")
}

// Identity 身份认证模块
func Identity() *Logger {
	return GetModule("identity")
}

// Complaint 投诉建议模块
func Complaint() *Logger {
	return GetModule("complaint")
}

// System 系统管理模块
func System() *Logger {
	return GetModule("system")
}

// 全局配置函数

// SetDefaultModule 设置默认模块
func SetDefaultModule(module string) {
	defaultLogger = NewLogger(module)
}

// GetDefaultLogger 获取默认日志器
func GetDefaultLogger() *Logger {
	return getDefaultLogger()
}

// Close 关闭日志系统
func Close() {
	GetManager().Close()
}
