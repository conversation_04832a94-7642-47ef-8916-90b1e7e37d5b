package lock

import (
	"context"
	"fmt"
	"sync"
	"time"

	"fincore/utils/log"
)

// 使用示例：展示如何使用重构后的锁模块

// Example1_BasicUsage 示例1：基本使用
func Example1_BasicUsage() {
	fmt.Println("=== 基本使用示例 ===")

	// 获取锁并设置5分钟过期时间
	lock := GetLock("user:1001:operation", 5*time.Minute)

	// 链式操作：设置超时时间并加锁
	lock.WithTimeout(10 * time.Second).Lock()

	fmt.Printf("获取锁成功，key: %s\n", lock.GetKey())

	// 模拟业务操作
	time.Sleep(100 * time.Millisecond)

	// 解锁
	lock.Unlock()
	fmt.Println("解锁成功")
}

// Example2_ChainedOperations 示例2：链式操作
func Example2_ChainedOperations() {
	fmt.Println("\n=== 链式操作示例 ===")

	// 创建带请求ID的上下文
	ctx := context.Background()
	ctx = log.SetRequestIDToContext(ctx, "req-chain-001")

	// 链式操作：获取锁 -> 设置上下文 -> 设置超时 -> 加锁 -> 解锁
	GetLock("order:2001:process", 10*time.Minute).
		WithContext(ctx).
		WithTimeout(5 * time.Second).
		Lock().
		Unlock()

	fmt.Println("链式操作完成")
}

// Example3_TryLock 示例3：尝试加锁
func Example3_TryLock() {
	fmt.Println("\n=== 尝试加锁示例 ===")

	lock := GetLock("resource:3001", 2*time.Minute)

	// 尝试加锁
	if success, _ := lock.TryLock(); success {
		fmt.Println("尝试加锁成功")

		// 模拟业务操作
		time.Sleep(50 * time.Millisecond)

		lock.Unlock()
		fmt.Println("解锁完成")
	} else {
		fmt.Println("尝试加锁失败，资源被占用")
	}
}

// Example4_UnlockByKey 示例4：通过key解锁
func Example4_UnlockByKey() {
	fmt.Println("\n=== 通过key解锁示例 ===")

	key := "payment:4001:process"

	// 获取锁并加锁
	lock := GetLock(key, 3*time.Minute)
	lock.Lock()
	fmt.Printf("锁定资源: %s\n", key)

	// 在其他地方通过key解锁
	err := UnlockByKey(key)
	if err != nil {
		fmt.Printf("通过key解锁失败: %v\n", err)
	} else {
		fmt.Printf("通过key解锁成功: %s\n", key)
	}
}

// Example5_ConcurrentAccess 示例5：并发访问
func Example5_ConcurrentAccess() {
	fmt.Println("\n=== 并发访问示例 ===")

	key := "shared:resource:5001"
	var wg sync.WaitGroup

	// 启动多个goroutine并发访问同一资源
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			lock := GetLock(key, 1*time.Minute)

			// 尝试加锁
			if success, _ := lock.TryLock(); success {
				fmt.Printf("Goroutine %d 获取锁成功\n", id)

				// 模拟业务处理
				time.Sleep(100 * time.Millisecond)

				lock.Unlock()
				fmt.Printf("Goroutine %d 释放锁\n", id)
			} else {
				fmt.Printf("Goroutine %d 获取锁失败\n", id)
			}
		}(i)
	}

	wg.Wait()
	fmt.Println("并发访问测试完成")
}

// Example6_LockStats 示例6：锁统计信息
func Example6_LockStats() {
	fmt.Println("\n=== 锁统计信息示例 ===")

	// 创建几个锁
	GetLock("stats:test:1", 1*time.Minute).Lock().Unlock()
	GetLock("stats:test:2", 2*time.Minute).Lock()
	GetLock("stats:test:3", 3*time.Minute)

	// 获取统计信息
	stats := GetStats()

	fmt.Printf("当前锁数量: %d\n", len(stats))
	for key, stat := range stats {
		fmt.Printf("锁: %s, 创建时间: %s, 过期时间: %s, 是否锁定: %v, 使用次数: %d\n",
			key,
			stat.CreatedAt.Format("15:04:05"),
			stat.ExpiresAt.Format("15:04:05"),
			stat.IsLocked,
			stat.UseCount)
	}
}

// Example7_CleanExpiredLocks 示例7：清理过期锁
func Example7_CleanExpiredLocks() {
	fmt.Println("\n=== 清理过期锁示例 ===")

	// 创建一些短期锁
	GetLock("temp:1", 100*time.Millisecond)
	GetLock("temp:2", 200*time.Millisecond)
	GetLock("temp:3", 300*time.Millisecond)

	fmt.Printf("创建锁后，当前锁数量: %d\n", len(GetStats()))

	// 等待锁过期
	time.Sleep(500 * time.Millisecond)

	// 清理过期锁
	cleaned := CleanExpiredLocks()
	fmt.Printf("清理了 %d 个过期锁\n", cleaned)
	fmt.Printf("清理后，当前锁数量: %d\n", len(GetStats()))
}

// Example8_LoggerInjection 示例8：日志对象注入
func Example8_LoggerInjection() {
	fmt.Println("\n=== 日志对象注入示例 ===")

	// 创建自定义日志对象
	customLogger := log.RegisterModule("custom_lock", "自定义锁模块")

	// 方式1：使用全局方法注入日志对象
	lock1 := GetLockWithLogger("demo:custom:1", customLogger, 5*time.Minute)
	lock1.Lock()
	fmt.Println("使用注入的日志对象加锁成功")
	lock1.Unlock()

	// 方式2：使用链式操作注入日志对象
	GetLock("demo:custom:2", 5*time.Minute).
		WithLogger(customLogger).
		Lock().
		Unlock()
	fmt.Println("链式操作注入日志对象成功")

	// 方式3：不注入日志对象（不会记录日志）
	lock3 := GetLock("demo:no_logger", 5*time.Minute)
	lock3.Lock()
	lock3.Unlock()
	fmt.Println("不注入日志对象成功（无日志记录）")

	// 方式4：结合上下文和自定义日志
	ctx := context.Background()
	ctx = log.SetRequestIDToContext(ctx, "custom-req-001")

	GetLockWithLogger("demo:custom:4", customLogger, 5*time.Minute).
		WithContext(ctx).
		Lock().
		Unlock()
	fmt.Println("结合上下文和自定义日志成功")
}

// Example9_ContextLogging 示例9：上下文日志记录
func Example9_ContextLogging() {
	fmt.Println("\n=== 上下文日志记录示例 ===")

	// 创建带用户信息的上下文
	ctx := context.Background()
	ctx = log.SetRequestIDToContext(ctx, "req-user-001")

	userID := "user123"
	operationID := "op456"

	// 使用上下文进行锁操作
	lock := GetLock(fmt.Sprintf("user:%s:operation:%s", userID, operationID), 5*time.Minute).
		WithContext(ctx)

	fmt.Printf("开始用户 %s 的操作 %s\n", userID, operationID)

	if success, _ := lock.TryLock(); success {
		fmt.Printf("用户 %s 操作 %s 获取锁成功\n", userID, operationID)

		// 模拟业务操作
		time.Sleep(100 * time.Millisecond)

		lock.Unlock()
		fmt.Printf("用户 %s 操作 %s 完成\n", userID, operationID)
	} else {
		fmt.Printf("用户 %s 操作 %s 获取锁失败\n", userID, operationID)
	}
}

// Example10_RealWorldScenario 示例10：真实业务场景
func Example10_RealWorldScenario() {
	fmt.Println("\n=== 真实业务场景示例 ===")

	userID := "1001"
	orderID := "ORDER_2024_001"

	// 场景1：用户下单操作
	userOrderKey := fmt.Sprintf("user:%s:order:%s", userID, orderID)

	lock := GetLock(userOrderKey, 10*time.Minute).
		WithTimeout(30 * time.Second)

	if success, _ := lock.TryLock(); success {
		fmt.Printf("开始处理用户 %s 的订单 %s\n", userID, orderID)

		// 模拟订单处理逻辑
		time.Sleep(200 * time.Millisecond)

		lock.Unlock()
		fmt.Printf("用户 %s 的订单 %s 处理完成\n", userID, orderID)
	} else {
		fmt.Printf("用户 %s 的订单 %s 正在处理中，请稍后重试\n", userID, orderID)
	}

	// 场景2：支付处理
	paymentKey := fmt.Sprintf("payment:%s", orderID)

	GetLock(paymentKey, 5*time.Minute).
		WithTimeout(10 * time.Second).
		Lock()

	fmt.Printf("开始处理订单 %s 的支付\n", orderID)
	time.Sleep(100 * time.Millisecond)

	// 通过key解锁
	UnlockByKey(paymentKey)
	fmt.Printf("订单 %s 支付处理完成\n", orderID)
}

// RunAllExamples 运行所有示例
func RunAllExamples() {
	fmt.Println("开始运行重构后的锁模块示例...")

	Example1_BasicUsage()
	Example2_ChainedOperations()
	Example3_TryLock()
	Example4_UnlockByKey()
	Example5_ConcurrentAccess()
	Example6_LockStats()
	Example7_CleanExpiredLocks()
	Example8_LoggerInjection()
	Example9_ContextLogging()
	Example10_RealWorldScenario()

	fmt.Println("\n所有示例运行完成！")
}

// 使用指南
func PrintUsageGuide() {
	fmt.Print(`
=== 重构后锁模块使用指南 ===

1. 基本使用：
   lock := GetLock("your-key", 5*time.Minute)
   lock.Lock()
   defer lock.Unlock()

2. 链式操作：
   GetLock("key", expiration).WithTimeout(10*time.Second).Lock().Unlock()

3. 尝试加锁：
   if success, lock := GetLock("key").TryLock(); success {
       defer lock.Unlock()
       // 业务逻辑
   }

4. 通过key解锁：
   UnlockByKey("your-key")

5. 清理过期锁：
   cleaned := CleanExpiredLocks()

6. 获取统计信息：
   stats := GetStats()

7. 日志对象注入：
   customLogger := log.RegisterModule("custom", "自定义模块")
   GetLockWithLogger("key", customLogger).Lock().Unlock()

   // 或者链式注入
   GetLock("key").WithLogger(customLogger).Lock().Unlock()

   // 不注入日志对象则不记录日志
   GetLock("key").Lock().Unlock() // 无日志记录

特点：
- ✅ 支持链式操作
- ✅ 自动过期机制
- ✅ 通过key解锁
- ✅ 业务无关的通用设计
- ✅ 并发安全
- ✅ 统计监控
- ✅ 上下文日志记录
- ✅ 请求链路追踪
- ✅ 日志对象注入
`)
}
