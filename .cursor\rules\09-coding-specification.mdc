---
description: go/*
alwaysApply: false
---
# 后端API实现规范

## 概述

本文档定义了后端API开发的标准规范，基于渠道管理和客户管理模块的最佳实践，确保代码的一致性、可维护性和可扩展性。

## 1. 项目结构规范

### 1.1 目录结构

```
go/src/app/business/{module}/
├── controller.go    # 控制器层 - 处理HTTP请求和响应
├── service.go       # 服务层 - 业务逻辑处理
└── schema.go        # 验证层 - 参数验证规则
```

### 1.2 模块命名规范

- 模块目录名使用**单数形式**，如：`channel`、`customer`、`order`
- 避免使用复数形式或缩写
- 使用小写字母，多个单词用下划线分隔

## 2. 分层架构规范

### 2.1 五层架构

```
Controller层 (HTTP处理)
    ↓
Schema层 (参数验证)
    ↓  
Service层 (业务逻辑)
    ↓
Model层 (数据访问)
    ↓
Utils层 (工具函数)
```

### 2.2 层级职责

| 层级 | 职责 | 禁止事项 |
|------|------|----------|
| Controller | HTTP请求处理、参数绑定、响应返回 | 业务逻辑、数据库操作 |
| Schema | 参数验证、数据格式校验 | 业务逻辑、数据库操作 |
| Service | 业务逻辑处理、数据转换 | 直接HTTP操作、参数验证 |
| Model | 数据库操作、数据模型定义 | 业务逻辑、HTTP操作 |
| Utils | 通用工具函数、类型转换 | 业务逻辑、数据库操作 |

## 3. Controller层规范

### 3.1 基本结构

```go
package {module}

import (
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/results"
	"reflect"
	"strconv"

	"github.com/gin-gonic/gin"
)

type {Module}Controller struct{}

func init() {
	controller := {Module}Controller{}
	gf.Register(&controller, reflect.TypeOf(controller).PkgPath())
}
```

### 3.2 方法命名规范

| 操作 | 方法名 | HTTP方法 | 路径示例 |
|------|--------|----------|----------|
| 列表查询 | List{Module}s | GET | `/api/{module}s` |
| 详情查询 | Get{Module}Detail | GET | `/api/{module}s/:id` |
| 创建 | Create{Module} | POST | `/api/{module}s` |
| 更新 | Update{Module} | PUT | `/api/{module}s/:id` |
| 删除 | Delete{Module} | DELETE | `/api/{module}s/:id` |
| 选项查询 | Get{Module}Options | GET | `/api/{module}s/options` |

### 3.3 标准处理流程

```go
func (c *{Module}Controller) List{Module}s(ctx *gin.Context) {
	// 1. 参数校验
	schema := Get{Module}ListSchema()
	validator := jsonschema.NewValidator(schema)
	
	// 从查询参数获取数据
	queryData := map[string]interface{}{
		"field1": ctx.Query("field1"),
		"field2": ctx.Query("field2"),
		"page":   ctx.Query("page"),
		"pageSize": ctx.Query("pageSize"),
	}
	
	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range queryData {
		if str, ok := v.(string); !ok || str != "" {
			cleanData[k] = v
		}
	}
	
	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}
	
	// 2. 业务逻辑处理
	service := {Module}Service{}
	result, err := service.Get{Module}List(ctx)
	if err != nil {
		results.Failed(ctx, "获取{模块}列表失败", err.Error())
		return
	}
	
	// 3. 返回结果
	results.Success(ctx, "获取{模块}列表成功", result, nil)
}
```

### 3.4 错误处理规范

- 使用统一的错误响应格式
- 错误信息要清晰明确，便于前端处理
- 区分业务错误和系统错误

```go
// 参数错误
results.Failed(ctx, "参数验证失败", validationResult.Errors)

// 业务错误  
results.Failed(ctx, "获取数据失败", err.Error())

// 成功响应
results.Success(ctx, "操作成功", result, nil)
```

## 4. Schema层规范

### 4.1 基本结构

```go
package {module}

import (
	"fincore/utils/jsonschema"
)

// Get{Module}ListSchema {模块}列表查询参数验证规则
func Get{Module}ListSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "{模块}列表查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			// 字段定义
		},
		Required: []string{}, // 必填字段
	}
}
```

### 4.2 字段验证规范

| 数据类型 | 验证规则 | 示例 |
|----------|----------|------|
| 字符串 | Type, MaxLength, Pattern | `Type: "string", MaxLength: 50` |
| 整数 | Type, Min, Max, Enum | `Type: "integer", Min: floatPtr(1)` |
| 浮点数 | Type, Min, Max | `Type: "number", Min: floatPtr(0)` |
| 枚举 | Type, Enum | `Type: "string", Enum: []string{"0", "1"}` |
| 日期时间 | Type, Pattern | `Pattern: "^\\d{4}-\\d{2}-\\d{2}( \\d{2}:\\d{2}:\\d{2})?$"` |

### 4.3 常用验证模式

```go
// 手机号验证
"mobile": {
	Type:        "string",
	Pattern:     "^1[3-9]\\d{9}$",
	Description: "手机号码",
},

// 分页参数
"page": {
	Type:        "string",
	Pattern:     "^[1-9]\\d*$",
	Default:     "1",
	Description: "页码（从1开始）",
},

// 状态枚举
"status": {
	Type:        "string",
	Enum:        []string{"0", "1", "2"},
	Description: "状态：0-禁用，1-启用，2-删除",
},
```

## 5. Service层规范

### 5.1 基本结构

```go
package {module}

import (
	"fmt"
	"strconv"
	"time"

	"fincore/model"
	"fincore/utils/convert"
	"fincore/utils/pagination"

	"github.com/gin-gonic/gin"
)

type {Module}Service struct{}
```

### 5.2 方法命名规范

- 与Controller层方法对应
- 使用动词+名词的形式
- 返回值统一使用 `(result, error)` 模式

```go
// 列表查询
func (s *{Module}Service) Get{Module}List(ctx *gin.Context) (*pagination.PaginationResponse, error)

// 详情查询
func (s *{Module}Service) Get{Module}Detail(id int64) (map[string]interface{}, error)

// 创建
func (s *{Module}Service) Create{Module}(data map[string]interface{}) (map[string]interface{}, error)

// 更新
func (s *{Module}Service) Update{Module}(id int64, data map[string]interface{}) error

// 删除
func (s *{Module}Service) Delete{Module}(id int64) error
```

### 5.3 参数处理规范

**使用公共工具函数进行类型转换：**

```go
// 从map获取参数
name := convert.GetStringFromMap(data, "name")
status := convert.GetIntFromMap(data, "status", 0)
amount := convert.GetFloatFromMap(data, "amount", 0.0)
enabled := convert.GetBoolFromMap(data, "enabled", false)

// 从gin.Context获取参数
if page := ctx.Query("page"); page != "" {
	if val, err := strconv.Atoi(page); err == nil && val > 0 {
		params.Page = val
	} else {
		params.Page = 1
	}
} else {
	params.Page = 1
}
```

### 5.4 错误处理规范

```go
// 统一错误格式
if err != nil {
	return nil, fmt.Errorf("操作失败: %v", err)
}

// 业务逻辑错误
if result == nil {
	return nil, fmt.Errorf("数据不存在")
}
```

## 6. Model层规范

### 6.1 结构体定义

```go
// {Module} {模块}数据模型
type {Module} struct {
	ID          int64     `json:"id" gorm:"primaryKey;autoIncrement" db:"id"`
	Name        string    `json:"name" db:"name"`
	Status      int       `json:"status" db:"status"`
	CreateTime  int64     `json:"createTime" db:"create_time"`
	UpdateTime  int64     `json:"updateTime" db:"update_time"`
}

// TableName 指定表名
func ({Module}) TableName() string {
	return "{table_name}"
}
```

### 6.2 查询参数结构

```go
// {Module}QueryParams {模块}查询参数
type {Module}QueryParams struct {
	// 基本查询字段
	Name   string `json:"name" form:"name"`
	Status *int   `json:"status" form:"status"`
	
	// 时间范围查询
	CreateTimeStart string `json:"createTimeStart" form:"createTimeStart"`
	CreateTimeEnd   string `json:"createTimeEnd" form:"createTimeEnd"`
	
	// 分页参数
	pagination.PaginationRequest
}
```

### 6.3 数据库操作规范

```go
// 列表查询（分页）
func Get{Module}List(params {Module}QueryParams) (*pagination.PaginationResponse, error) {
	baseQuery := DB().Table("{table_name}")
	countQuery := DB().Table("{table_name}")
	
	// 应用查询条件
	apply{Module}QueryConditions(baseQuery, params)
	apply{Module}QueryConditions(countQuery, params)
	
	// 设置查询字段和排序
	baseQuery = baseQuery.Fields("id, name, status, create_time, update_time")
	baseQuery = baseQuery.OrderBy("create_time DESC")
	
	// 执行分页查询
	return pagination.PaginateWithCustomQuery(countQuery, baseQuery, params.PaginationRequest)
}

// 详情查询
func Get{Module}ByID(id int64) (map[string]interface{}, error) {
	return DB().Table("{table_name}").Where("id", id).First()
}

// 创建
func Create{Module}(data map[string]interface{}) (map[string]interface{}, error) {
	data["create_time"] = time.Now().Unix()
	data["update_time"] = time.Now().Unix()
	
	result, err := DB().Table("{table_name}").Data(data).Insert()
	if err != nil {
		return nil, err
	}
	
	// 返回创建的记录
	if id, ok := result["id"]; ok {
		return Get{Module}ByID(convert.MustConvertToInt64(id, 0))
	}
	
	return result, nil
}

// 更新
func Update{Module}(id int64, data map[string]interface{}) error {
	data["update_time"] = time.Now().Unix()
	_, err := DB().Table("{table_name}").Where("id", id).Data(data).Update()
	return err
}

// 删除
func Delete{Module}(id int64) error {
	_, err := DB().Table("{table_name}").Where("id", id).Delete()
	return err
}
```

## 7. 工具函数使用规范

### 7.1 类型转换

**必须使用公共工具函数：**

```go
// 使用 convert 包的工具函数
import "fincore/utils/convert"

// 字符串转换
str := convert.ConvertToString(value)

// 数值转换（带默认值）
intVal := convert.MustConvertToInt(value, 0)
floatVal := convert.MustConvertToFloat(value, 0.0)
boolVal := convert.MustConvertToBool(value, false)

// 从map获取值
name := convert.GetStringFromMap(data, "name")
status := convert.GetIntFromMap(data, "status", 0)
```

### 7.2 随机字符串生成

```go
// 使用 gf 包的随机字符串函数
import "fincore/utils/gf"

// 生成随机字符串
code := gf.RandString(10)
code = strings.ToUpper(code) // 转大写
```

### 7.3 时间处理

```go
// 使用 datetime 包处理时间
import "fincore/utils/datetime"

// 解析时间字符串
timestamp, err := datetime.ParseTimeString("2024-01-01 12:00:00")
```

## 8. 响应格式规范

### 8.1 成功响应

```go
// 列表数据
results.Success(ctx, "获取列表成功", paginationResult, nil)

// 详情数据
results.Success(ctx, "获取详情成功", detailData, nil)

// 操作成功（无返回数据）
results.Success(ctx, "操作成功", nil, nil)
```

### 8.2 错误响应

```go
// 参数验证失败
results.Failed(ctx, "参数验证失败", validationResult.Errors)

// 业务逻辑错误
results.Failed(ctx, "操作失败", err.Error())

// 数据不存在
results.Failed(ctx, "数据不存在", "未找到指定的记录")
```

## 9. 代码质量规范

### 9.1 命名规范

- **包名**：小写，简洁明了
- **结构体**：大驼峰命名（PascalCase）
- **方法名**：大驼峰命名（公开方法）、小驼峰命名（私有方法）
- **变量名**：小驼峰命名（camelCase）
- **常量名**：全大写，下划线分隔

### 9.2 注释规范

```go
// {Module}Controller {模块}控制器
type {Module}Controller struct{}

// List{Module}s 获取{模块}列表
// @Summary 获取{模块}列表
// @Description 支持分页和条件筛选的{模块}列表查询
// @Tags {模块}管理
// @Accept json
// @Produce json
// @Param page query int false "页码"
// @Param pageSize query int false "每页数量"
// @Success 200 {object} results.Response
// @Router /api/{module}s [get]
func (c *{Module}Controller) List{Module}s(ctx *gin.Context) {
	// 实现代码...
}
```

### 9.3 错误处理

- 所有可能出错的操作都要进行错误检查
- 使用有意义的错误信息
- 区分不同类型的错误

```go
// 好的错误处理
result, err := service.GetData(id)
if err != nil {
	results.Failed(ctx, "获取数据失败", err.Error())
	return
}

if result == nil {
	results.Failed(ctx, "数据不存在", "未找到指定的记录")
	return
}
```

## 10. 性能优化规范

### 10.1 数据库查询优化

- 只查询需要的字段
- 合理使用索引
- 避免N+1查询问题
- 使用分页查询

```go
// 好的查询方式
baseQuery = baseQuery.Fields("id, name, status, create_time")
baseQuery = baseQuery.OrderBy("create_time DESC")
```

### 10.2 并发处理

- 使用适当的并发控制
- 避免竞态条件
- 合理使用缓存

## 11. 安全规范

### 11.1 输入验证

- 所有用户输入都要进行验证
- 使用白名单而不是黑名单
- 防止SQL注入

### 11.2 权限控制

- 实现适当的访问控制
- 验证用户权限
- 记录敏感操作日志

## 12. 测试规范

### 12.1 单元测试

- 每个Service方法都要有对应的测试
- 测试覆盖率要达到80%以上
- 包含正常和异常情况的测试

### 12.2 集成测试

- 测试完整的API流程
- 验证数据库操作
- 测试错误处理逻辑

## 13. 部署和监控

### 13.1 日志记录

- 记录关键操作日志
- 使用结构化日志格式
- 包含必要的上下文信息

### 13.2 性能监控

- 监控API响应时间
- 监控数据库查询性能
- 设置适当的告警阈值

---

## 总结

本规范基于项目实际需求和最佳实践制定，旨在提高代码质量和开发效率。所有开发人员都应严格遵循此规范，确保代码的一致性和可维护性。

**关键原则：**
1. **分层清晰**：严格按照五层架构开发
2. **职责单一**：每层只处理自己的职责
3. **工具复用**：优先使用公共工具函数
4. **错误处理**：完善的错误处理机制
5. **代码质量**：清晰的命名和充分的注释
``` 