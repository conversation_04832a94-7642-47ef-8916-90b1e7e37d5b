package notice

import (
	"context"
	"fmt"
	"time"

	"fincore/app/scheduler/tasks"
	"fincore/model"
	"fincore/thirdparty/sms"
	"fincore/utils/convert"
	"fincore/utils/gform"
	"fincore/utils/log"
)

// AvailableQuotaNoticeTask 可用余额通知任务
// 用户额度没有使用，提醒用户来借款
type AvailableQuotaNoticeTask struct {
	*tasks.BaseTask
	logger *log.Logger
	ctx    context.Context
}

// NewAvailableQuotaNoticeTask 创建额度未使用通知任务
func NewAvailableQuotaNoticeTask() *AvailableQuotaNoticeTask {
	baseTask := tasks.NewBaseTask(
		"available_quota_notice",
		"额度未使用通知任务",
		// 每天11点执行一次
		"0 0 11 * * *",
		//"0 */1 * * * *",
		1*time.Hour, // 超时时间1小时
	)

	// 设置为单例模式，避免重复执行
	baseTask.SetConcurrencyMode(tasks.ConcurrencyModeSingleton)
	// 设置重试次数和间隔
	baseTask.SetRetryCount(3).SetRetryInterval(30 * time.Second)

	logger := log.RegisterModule("available_quota_notice_task", "可用余额通知任务")
	ctx := context.Background()
	return &AvailableQuotaNoticeTask{
		BaseTask: baseTask,
		logger:   logger,
		ctx:      ctx,
	}
}

const platform = "悦心易"

// Execute 执行额度未使用通知任务
func (t *AvailableQuotaNoticeTask) Execute(ctx context.Context) error {

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "start_execution"),
	).Info("开始执行额度未使用通知任务")

	startTime := time.Now()
	var processedCount, successCount, failureCount int

	toNotices, err := t.getQuotaAvailableUsers()
	if err != nil {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "query_quota_available_users"),
			log.String("error", err.Error()),
		).Error("查询额度未使用用户失败")
		return fmt.Errorf("查询额度未使用用户失败: %v", err)
	}

	if len(toNotices) == 0 {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "no_notices_found"),
		).Info("未找到需要通知的用户")
		return nil
	}
	processedCount = len(toNotices)

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "sms_available_quota_notices"),
		log.Int("notice_count", processedCount),
	).Info("找到需要通知的用户")
	now := time.Now()
	// 分批处理
	const batchSize = 100
	for i := 0; i < len(toNotices); i += batchSize {
		end := i + batchSize
		if end > len(toNotices) {
			end = len(toNotices)
		}
		batch := toNotices[i:end]
		batchLen := len(batch)
		notices := make([]sms.NoticeAvailableQuotaParams, 0, batchLen)
		toInsert := make([]map[string]interface{}, 0, batchLen)
		toUpdate := make([]map[string]interface{}, 0, batchLen)
		// 遍历处理每条通知
		for j := range batchLen {

			// 检查上下文是否被取消
			select {
			case <-ctx.Done():
				t.logger.WithFields(
					log.String("task", t.GetName()),
					log.String("operation", "context_cancelled"),
					log.Int("processed_count", processedCount-1),
				).Warn("任务被取消，停止处理")
				return ctx.Err()
			default:
			}
			times := convert.GetIntFromMap(batch[j], "times", 0)
			noticeDate := convert.ConvertToTime(batch[j]["noticeDate"])
			needNotice := false
			needUpdate := false
			switch times {
			case 0:
				toInsert = append(toInsert, map[string]interface{}{
					"uid":        convert.GetIntFromMap(batch[j], "uid", 0),
					"times":      1,
					"noticeDate": now.Format("2006-01-02 15:04:05"),
				})
				needNotice = true
			// 发送一次后，2天后再次发送
			case 1:
				if noticeDate.Add(2 * 24 * time.Hour).Before(now) {
					needNotice = true
					needUpdate = true
				}
			// 发送两次后，5天后再次发送
			case 2:
				if noticeDate.Add(5 * 24 * time.Hour).Before(now) {
					needNotice = true
					needUpdate = true
				}

			// 发送三次后，10天后再发送
			case 3:
				if noticeDate.Add(10 * 24 * time.Hour).Before(now) {
					needNotice = true
					needUpdate = true
				}

			// 发送四次后，30天后再发送
			case 4:
				if noticeDate.Add(30 * 24 * time.Hour).Before(now) {
					needNotice = true
					needUpdate = true
				}
			}

			if needNotice {
				notices = append(notices, sms.NoticeAvailableQuotaParams{
					Username: convert.GetStringFromMap(batch[j], "username"),
					Platform: platform,
					Amount:   convert.GetStringFromMap(batch[j], "allQuota"),
					Mobile:   convert.GetStringFromMap(batch[j], "mobile"),
					URL:      "",
				})
			}

			if needUpdate {
				toUpdate = append(toUpdate, map[string]interface{}{
					"uid":        convert.GetIntFromMap(batch[j], "uid", 0),
					"times":      times + 1,
					"noticeDate": now.Format("2006-01-02 15:04:05"),
				})
			}
		}

		// 发送短信
		err := sms.SendAvailableQuotaNoticeSms(notices)
		if err != nil {
			t.logger.WithFields(
				log.String("task", t.GetName()),
				log.String("operation", "send_sms"),
				log.String("error", err.Error()),
			).Error("批量发送短信失败")
			failureCount += batchLen
			continue
		}
		t.insertNoticeTimes(toInsert)
		t.updateNoticeTimes(toUpdate)
		successCount += batchLen
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.Int("batchLen", batchLen),
			log.String("operation", "batch_send_success"),
		).Info("批量短信通知成功")
	}
	// 记录执行统计
	duration := time.Since(startTime)
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "execution_completed"),
		log.Int("processed_count", processedCount),
		log.Int("success_count", successCount),
		log.Int("failure_count", failureCount),
		log.String("duration", duration.String()),
	).Info("额度可用通知任务执行完成")

	return nil
}

// 查询所有额度未使用的用户
func (t *AvailableQuotaNoticeTask) getQuotaAvailableUsers() ([]gform.Data, error) {
	// 额度未使用的用户，并且发送次数小于5
	query := `
SELECT
	u.id AS uid,
	u.name AS username,
	u.mobile AS mobile,
	u.allQuota AS allQuota,
	q.times AS times,
	q.noticeDate AS noticeDate 
FROM
	business_app_account u
	LEFT JOIN quota_notice q ON u.id = q.uid 
WHERE
	u.allQuota > 0 
	AND u.allQuota = u.reminderQuota 
	AND (q.times < 5 OR q.times is NULL)
	`

	result, err := model.DB(model.WithContext(t.ctx)).Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询额度未使用用户失败: %v", err)
	}
	return result, nil
}

func (t *AvailableQuotaNoticeTask) updateNoticeTimes(toUpdate []map[string]interface{}) {
	if len(toUpdate) == 0 {
		return
	}
	for _, data := range toUpdate {
		uid := data["uid"]
		delete(data, "uid")
		_, err := model.DB(model.WithContext(t.ctx)).Table("quota_notice").Where("uid", uid).Data(data).Update()
		if err != nil {
			t.logger.WithFields(
				log.String("task", t.GetName()),
				log.String("operation", "update_notice_times"),
				log.String("error", err.Error()),
			).Error("更新通知次数失败")
		}
	}
}

func (t *AvailableQuotaNoticeTask) insertNoticeTimes(toInsert []map[string]interface{}) {
	if len(toInsert) == 0 {
		return
	}
	_, err := model.DB(model.WithContext(t.ctx)).Table("quota_notice").Insert(toInsert)
	if err != nil {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "insert_notice_times"),
			log.String("error", err.Error()),
		).Error("插入通知次数失败")
	}
}

// OnStart 任务开始执行前的回调
func (t *AvailableQuotaNoticeTask) OnStart(ctx context.Context) error {

	// task_ 开头，记录整个周期所有 sql 执行日志
	requestID := "task_" + t.GetName() + "_" + time.Now().Format("20060102150405")
	t.ctx = context.WithValue(t.ctx, log.RequestIDKey, requestID)
	t.logger = t.logger.WithRequestID(requestID)

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_starting"),
	).Info("可用余额通知任务即将开始")
	return nil
}

// OnSuccess 任务执行成功后的回调
func (t *AvailableQuotaNoticeTask) OnSuccess(ctx context.Context) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_success"),
	).Info("可用余额通知任务执行成功")
	return nil
}

// OnError 任务执行失败后的回调
func (t *AvailableQuotaNoticeTask) OnError(ctx context.Context, err error) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_error"),
		log.String("error", err.Error()),
	).Error("可用余额通知任务执行失败")
	return nil
}
