package packageName

import (
	"encoding/json"
	"fincore/model"
	"fincore/utils/gf"
	"fincore/utils/results"
	"io"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// 用于自动注册路由
type Replace struct {
}

func init() {
	fpath := Replace{}
	gf.Register(&fpath, reflect.TypeOf(fpath).PkgPath())
}

// 获取列表
func (api *Replace) get_list(c *gin.Context) {
	businessID := c.Get<PERSON>eader("Businessid")
	title := c.<PERSON>fault<PERSON>("title", "")
	lastid := c.<PERSON>("lastid", "")
	_pageSize := c.<PERSON>("pageSize", "10")
	pageSize, _ := strconv.Atoi(_pageSize)
	MDB := model.DB().Table("{tablename}").
	Fields("{fields}").Where("businessID", businessID)
	MDBC := model.DB().Table("{tablename}").
		Where("businessID", businessID)
	if title != "" {
		MDB.Where("title", "like", "%"+title+"%")
		MDBC.Where("title", "like", "%"+title+"%")
	}
	if lastid != "" {
		MDB.Where("id", "<", lastid)
	}
	list, err := MDB.Limit(pageSize).Order("id desc").Get()
	if err != nil {
		results.Failed(c, err.Error(), nil)
	} else {
		var new_lastid interface{}
		rooturl, _ := model.DB().Table("common_config").Where("keyname", "rooturl").Value("keyvalue")
		for key, val := range list {
			if _, ok := val["image"]; ok && !strings.Contains(val["image"].(string), "http") && rooturl != nil {
				val["image"] = rooturl.(string) + val["image"].(string)
			}
			if (key + 1) == len(list) {
				new_lastid = val["id"]
			}
		}
		var totalCount int64
		totalCount, _ = MDBC.Count()
		results.Success(c, "获取全部列表", map[string]interface{}{
			"lastid":   new_lastid,
			"pageSize": pageSize,
			"total":    totalCount,
			"items":    list}, nil)
	}
}

// 获取详情
func (api *Replace) get_detail(c *gin.Context) {
	id := c.DefaultQuery("id", "")
	if id == "" {
		results.Failed(c, "请传参数id", nil)
	} else {
		data, err := model.DB().Table("{tablename}").Where("id", id).
		Fields("{fields}").First()
		if err != nil {
			results.Failed(c, "获取详情失败", err)
		} else {
			if data != nil {
				rooturl, _ := model.DB().Table("common_config").Where("keyname", "rooturl").Value("keyvalue")
				if _, ok := data["image"]; ok && !strings.Contains(data["image"].(string), "http") && rooturl != nil {
					data["image"] = rooturl.(string) + data["image"].(string)
				}
			}
			results.Success(c, "获取详情成功！", data, nil)
		}
	}
}

// 保存
func (api *Replace) save(c *gin.Context) {
	//获取post传过来的data
	body, _ := io.ReadAll(c.Request.Body)
	var parameter map[string]interface{}
	_ = json.Unmarshal(body, &parameter)
	//当前用户
	var f_id float64 = 0
	if parameter["id"] != nil {
		f_id = parameter["id"].(float64)
	}
	if f_id == 0 {
		delete(parameter, "id")
		parameter["createtime"] = time.Now().Unix()
		parameter["businessID"] = c.GetHeader("Businessid")
		addId, err := model.DB().Table("{tablename}").Data(parameter).InsertGetId()
		if err != nil {
			results.Failed(c, "添加失败", err)
		} else {
			if addId != 0 {
				model.DB().Table("{tablename}").
					Data(map[string]interface{}{"weigh": addId}).
					Where("id", addId).
					Update()
			}
			results.Success(c, "添加成功！", addId, nil)
		}
	} else {
		res, err := model.DB().Table("{tablename}").
			Data(parameter).
			Where("id", f_id).
			Update()
		if err != nil {
			results.Failed(c, "更新失败", err)
		} else {
			results.Success(c, "更新成功！", res, nil)
		}
	}
}

// 删除
func (api *Replace) del(c *gin.Context) {
	//获取post传过来的data
	body, _ := io.ReadAll(c.Request.Body)
	var parameter map[string]interface{}
	_ = json.Unmarshal(body, &parameter)
	ids := parameter["ids"]
	res2, err := model.DB().Table("{tablename}").WhereIn("id", ids.([]interface{})).Delete()
	if err != nil {
		results.Failed(c, "删除失败", err)
	} else {
		results.Success(c, "删除成功！", res2, nil)
	}
}
