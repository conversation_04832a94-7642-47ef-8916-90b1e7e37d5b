package hetong

// Company 公司基础信息结构体
type Company struct {
	Name        string `json:"name"`         // 公司名称
	LegalRep    string `json:"legal_rep"`    // 法人姓名
	Address     string `json:"address"`      // 公司地址
	Telephone   string `json:"telephone"`    // 公司电话
	AccountName string `json:"account_name"` // 账户名称
	BankName    string `json:"bank_name"`    // 开户行
	BankAccount string `json:"bank_account"` // 银行卡号
}

// AllCompanyInfo 所有公司信息集合
type AllCompanyInfo struct {
	AssetManagement Company `json:"asset_management"` // 资管公司
	Guarantor       Company `json:"guarantor1"`       // 担保公司
	MicroLoan       Company `json:"micro_loan"`       // 小贷公司
}

// 常量定义 - 公司固定信息
const (
	// 资管公司信息
	AssetMgmtName        = "武汉市几何资产投资管理有限公司"
	AssetMgmtLegalRep    = "詹学杰"
	AssetMgmtAddress     = "湖北省武汉市武昌区普提金国际金融中心10幢1单元5204 "
	AssetMgmtTelephone   = "/"
	AssetMgmtAccountName = "武汉市几何资产投资管理有限公司"
	AssetMgmtBankName    = "招商银行广州丰兴支行"
	AssetMgmtBankAccount = "***************"

	// 担保公司1信息
	Guarantor1Name        = "武汉盛唐融资担保有限公司"
	Guarantor1LegalRep    = "/"
	Guarantor1Address     = "/"
	Guarantor1Telephone   = "/"
	Guarantor1AccountName = "/"
	Guarantor1BankName    = "/"
	Guarantor1BankAccount = "/"

	// 担保公司2信息
	Guarantor2Name        = "上海百坤信息科技有限公司"
	Guarantor2LegalRep    = "/"
	Guarantor2Address     = "/"
	Guarantor2Telephone   = "/"
	Guarantor2AccountName = "/"
	Guarantor2BankName    = "/"
	Guarantor2BankAccount = "/"

	// 小贷公司信息
	MicroLoanName        = "南宁市金沙小额贷款有限公司"
	MicroLoanLegalRep    = "钟海斌"
	MicroLoanAddress     = "南宁市青秀区凤岭南路16号保利领秀广场1号楼十九层1907、1908号办公"
	MicroLoanTelephone   = "/"
	MicroLoanAccountName = "/"
	MicroLoanBankName    = "/"
	MicroLoanBankAccount = "/"
)

//合同模板
const (
	ENTRUST_GUARANTEE_TEMPLATE_ID          = "TN0F9F4D87D35244659F318E07DD597653"      // 承诺书书模板ID
	INTERMEDIARY_TEMPLATE_ID               = "**********************************-3727" // 居间担保协议书模板ID
	DAIKUAN_TEMPLATE_ID                    = "**********************************"      // 借款合同模板ID
	GUARANTEE_TEMPLATE_ID                  = "TNE462B6A508194CE094BB2DBB414A44DB"      // 担保协议模板ID
	LOAN_NOTICE_TEMPLATE_ID                = "**********************************"      // 贷款告知书模板ID
	CREDITOR_ASSIGNMENT_NOTICE_TEMPLATE_ID = "TNC7A826CED4484113BE7472B87437CBD5"      //债权转让通知书模板ID

)

const (
	XIAODAI_ACCOUNT = "ASIGN914501000527452668"
	DANBAO1_ACCOUNT = "ASIGN91420103796300796R"
	DANBAO2_ACCOUNT = "ASIGN91310120MA1HRUFRXJ"
	ZIGUAN_ACCOUNT  = "ASIGN91420106MAEGR0E11T"
)

// Borrower 借款人信息
type Borrower struct {
	Name              string           `json:"name"`               // 借款人姓名
	Telephone         string           `json:"telephone"`          // 借款人电话
	Address           string           `json:"address"`            // 借款人地址
	IDCard            string           `json:"id_card"`            // 借款人身份证信息
	BankName          string           `json:"bank_name"`          // 借款人开户银行
	BankAccount       string           `json:"bank_account"`       // 借款人银行卡账号
	SerialNo          string           `json:"serial_no"`          // 实名认证流水号
	EmergencyContact1 EmergencyContact `json:"emergency_contact1"` // 紧急联系人1
	EmergencyContact2 EmergencyContact `json:"emergency_contact2"` // 紧急联系人2
}

// EmergencyContact 紧急联系人信息
type EmergencyContact struct {
	Name     string `json:"name"`     // 联系人姓名
	Relation string `json:"relation"` // 联系人关系
	Phone    string `json:"phone"`    // 联系人电话
}

// BankCardInfo 银行卡信息结构体
type BankCardInfo struct {
	BankName    string `json:"bank_name"`    // 开户银行
	BankAccount string `json:"bank_account"` // 银行卡号
	BankBranch  string `json:"bank_branch"`  // 开户支行(可选)
	CardType    string `json:"card_type"`    // 卡类型(储蓄卡/信用卡)
}

type AuthResult int

const (
	AuthResultPending AuthResult = 0 // 认证中
	AuthResultSuccess AuthResult = 1 // 认证成功
	AuthResultFailed  AuthResult = 2 // 认证失败
)

// RealNameAuthResponse 实名认证结果查询响应结构
type RealNameAuthResponse struct {
	SerialNo     string     `json:"serialNo"`     // 认证流水号
	UserType     int        `json:"userType"`     // 用户类型(1:企业,2:个人)
	Result       AuthResult `json:"result"`       // 认证结果
	AuthTypeCode int        `json:"authTypeCode"` // 认证类型码
	AuthTypeName string     `json:"authTypeName"` // 认证类型名称
}

type LoanAmountInfo struct {
	LoanAmount        float64 `json:"loan_amount"`          // 借款金额
	LoanAmountInWords string  `json:"loan_amount_in_words"` // 借款金额大写
	GuaranteeFee      float64 `json:"guarantee_fee"`        // 担保费
	FirstGuaranteeFee float64 `json:"first_guarantee_fee"`  // 第一笔担保费(借钱时不收)
	HalfGuaranteeFee  float64 `json:"half_guarantee_fee"`   // 中间一半担保费
}

type AiQianKey struct {
}

// 常量定义，爱签中 签合同时对应各个参数的键名，合同中的参数变，此处也要变。

const (
	// 借款人信息
	BorrowerName_key        = "BorrowerName"        // 借款人姓名
	BorrowerTelephone_key   = "BorrowerTelephone"   // 借款人电话
	BorrowerAddress_key     = "BorrowerAddress"     // 借款人地址
	BorrowerIDCard_key      = "BorrowerIDCard"      // 借款人身份证信息
	BorrowerBankName_key    = "BorrowerBankName"    // 借款人银行卡开户银行
	BorrowerBankAccount_key = "BorrowerBankAccount" // 借款人银行卡账号
	BorrowerAccountName_key = "BorrowerAccountName" // 借款人银行卡账户名

	EmergencyContact1Name_key     = "EmgContact1Name" // 紧急联系人1姓名
	EmergencyContact1Relation_key = "EmgContact1Rel"  // 紧急联系人1关系
	EmergencyContact1Phone_key    = "EmgContact1Ph"   // 紧急联系人1联系方式

	EmergencyContact2Name_key     = "EmgContact2Name" // 紧急联系人2姓名
	EmergencyContact2Relation_key = "EmgContact2Rel"  // 紧急联系人2关系
	EmergencyContact2Phone_key    = "EmgContact2Ph"   // 紧急联系人2联系方式

	// 公司信息
	AssetMgmtCompanyName_key = "AssetMgmtCompanyName" // 资管公司名称
	AssetMgmtAccountName_key = "AssetMgmtAccountName" // 资管账户名称
	AssetMgmtBankAccount_key = "AssetMgmtBankAccount" // 资管账号
	AssetMgmtBankName_key    = "AssetMgmtBankName"    // 资管开户行

	Guarantor1CompanyName_key = "GuarantorCompanyName" // 担保公司1名称
	Guarantor1LegalPerson_key = "GuarantorLegalPerson" // 担保公司1法人姓名
	Guarantor1Address_key     = "GuarantorAddress"     // 担保公司1地址
	Guarantor1Tel_key         = "GuarantorTel"         // 担保公司1电话

	Guarantor2CompanyName_key = "Guarantor2CompanyNam"  // 担保公司2名称
	Guarantor2LegalPerson_key = "Guarantor2LegalPerson" // 担保公司2法人姓名
	Guarantor2Address_key     = "Guarantor2Address"     // 担保公司2地址

	MicroLoanCompanyName_key = "MicroLoanCompanyName" // 小贷公司名称
	MicroLoanLegalPerson_key = "MicroLoanLegalPerson" // 小贷公司法人姓名
	MicroLoanAddress_key     = "MicroLoanAddress"     // 小贷公司地址
	MicroLoanTelephone_key   = "MicroLoanTelephone"   // 小贷公司电话

	// 印章
	BorrowerSeal_key     = "BorrowerSeal"   // 借款人印章
	BorrowerSealTime_key = "BorrowerSeal"   // 借款人印章时间
	AssetMgmtSeal_key    = "AssetMgmtSeal"  // 资管印章
	Guarantor1Seal_key   = "Guarantor1Seal" // 担保公司1印章
	Guarantor2Seal_key   = "Guarantor2Seal" // 担保公司2印章
	MicroLoanSeal_key    = "MicroLoanSeal"  // 小贷公司印章

	// 日期
	SigningDate_key       = "SigningDate"       // 签约日期
	FirstPaymentDate_key  = "FirstPaymentDate"  // 第一笔钱的时间
	SecondPaymentDate_key = "SecondPaymentDate" // 第二笔钱的时间

	// 金额
	LoanAmount_key          = "LoanAmount"          // 借款金额
	TotalPeriods_key        = "TotalPeriods"        // 总期数
	LoanAmountInWords_key   = "LoanAmountInWords"   // 借款金额大写
	GuaranteeFee_key        = "GuaranteeFee"        // 担保费
	GuaranteeInWordsFee_key = "GuaranteeInWordsFee" // 担保费大写
	FirstGuaranteeFee_key   = "FirstGuaranteeFee"   // 第一笔担保费(借钱时不收)
	HalfGuaranteeFee_key    = "HalfGuaranteeFee"    // 中间一半担保费
	DuePrincipal_key        = "DuePrincipal"        // 实际放款金额明细（用于展示）
	ToTalDayKey             = "ToTalDay"            // 总天数
	BeginDate_key           = "BeginDate"           // 开始日期
	EndDate_key             = "EndDate"             // 结束日期
	AnnualInterestRate_key  = "AnnualInterestRate"  // 年利率
	// 合同ID
	ContractID_key = "ContractID"

	// 贷款方案
	GuaranteePlan1_key = "GuaranteePlan1" // 担保方案1 固定1
	GuaranteePlan2_key = "GuaranteePlan2" // 担保方案2 固定2

	TableKeyword_key = "plan" // 表格模板中表格的关键字(用于贷款告知书等表格模板
)

// BaseResponse 基础响应结构
type BaseResponse struct {
	Code int    `json:"code"` // 响应码(100000=成功)
	Msg  string `json:"msg"`  // 响应信息
}

type SignUser struct {
	Account    string `json:"account"`
	SignUrl    string `json:"signUrl"`
	PwdSignUrl string `json:"pwdSignUrl"`
	SignOrder  int    `json:"signOrder"`
	Name       string `json:"name"`
	IDCard     string `json:"idCard"`
	SignMark   string `json:"signMark"`
}

type ContractResponseData struct {
	ContractNo   string     `json:"contractNo"`
	ContractName string     `json:"contractName"`
	ValidityTime string     `json:"validityTime"`
	PreviewUrl   string     `json:"previewUrl"`
	SignUser     []SignUser `json:"signUser"`
}

type ContractResponse struct {
	Code int                  `json:"code"`
	Msg  string               `json:"msg"`
	Data ContractResponseData `json:"data"`
}

// 解析响应
type AuthResponse struct {
	Code int        `json:"code"`
	Msg  string     `json:"msg"`
	Data []AuthItem `json:"data"`
}

type AuthItem struct {
	AuthTypeCode int    `json:"authTypeCode"`
	AuthTypeName string `json:"authTypeName"`
	Result       int    `json:"result"`
	SerialNo     string `json:"serialNo"`
	UserType     int    `json:"userType"`
}
