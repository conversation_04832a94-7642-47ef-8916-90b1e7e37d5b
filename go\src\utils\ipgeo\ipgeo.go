package ipgeo

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"fincore/utils/utilstool/goredis"
)

// IPLocation IP地理位置信息结构体
// 用于存储IP地址对应的地理位置详细信息
type IPLocation struct {
	IP            string  `json:"ip"`             // IP地址
	Country       string  `json:"country"`        // 国家名称
	CountryCode   string  `json:"country_code"`   // 国家代码（如：CN、US）
	Region        string  `json:"region"`         // 省份/州名称
	RegionCode    string  `json:"region_code"`    // 省份/州代码
	City          string  `json:"city"`           // 城市名称
	PostalCode    string  `json:"postal_code"`    // 邮政编码
	ContinentCode string  `json:"continent_code"` // 大洲代码
	Latitude      float64 `json:"latitude"`       // 纬度坐标
	Longitude     float64 `json:"longitude"`      // 经度坐标
	Organization  string  `json:"organization"`   // 组织机构名称（ASN + ISP）
	Timezone      string  `json:"timezone"`       // 时区
}

// IPAPIResponse ip-api.com API响应结构体
// 用于解析 ip-api.com 服务返回的JSON数据
type IPAPIResponse struct {
	Status      string  `json:"status"`      // 查询状态（success/fail）
	Country     string  `json:"country"`     // 国家名称
	CountryCode string  `json:"countryCode"` // 国家代码（ISO 3166-1 alpha-2）
	Region      string  `json:"region"`      // 省份/州代码
	RegionName  string  `json:"regionName"`  // 省份/州全名
	City        string  `json:"city"`        // 城市名称
	Zip         string  `json:"zip"`         // 邮政编码
	Lat         float64 `json:"lat"`         // 纬度坐标
	Lon         float64 `json:"lon"`         // 经度坐标
	Timezone    string  `json:"timezone"`    // 时区标识符
	ISP         string  `json:"isp"`         // 互联网服务提供商名称
	Org         string  `json:"org"`         // 组织机构名称
	AS          string  `json:"as"`          // 自治系统信息
	Query       string  `json:"query"`       // 查询的IP地址
}

// GetIPLocation 获取IP地理位置信息（带Redis缓存）
// 优先从Redis缓存中获取，缓存未命中时调用API并缓存结果
func GetIPLocation(ip string) (*IPLocation, error) {
	// 验证IP地址格式
	if ip == "" || ip == "127.0.0.1" || ip == "::1" || ip == "localhost" {
		return &IPLocation{
			IP:      ip,
			Country: "本地",
			City:    "本地",
		}, nil
	}

	// 先尝试从Redis缓存中获取
	if location, err := getIPLocationFromCache(ip); err == nil && location != nil {
		return location, nil
	}

	// 缓存未命中，调用API获取数据
	location, err := getIPLocationFromAPI(ip)
	if err != nil {
		return nil, err
	}

	// 将结果缓存到Redis（异步执行，不影响主流程）
	go func() {
		if err := setIPLocationToCache(ip, location); err != nil {
			// 缓存失败不影响主流程，只记录错误（这里可以根据需要添加日志）
			fmt.Printf("缓存IP地理位置信息失败: %v\n", err)
		}
	}()

	return location, nil
}

// getIPLocationFromCache 从Redis缓存中获取IP地理位置信息
func getIPLocationFromCache(ip string) (*IPLocation, error) {
	// 构建缓存键
	cacheKey := fmt.Sprintf("ipgeo:location:%s", ip)

	// 从Redis获取缓存数据
	exists, jsonData := goredis.Get(cacheKey)
	if !exists || jsonData == "" {
		return nil, fmt.Errorf("缓存未命中")
	}

	// 反序列化JSON数据
	var location IPLocation
	if err := json.Unmarshal([]byte(jsonData), &location); err != nil {
		return nil, fmt.Errorf("反序列化缓存数据失败: %v", err)
	}

	return &location, nil
}

// setIPLocationToCache 将IP地理位置信息缓存到Redis
func setIPLocationToCache(ip string, location *IPLocation) error {
	// 构建缓存键
	cacheKey := fmt.Sprintf("ipgeo:location:%s", ip)

	// 序列化数据
	jsonData, err := json.Marshal(location)
	if err != nil {
		return fmt.Errorf("序列化地理位置数据失败: %v", err)
	}

	// 设置缓存，过期时间为1年（IP地理位置信息基本不会变化）
	expiration := 365 * 24 * time.Hour
	success, err := goredis.SetEX(cacheKey, string(jsonData), expiration)
	if err != nil {
		return fmt.Errorf("设置Redis缓存失败: %v", err)
	}
	if !success {
		return fmt.Errorf("Redis缓存设置失败")
	}

	return nil
}

// getIPLocationFromAPI 从API获取IP地理位置信息
func getIPLocationFromAPI(ip string) (*IPLocation, error) {
	// 构建请求URL - 使用ip-api.com服务，指定中文语言
	url := fmt.Sprintf("http://ip-api.com/json/%s?lang=zh-CN", ip)

	// 创建HTTP客户端，设置超时时间
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	// 发送GET请求
	resp, err := client.Get(url)
	if err != nil {
		return nil, fmt.Errorf("请求IP地理位置服务失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("IP地理位置服务返回错误状态码: %d", resp.StatusCode)
	}

	// 解析JSON响应
	var geoResp IPAPIResponse
	if err := json.NewDecoder(resp.Body).Decode(&geoResp); err != nil {
		return nil, fmt.Errorf("解析IP地理位置响应失败: %v", err)
	}

	// 检查API响应状态
	if geoResp.Status != "success" {
		return nil, fmt.Errorf("IP地理位置查询失败: %s", geoResp.Status)
	}

	// 转换为标准格式
	location := &IPLocation{
		IP:            geoResp.Query,
		Country:       geoResp.Country,
		CountryCode:   geoResp.CountryCode,
		Region:        geoResp.RegionName,
		RegionCode:    geoResp.Region,
		City:          geoResp.City,
		PostalCode:    geoResp.Zip,
		ContinentCode: "", // ip-api.com不提供大洲代码
		Latitude:      geoResp.Lat,
		Longitude:     geoResp.Lon,
		Organization:  geoResp.Org,
		Timezone:      geoResp.Timezone,
	}

	return location, nil
}

// FormatLocation 格式化地理位置为可读字符串
// 返回格式：国家 省份 城市
func FormatLocation(location *IPLocation) string {
	if location == nil {
		return ""
	}

	// 特殊处理本地IP
	if location.Country == "本地" {
		return "本地"
	}

	var parts []string

	// 添加国家
	if location.Country != "" {
		parts = append(parts, location.Country)
	}

	// 添加省份/地区
	if location.Region != "" && location.Region != location.Country {
		parts = append(parts, location.Region)
	}

	// 添加城市
	if location.City != "" && location.City != location.Region && location.City != "本地" {
		parts = append(parts, location.City)
	}

	// 如果没有任何信息，返回IP地址
	if len(parts) == 0 && location.IP != "" {
		return location.IP
	}

	return strings.Join(parts, " ")
}

// GetIPLocationString 获取IP地理位置字符串（便捷方法）
// 直接返回格式化的地理位置字符串
func GetIPLocationString(ip string) string {
	location, err := GetIPLocation(ip)
	if err != nil {
		// 如果查询失败，返回原IP地址
		return ip
	}

	formatted := FormatLocation(location)
	if formatted == "" {
		return ip
	}

	return formatted
}

// BatchGetIPLocations 批量获取IP地理位置信息（带Redis缓存优化）
// 优先从缓存获取，只对缓存未命中的IP调用API，减少API请求次数
func BatchGetIPLocations(ips []string) map[string]*IPLocation {
	results := make(map[string]*IPLocation)
	uncachedIPs := make([]string, 0)

	// 第一步：尝试从缓存中获取所有IP的地理位置信息
	for _, ip := range ips {
		if ip == "" {
			continue
		}

		// 先尝试从缓存获取
		if location, err := getIPLocationFromCache(ip); err == nil && location != nil {
			results[ip] = location
		} else {
			// 缓存未命中，记录需要API查询的IP
			uncachedIPs = append(uncachedIPs, ip)
		}
	}

	// 第二步：对缓存未命中的IP调用API查询
	for _, ip := range uncachedIPs {
		location, err := getIPLocationFromAPI(ip)
		if err != nil {
			// 如果查询失败，创建一个只包含IP的记录
			location = &IPLocation{
				IP:      ip,
				Country: "未知",
				City:    "未知",
			}
		}

		results[ip] = location

		// 异步缓存结果
		go func(ipAddr string, loc *IPLocation) {
			if err := setIPLocationToCache(ipAddr, loc); err != nil {
				fmt.Printf("批量缓存IP地理位置信息失败 [%s]: %v\n", ipAddr, err)
			}
		}(ip, location)

		// 添加短暂延迟，避免API限制
		time.Sleep(100 * time.Millisecond)
	}

	return results
}

// IsValidIP 简单的IP地址格式验证
func IsValidIP(ip string) bool {
	if ip == "" {
		return false
	}

	// 简单验证：检查是否包含点号（IPv4）或冒号（IPv6）
	return strings.Contains(ip, ".") || strings.Contains(ip, ":")
}

// ClearIPLocationCache 清除指定IP的地理位置缓存
func ClearIPLocationCache(ip string) error {
	cacheKey := fmt.Sprintf("ipgeo:location:%s", ip)
	success, err := goredis.Del(cacheKey)
	if err != nil {
		return fmt.Errorf("删除IP地理位置缓存失败: %v", err)
	}
	if !success {
		return fmt.Errorf("删除IP地理位置缓存失败")
	}
	return nil
}

// RefreshIPLocationCache 刷新指定IP的地理位置缓存
// 强制重新从API获取数据并更新缓存
func RefreshIPLocationCache(ip string) (*IPLocation, error) {
	// 先删除现有缓存
	_ = ClearIPLocationCache(ip) // 忽略删除错误

	// 从API获取最新数据
	location, err := getIPLocationFromAPI(ip)
	if err != nil {
		return nil, err
	}

	// 更新缓存
	if err := setIPLocationToCache(ip, location); err != nil {
		// 缓存失败不影响返回结果，只记录错误
		fmt.Printf("刷新IP地理位置缓存失败: %v\n", err)
	}

	return location, nil
}

// GetIPLocationCacheKey 获取IP地理位置缓存键名（用于调试或监控）
func GetIPLocationCacheKey(ip string) string {
	return fmt.Sprintf("ipgeo:location:%s", ip)
}

// CheckIPLocationCacheExists 检查指定IP的地理位置缓存是否存在
func CheckIPLocationCacheExists(ip string) bool {
	cacheKey := fmt.Sprintf("ipgeo:location:%s", ip)
	exists, _ := goredis.Get(cacheKey)
	return exists
}
