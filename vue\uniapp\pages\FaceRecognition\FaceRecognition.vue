<template>
	<view class="auth-container">
		<!-- <view class="custom-header-box">
			<view class="custom-header">
				  <uni-icons class="custom-header-icons" type="left" size="25" @click="goBackHome"></uni-icons>
			      <text class="custom-header-title">实名认证</text>
			</view>
		</view> -->
		<div :style="{ height: statusBarHeight + 'px' }"></div>
		<uni-nav-bar left-icon="left" title="实名认证" @clickLeft="goBackHome"></uni-nav-bar>
		<!-- 表单区域 -->
		<view class="form-container">
			<!-- 基本信息 -->
			<view class="form-section">
				<view class="section-item-box">
					<!-- 学历 -->
					<view class="form-item" @click="showPicker('degree', formData.degree)">
						<text class="label">学历：</text>
						<text class="value" :class="{ selected: formData.degree }">
							{{ formData.degree || "请选择学历" }}
						</text>
						<!-- <uni-icons type="arrowright" size="16" color="#999"></uni-icons> -->
					</view>

					<!-- 婚姻状况 -->
					<view class="form-item" @click="showPicker('marry', formData.marry)">
						<text class="label">婚姻状况：</text>
						<text class="value" :class="{ selected: formData.marry }">
							{{ formData.marry || "请选择婚姻状况" }}
						</text>
						<!-- <uni-icons type="arrowright" size="16" color="#999"></uni-icons> -->
					</view>
				</view>

				<!-- <view class="section-item-box">
					<view class="form-item">
						<text class="label">详细地址：</text>
						<input v-model="formData.address" placeholder="请输入详细地址" placeholder-class="placeholder" />
					</view>
				</view> -->

				<view class="section-item-box">
					<!-- 工作行业 -->
					<view class="form-item" @click="showPicker('occupation', formData.occupation)">
						<text class="label">工作行业：</text>
						<text class="value" :class="{ selected: formData.occupation }">
							{{ formData.occupation || "请选择工作行业" }}
						</text>
						<!-- <uni-icons type="arrowright" size="16" color="#999"></uni-icons> -->
					</view>

					<!-- 年收入状况 -->
					<view class="form-item" @click="showPicker('yearrevenue', formData.yearrevenue)">
						<text class="label">年收入状况：</text>
						<text class="value" :class="{ selected: formData.yearrevenue }">
							{{ formData.yearrevenue || "请选择年收入状况" }}
						</text>
						<!-- <uni-icons type="arrowright" size="16" color="#999"></uni-icons> -->
					</view>
				</view>

				<view class="section-item-box">
					<!-- 借款用途 -->
					<view class="form-item" @click="showPicker('purposeofborrowing', formData.purposeofborrowing)">
						<text class="label">借款用途：</text>
						<text class="value" :class="{ selected: formData.purposeofborrowing }">
							{{ formData.purposeofborrowing || "请选择借款用途" }}
						</text>
						<!-- <uni-icons type="arrowright" size="16" color="#999"></uni-icons> -->
					</view>
				</view>

			</view>

			<!-- 联系人信息 -->
			<view class="form-section">
				<!-- 联系人1 -->
				<view class="contact-item">
					<!-- <text class="contact-label">关系1</text> -->
					<view class="contact-form">
						<picker mode="selector" :range="relationOptions" @change="(e) => handleRelationChange(0, e)">
							<view class="picker" :style="{ color: formData.emergencycontact0relation?'#000':'' }">
								{{ formData.emergencycontact0relation || "请选择联系人与您的关系" }}
							</view>
						</picker>
						<input :value="formData.emergencycontact0name"
							@input="formData.emergencycontact0name = $event.detail.value" placeholder="联系人姓名"
							placeholder-class="placeholder" />
						<input v-model="formData.emergencycontact0phone" type="number" placeholder="联系人手机号"
							placeholder-class="placeholder" maxlength="11" />
					</view>
				</view>

				<!-- 联系人2 -->
				<view class="contact-item">
					<!-- <text class="contact-label">关系2</text> -->
					<view class="contact-form">
						<picker mode="selector" :range="relationOptions" @change="(e) => handleRelationChange(1, e)">
							<view class="picker" :style="{ color: formData.emergencycontact1relation?'#000':'' }">
								{{formData.emergencycontact1relation || "请选择联系人与您的关系"}}
							</view>
						</picker>
						<input :value="formData.emergencycontact1name"
							@input="formData.emergencycontact1name = $event.detail.value" placeholder="联系人姓名"
							placeholder-class="placeholder" />
						<input v-model="formData.emergencycontact1phone" type="number" placeholder="联系人手机号"
							placeholder-class="placeholder" maxlength="11" />
					</view>
				</view>
			</view>
		</view>

		<!-- 提交按钮 -->
		<button class="submit-btn" :class="!canSubmit?'disabled':''" @click="handleSubmit">
			{{ submitBtnTxt }}
		</button>
		
		<!-- 选择器 -->
		<uni-popup ref="pickerPopup" type="bottom">
			<view class="picker-container">
				<view class="picker-header">
					<text class="picker-cancel" @click="closePicker">取消</text>
					<text class="picker-title">{{ pickerTitle }}</text>
					<text class="picker-confirm" @click="confirmPicker">确定</text>
				</view>
				<picker-view class="picker-view" :value="pickerValue" @change="handlePickerChange">
					<picker-view-column>
						<view class="picker-item" v-for="(item, index) in currentOptions" :key="index">
							{{ item }}
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import {
		ref,
		computed
	} from "vue";
	
	import userApi from "@/api/user";
	
	import user from '@/store/user.js';
	
	import {
		onShow,
		onLoad
	} from '@dcloudio/uni-app';
	const statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
	
	const userStore = user();
	// 表单数据
	const formData = ref({
		// 基本信息
		degree: "",
		marry: "",
		address: "",
		occupation: "",
		yearrevenue: "",
		purposeofborrowing: "",

		// 联系人信息
		emergencycontact0relation: "",
		emergencycontact0name: "",
		emergencycontact0phone: "",
		emergencycontact1relation: "",
		emergencycontact1name: "",
		emergencycontact1phone: ""
	});
	
	let submitBtnTxt = ref('提交认证')

	// 选项数据
	const educationOptions = [
		"小学",
		"初中",
		"高中/中专",
		"大专",
		"本科",
		"硕士",
		"博士",
	];
	const marryOptions = ["未婚", "已婚", "离异", "丧偶"];
	const occupationOptions = [
		"IT/互联网",
		"金融",
		"教育",
		"医疗",
		"建筑",
		"制造业",
		"服务业",
		"自由职业",
		"其他",
	];
	const revenueOptions = ["5万以下", "5-10万", "10-20万", "20-50万", "50万以上"];
	const purposeOptions = [
		"日常消费",
		"医疗支出",
		"教育支出",
		"装修",
		"旅游",
		"其他",
	];
	const relationOptions = ["父母", "配偶", "子女", "兄弟姐妹", "朋友", "同事"];

	// 选择器相关状态
	const pickerPopup = ref(null);
	const currentField = ref("");
	const pickerValue = ref([0]);
	const currentOptions = ref([]);
	const pickerTitle = ref("请选择");
	
	const pageType = ref();
	onLoad(async (e) => {
		if(e.type == 'edit') {
			pageType.value = e.type;
			uni.setNavigationBarTitle({
				title: "身份信息"
			})
			submitBtnTxt.value = '提交资料';
		}
		const userInfo = await userStore.getInfo();
		//console.log('11',userInfo)
		if(userInfo && userInfo.uid) {
			// const userInfo = userStore.userInfo;
			formData.value.degree = userInfo.degree || '';
			formData.value.marry = userInfo.marry || '';
			formData.value.address = userInfo.address || '';
			formData.value.occupation = userInfo.occupation || '';
			formData.value.yearrevenue = userInfo.yearRevenue || '';
			formData.value.purposeofborrowing = userInfo.purposeOfBorrowing || '';
			formData.value.emergencycontact0relation = userInfo.emergencyContact0Relation || '';
			formData.value.emergencycontact0name = userInfo.emergencyContact0Name || '';
			formData.value.emergencycontact0phone = userInfo.emergencyContact0Phone || '';
			
			formData.value.emergencycontact1relation = userInfo.emergencyContact1Relation || '';
			formData.value.emergencycontact1name = userInfo.emergencyContact1Name || '';
			formData.value.emergencycontact1phone = userInfo.emergencyContact1Phone || '';
			//console.log('22', formData.value.address)
			if(!formData.value.address || formData.value.address == null || formData.value.address == undefined || formData.value.address=="") {
				getLocation();
			}
		}else{
			getLocation();	
		}
	})
	function getLocation() {
		let _this = this;
		console.log('getLocation');
		uni.getLocation({
			// #ifdef WEB
			type: 'wgs84',
			// #endif
			// #ifndef WEB
			type: 'gcj02',
			// #endif
			success: function(res) {
				const longitude = res.longitude;
				const latitude = res.latitude;
				// 使用经纬度查询详细地址
				console.log('使用经纬度查询详细地址', res)
				getAddress(longitude, latitude);
			},
			fail: function(err) {
				console.log('获取地址失败',err)
			}
		});
	}
	function getAddress(longitude, latitude) {
		const apiKey = '7317c32b5e2ebc7e0306876db5e74f20';
		const url = `https://restapi.amap.com/v3/geocode/regeo`;
	
		const params = {
			output: 'json',
			key: apiKey,
			location: `${longitude},${latitude}`,
			extensions: 'base'
		}
	
		uni.request({
			url: url,
			data: params,
			success: (res) => {
				console.log(res.data.regeocode.formatted_address)
				formData.value.address = res.data.regeocode.formatted_address;
			},
			fail: (err) => {
				console.error('请求失败', err);
			}
		});
	}
	function goBackHome() {
		if(pageType.value == 'edit') {
			uni.switchTab({
				url: '/pages/profile/profile'
			})
		}else{
			uni.switchTab({
				url: '/pages/index/index'
			})
		}
	}
	// 显示选择器
	const showPicker = (field, data) => {
		currentField.value = field;
		let index = 0;
		switch (field) {
			case "degree":
				index = educationOptions.findIndex(el => el == data);
				currentOptions.value = educationOptions;
				pickerTitle.value = "选择学历";
				break;
			case "marry":
				index = marryOptions.findIndex(el => el == data);
				currentOptions.value = marryOptions;
				pickerTitle.value = "选择婚姻状况";
				break;
			case "occupation":
				index = occupationOptions.findIndex(el => el == data);
				currentOptions.value = occupationOptions;
				pickerTitle.value = "选择工作行业";
				break;
			case "yearrevenue":
				index = revenueOptions.findIndex(el => el == data);
				currentOptions.value = revenueOptions;
				pickerTitle.value = "选择年收入状况";
				break;
			case "purposeofborrowing":
				index = purposeOptions.findIndex(el => el == data);
				currentOptions.value = purposeOptions;
				pickerTitle.value = "选择借款用途";
				break;
		}
		pickerValue.value = [index == -1?0:index];
		pickerPopup.value.open();
	};
	

	// 处理选择器变化
	const handlePickerChange = (e) => {
		pickerValue.value = e.detail.value;
	};

	// 确认选择
	const confirmPicker = () => {
		const index = pickerValue.value[0];
		formData.value[currentField.value] = currentOptions.value[index];
		pickerPopup.value.close();
	};

	// 关闭选择器
	const closePicker = () => {
		pickerPopup.value.close();
	};

	// 处理联系人关系选择
	const handleRelationChange = (index, e) => {
		const relation = relationOptions[e.detail.value];
		if (index === 0) {
			formData.value.emergencycontact0relation = relation;
		} else {
			formData.value.emergencycontact1relation = relation;
		}
	};

	// 提交表单
	const handleSubmit = async () => {
		// "degree",
		// "marry",
		// "address",
		// "occupation",
		// "yearrevenue",
		// "purposeofborrowing",
		// "emergencycontact0relation",
		// "emergencycontact0name",
		// "emergencycontact0phone",
		// "emergencycontact1relation",
		// "emergencycontact1name",
		// "emergencycontact1phone",
		
		if(!formData.value.degree) {
			uni.showToast({
				title: "请选择学历",
				icon: "none"
			});
			return;
		}
		if(!formData.value.marry) {
			uni.showToast({
				title: "请选择婚姻状况",
				icon: "none"
			});
			return;
		}
		if(!formData.value.address) {
			uni.showToast({
				title: "请输入详细地址",
				icon: "none"
			});
			return;
		}
		if(!formData.value.occupation) {
			uni.showToast({
				title: "请选择工作行业",
				icon: "none"
			});
			return;
		}
		if(!formData.value.yearrevenue) {
			uni.showToast({
				title: "请选择年收入状况",
				icon: "none"
			});
			return;
		}
		if(!formData.value.purposeofborrowing) {
			uni.showToast({
				title: "请选择借款用途",
				icon: "none"
			});
			return;
		}
		
		if(!formData.value.emergencycontact0relation) {
			uni.showToast({
				title: "请选择联系人1与您的关系",
				icon: "none"
			});
			return;
		}
		if(!formData.value.emergencycontact0name) {
			uni.showToast({
				title: "请输入联系人1的姓名",
				icon: "none"
			});
			return;
		}
		if(!formData.value.emergencycontact0phone) {
			uni.showToast({
				title: "请输入联系人1的手机号",
				icon: "none"
			});
			return;
		}
		
		if(!formData.value.emergencycontact1relation) {
			uni.showToast({
				title: "请选择联系人2与您的关系",
				icon: "none"
			});
			return;
		}
		if(!formData.value.emergencycontact1name) {
			uni.showToast({
				title: "请输入联系人2的姓名",
				icon: "none"
			});
			return;
		}
		if(!formData.value.emergencycontact1phone) {
			uni.showToast({
				title: "请输入联系人2的手机号",
				icon: "none"
			});
			return;
		}
		
		// 校验手机号格式
		if (!/^1[3-9]\d{9}$/.test(formData.value.emergencycontact0phone)) {
			uni.showToast({
				title: "联系人1手机号格式不正确",
				icon: "none"
			});
			return;
		}
		if (!/^1[3-9]\d{9}$/.test(formData.value.emergencycontact1phone)) {
			uni.showToast({
				title: "联系人2手机号格式不正确",
				icon: "none"
			});
			return;
		}
		// 校验姓名只能为汉字
		if (!/^[\u4e00-\u9fa5]+$/.test(formData.value.emergencycontact0name)) {
			uni.showToast({
				title: "联系人1姓名只能输入汉字",
				icon: "none"
			});
			return;
		}
		if (!/^[\u4e00-\u9fa5]+$/.test(formData.value.emergencycontact1name)) {
			uni.showToast({
				title: "联系人2姓名只能输入汉字",
				icon: "none"
			});
			return;
		}

		try {
			const res = await userApi.postUserInfo2(formData.value);

			uni.showToast({
				title: "认证成功",
				icon: "success",
			});

			// 正确跳转到首页
			uni.switchTab({
				url: "/pages/index/index",
			});
		} catch (error) {
			uni.showToast({
				title: error.message || "提交失败",
				icon: "none",
			});
		}
		console.log("表单提交处理完成");
	};

	// 计算是否可提交
	const canSubmit = computed(() => {
		const requiredFields = [
			"degree",
			"marry",
			"address",
			"occupation",
			"yearrevenue",
			"purposeofborrowing",
			"emergencycontact0relation",
			"emergencycontact0name",
			"emergencycontact0phone",
			"emergencycontact1relation",
			"emergencycontact1name",
			"emergencycontact1phone",
		];

		return requiredFields.every((field) => {
			const value = formData.value[field];
			// 手机号需要验证格式
			if (field.includes("phone")) {
				return /^1[3-9]\d{9}$/.test(value);
			}
			return value && value.trim() !== "";
		});
	});
</script>

<style lang="scss" scoped>
	.auth-container {
		padding: 0;
		background-color: #eff2f7;
		min-height: 100vh;
		overflow: hidden;

		.header {
			padding: 30rpx 0;
			text-align: center;

			.title {
				font-size: 36rpx;
				font-weight: bold;
				color: #333;
			}

			.subtitle {
				font-size: 24rpx;
				color: #999;
				margin-top: 10rpx;
				display: block;
			}
		}

		.form-container {
			// background-color: #fff;
			border-radius: 15rpx;
			padding: 20rpx;
		}

		.form-section {
			// width: 100%;
			// height: 60px;
			// padding: 10px 0;
			// background-color: deeppink;
			// border-bottom: 1rpx solid #f0f0f0;

			&:last-child {
				border-bottom: none;
			}

			.section-title {
				font-size: 28rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 20rpx;
			}

		}
		.section-item-box{
			background-color: #fff;
			border-radius: 20rpx;
			margin-bottom: 20rpx;
		}

		.form-item {
			display: flex;
			align-items: center;
			padding: 20rpx 20rpx;

			&:last-child {
				border-bottom: none;
			}

			.label {
				width: 180rpx;
				font-size: 28rpx;
				color: #333;
			}

			.value {
				flex: 1;
				font-size: 28rpx;
				color: #999;
				height: 32px;
				line-height: 32px;
			}

			.value.selected {
				color: #333;
			}

			input {
				flex: 1;
				height: 32px;
				line-height: 32px;
				border: none;
				outline: none;
				font-size: 14px;
				color: #000;
				text-align: left;
			}

			input:focus {
				color: #000;
			}

			.placeholder {
				color: #999;
			}
		}

		.contact-item {
			margin-bottom: 30rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.contact-label {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 20rpx;
				display: block;
			}

			.contact-form {
				background-color: #f9f9f9;
				border-radius: 8rpx;
				padding: 20rpx;
				margin-top: 13px;
				border-radius: 10px;

				.picker {
					font-size: 28rpx;
					color: #999;
					padding: 20rpx 0;
					border-bottom: 1rpx solid #eee;
				}

				input {
					width: 100%;
					font-size: 28rpx;
					padding: 20rpx 0;
					border-bottom: 1rpx solid #eee;
					// padding-left: 16rpx;

					&:last-child {
						border-bottom: none;
					}
				}
			}
		}

		.submit-btn {
			margin: 40rpx 20rpx;
			background-color: #1a73e8;
			color: #fff;
			border-radius: 50rpx;
			font-size: 32rpx;
			height: 90rpx;
			line-height: 90rpx;

			&.disabled {
				background-color: #ccc;
			}
		}

		.picker-container {
			background-color: #fff;
			border-radius: 20rpx 20rpx 0 0;
			padding-bottom: env(safe-area-inset-bottom);

			.picker-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx 30rpx;
				border-bottom: 1rpx solid #eee;

				.picker-cancel,
				.picker-confirm {
					font-size: 28rpx;
					color: #1a73e8;
				}

				.picker-title {
					font-size: 32rpx;
					font-weight: bold;
				}
			}

			.picker-view {
				height: 400rpx;

				.picker-item {
					font-size: 28rpx;
					text-align: center;
					line-height: 80rpx;
				}
			}
		}
	}

	// input[placeholder="请输入详细地址"],
	// input[placeholder="请输入您的详细地址 (选填)"] {
	// 	text-align: right;
	// }
	
	
	.custom-header-box{
		height: 80rpx;
		position: relative;
	}
	.custom-header {
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  height: 80rpx; /* 根据需要调整 */
	  padding: 0 20rpx; /* 根据需要调整 */
	  position: fixed;
	  background-color: #F8F8F8;
	  left: 0;
	  top: 0;
	  width: 100%;
	  box-sizing: border-box;
	  z-index: 10;
	}
	.custom-header-icons{
		position: absolute;
		left: 10rpx;
		top: 15rpx;
	}
	.custom-header-title {
		font-weight: bold;
		font-size: 16px;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
</style>