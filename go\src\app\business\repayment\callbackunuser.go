package repayment

import (
	"fincore/app/dianziqian/utils"
	"fincore/model"
	"fincore/utils/decimal"
	"fincore/utils/gform"
	"fincore/utils/lock"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
)

// 代扣类型常量
const (
	WithholdTypeAsset     = "ASSET"     // 资产代扣
	WithholdTypeGuarantee = "GUARANTEE" // 担保代扣
)

// 代付回调状态常量
const (
	DisbursementCallbackStatusSuccess    = "00" // 成功
	DisbursementCallbackStatusProcessing = "01" // 处理中
	DisbursementCallbackStatusFailed     = "03" // 失败
)

// 代付回调响应码常量
const (
	CallbackResponseCodeSuccess = "000000" // 成功
	CallbackResponseCodeFailed  = "000001" // 失败
)

// 回调基础结构体
type CallbackBase struct {
	RespCode string `json:"resp_code"` // 响应码 成功返回 000000
	RespMsg  string `json:"resp_msg"`  // 响应码不为 0 返回失败原因
	SignType string `json:"sign_type"` // 签名类型 支持CERT和RSA必须大写,API接入方式使用CERT
	Sign     string `json:"sign"`      // 签名
}

// 代付回调通知
type DisbursementCallbackRequest struct {
	CallbackBase
	OrderNo       string `json:"order_no"`                        // 订单号
	TraceNo       string `json:"trace_no"`                        // 交易流水号
	OrderAmount   string `json:"order_amount"`                    // 订单金额 保留两位小数
	Status        string `json:"status"`                          // 状态 00 成功，01 处理中，03 失败
	FeeAmount     string `optional:"true" json:"fee"`             // 手续费 保留两位小数
	OboType       string `optional:"true" json:"obo_type"`        // 业务类型 01 代付，02 代收
	Remark        string `optional:"true" json:"remark"`          // 商户请求传入的remark, 原样返回
	TtfReturnCode string `optional:"true" json:"ttf_return_code"` // 失败时可能会返回
	TtfReturnMsg  string `optional:"true" json:"ttf_return_msg"`  // 失败时可能会返回
}

type DisbursementCallbackResponse struct {
	RespCode string `json:"resp_code"` // 响应码 成功返回 000000
	RespMsg  string `json:"resp_msg"`  // 响应码不为 0 返回失败原因
}
type PaymentCallbackRequest struct {
	CallbackBase
	MerNo           string  `json:"mer_no"`                            // 商户号
	OrderNo         string  `json:"order_no"`                          // 订单号
	OffsetAmount    float64 `json:"offset_amount"`                     // 优惠金额
	PaidAmout       float64 `json:"paid_amount"`                       // 实付金额
	TradeNo         string  `json:"trade_no"`                          // 交易流水号
	OrderTime       string  `json:"order_time"`                        // 订单时间
	Status          string  `json:"status"`                            // 状态 00 成功，01 处理中，03 失败
	SuccessTime     string  `json:"success_time"`                      // 成功时间
	SuccessAmount   string  `json:"success_amount"`                    // 成功金额
	Remark          string  `json:"remark" optional:"true"`            // 备注
	TtfReturnCode   string  `json:"ttf_return_code" optional:"true"`   // 失败时可能会返回
	TtfReturnMsg    string  `json:"ttf_return_msg" optional:"true"`    // 失败时可能会返回
	TraceCode       string  `json:"trace_code" optional:"true"`        // T0001:担保交易 T0002:即时交易
	PayProductCode  string  `json:"pay_product_code" optional:"true"`  // 支付产品码
	OffestDetail    string  `json:"offest_detail" optional:"true"`     // 优惠详情
	ChannelSerialNo string  `json:"channel_serial_no" optional:"true"` // 渠道流水号
	PeriodNum       string  `json:"period_num" optional:"true"`        // 信用卡分期支付必填，暂只有：3、6、9、12、18、24、36、60
	Attach          string  `json:"attach" optional:"true"`            // 附加信息
}

// HandlePaymentCallback 处理支付回调通知

func (s *PaymentService) HandlePaymentCallback(req *PaymentCallbackRequest) (err error) {
	// 🔒 添加回调处理锁，防止并发回调
	lockKey := fmt.Sprintf("payment_callback:%s", req.OrderNo)
	callbackLock := lock.GetLock(lockKey, 30*time.Second)

	callbackLock.Lock()
	defer callbackLock.Unlock()

	// 分解 order_no 获取订单编号
	orderNo, transactionNo, withholdType, err := ParsePayOrderNoFields(req.OrderNo)
	if err != nil {
		return err
	}

	// 根据流水号和订单编号查询交易记录
	transaction, err := s.transactionModel.GetTransactionByNo(transactionNo)
	if err != nil {
		return err
	}

	if transaction.OrderNo != orderNo {
		return fmt.Errorf("订单编号不匹配")
	}

	if *transaction.WithholdType != withholdType {
		return fmt.Errorf("代扣类型与流水记录不匹配")
	}

	callbackResult, err := utils.StructToJson(req)
	if err != nil {
		return fmt.Errorf("转换结果失败: %v", err)
	}

	// 根据回调状态调用不同的处理函数
	switch req.Status {
	case DisbursementCallbackStatusSuccess:
		err = s.handlePaymentSuccess(transaction, req, withholdType, callbackResult)
	case DisbursementCallbackStatusFailed:
		err = s.handlePaymentFailed(transaction, req, callbackResult)
	case DisbursementCallbackStatusProcessing:
		err = s.handlePaymentProcessing(transaction, req, callbackResult)
	default:
		err = fmt.Errorf("未知的回调状态: %s", req.Status)
	}
	return
}

// handlePaymentSuccess 处理支付成功回调
func (s *PaymentService) handlePaymentSuccess(transaction *model.BusinessPaymentTransactions, req *PaymentCallbackRequest, withholdType string, callbackResult string) error {
	return model.DB(model.WithContext(s.ctx)).Transaction(func(tx gform.IOrm) error {
		// 更新支付流水
		updateMap := map[string]interface{}{
			"status":                 model.TransactionStatusSuccess,
			"amount":                 model.Decimal(req.PaidAmout),
			"channel_transaction_no": req.ChannelSerialNo,
			"error_code":             req.RespCode,
			"error_message":          req.RespMsg,
			"callback_result":        callbackResult,
		}
		updateErr := s.transactionModel.UpdateTransactionStatus(tx, model.UpdateTransactionStatusResultWhere{
			ID: transaction.ID,
		}, updateMap)
		if updateErr != nil {
			return fmt.Errorf("更新支付流水失败: %v", updateErr)
		}

		// 更新账单
		billID := transaction.BillID
		if billID == nil {
			return fmt.Errorf("账单ID为空")
		}

		// 获取账单
		bill, err := s.billModel.GetBillByID(*billID)
		if err != nil {
			return fmt.Errorf("获取账单失败: %v", err)
		}

		// 当期已还金额累加
		updateBillMap := map[string]interface{}{
			"paid_amount": bill.PaidAmount + model.Decimal(req.PaidAmout),
		}
		// 支付资管费，对应账单的资管费累减
		if withholdType == "ASSET" {
			updateBillMap["asset_management_entry"] = bill.AssetManagementEntry - model.Decimal(req.PaidAmout)
		} else {
			// 支付担保费，对应账单的担保费累减
			updateBillMap["due_guarantee_fee"] = bill.DueGuaranteeFee - model.Decimal(req.PaidAmout)
		}

		// 是否还清
		if updateBillMap["paid_amount"].(model.Decimal) >= updateBillMap["total_due_amount"].(model.Decimal) {
			now := time.Now()
			updateBillMap["paid_at"] = now
			updateBillMap["status"] = model.RepaymentBillStatusSettled
		} else {
			updateBillMap["status"] = model.RepaymentBillStatusPartialPaid
		}

		_, updateBillErr := s.billModel.UpdateBill(tx, model.UpdateBillResultWhere{
			ID: *billID,
		}, updateBillMap)
		if updateBillErr != nil {
			return fmt.Errorf("更新账单失败: %v", updateBillErr)
		}

		// 获取订单
		order, err := s.orderModel.GetOrderByID(tx, transaction.OrderID)
		if err != nil {
			return fmt.Errorf("获取订单失败: %v", err)
		}

		// 更新订单已还金额
		amountPaid := order.AmountPaid.Add(decimal.Decimal(req.PaidAmout))

		orderUpdateMap := map[string]interface{}{
			"amount_paid": amountPaid,
		}

		// 检查并更新订单状态
		if err := s.checkAndUpdateOrderStatus(tx, transaction.OrderID, amountPaid, order.TotalRepayableAmount, orderUpdateMap); err != nil {
			return err
		}

		orderUpdateErr := s.orderModel.UpdateOrder(tx, model.UpdateOrderCondition{
			ID: transaction.OrderID,
		}, orderUpdateMap)
		if orderUpdateErr != nil {
			return fmt.Errorf("更新订单已还金额失败: %v", orderUpdateErr)
		}

		return nil
	})
}

// checkAndUpdateOrderStatus 检查并更新订单状态
func (s *PaymentService) checkAndUpdateOrderStatus(tx gform.IOrm, orderID int, amountPaid decimal.Decimal, totalRepayableAmount decimal.Decimal, orderUpdateMap map[string]interface{}) error {
	// 订单已经还清
	if amountPaid >= totalRepayableAmount {
		orderUpdateMap["status"] = model.OrderStatusCompleted
		now := time.Now()
		orderUpdateMap["completed_at"] = now

		// 可以在这里添加订单完成后的其他业务逻辑
		// 例如：发送通知、更新用户信用等
	}

	return nil
}

// handlePaymentFailed 处理支付失败回调
func (s *PaymentService) handlePaymentFailed(transaction *model.BusinessPaymentTransactions, req *PaymentCallbackRequest, callbackResult string) error {
	updateMap := map[string]interface{}{
		"status":          model.TransactionStatusFailed,
		"error_code":      req.RespCode,
		"error_message":   req.RespMsg,
		"callback_result": callbackResult,
	}

	updateTransactionErr := s.transactionModel.UpdateTransactionStatus(nil, model.UpdateTransactionStatusResultWhere{
		ID: transaction.ID,
	}, updateMap)

	if updateTransactionErr != nil {
		return fmt.Errorf("更新流水状态失败: %v", updateTransactionErr)
	}

	return nil
}

// handlePaymentProcessing 处理支付处理中回调
func (s *PaymentService) handlePaymentProcessing(transaction *model.BusinessPaymentTransactions, req *PaymentCallbackRequest, callbackResult string) error {
	// 处理中状态，更新回调结果但不改变业务状态
	updateMap := map[string]interface{}{
		"callback_result": callbackResult,
	}

	updateTransactionErr := s.transactionModel.UpdateTransactionStatus(nil, model.UpdateTransactionStatusResultWhere{
		ID: transaction.ID,
	}, updateMap)

	if updateTransactionErr != nil {
		return fmt.Errorf("更新流水回调结果失败: %v", updateTransactionErr)
	}

	// 处理中状态不做其他操作，等待补偿机制处理
	return nil
}

// PaymentCallback 处理支付回调
func (m *Manager) PaymentCallback(ctx *gin.Context) {
	// 绑定回调参数
	var req PaymentCallbackRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(400, gin.H{"error": "参数绑定失败: " + err.Error()})
		return
	}

	// 调用业务层处理回调
	businessService := NewPaymentService(ctx)
	if err := businessService.HandlePaymentCallback(&req); err != nil {
		ctx.JSON(500, gin.H{"error": "处理支付回调失败: " + err.Error()})
		return
	}

	// 返回成功响应（第三方支付要求）
	ctx.JSON(200, gin.H{"code": "0000", "message": "success"})
}
