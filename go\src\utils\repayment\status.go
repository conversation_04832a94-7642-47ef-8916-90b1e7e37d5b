package repayment

// 还款状态映射
const (
	// 原始账单状态
	BillStatusUnpaid        = 0 // 待支付
	BillStatusPaid          = 1 // 已支付
	BillStatusOverduePaid   = 2 // 逾期已支付
	BillStatusOverdueUnpaid = 3 // 逾期待支付
	BillStatusCancelled     = 4 // 已取消
	BillStatusSettled       = 5 // 已结算
	BillStatusRefunded      = 6 // 已退款
	BillStatusPartialPaid   = 7 // 部分还款
	BillStatusEarlySettled  = 8 // 提前结清
)

// 还款状态文案
const (
	RepaymentStatusPending = "待还款"
	RepaymentStatusPaid    = "已还款"
)

// GetRepaymentStatusText 根据账单状态获取还款状态文案
func GetRepaymentStatusText(billStatus int) string {
	switch billStatus {
	case BillStatusUnpaid, BillStatusOverdueUnpaid, BillStatusCancelled, BillStatusPartialPaid:
		return RepaymentStatusPending
	case BillStatusPaid, BillStatusOverduePaid:
		return RepaymentStatusPaid
	default:
		return RepaymentStatusPending
	}
}
