# Fincore Deployer 部署包

## 概述

这是Fincore项目的完整部署包，包含了编译后的应用程序和前端资源。

## 目录结构

```
deployer/
├── build/                   # 构建产物
│   ├── app/                 # Go后端应用
│   │   ├── bin/             # 可执行文件
│   │   │   └── fincore      # 主程序
│   │   ├── config/          # 配置文件
│   │   ├── logs/            # 日志目录
│   │   ├── runtime/         # 运行时目录
│   │   └── static/          # 静态文件
│   ├── business/            # Business前端
│   ├── uniapp/              # UniApp前端
│   └── build_info.txt       # 构建信息
├── build.sh                 # 构建脚本
├── deploy.sh                # 完整部署脚本
├── deploy-simple.sh         # 简化部署脚本
├── verify-deployment.sh     # 部署验证脚本
└── watchdog/                # 系统服务配置
    └── systemd/             # systemd服务文件
        └── fincore.service  # Fincore服务配置
```

## 快速部署
```bash
# 执行完整部署脚本
sudo ./deploy.sh --start --check
```

### 3. 验证部署

```bash
# 查看服务状态
sudo systemctl status fincore

# 检查端口监听
netstat -tuln | grep 8108

# 运行验证脚本
sudo ./verify-deployment.sh
```

## 部署配置

### 环境变量

```bash
export DEPLOY_PATH="/opt/fincore"    # 部署路径
export INSTALL_USER="fincore"       # 运行用户
export INSTALL_GROUP="fincore"      # 运行用户组
```

## 服务管理

### 启动服务

```bash
sudo systemctl start fincore
```

### 停止服务

```bash
sudo systemctl stop fincore
```

### 重启服务

```bash
sudo systemctl restart fincore
```

### 查看状态

```bash
# 查看systemd状态
sudo systemctl status fincore

# 查看进程状态
ps aux | grep fincore

# 检查端口监听
netstat -tuln | grep 8108
```

### 查看日志

```bash
# 查看systemd日志
sudo journalctl -u fincore -f

# 查看应用日志
tail -f /opt/fincore/build/app/bin/logs/fincore.out

# 查看错误日志
sudo journalctl -u fincore -n 50
```

## 更新部署

### 更新应用

```bash
# 1. 停止服务
sudo systemctl stop fincore

# 2. 备份当前版本
sudo cp -r /opt/fincore/build /opt/fincore/build.backup.$(date +%Y%m%d_%H%M%S)

# 3. 更新文件
sudo ./deploy-simple.sh

# 4. 启动服务
sudo systemctl start fincore

# 5. 验证更新
sudo ./verify-deployment.sh
```

### 回滚版本

```bash
# 停止服务
sudo systemctl stop fincore

# 恢复备份
sudo rm -rf /opt/fincore/build
sudo mv /opt/fincore/build.backup.YYYYMMDD_HHMMSS /opt/fincore/build

# 启动服务
sudo systemctl start fincore
```

### 文件权限

```bash
# 设置正确的文件权限
sudo chmod 755 /opt/fincore/build/app/bin/fincore
sudo chmod 644 /opt/fincore/build/app/bin/resource/config.yml
sudo chmod 755 /opt/fincore/build/app/bin/fincore-daemon.sh
```

## 备份策略

### 数据备份

```bash
# 备份配置文件
tar -czf config_backup_$(date +%Y%m%d).tar.gz /opt/fincore/build/app/bin/resource/

# 备份日志文件
tar -czf logs_backup_$(date +%Y%m%d).tar.gz /opt/fincore/build/app/bin/logs/
```