package captcha

import (
	"errors"
	"fincore/utils/gf"
	"fincore/utils/log"
	"fincore/utils/results"
	"fincore/utils/utilstool/goredis"
	_ "fmt"
	"reflect"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/mojocn/base64Captcha"
)

var store = base64Captcha.DefaultMemStore

// 验证码页面
/**
* 使用 Index 是省略路径中的index
* 本路径为： /admin/user/login -省去了index
 */
type Index struct {
	NoNeedLogin []string //忽略登录接口配置-忽略全部传[*]
	NoNeedAuths []string //忽略RBAC权限认证接口配置-忽略全部传[*]
}

func init() {
	fpath := Index{NoNeedLogin: []string{"GetCaptcha", "ValidateCaptcha"}, NoNeedAuths: []string{"*"}}
	gf.Register(&fpath, reflect.TypeOf(fpath).PkgPath())
}

/**
* 1 获取图形验证码
 */
func (api *Index) GetCaptcha(c *gin.Context) {
	// 生成验证码
	driver := base64Captcha.NewDriverDigit(80, 240, 5, 0.7, 80)
	cp := base64Captcha.NewCaptcha(driver, store)
	idKey, b64s, answer, err := cp.Generate()
	if err != nil {
		results.Failed(c, "生成验证码失败", err)
		return
	}
	log.Info("验证码为：", answer)
	_ = answer
	//验证码id为key，验证码为value，存入redis，并设置过期时间为60s并且60s内不能重复获取
	Ok, err := goredis.SetNX(idKey, answer, 60*time.Second)
	if !Ok {
		results.Failed(c, "验证码存储失败", err)
		return
	}
	// store.Set(idKey, answer) // Store the answer for 180 seconds

	type captchaData struct {
		CaptchaId string `json:"captchaId"`
		Data      string `json:"captchaImage"`
	}
	data := captchaData{CaptchaId: idKey, Data: b64s}

	// 注册 session 中间件，"session_name" 是 session 的名称

	// //存入图形验证码id
	// Ok ,err = goredis.SetNX("captchaId", idKey, 60*time.Second)
	// if !Ok {
	// 	results.Failed(c, "验证码存储失败", err)
	// 	return
	// }
	// 返回验证码ID和Base64图像
	results.Success(c, "验证码获取成功", data, nil)
}

/**
* 2 验证图形验证码
 */
func ValidateCaptcha(captchaId, captcha string) error {

	if captcha == "" {
		return errors.New("验证码不能为空")
	}
	//验证图形验证码
	//获取验证码
	Ok, answer := goredis.Get(captchaId)
	if !Ok {
		return errors.New("验证码已过期")
	}
	if answer != captcha {
		return errors.New("图形验证码错误")
	}

	return nil
}
