# 阿里云OSS文件上传集成

本模块提供了阿里云对象存储服务(OSS)的集成功能，支持文件上传到OSS或本地存储的无缝切换。

## 功能特性

- 支持阿里云OSS文件上传
- 支持本地文件存储
- 配置化切换存储方式：切换成本地 or oss存储模式
- 支持自定义域名访问：目前没有使用，默认为空即可

## 配置说明

### 1. 配置文件设置

在 `config.yml` 或 `config_dev.yml` 中添加OSS配置：

```yaml
oss:
  # 是否启用OSS (true/false)
  enabled: true
  # OSS访问域名端点
  endpoint: oss-cn-hangzhou.aliyuncs.com
  # 访问密钥ID
  accessKeyId: your-access-key-id
  # 访问密钥Secret
  accessKeySecret: your-access-key-secret
  # 存储桶名称
  bucketName: your-bucket-name
  # 文件存储基础路径
  basePath: uploads/
  # 自定义域名（可选，用于文件访问）
  domain: https://your-custom-domain.com
```

## 使用方法

### 1. 检查OSS是否启用

```go
if oss.IsOSSEnabled() {
    // OSS已启用
} else {
    // 使用本地存储
}
```

### 2. 获取OSS客户端

```go
client, err := oss.GetOSSClient()
if err != nil {
    // 处理错误
}
```

### 3. 上传文件

```go
// 打开文件
file, err := os.Open("path/to/file.jpg")
if err != nil {
    // 处理错误
}
defer file.Close()

// 上传到OSS
fileURL, err := client.UploadFile("images/file.jpg", file)
if err != nil {
    // 处理错误
}

fmt.Println("文件URL:", fileURL)
```

### 4. 生成签名URL（安全访问）

```go
// 生成1小时有效期的签名URL
signedURL, err := client.GetSignedURL("images/file.jpg", time.Hour)
if err != nil {
    // 处理错误
}

fmt.Println("签名URL:", signedURL)
```

### 5. 从完整URL提取ObjectKey

```go
// 从完整URL中提取ObjectKey
objectKey := oss.GetObjectKeyFromURL("https://bucket.oss-region.aliyuncs.com/images/file.jpg")
fmt.Println("ObjectKey:", objectKey) // 输出: images/file.jpg
```

## 文件上传接口集成

文件上传功能已集成到 `app/uniapp/common/uploadfile.go` 中的 `Onefile` 方法。该方法会：

1. 检查OSS是否启用
2. 如果启用OSS，将文件上传到阿里云OSS
3. 如果未启用OSS，将文件保存到本地
4. 返回统一的响应格式

### API响应格式

```json
{
  "id": 123,
  "uid": "文件唯一标识",
  "name": "原始文件名",
  "status": "done",
  "url": "",
  "thumb": "",
  "cover_url": "视频封面URL",
  "response": "上传成功",
  "is_oss": true,
  "time": 1640995200
}
```

**注意：** 当启用OSS时，`url` 和 `thumb` 字段为空，前端需要通过文件访问API获取签名URL。

## 文件访问接口

为了确保文件访问的安全性，系统提供了专门的文件访问API，注意该接口仅支持获取该用户自己上传的文件：

### 1. 获取文件访问URL

**接口：** `GET /uniapp/common/fileaccess/getFileURL`

**参数：**
- `file_id`: 文件ID（必需）
- `expires`: 过期时间（可选，默认1小时，单位：小时）

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "url": "https://bucket.oss-region.aliyuncs.com/path/file.jpg?Expires=xxx&OSSAccessKeyId=xxx&Signature=xxx",
    "expires_at": "2024-01-01T12:00:00Z"
  }
}
```

### 2. 获取文件信息

**接口：** `GET /uniapp/common/fileaccess/getFileInfo`

**参数：**
- `file_id`: 文件ID（必需）

**响应：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 123,
    "name": "file.jpg",
    "size": 1024000,
    "mimetype": "image/jpeg",
    "upload_time": "2024-01-01T10:00:00Z"
  }
}
```

## 安全特性

### 1. 签名URL访问
- **临时访问**：生成带有过期时间的签名URL，确保文件访问的时效性
- **权限控制**：只有通过后端API才能获取文件访问链接，便于权限验证
- **防盗链**：签名URL包含访问凭证，防止未授权访问

### 2. 访问控制
- **身份验证**：文件访问API需要用户登录认证
- **权限验证**：可以在API中添加文件访问权限检查
- **审计日志**：记录文件访问行为，便于安全审计

### 3. 数据保护
- **密钥安全**：AccessKey仅存储在后端，前端无法获取
- **传输加密**：支持HTTPS传输，保护数据安全
- **存储加密**：支持OSS服务端加密


## 故障排除

### 常见问题

1. **上传失败**
   - 检查AccessKey是否正确
   - 确认Bucket权限设置
   - 验证网络连接

2. **文件访问404**
   - 检查Bucket读权限
   - 确认文件路径正确
   - 验证域名配置

3. **配置不生效**
   - 重启应用服务
   - 检查配置文件语法
