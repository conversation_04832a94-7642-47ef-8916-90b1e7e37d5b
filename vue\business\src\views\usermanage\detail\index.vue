<script setup lang="ts">
  import { useRouter, useRoute } from 'vue-router';
  import { onMounted, ref, reactive } from 'vue';

  const $route = useRoute();
  const $router = useRouter();
  const loading = ref(false);
  const activeKey = ref('1');
  import { Picture as IconPicture } from '@element-plus/icons-vue';
  import {
    getCustomerDetail,
    getReports,
    getBankCardList,
    updateCustomerRemark,
    updateCustomerStatus,
    unlockCustomer, getProductsByAmount, updateCustomerQuota
  } from '@/api/usermanage';
  import type {
    CustomerListItem,
    FkDataList
  } from '@/api/usermanage';
  import RiskControlReport from '@/components/riskControlReport/index.vue';
  import {ElMessageBox} from "element-plus";
  import {Message} from "@arco-design/web-vue";
  import router from "@/router";
  import { throttle } from 'lodash';
  const url_base = window.globalConfig.Upload_url_base;

  const chtableData: any = [];

  onMounted(() => {
    fetchData($route.params.id as string);
  });

  // 用户详情信息
  const detailData = ref({});

  // 风控相关
  const fkTabList = ref<FkDataList>([]);

  // 银行卡列表
  const cardList = ref<any>([]);
  const fetchData = async (id: string) => {
    detailData.value = await getCustomerDetail(id);

    fkTabList.value = await getReports({
      customer_id: id,
      start_date: '',
      end_date: '',
    });

    const cardRes = await getBankCardList({ customer_id: id });
    if(cardRes.list && cardRes.list.length > 0) {
      cardRes.list.forEach((item: any) => {
        item.name = detailData.value.name
      })
    }
    cardList.value = cardRes.list;
  };
  const goBack = () => {
    $router.push('/usermanage/usermanagelist');
  };
  // 一键拉黑拉白操作
  const handleOneKeyOper = (type: number) => {
    ElMessageBox.confirm(
        `确定一键${type == 1?'拉白':'拉黑'}吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    ).then(() => {
      updateCustomerStatus({
        id: detailData.value.id,
        status: type
      }).then(res => {
        Message.success(`${type == 1?'拉白':'拉黑'}操作成功`);
        fetchData($route.params.id as string);
      })
    })
  }

  // 解除注销
  const handleRelieveCancelAccount = () => {
    ElMessageBox.confirm(
      `确定解除注销此用户？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      unlockCustomer(detailData.value.id).then(res => {
        Message.success('解除注销成功')
      })
    })
  }

  // 修改用户额度
  import type { FormInstance } from 'element-plus'
  const amountFormRef = ref<FormInstance>();
  const handleEditAmount = () => {
    dialogAmountFormVisible.value = true;
  }
  const dialogAmountFormVisible = ref(false);
  const amountform = reactive({
    amount: '',
  })
  const amountCurrentRow = ref()
  const amountTableData = ref<any>([])
  // 获取额度匹配的产品
  const doSearch = () => {
    if(!amountform.amount) {
      amountCurrentRow.value = '';
      amountTableData.value = [];
      return false
    }
    getProductsByAmount({ loan_amount: amountform.amount }).then(res => {
      amountTableData.value = res || [];
    })
  };
  const handleAmountInput = throttle(doSearch, 1000)


  const handleAmountCurrentChange = (val: any) => {
    // console.log(val)
    amountCurrentRow.value = val ? val.id: '';
  }
  function handleConfirmEditAmount(formEl : FormInstance) {
    if(!amountCurrentRow.value) {
      return Message.warning("请选择匹配产品")
    }
    formEl.validate((valid) => {
      console.log("valid", valid);
      if (valid) {
        // console.log('submit!')
        // console.log(amountCurrentRow.value)
        // console.log(amountform.amount)
        updateCustomerQuota({
          customerId: Number($route.params.id),
          productId: Number(amountCurrentRow.value),
          amount: Number(amountform.amount),
        }).then(res => {
          Message.success('修改成功')
          fetchData($route.params.id as string);
          dialogAmountFormVisible.value = false;
          amountform.amount = '';
          amountCurrentRow.value = '';
          amountTableData.value = [];
        })
      }
    })
  }


  const handleViewOrder = () => {
    if(detailData.value.totalOrderCount == 0) {
      return Message.warning('该客户无订单信息');
    }
    router.push({
      path: '/ordermanagement/Orderlist',
      query: {
        user_id_card: detailData.value.idCardFull
      }
    })
  }

  // const handleFreeze = () => {}


  // 备注弹窗
  const remarkModalVisible = ref(false);
  const remarkFormRef = ref();
  const remarkForm = reactive({
    userRemark: '',
  });


  const handleEditRemark = () => {
    remarkForm.userRemark = detailData.value.userRemark;
    remarkModalVisible.value = true;
  };
  // 备注提交
  const handleRemarkSubmit = async () => {
    try {
      await remarkFormRef.value?.validate();
      if (detailData.value) {
        await updateCustomerRemark(detailData.value.id, {
          userRemark: remarkForm.userRemark,
        });
        Message.success('更新备注成功');
        fetchData($route.params.id as string);
        remarkModalVisible.value = false;
      }
    } catch (error) {
      Message.error('更新备注失败');
    }
  };

  const handleRemarkCancel = () => {
    remarkModalVisible.value = false;
    remarkForm.userRemark = '';
  };
</script>

<template>
  <div class="container">
    <div class="info-head">
      <el-space>
        <el-button type="primary" link @click="goBack">
          <template #icon>
            <icon-arrow-left />
          </template>
          返回列表
        </el-button>
        <el-divider direction="vertical" />
        客户详情
      </el-space>
    </div>

    <div class="info-box">
      <el-descriptions title="客户信息">
        <template #title>
          <div class="title">客户信息</div>
        </template>
        <template #extra>
          <el-button type="primary" @click="fetchData($route.params.id)">刷新</el-button>
        </template>
        <el-descriptions-item label="姓名：">{{
          detailData.name
        }}</el-descriptions-item>
        <el-descriptions-item label="手机号：">{{
          detailData.mobile
        }}</el-descriptions-item>
        <el-descriptions-item label="身份证号：">{{
          detailData.idCardFull
        }}</el-descriptions-item>
        <el-descriptions-item label="年龄：">{{
          detailData.age
        }}</el-descriptions-item>
        <el-descriptions-item label="性别：">{{
          detailData.gender
        }}</el-descriptions-item>
        <el-descriptions-item label="身份证到期日：">{{
          detailData.ocrEndDate
        }}</el-descriptions-item>
        <el-descriptions-item label="人脸识别照片：">
          <el-image
            style="width: 150px; height: 150px"
            :src="detailData.facePhotoUrl??url_base + '/' + detailData.facePhotoUrl"
            :preview-src-list="[detailData.facePhotoUrl??url_base + '/' + detailData.facePhotoUrl]"
            fit="cover"
          >
            <template #error>
              <div class="image-slot">
                <el-icon><icon-picture /></el-icon>
              </div>
            </template>
          </el-image>
        </el-descriptions-item>
        <el-descriptions-item label="身份证照片：" :span="2">
          <el-image
            style="width: 150px; height: 100px"
            :src="detailData.idCardFrontUrl??url_base + '/' + detailData.idCardFrontUrl"
            :preview-src-list="[detailData.idCardFrontUrl??url_base + '/' + detailData.idCardFrontUrl]"
            fit="cover"
          >
            <template #error>
              <div class="image-slot">
                <el-icon><icon-picture /></el-icon>
              </div>
            </template>
          </el-image>
          <el-image
            style="width: 150px; height: 100px; margin-left: 10px"
            :src="detailData.idCardBackUrl?? url_base + '/' + detailData.idCardBackUrl"
            :preview-src-list="[detailData.idCardBackUrl?? url_base + '/' + detailData.idCardBackUrl]"
            fit="cover"
          >
            <template #error>
              <div class="image-slot">
                <el-icon><icon-picture /></el-icon>
              </div>
            </template>
          </el-image>

        </el-descriptions-item>
        <el-descriptions-item label="全部订单数：">
          <el-space>
            <el-tag type="primary">{{ detailData.totalOrderCount }}</el-tag>
            <el-button type="primary" size="small" @click="handleViewOrder">查看</el-button>
          </el-space>
        </el-descriptions-item>
        <el-descriptions-item label="在途/完成订单数：">
          {{ detailData.borrowingOrderCount }}/{{ detailData.totalOrderCount }}
        </el-descriptions-item>
        <el-descriptions-item label="复购次数：">{{ detailData.loanCount }}</el-descriptions-item>
        <el-descriptions-item label="认证状态：">{{
          detailData.identityStatusText
        }}</el-descriptions-item>
        <el-descriptions-item label="进件时间：">{{
          detailData.loanTime
        }}</el-descriptions-item>
        <el-descriptions-item label="模型分：">{{ detailData.riskScore }}</el-descriptions-item>
        <el-descriptions-item label="总额度：">{{
          detailData.allQuota
        }}</el-descriptions-item>
        <el-descriptions-item label="剩余额度：">{{
          detailData.reminderQuota
        }}</el-descriptions-item>
        <el-descriptions-item label="风控备注：">-</el-descriptions-item>
      </el-descriptions>
      <div class="info-box-btn">
        <el-space>
          <el-button type="primary">通讯录</el-button>
          <el-button type="primary" @click="handleOneKeyOper(1)">一键拉白</el-button>
          <el-button type="primary" @click="handleOneKeyOper(2)">一键拉黑</el-button>
          <el-button type="primary" @click="handleEditAmount">修改额度</el-button>
<!--          <el-button type="primary" @click="handleFreeze">冻结</el-button>-->
          <el-button type="primary" @click="handleEditRemark()">用户备注</el-button>
          <el-button type="primary" @click="handleRelieveCancelAccount">用户解除注销</el-button>
          <el-button type="primary">异常风控刷新</el-button>
        </el-space>
      </div>
    </div>

    <!--  修改额度弹窗  -->
    <el-dialog v-model="dialogAmountFormVisible" title="修改额度" width="800">

      <el-descriptions>
        <el-descriptions-item label="总额度：">{{detailData.allQuota }}</el-descriptions-item>
        <el-descriptions-item label="剩余额度：">{{detailData.reminderQuota }}</el-descriptions-item>
      </el-descriptions>

      <el-form :model="amountform" prop="amount" ref="amountFormRef">
        <el-form-item label="修改额度" prop="amount" :rules="[{required: true,message: '请输入要修改的额度',trigger: 'blur'}]">
          <el-input v-model="amountform.amount" type="number" autocomplete="off" @input="handleAmountInput"/>
        </el-form-item>
      </el-form>
      <div class="amountTable">
        <el-table
          ref="singleTableRef"
          :data="amountTableData"
          highlight-current-row
          height="350"
          @current-change="handleAmountCurrentChange"
        >
          <el-table-column type="index" width="50" />
          <el-table-column width="35">
            <template #default="scope">
              <el-radio v-model="amountCurrentRow" :value="scope.row.id"></el-radio>
            </template>
          </el-table-column>
          <el-table-column property="rule_name" label="规则名称" />
          <el-table-column property="loan_amount" label="初始额度" width="120" />
          <el-table-column property="loan_period" label="借款周期" width="120" />
          <el-table-column property="total_periods" label="总期数" width="120" />
          <el-table-column property="rule_category" label="规则类型" width="120" />
        </el-table>
      </div>


      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogAmountFormVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmEditAmount(amountFormRef)">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>


    <!-- 编辑备注弹窗 -->
    <a-modal
        v-model:visible="remarkModalVisible"
        title="编辑用户备注"
        @ok="handleRemarkSubmit"
        @cancel="handleRemarkCancel"
    >
      <a-form ref="remarkFormRef" :model="remarkForm" layout="vertical">
        <a-form-item field="userRemark" label="用户备注" :rules="[{ required: true, message: '请输入用户备注' }]">
          <a-textarea
              v-model="remarkForm.userRemark"
              placeholder="请输入用户备注"
              :max-length="500"
              show-word-limit
              :auto-size="{ minRows: 3, maxRows: 6 }"
          />
        </a-form-item>
      </a-form>
    </a-modal>


    <!--    tabs - 客户信息/风控报告/审批结论/催收记录    -->
    <div class="info-tabs">
      <el-tabs v-model="activeKey" class="demo-tabs">
        <el-tab-pane label="客户信息" name="1">
          <el-descriptions title="客户信息">
            <template #title>
              <div class="title">客户信息</div>
            </template>
            <el-descriptions-item label="紧急联系人姓名：" :span="1">{{
              detailData.emergencyContact0Name
            }}</el-descriptions-item>
            <el-descriptions-item label="紧急联系人电话：" :span="1">{{
              detailData.emergencyContact0Phone
            }}</el-descriptions-item>
            <el-descriptions-item label="紧急联系人关系：" :span="1">{{
              detailData.emergencyContact0Relation
            }}</el-descriptions-item>
            <el-descriptions-item label="紧急联系人姓名：" :span="1">{{
              detailData.emergencyContact1Name
            }}</el-descriptions-item>
            <el-descriptions-item label="紧急联系人电话：" :span="1">{{
              detailData.emergencyContact1Phone
            }}</el-descriptions-item>
            <el-descriptions-item label="紧急联系人关系：" :span="1">{{
              detailData.emergencyContact1Relation
            }}</el-descriptions-item>
            <el-descriptions-item label="学历：">{{
              detailData.degree
            }}</el-descriptions-item>
            <el-descriptions-item label="婚姻：">{{
              detailData.marry
            }}</el-descriptions-item>
            <el-descriptions-item label="职业：">{{
              detailData.occupation
            }}</el-descriptions-item>
            <el-descriptions-item label="年收入：">{{
              detailData.yearRevenue
            }}</el-descriptions-item>
          </el-descriptions>

          <div class="card-list">
            <div class="title">客户银行卡</div>

            <div class="card-table">
              <el-table :data="cardList" style="width: 100%">
                <el-table-column type="index" label="No" width="50" />
                <el-table-column prop="name" label="持卡人姓名" width="150" />
                <el-table-column prop="bank_name" label="所属银行"></el-table-column>
                <el-table-column prop="bank_card_no" label="银行卡号"></el-table-column>
                <el-table-column prop="card_type_name" label="银行卡类型"></el-table-column>
                <el-table-column prop="bank_phone" label="绑定手机号"></el-table-column>
                <el-table-column prop="created_at" label="绑定时间"></el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="风控报告" name="2">
          <template v-if="fkTabList && fkTabList.length > 0">
            <RiskControlReport :fkTabList="fkTabList" />
          </template>
          <el-empty v-else description="暂无风控报告" />
        </el-tab-pane>
        <el-tab-pane label="审批结论" name="3">
          <el-descriptions :column="1">
            <el-descriptions-item label="审批结果：">
<!--              <el-tag type="primary">审批通过</el-tag>-->
            </el-descriptions-item>
            <el-descriptions-item label="审批时间："
              ></el-descriptions-item
            >
            <el-descriptions-item label="审批人："></el-descriptions-item>
            <el-descriptions-item label="小记："></el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="催收记录" name="4">
          <div class="title">商家催收</div>

          <div class="cuishou-table">
            <el-table :data="chtableData" style="width: 100%">
              <el-table-column type="index" label="No" width="50" />
              <el-table-column prop="a1" label="订单号" width="150" />
              <el-table-column prop="a2" label="记录人" />
              <el-table-column prop="a3" label="记录时间" />
              <el-table-column prop="a4" label="结果" />
              <el-table-column prop="a5" label="小记" />
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<style scoped lang="less">
  .container {
    margin: 10px;
  }

  .info-head {
    background: #fff;
    margin-bottom: 10px;
    padding: 10px 0;
  }

  .info-box {
    padding: 10px;
    background: #fff;

    .info-box-btn {
      padding: 10px 0;
    }
  }

  .info-tabs {
    background: #fff;
    margin-top: 15px;
    padding: 0 10px 10px;
  }

  .title {
    padding-left: 10px;
    position: relative;
    color: #333;
    font-size: 16px;
    font-weight: bold;
    &:after {
      content: '';
      position: absolute;
      background: #409eff;
      width: 3px;
      height: 100%;
      left: 0;
      top: 0;
      border-radius: 10px;
    }
  }

  .card-list {
    padding: 20px 0;


  }
  .card-table, .amountTable, .cuishou-table {
    margin-top: 20px;
    :deep(.el-table__header-wrapper){
    }
    :deep(.el-table th.el-table__cell){
      background: #efefef;
    }
    :deep(.el-table thead) {
      color: #333;
    }
  }

  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: var(--el-fill-color-light);
    color: var(--el-text-color-secondary);
    font-size: 30px;
  }

 
  .fk-tab-title {
    margin-bottom: 10px;
  }
</style>
