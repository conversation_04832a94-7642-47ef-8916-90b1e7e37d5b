package lock

import (
	"context"
	"sync"
	"testing"
	"time"

	"fincore/utils/log"
)

// TestBasicLockUnlock 测试基本的加锁解锁功能
func TestBasicLockUnlock(t *testing.T) {
	lock := GetLock("test:basic", 1*time.Minute)

	// 测试加锁
	lock.Lock()

	// 检查锁状态
	stats := GetStats()
	if stat, exists := stats["test:basic"]; !exists || !stat.IsLocked {
		t.Error("锁应该处于锁定状态")
	}

	// 测试解锁
	lock.Unlock()

	// 检查锁状态
	stats = GetStats()
	if stat, exists := stats["test:basic"]; !exists || stat.IsLocked {
		t.<PERSON>rror("锁应该处于解锁状态")
	}
}

// TestChainedOperations 测试链式操作
func TestChainedOperations(t *testing.T) {
	// 测试链式操作不会panic
	defer func() {
		if r := recover(); r != nil {
			t.Errorf("链式操作不应该panic: %v", r)
		}
	}()

	GetLock("test:chain", 1*time.Minute).
		WithTimeout(5 * time.Second).
		Lock().
		Unlock()
}

// TestTryLock 测试尝试加锁功能
func TestTryLock(t *testing.T) {
	lock1 := GetLock("test:trylock", 1*time.Minute)
	lock2 := GetLock("test:trylock", 1*time.Minute)

	// 第一个锁应该成功
	success1, _ := lock1.TryLock()
	if !success1 {
		t.Error("第一个TryLock应该成功")
	}

	// 第二个锁应该失败（同一个key）
	success2, _ := lock2.TryLock()
	if success2 {
		t.Error("第二个TryLock应该失败")
	}

	// 释放第一个锁
	lock1.Unlock()

	// 现在第二个锁应该成功
	success3, _ := lock2.TryLock()
	if !success3 {
		t.Error("释放后TryLock应该成功")
	}

	lock2.Unlock()
}

// TestUnlockByKey 测试通过key解锁
func TestUnlockByKey(t *testing.T) {
	key := "test:unlockbykey"
	lock := GetLock(key, 1*time.Minute)

	// 加锁
	lock.Lock()

	// 通过key解锁
	err := UnlockByKey(key)
	if err != nil {
		t.Errorf("通过key解锁失败: %v", err)
	}

	// 检查锁状态
	stats := GetStats()
	if stat, exists := stats[key]; !exists || stat.IsLocked {
		t.Error("锁应该处于解锁状态")
	}
}

// TestLockExpiration 测试锁过期功能
func TestLockExpiration(t *testing.T) {
	// 创建一个很短的过期时间
	lock := GetLock("test:expiration", 100*time.Millisecond)
	lock.Lock()

	// 等待过期
	time.Sleep(200 * time.Millisecond)

	// 清理过期锁
	cleaned := CleanExpiredLocks()
	if cleaned == 0 {
		t.Error("应该清理到过期的锁")
	}

	// 检查锁是否被清理
	stats := GetStats()
	if _, exists := stats["test:expiration"]; exists {
		t.Error("过期的锁应该被清理")
	}
}

// TestConcurrentAccess 测试并发访问
func TestConcurrentAccess(t *testing.T) {
	key := "test:concurrent"
	var wg sync.WaitGroup
	var successCount int
	var mu sync.Mutex

	// 启动多个goroutine尝试获取同一个锁
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			lock := GetLock(key, 1*time.Minute)
			if success, _ := lock.TryLock(); success {
				mu.Lock()
				successCount++
				mu.Unlock()

				// 持有锁一段时间
				time.Sleep(10 * time.Millisecond)
				lock.Unlock()
			}
		}()
	}

	wg.Wait()

	// 在并发情况下，只有一个goroutine应该成功获取锁
	if successCount != 1 {
		t.Errorf("期望只有1个goroutine成功获取锁，实际: %d", successCount)
	}
}

// TestLockStats 测试锁统计功能
func TestLockStats(t *testing.T) {
	// 创建一个新的锁管理器实例用于测试
	manager := NewSimpleLockManager()

	// 创建几个锁
	lock1 := manager.GetLock("test:stats:1", 1*time.Minute)
	lock2 := manager.GetLock("test:stats:2", 2*time.Minute)
	manager.GetLock("test:stats:3", 3*time.Minute) // lock3 不加锁

	lock1.Lock()
	lock2.Lock()

	stats := manager.GetStats()

	// 检查统计信息
	if len(stats) != 3 {
		t.Errorf("期望3个锁，实际: %d", len(stats))
	}

	// 检查锁状态
	if stat1, exists := stats["test:stats:1"]; !exists || !stat1.IsLocked {
		t.Error("test:stats:1 应该处于锁定状态")
	}

	if stat2, exists := stats["test:stats:2"]; !exists || !stat2.IsLocked {
		t.Error("test:stats:2 应该处于锁定状态")
	}

	if stat3, exists := stats["test:stats:3"]; !exists || stat3.IsLocked {
		t.Error("test:stats:3 应该处于解锁状态")
	}

	lock1.Unlock()
	lock2.Unlock()
}

// TestWithTimeout 测试超时设置
func TestWithTimeout(t *testing.T) {
	lock := GetLock("test:timeout", 1*time.Minute)

	// 设置超时时间
	lock.WithTimeout(100 * time.Millisecond)

	// 这个测试主要确保WithTimeout不会panic
	// 实际的超时行为在真实场景中更容易测试
	lock.Lock()
	lock.Unlock()
}

// TestGetKey 测试获取锁key功能
func TestGetKey(t *testing.T) {
	expectedKey := "test:getkey"
	lock := GetLock(expectedKey, 1*time.Minute)

	actualKey := lock.GetKey()
	if actualKey != expectedKey {
		t.Errorf("期望key: %s, 实际key: %s", expectedKey, actualKey)
	}
}

// BenchmarkLockUnlock 基准测试：加锁解锁性能
func BenchmarkLockUnlock(b *testing.B) {
	for i := 0; i < b.N; i++ {
		key := "bench:lock"
		lock := GetLock(key, 1*time.Minute)
		lock.Lock()
		lock.Unlock()
	}
}

// BenchmarkTryLock 基准测试：尝试加锁性能
func BenchmarkTryLock(b *testing.B) {
	for i := 0; i < b.N; i++ {
		key := "bench:trylock"
		lock := GetLock(key, 1*time.Minute)
		success, _ := lock.TryLock()
		if success {
			lock.Unlock()
		}
	}
}

// BenchmarkChainedOperations 基准测试：链式操作性能
func BenchmarkChainedOperations(b *testing.B) {
	for i := 0; i < b.N; i++ {
		key := "bench:chain"
		GetLock(key, 1*time.Minute).
			WithTimeout(1 * time.Second).
			Lock().
			Unlock()
	}
}

// TestWithContext 测试上下文功能
func TestWithContext(t *testing.T) {
	// 创建带请求ID的上下文
	ctx := context.Background()
	ctx = log.SetRequestIDToContext(ctx, "test-req-123")

	lock := GetLock("test:context", 1*time.Minute).WithContext(ctx)

	// 测试加锁解锁
	lock.Lock()
	lock.Unlock()

	// 测试尝试加锁
	success, _ := lock.TryLock()
	if !success {
		t.Error("TryLock应该成功")
	}
	lock.Unlock()
}

// TestWithLogger 测试日志注入功能
func TestWithLogger(t *testing.T) {
	// 创建自定义日志对象
	customLogger := log.RegisterModule("custom_lock", "自定义锁模块")

	// 测试全局方法
	lock1 := GetLockWithLogger("test:custom:logger:1", customLogger, 1*time.Minute)
	lock1.Lock()
	lock1.Unlock()

	// 测试链式操作
	lock2 := GetLock("test:custom:logger:2", 1*time.Minute).WithLogger(customLogger)
	lock2.Lock()
	lock2.Unlock()

	// 测试管理器方法
	manager := NewSimpleLockManager()
	lock3 := manager.GetLockWithLogger("test:custom:logger:3", customLogger, 1*time.Minute)
	lock3.Lock()
	lock3.Unlock()

	// 测试不注入日志对象（不会记录日志）
	lock4 := manager.GetLock("test:custom:logger:4", 1*time.Minute)
	lock4.Lock()
	lock4.Unlock()

	// 测试全局方法不注入日志对象
	lock5 := GetLock("test:custom:logger:5", 1*time.Minute)
	lock5.Lock()
	lock5.Unlock()
}
