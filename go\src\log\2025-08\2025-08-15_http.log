{"level":"dev.info","ts":"[2025-08-15 09:10:47.344]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"185bcb83f1925b1c9bb29ab9","method":"GET","url":"/business/statistics/statisticscontroller/getChannelDueStatistics","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/statistics/statisticscontroller/getChannelDueStatistics","query":"channel_id&due_date_start&due_date_end&period_number&is_new_user&page_size=10"}
{"level":"dev.info","ts":"[2025-08-15 09:10:47.433]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"185bcb83f1925b1c9bb29ab9","method":"GET","url":"/business/statistics/statisticscontroller/getChannelDueStatistics","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0886264,"response_size":1189}
{"level":"dev.info","ts":"[2025-08-15 09:10:52.196]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"185bcb8512b06d74c2e3b202","method":"GET","url":"/business/statistics/statisticscontroller/getChannelDueStatistics","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/statistics/statisticscontroller/getChannelDueStatistics","query":"channel_id&due_date_start&due_date_end&period_number&is_new_user&page_size=10"}
{"level":"dev.info","ts":"[2025-08-15 09:10:52.298]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"185bcb8512b06d74c2e3b202","method":"GET","url":"/business/statistics/statisticscontroller/getChannelDueStatistics","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.1032481,"response_size":1189}
