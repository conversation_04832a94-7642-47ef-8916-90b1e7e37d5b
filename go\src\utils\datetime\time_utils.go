package datetime

import (
	"time"
)

// ParseTimeString 解析时间字符串为时间戳
func ParseTimeString(timeStr string) (int64, error) {
	if timeStr == "" {
		return 0, nil
	}

	// 尝试多种时间格式
	formats := []string{
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05.000Z",
		"2006-01-02",
		"01/02/2006",
		"01/02/2006 15:04:05",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, timeStr); err == nil {
			return t.Unix(), nil
		}
	}

	return 0, nil
}

// FormatTimestamp 格式化时间戳为字符串
func FormatTimestamp(timestamp int64, format string) string {
	if timestamp == 0 {
		return ""
	}

	if format == "" {
		format = "2006-01-02 15:04:05"
	}

	return time.Unix(timestamp, 0).Format(format)
}

// GetCurrentTimestamp 获取当前时间戳
func GetCurrentTimestamp() int64 {
	return time.Now().Unix()
}

// IsValidTimeRange 验证时间范围是否有效
func IsValidTimeRange(startTime, endTime int64) bool {
	if startTime == 0 || endTime == 0 {
		return true
	}
	return startTime <= endTime
}

// GetTimeRangeFromStrings 从字符串获取时间范围
func GetTimeRangeFromStrings(startStr, endStr string) (startTime, endTime int64, err error) {
	if startStr != "" {
		startTime, err = ParseTimeString(startStr)
		if err != nil {
			return 0, 0, err
		}
	}

	if endStr != "" {
		endTime, err = ParseTimeString(endStr)
		if err != nil {
			return 0, 0, err
		}
		// 如果是日期格式，结束时间应该到当天的23:59:59
		if len(endStr) == 10 { // 格式如 "2023-01-01"
			endTime += 86399 // 加上23小时59分59秒
		}
	}

	return startTime, endTime, nil
}
