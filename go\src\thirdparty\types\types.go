package types

import "time"

// Response 统一响应结构
type Response struct {
	Success    bool                   `json:"success"`
	Code       string                 `json:"code"`
	Message    string                 `json:"message"`
	Data       map[string]interface{} `json:"data"`
	RawData    []byte                 `json:"-"`
	StatusCode int                    `json:"statusCode"`
}

// Request 统一请求结构
type Request struct {
	Method      string                 `json:"method"`
	Path        string                 `json:"path"`
	Headers     map[string]string      `json:"headers"`
	Data        map[string]interface{} `json:"data"`
	QueryParams map[string]string      `json:"queryParams"`
	Files       []FileUpload           `json:"files,omitempty"`
}

// FileUpload 文件上传结构
type FileUpload struct {
	FieldName string `json:"fieldName"`
	FileName  string `json:"fileName"`
	FilePath  string `json:"filePath"`
	Content   []byte `json:"content"`
}

// ServiceConfig 服务配置接口
type ServiceConfig interface {
	GetString(key string) string
	GetInt(key string) int
	GetBool(key string) bool
	GetDuration(key string) time.Duration
	GetStringMap(key string) map[string]string
}
