package sumpay

import (
	"crypto"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"errors"
	"fmt"
	"os"
	"sort"
	"strings"

	"github.com/tjfoc/gmsm/sm2"
)

// VerifyMsgOld 通用验签方法：根据 certType 调用 SM2 或 非SM2 逻辑
func VerifyMsgOld(ciphertext, plaintext, publicKeyPath, certType string) (bool, error) {
	certType = strings.ToUpper(certType)

	if certType == "SM2" {
		return verifyByTtfPublicKey(publicKeyPath, ciphertext, plaintext)
	} else if certType == "CERT" {
		return verifyByCert(ciphertext, plaintext, publicKeyPath)
	}

	return verifyMsgOldNonSM2(ciphertext, plaintext, publicKeyPath)
}

// verifyByCert 自动识别证书中的公钥类型（SM2 或 RSA）并验签
func verifyByCert(ciphertext, plaintext, certPath string) (bool, error) {
	certData, err := os.ReadFile(certPath)
	if err != nil {
		return false, err
	}

	block, _ := pem.Decode(certData)
	if block == nil {
		return false, errors.New("无法解析 PEM 格式证书")
	}

	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return false, err
	}

	switch pubKey := cert.PublicKey.(type) {
	case *rsa.PublicKey:
		signature, err := base64.StdEncoding.DecodeString(ciphertext)
		if err != nil {
			return false, err
		}
		hasher := crypto.SHA256.New()
		hasher.Write([]byte(plaintext))
		digest := hasher.Sum(nil)
		err = rsa.VerifyPKCS1v15(pubKey, crypto.SHA256, digest, signature)
		return err == nil, nil

	case *sm2.PublicKey:
		signData, err := base64.StdEncoding.DecodeString(ciphertext)
		if err != nil {
			return false, err
		}
		ok := pubKey.Verify([]byte(plaintext), signData)
		return ok, nil
	default:
		return false, errors.New("证书公钥类型不支持")
	}
}

// verifyByTtfPublicKey 使用 SM2 公钥证书进行验签
func verifyByTtfPublicKey(certPath, signBase64, source string) (bool, error) {
	certData, err := os.ReadFile(certPath)
	if err != nil {
		return false, err
	}

	block, _ := pem.Decode(certData)
	if block == nil {
		return false, errors.New("无法解析 PEM 格式证书")
	}

	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return false, err
	}

	sm2PubKey, ok := cert.PublicKey.(*sm2.PublicKey)
	if !ok {
		return false, errors.New("证书中的公钥不是 SM2 类型")
	}

	signData, err := base64.StdEncoding.DecodeString(signBase64)
	if err != nil {
		return false, err
	}

	verified := sm2PubKey.Verify([]byte(source), signData)
	return verified, nil
}

// verifyMsgOldNonSM2 使用 RSA + SHA256 方式验签
func verifyMsgOldNonSM2(ciphertext, plaintext, publicKeyPath string) (bool, error) {
	certFile, err := os.ReadFile(publicKeyPath)
	if err != nil {
		return false, err
	}

	block, _ := pem.Decode(certFile)
	if block == nil {
		return false, errors.New("无法解析PEM证书")
	}

	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return false, err
	}

	pubKey, ok := cert.PublicKey.(*rsa.PublicKey)
	if !ok {
		return false, errors.New("证书不是 RSA 公钥")
	}

	signature, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return false, err
	}

	hasher := crypto.SHA256.New()
	hasher.Write([]byte(plaintext))
	digest := hasher.Sum(nil)

	err = rsa.VerifyPKCS1v15(pubKey, crypto.SHA256, digest, signature)
	if err != nil {
		return false, nil
	}

	return true, nil
}

// GenerateSignString 生成排序拼接的签名字符串（去除 sign 字段）
func GenerateSignString(data map[string]interface{}) string {
	keys := make([]string, 0, len(data))
	for k := range data {
		if k == "sign" || data[k] == nil || data[k] == "" {
			continue
		}
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var sb strings.Builder
	for i, k := range keys {
		if i > 0 {
			sb.WriteString("&")
		}
		sb.WriteString(fmt.Sprintf("%s=%v", k, data[k]))
	}
	return sb.String()
}
