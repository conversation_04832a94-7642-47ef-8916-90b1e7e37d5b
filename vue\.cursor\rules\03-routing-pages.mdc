---
description: *
alwaysApply: false
---
# 路由与页面结构

## 路由系统

本项目使用 Vue Router 管理路由。路由配置主要位于以下文件中：

- [business/src/router](mdc:business/src/router) - 业务系统路由
- [admin/src/router](mdc:admin/src/router) - 管理系统路由
- [uniapp/utils/router](mdc:uniapp/utils/router) - 移动端路由

## 路由定义

Admin 和 Business 子项目的路由结构相似：

```
router/
  ├── app-menus/         # 应用菜单配置
  ├── constants.ts       # 路由常量
  ├── guard/             # 路由守卫
  ├── helper/            # 路由辅助函数
  ├── index.ts           # 路由主文件
  ├── routes/            # 路由定义
  └── types.ts           # 路由类型定义
```

路由定义主要位于 routes 目录：

- [business/src/router/routes](mdc:business/src/router/routes) - 业务系统路由定义
- [admin/src/router/routes](mdc:admin/src/router/routes) - 管理系统路由定义

## 路由守卫

路由守卫用于权限控制和页面跳转控制：

- [business/src/router/guard](mdc:business/src/router/guard) - 业务系统路由守卫
- [admin/src/router/guard](mdc:admin/src/router/guard) - 管理系统路由守卫

## 页面结构

### Admin 和 Business 子项目

页面视图位于 views 目录，按业务模块组织：

- [business/src/views](mdc:business/src/views) - 业务系统页面
- [admin/src/views](mdc:admin/src/views) - 管理系统页面

主要业务模块包括：

#### 业务系统
- [business/src/views/channel](mdc:business/src/views/channel) - 渠道管理
- [business/src/views/dashboard](mdc:business/src/views/dashboard) - 仪表盘
- [business/src/views/datacenter](mdc:business/src/views/datacenter) - 数据中心
- [business/src/views/ordermanagement](mdc:business/src/views/ordermanagement) - 订单管理
- [business/src/views/system](mdc:business/src/views/system) - 系统管理

#### 管理系统
- [admin/src/views/dashboard](mdc:admin/src/views/dashboard) - 仪表盘
- [admin/src/views/system](mdc:admin/src/views/system) - 系统管理
- [admin/src/views/business](mdc:admin/src/views/business) - 业务管理

### UniApp 子项目

移动端页面结构较为简单：

- [uniapp/pages](mdc:uniapp/pages) - 移动端页面

## 页面布局

页面通常使用以下布局组件：

- [business/src/layout/default-layout.vue](mdc:business/src/layout/default-layout.vue) - 默认布局
- [business/src/layout/page-layout.vue](mdc:business/src/layout/page-layout.vue) - 页面布局

## 菜单配置

菜单配置决定了侧边栏导航的结构：

- [business/src/router/app-menus](mdc:business/src/router/app-menus) - 业务系统菜单
- [admin/src/router/app-menus](mdc:admin/src/router/app-menus) - 管理系统菜单
