package log

import (
	"context"
	"errors"
	"time"

	"github.com/gin-gonic/gin"
)

// ExampleBasicUsage 基础用法示例
func ExampleBasicUsage() {
	// 向后兼容的用法（保持现有代码不变）
	Info("用户 %s 登录成功", "张三")
	Debug("当前状态: %+v", map[string]interface{}{"status": "active"})
	Error("处理请求失败: %v", errors.New("网络超时"))
	Warn("警告信息: %s", "内存使用率过高")
}

// ExampleModuleLogging 模块化日志示例
func ExampleModuleLogging() {
	// 使用预定义的模块日志器
	Customer().Info("客户注册成功")
	Order().Info("订单创建完成")
	BankCard().Info("银行卡绑定成功")

	// 自定义模块
	WithModule("custom_module").Info("自定义模块日志")
}

// ExampleStructuredLogging 结构化日志示例
func ExampleStructuredLogging() {
	// 单个字段
	WithField("user_id", 12345).Info("用户操作")

	// 多个字段
	WithFields(
		String("order_id", "ORD123456"),
		Float64("amount", 99.99),
		String("currency", "CNY"),
		Bool("success", true),
		Time("created_at", time.Now()),
	).Info("订单处理完成")

	// 链式调用
	Customer().
		WithField("mobile", "***********").
		WithField("channel", "app").
		WithField("ip", "***********").
		Info("客户登录")
}

// ExampleContextLogging 上下文日志示例
func ExampleContextLogging() {
	// 普通上下文
	ctx := context.Background()
	ctx = SetRequestIDToContext(ctx, "req-12345")

	WithContext(ctx).Info("带请求ID的日志")

	// 添加用户ID
	WithContext(ctx).
		WithUserID(67890).
		Info("带用户ID和请求ID的日志")
}

// ExampleGinMiddleware Gin中间件使用示例
func ExampleGinMiddleware() {
	router := gin.New()

	// 添加请求ID中间件
	router.Use(RequestIDMiddleware())

	// 添加访问日志中间件
	router.Use(AccessLogMiddleware())

	// 添加错误日志中间件
	router.Use(ErrorLogMiddleware())

	router.GET("/api/users/:id", func(c *gin.Context) {
		// 在处理函数中使用日志
		logger := WithGinContext(c)

		userID := c.Param("id")
		logger.WithField("user_id", userID).Info("开始处理用户查询")

		// 模拟业务处理
		time.Sleep(time.Millisecond * 100)

		logger.WithFields(
			String("user_id", userID),
			String("result", "success"),
			Duration("processing_time", time.Millisecond*100),
		).Info("用户查询完成")

		c.JSON(200, gin.H{"user_id": userID})
	})
}

// ExampleBusinessScenarios 业务场景示例
func ExampleBusinessScenarios() {
	// 客户注册场景
	customerRegistrationScenario()

	// 订单处理场景
	orderProcessingScenario()

	// 银行卡绑定场景
	bankCardBindingScenario()

	// 错误处理场景
	errorHandlingScenario()
}

// customerRegistrationScenario 客户注册场景
func customerRegistrationScenario() {
	requestID := "req-customer-001"
	mobile := "***********"

	// 开始注册
	Customer().WithFields(
		RequestID(requestID),
		String("mobile", mobile),
		String("channel", "mobile_app"),
		String("action", "register_start"),
	).Info("客户注册开始")

	// 验证手机号
	Customer().WithFields(
		RequestID(requestID),
		String("mobile", mobile),
		String("action", "mobile_verification"),
		Bool("verified", true),
	).Info("手机号验证通过")

	// 注册完成
	Customer().WithFields(
		RequestID(requestID),
		String("mobile", mobile),
		Int64("customer_id", 12345),
		String("action", "register_complete"),
		Duration("total_time", time.Second*3),
	).Info("客户注册完成")
}

// orderProcessingScenario 订单处理场景
func orderProcessingScenario() {
	requestID := "req-order-001"
	orderID := "ORD20250717001"
	userID := int64(12345)

	// 创建订单
	Order().WithFields(
		RequestID(requestID),
		String("order_id", orderID),
		Int64("user_id", userID),
		Float64("amount", 1999.99),
		String("currency", "CNY"),
		String("action", "order_created"),
	).Info("订单创建成功")

	// 库存检查
	Order().WithFields(
		RequestID(requestID),
		String("order_id", orderID),
		String("action", "inventory_check"),
		Bool("available", true),
	).Info("库存检查通过")

	// 订单确认
	Order().WithFields(
		RequestID(requestID),
		String("order_id", orderID),
		String("status", "confirmed"),
		String("action", "order_confirmed"),
	).Info("订单确认完成")
}

// bankCardBindingScenario 银行卡绑定场景
func bankCardBindingScenario() {
	requestID := "req-bankcard-001"
	userID := int64(12345)
	cardNumber := "****1234"

	// 开始绑定
	BankCard().WithFields(
		RequestID(requestID),
		Int64("user_id", userID),
		String("card_number", cardNumber),
		String("action", "bind_start"),
	).Info("银行卡绑定开始")

	// 验证银行卡
	BankCard().WithFields(
		RequestID(requestID),
		Int64("user_id", userID),
		String("card_number", cardNumber),
		String("bank_name", "工商银行"),
		String("action", "card_verification"),
		Bool("verified", true),
	).Info("银行卡验证通过")

	// 绑定完成
	BankCard().WithFields(
		RequestID(requestID),
		Int64("user_id", userID),
		String("card_number", cardNumber),
		Int64("card_id", 67890),
		String("action", "bind_complete"),
	).Info("银行卡绑定完成")
}

// errorHandlingScenario 错误处理场景
func errorHandlingScenario() {
	requestID := "req-error-001"
	orderID := "ORD20250717002"

	// 模拟业务错误
	err := errors.New("余额不足")

	Order().WithError(err).WithFields(
		RequestID(requestID),
		String("order_id", orderID),
		String("error_type", "business_error"),
		String("action", "payment_failed"),
	).Error("订单支付失败")

	// 模拟系统错误
	systemErr := errors.New("数据库连接超时")

	Order().WithError(systemErr).WithFields(
		RequestID(requestID),
		String("order_id", orderID),
		String("error_type", "system_error"),
		String("action", "database_operation"),
	).Error("系统错误")

	// 警告日志
	Order().WithFields(
		RequestID(requestID),
		String("order_id", orderID),
		String("warning_type", "performance"),
		Duration("response_time", time.Second*5),
	).Warn("订单处理响应时间过长")
}

// ExamplePerformanceLogging 性能日志示例
func ExamplePerformanceLogging() {
	start := time.Now()

	// 模拟业务处理
	time.Sleep(time.Millisecond * 500)

	duration := time.Since(start)

	// 记录性能日志
	Order().WithFields(
		String("operation", "create_order"),
		Duration("execution_time", duration),
		String("performance_level", getPerformanceLevel(duration)),
	).Info("操作性能统计")
}

// getPerformanceLevel 获取性能等级
func getPerformanceLevel(duration time.Duration) string {
	if duration < time.Millisecond*100 {
		return "excellent"
	} else if duration < time.Millisecond*500 {
		return "good"
	} else if duration < time.Second {
		return "acceptable"
	} else {
		return "poor"
	}
}
