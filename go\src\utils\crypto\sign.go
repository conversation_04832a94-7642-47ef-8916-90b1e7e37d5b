package crypto

import (
	"crypto"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"errors"
	"fmt"
	"os"

	"software.sslmate.com/src/go-pkcs12"
)

// SignUtil 签名工具
type SignUtil struct{}

// SignMessage 对消息进行签名
func (s *SignUtil) SignMessage(message, privateKeyPath, password, charset, certType string) (string, error) {
	// 读取私钥文件
	privateKey, err := s.loadPrivateKey(privateKeyPath, password, certType)
	if err != nil {
		return "", fmt.Errorf("加载私钥失败: %v", err)
	}

	// 计算消息的SHA256哈希
	hash := sha256.Sum256([]byte(message))

	// 使用RSA私钥进行签名
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hash[:])
	if err != nil {
		return "", fmt.Errorf("签名失败: %v", err)
	}

	// 将签名结果进行Base64编码
	return base64.StdEncoding.EncodeToString(signature), nil
}

// VerifySignature 验证签名
func (s *SignUtil) VerifySignature(message, signature, publicKeyPath, charset string) error {
	// 读取公钥文件
	publicKey, err := s.loadPublicKey(publicKeyPath)
	if err != nil {
		return fmt.Errorf("加载公钥失败: %v", err)
	}

	// 解码签名
	signatureBytes, err := base64.StdEncoding.DecodeString(signature)
	if err != nil {
		return fmt.Errorf("签名解码失败: %v", err)
	}

	// 计算消息的SHA256哈希
	hash := sha256.Sum256([]byte(message))

	// 验证签名
	err = rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hash[:], signatureBytes)
	if err != nil {
		return fmt.Errorf("签名验证失败: %v", err)
	}

	return nil
}

// loadPrivateKey 加载私钥
func (s *SignUtil) loadPrivateKey(keyPath, password, certType string) (*rsa.PrivateKey, error) {
	keyData, err := os.ReadFile(keyPath)
	if err != nil {
		return nil, err
	}

	if certType == "CERT" {
		// 处理PKCS#12格式的证书文件
		// 使用DecodeChain方法解析PKCS#12文件
		privateKey, _, _, err := pkcs12.DecodeChain(keyData, password)
		if err != nil {
			// 如果DecodeChain失败，尝试旧的Decode方法
			privateKey, _, err = pkcs12.Decode(keyData, password)
			if err != nil {
				return nil, fmt.Errorf("PKCS#12解码失败: %v", err)
			}
		}

		rsaPrivateKey, ok := privateKey.(*rsa.PrivateKey)
		if !ok {
			return nil, errors.New("私钥不是RSA格式")
		}

		return rsaPrivateKey, nil
	} else {
		// 处理PEM格式的私钥文件
		block, _ := pem.Decode(keyData)
		if block == nil {
			return nil, errors.New("无法解析PEM格式的私钥")
		}

		var privateKey interface{}
		if x509.IsEncryptedPEMBlock(block) {
			privateKey, err = x509.DecryptPEMBlock(block, []byte(password))
			if err != nil {
				return nil, err
			}
		} else {
			privateKey = block.Bytes
		}

		key, err := x509.ParsePKCS1PrivateKey(privateKey.([]byte))
		if err != nil {
			// 尝试PKCS#8格式
			keyInterface, err := x509.ParsePKCS8PrivateKey(privateKey.([]byte))
			if err != nil {
				return nil, err
			}
			key, ok := keyInterface.(*rsa.PrivateKey)
			if !ok {
				return nil, errors.New("私钥不是RSA格式")
			}
			return key, nil
		}

		return key, nil
	}
}

// loadPublicKey 加载公钥
func (s *SignUtil) loadPublicKey(keyPath string) (*rsa.PublicKey, error) {
	keyData, err := os.ReadFile(keyPath)
	if err != nil {
		return nil, err
	}

	// 尝试解析PEM格式
	block, _ := pem.Decode(keyData)
	if block != nil {
		cert, err := x509.ParseCertificate(block.Bytes)
		if err != nil {
			return nil, err
		}

		publicKey, ok := cert.PublicKey.(*rsa.PublicKey)
		if !ok {
			return nil, errors.New("公钥不是RSA格式")
		}

		return publicKey, nil
	}

	// 尝试解析DER格式
	cert, err := x509.ParseCertificate(keyData)
	if err != nil {
		return nil, err
	}

	publicKey, ok := cert.PublicKey.(*rsa.PublicKey)
	if !ok {
		return nil, errors.New("公钥不是RSA格式")
	}

	return publicKey, nil
}

// NewSignUtil 创建签名工具实例
func NewSignUtil() *SignUtil {
	return &SignUtil{}
}

// ===== 数字签名和加密功能 =====

// GenerateSignString 生成待签名字符串
// 根据商盟签名规范，对参数进行字典序排序并拼接成待签名字符串
// 参数:
//   - params: 参与签名的参数map，不包含sign和sign_type
//
// 返回:
//   - string: 待签名字符串，格式为 key1=value1&key2=value2
//   - error: 错误信息
func GenerateSignString(params map[string]string) (string, error) {
	if params == nil {
		return "", errors.New("参数不能为空")
	}

	// 过滤掉sign和sign_type参数
	filteredParams := make(map[string]string)
	for key, value := range params {
		if key != "sign" && key != "sign_type" {
			// 包含空值参数，但需要使用原始值（非URL编码值）
			filteredParams[key] = value
		}
	}

	if len(filteredParams) == 0 {
		return "", errors.New("没有可用于签名的参数")
	}

	// 获取所有键并进行字典序排序
	keys := make([]string, 0, len(filteredParams))
	for key := range filteredParams {
		keys = append(keys, key)
	}

	// 使用sort包进行字典序排序
	sortKeys(keys)

	// 拼接成待签名字符串
	var signParts []string
	for _, key := range keys {
		value := filteredParams[key]
		// 对于URL编码的值，需要先解码获取原始值用于签名
		if decodedValue, err := urlDecode(value); err == nil {
			value = decodedValue
		}
		signParts = append(signParts, key+"="+value)
	}

	return joinStrings(signParts, "&"), nil
}

// SignWithAES256 使用AES256加密数据并返回加密结果和加密的AES密钥
// 实现一次一密机制，每次加密都生成新的AES密钥
// 参数:
//   - data: 待加密的数据
//   - rsaPublicKeyPath: RSA公钥证书路径，用于加密AES密钥
//
// 返回:
//   - encryptedData: AES256加密后的数据（Base64编码）
//   - encryptedAESKey: RSA加密后的AES密钥（Base64编码）
//   - err: 错误信息
func SignWithAES256(data string, rsaPublicKeyPath string) (encryptedData string, encryptedAESKey string, err error) {
	if data == "" {
		return "", "", errors.New("待加密数据不能为空")
	}
	if rsaPublicKeyPath == "" {
		return "", "", errors.New("RSA公钥路径不能为空")
	}

	// 1. 生成32字节的AES256密钥（一次一密）
	aesKey := make([]byte, 32) // AES256需要32字节密钥
	if _, err := cryptoRandRead(aesKey); err != nil {
		return "", "", fmt.Errorf("生成AES密钥失败: %v", err)
	}

	// 2. 使用AES256-GCM模式加密数据
	encryptedDataBytes, err := aesGCMEncrypt([]byte(data), aesKey)
	if err != nil {
		return "", "", fmt.Errorf("AES加密失败: %v", err)
	}

	// 3. 加载RSA公钥
	signUtil := &SignUtil{}
	rsaPublicKey, err := signUtil.loadPublicKey(rsaPublicKeyPath)
	if err != nil {
		return "", "", fmt.Errorf("加载RSA公钥失败: %v", err)
	}

	// 4. 使用RSA公钥加密AES密钥
	encryptedAESKeyBytes, err := rsaEncrypt(rsaPublicKey, aesKey)
	if err != nil {
		return "", "", fmt.Errorf("RSA加密AES密钥失败: %v", err)
	}

	// 5. 返回Base64编码的结果
	encryptedData = base64Encode(encryptedDataBytes)
	encryptedAESKey = base64Encode(encryptedAESKeyBytes)

	return encryptedData, encryptedAESKey, nil
}

// VerifySignString 验证签名字符串
// 参数:
//   - params: 包含所有参数的map（包括sign）
//   - expectedSign: 期望的签名值
//
// 返回:
//   - bool: 验证结果
//   - error: 错误信息
func VerifySignString(params map[string]string, expectedSign string) (bool, error) {
	if params == nil {
		return false, errors.New("参数不能为空")
	}
	if expectedSign == "" {
		return false, errors.New("期望签名不能为空")
	}

	// 生成待签名字符串
	signString, err := GenerateSignString(params)
	if err != nil {
		return false, fmt.Errorf("生成签名字符串失败: %v", err)
	}

	// 从参数中获取实际签名
	actualSign, exists := params["sign"]
	if !exists {
		return false, errors.New("参数中缺少sign字段")
	}

	// 比较签名（这里可以根据实际需求进行签名验证）
	// 目前简单比较实际签名和期望签名
	// 在实际应用中，可能需要使用signString进行更复杂的验证
	_ = signString // 避免未使用变量警告
	return actualSign == expectedSign, nil
}

// DecryptWithAES256 使用AES256解密数据
// 参数:
//   - encryptedData: AES256加密的数据（Base64编码）
//   - encryptedAESKey: RSA加密的AES密钥（Base64编码）
//   - rsaPrivateKeyPath: RSA私钥路径
//   - password: 私钥密码
//
// 返回:
//   - string: 解密后的原始数据
//   - error: 错误信息
func DecryptWithAES256(encryptedData string, encryptedAESKey string, rsaPrivateKeyPath string, password string) (string, error) {
	if encryptedData == "" {
		return "", errors.New("加密数据不能为空")
	}
	if encryptedAESKey == "" {
		return "", errors.New("加密的AES密钥不能为空")
	}
	if rsaPrivateKeyPath == "" {
		return "", errors.New("RSA私钥路径不能为空")
	}

	// 1. 解码Base64数据
	encryptedDataBytes, err := base64Decode(encryptedData)
	if err != nil {
		return "", fmt.Errorf("解码加密数据失败: %v", err)
	}

	encryptedAESKeyBytes, err := base64Decode(encryptedAESKey)
	if err != nil {
		return "", fmt.Errorf("解码加密AES密钥失败: %v", err)
	}

	// 2. 加载RSA私钥
	signUtil := &SignUtil{}
	rsaPrivateKey, err := signUtil.loadPrivateKey(rsaPrivateKeyPath, password, "PEM")
	if err != nil {
		return "", fmt.Errorf("加载RSA私钥失败: %v", err)
	}

	// 3. 使用RSA私钥解密AES密钥
	aesKey, err := rsaDecrypt(rsaPrivateKey, encryptedAESKeyBytes)
	if err != nil {
		return "", fmt.Errorf("RSA解密AES密钥失败: %v", err)
	}

	// 4. 使用AES密钥解密数据
	decryptedData, err := aesGCMDecrypt(encryptedDataBytes, aesKey)
	if err != nil {
		return "", fmt.Errorf("AES解密数据失败: %v", err)
	}

	return string(decryptedData), nil
}

// ===== 辅助函数 =====

// sortKeys 对字符串切片进行字典序排序
func sortKeys(keys []string) {
	// 使用冒泡排序实现字典序排序
	n := len(keys)
	for i := 0; i < n-1; i++ {
		for j := 0; j < n-i-1; j++ {
			if keys[j] > keys[j+1] {
				keys[j], keys[j+1] = keys[j+1], keys[j]
			}
		}
	}
}

// urlDecode URL解码
func urlDecode(value string) (string, error) {
	// 检查是否包含需要解码的字符
	if !containsURLEncodedChars(value) {
		return value, nil
	}

	// 简单的URL解码实现
	decoded := value
	decoded = replaceString(decoded, "%40", "@")
	decoded = replaceString(decoded, "%26", "&")
	decoded = replaceString(decoded, "%3D", "=")
	decoded = replaceString(decoded, "%2B", "+")
	decoded = replaceString(decoded, "%2F", "/")
	decoded = replaceString(decoded, "%3F", "?")
	decoded = replaceString(decoded, "%23", "#")
	decoded = replaceString(decoded, "%5B", "[")
	decoded = replaceString(decoded, "%5D", "]")
	decoded = replaceString(decoded, "%20", " ")

	return decoded, nil
}

// containsURLEncodedChars 检查字符串是否包含URL编码字符
func containsURLEncodedChars(s string) bool {
	return containsString(s, "%")
}

// joinStrings 连接字符串切片
func joinStrings(parts []string, separator string) string {
	// 简单的字符串连接实现
	if len(parts) == 0 {
		return ""
	}
	if len(parts) == 1 {
		return parts[0]
	}

	result := parts[0]
	for i := 1; i < len(parts); i++ {
		result += separator + parts[i]
	}
	return result
}

// replaceString 替换字符串
func replaceString(s, old, new string) string {
	result := ""
	for i := 0; i < len(s); {
		if i+len(old) <= len(s) && s[i:i+len(old)] == old {
			result += new
			i += len(old)
		} else {
			result += string(s[i])
			i++
		}
	}
	return result
}

// containsString 检查字符串是否包含子字符串
func containsString(s, substr string) bool {
	return indexOfString(s, substr) >= 0
}

// indexOfString 查找子字符串的位置
func indexOfString(s, substr string) int {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}

// ===== 加密相关辅助函数 =====

// cryptoRandRead 生成随机字节
func cryptoRandRead(b []byte) (int, error) {
	return rand.Read(b)
}

// base64Encode Base64编码
func base64Encode(data []byte) string {
	return base64.StdEncoding.EncodeToString(data)
}

// base64Decode Base64解码
func base64Decode(data string) ([]byte, error) {
	return base64.StdEncoding.DecodeString(data)
}

// aesGCMEncrypt 使用AES-GCM模式加密数据
func aesGCMEncrypt(plaintext, key []byte) ([]byte, error) {
	// 创建AES cipher
	block, err := createAESCipher(key)
	if err != nil {
		return nil, err
	}

	// 创建GCM模式
	gcm, err := createGCMMode(block)
	if err != nil {
		return nil, err
	}

	// 生成随机nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := cryptoRandRead(nonce); err != nil {
		return nil, err
	}

	// 加密数据
	ciphertext := gcm.Seal(nonce, nonce, plaintext, nil)
	return ciphertext, nil
}

// aesGCMDecrypt 使用AES-GCM模式解密数据
func aesGCMDecrypt(ciphertext, key []byte) ([]byte, error) {
	// 创建AES cipher
	block, err := createAESCipher(key)
	if err != nil {
		return nil, err
	}

	// 创建GCM模式
	gcm, err := createGCMMode(block)
	if err != nil {
		return nil, err
	}

	// 检查密文长度
	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return nil, errors.New("密文长度不足")
	}

	// 提取nonce和密文
	nonce, ciphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]

	// 解密数据
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, err
	}

	return plaintext, nil
}

// createAESCipher 创建AES cipher
func createAESCipher(key []byte) (cipher.Block, error) {
	return aes.NewCipher(key)
}

// createGCMMode 创建GCM模式
func createGCMMode(block cipher.Block) (cipher.AEAD, error) {
	return cipher.NewGCM(block)
}

// rsaEncrypt 使用RSA公钥加密数据
func rsaEncrypt(publicKey *rsa.PublicKey, data []byte) ([]byte, error) {
	return rsa.EncryptPKCS1v15(rand.Reader, publicKey, data)
}

// rsaDecrypt 使用RSA私钥解密数据
func rsaDecrypt(privateKey *rsa.PrivateKey, ciphertext []byte) ([]byte, error) {
	return rsa.DecryptPKCS1v15(rand.Reader, privateKey, ciphertext)
}
