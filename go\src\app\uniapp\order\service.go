package order

import (
	"context"
	businessorder "fincore/app/business/order"
	"fincore/model"
	"fincore/utils/lock"
	"fincore/utils/log"
	"fincore/utils/repayment"
	"fincore/utils/shopspringutils"
	"fmt"
)

// OrderService 订单服务
type OrderService struct {
	logger             *log.Logger
	accountService     *model.BusinessAppAccountService
	loanOrderService   *model.BusinessLoanOrdersService
	productRuleService *model.ProductRulesService
	ctx                context.Context
}

// RepaymentPreviewResponse 还款计划预览响应结构
type RepaymentPreviewResponse struct {
	TotalRepayableAmount float64                  `json:"total_repayable_amount"` // 还款总额
	TotalPeriods         int                      `json:"total_periods"`          // 还款期数
	Periods              []RepaymentPreviewPeriod `json:"periods"`                // 每期还款计划
}

// RepaymentPreviewPeriod 还款计划期数信息
type RepaymentPreviewPeriod struct {
	PeriodNumber   int     `json:"period_number"`    // 期数
	DueDate        string  `json:"due_date"`         // 每期还款到期日期
	TotalDueAmount float64 `json:"total_due_amount"` // 当期应还总额
}

// UserOrderHistoryItem 用户订单历史项
type UserOrderHistoryItem struct {
	OrderID              int     `json:"order_id"`               // 订单ID
	OrderNo              string  `json:"order_no"`               // 订单编号
	Status               int     `json:"status"`                 // 订单状态(0-3)
	StatusText           string  `json:"status_text"`            // 订单状态文本
	CreatedAt            string  `json:"created_at"`             // 借款时间（创单时间）
	TotalRepayableAmount float64 `json:"total_repayable_amount"` // 还款总金额
}

// UserOrderHistoryResponse 用户订单历史响应
type UserOrderHistoryResponse struct {
	Orders []UserOrderHistoryItem `json:"orders"` // 订单列表
}

type Option func(*OrderService)

func WithAccountService() func(*OrderService) {
	return func(s *OrderService) {
		s.accountService = model.NewBusinessAppAccountService()
	}
}

func WithLoanOrderService() func(*OrderService) {
	return func(s *OrderService) {
		s.loanOrderService = model.NewBusinessLoanOrdersService(s.ctx)
	}
}

func WithProductRuleService() func(*OrderService) {
	return func(s *OrderService) {
		s.productRuleService = model.NewProductRulesService()
	}
}

func NewOrderService(ctx context.Context, options ...Option) *OrderService {
	service := &OrderService{
		ctx:    ctx,
		logger: log.Order().WithContext(ctx),
	}
	for _, option := range options {
		option(service)
	}
	return service
}

// GetRepaymentPreview 获取还款计划预览
func (s *OrderService) GetRepaymentPreview(productRuleID int) (resp *RepaymentPreviewResponse, err error) {
	// 1. 查询产品规则
	productRule, err := s.productRuleService.GetProductRuleByID(productRuleID)
	if err != nil {
		return
	}

	if productRule == nil {
		return nil, fmt.Errorf("产品规则不存在")
	}

	// 2. 使用还款计算器计算还款计划
	schedule, err := repayment.CalculateRepaymentSchedule(productRule)
	if err != nil {
		return nil, fmt.Errorf("计算还款计划失败: %v", err)
	}

	// 3. 转换为响应格式
	resp = &RepaymentPreviewResponse{
		TotalRepayableAmount: schedule.TotalRepayableAmount,
		TotalPeriods:         schedule.TotalPeriods,
		Periods:              make([]RepaymentPreviewPeriod, 0, len(schedule.Periods)),
	}

	for _, period := range schedule.Periods {
		responsePeriod := RepaymentPreviewPeriod{
			PeriodNumber:   period.PeriodNumber,
			DueDate:        period.DueDate,
			TotalDueAmount: period.TotalDueAmount,
		}
		resp.Periods = append(resp.Periods, responsePeriod)
	}

	return
}

// OrderBillsResponse 订单账单查询响应结构
type OrderBillsResponse struct {
	LoanDate          string     `json:"loan_date"`          // 借款日期
	LoanAmount        float64    `json:"loan_amount"`        // 借款总金额
	TotalPeriods      int        `json:"total_periods"`      // 分期数
	DaysToNextDue     int        `json:"days_to_next_due"`   // 距离最近一期还款天数
	RepaymentSchedule []BillItem `json:"repayment_schedule"` // 还款计划列表
}

// AllOrderBillsResponse 所有在途订单账单查询响应结构
type AllOrderBillsResponse struct {
	PendingLoan       bool               `json:"pending_loan"`       // 是否全部待放款
	LoanTotalAmount   float64            `json:"loan_total_amount"`  // 借款总金额（所有订单的应还总金额 - 已付总额的累加）
	DaysToNextDue     int                `json:"days_to_next_due"`   // 距离最近一期还款天数（逾期返回负数）
	RepaymentSchedule []AllOrderBillItem `json:"repayment_schedule"` // 所有订单还款计划列表
}

// AllOrderBillItem 所有订单账单项结构体
type AllOrderBillItem struct {
	BillID          int     `json:"bill_id"`           // 账单ID
	LoanDate        string  `json:"loan_date"`         // 借款日期
	OrderLoanAmount float64 `json:"order_loan_amount"` // 所属订单借款总金额（申请贷款金额）
	OrderStatus     int     `json:"order_status"`      // 所属订单状态 0-待放款 1-放款中
	PendingAmount   float64 `json:"pending_amount"`    // 待还款金额 = 当期应还总额 - 当期已还金额
	DueDate         string  `json:"due_date"`          // 最后还款日
	TotalPeriods    int     `json:"total_periods"`     // 所属订单总期数
	CurrentPeriod   int     `json:"current_period"`    // 当前期数
	BillStatus      int     `json:"bill_status"`       // 账单状态 0-待还款 1-还款中
}

// GetAllInProgressOrderBills 获取用户所有在途订单账单信息
func (s *OrderService) GetAllInProgressOrderBills(userID int) (resp *AllOrderBillsResponse, err error) {
	// 1. 从Repository层获取所有已放款订单的账单数据
	allOrderBills, err := GetAllInLentOrderBillsByUserID(s.ctx, userID)
	if err != nil {
		return
	}

	// 2. 已放款计算借款总金额（应还总金额 - 已付总额的累加 - 累计减免金额）
	var loanTotalAmount float64
	for _, bill := range allOrderBills {
		if bill.OrderStatus == model.OrderStatusDisbursed {
			loanTotalAmount = shopspringutils.AddAmountsWithDecimal(loanTotalAmount, bill.PendingAmount)
		}
	}

	// 已经款总金额精确保留两位小数
	loanTotalAmount = shopspringutils.CeilToTwoDecimal(loanTotalAmount)

	// 3. 计算距离最近一期还款天数
	daysToNextDue := 0
	var pendingLoan bool
	if len(allOrderBills) > 0 {
		pendingLoan = true
		// 找到最近的还款日期
		nearestDueDate := allOrderBills[0].DueDate
		for _, bill := range allOrderBills {
			if bill.DueDate < nearestDueDate {
				nearestDueDate = bill.DueDate
			}
		}
		daysToNextDue = CalculateDaysToNextDue(nearestDueDate)
	}

	// 剔除掉待放款的账单
	allOrderBills = removePendingOrderBills(allOrderBills)

	// 4. 构建响应数据
	resp = &AllOrderBillsResponse{
		PendingLoan:       pendingLoan,
		LoanTotalAmount:   loanTotalAmount,
		DaysToNextDue:     daysToNextDue,
		RepaymentSchedule: allOrderBills,
	}

	return
}

// removePendingOrderBills 剔除掉待放款的账单
func removePendingOrderBills(allOrderBills []AllOrderBillItem) []AllOrderBillItem {
	var newAllOrderBills []AllOrderBillItem
	for _, bill := range allOrderBills {
		if bill.OrderStatus != 0 {
			newAllOrderBills = append(newAllOrderBills, bill)
		}
	}
	return newAllOrderBills
}

// CheckCanCreateResponse 检查是否可创建订单响应结构
type CheckCanCreateResponse struct {
	CanCreate bool   `json:"can_create"` // 是否可创建订单
	Message   string `json:"message"`    // 提示信息
}

// CheckCanCreate 检查用户是否可创建订单
func (s *OrderService) CheckCanCreate(userID int, loanAmount float64) (resp *CheckCanCreateResponse, err error) {
	s.logger.Infof("检查用户是否可创建订单，用户ID: %d, 申请额度: %.2f", userID, loanAmount)

	// 用户维度加锁
	key := fmt.Sprintf("check_can_create_%d", userID)
	orderLock := lock.GetLockWithLogger(key, s.logger)
	orderLock.Lock()
	defer orderLock.Unlock()

	resp = &CheckCanCreateResponse{
		CanCreate: true,
		Message:   "可以创建订单",
	}

	// 1. 检查是否存在待放款订单
	pendingOrders, err := s.loanOrderService.CountPendingDisbursementOrdersByUserID(userID)
	if err != nil {
		s.logger.Errorf("查询待放款订单失败: %v", err)
		resp.CanCreate = false
		resp.Message = fmt.Sprintf("查询待放款订单失败: %v", err)
		return resp, nil
	}

	if pendingOrders > 0 {
		s.logger.Infof("用户存在待放款订单，数量: %d", pendingOrders)
		resp.CanCreate = false
		resp.Message = "存在待放款订单"
		return resp, nil
	}

	// 2. 检查在途订单数量（状态为0-待放款和1-放款中）
	inProgressOrders, err := s.loanOrderService.CountInProgressOrdersByUserID(nil, userID)
	if err != nil {
		s.logger.Errorf("查询在途订单失败: %v", err)
		resp.CanCreate = false
		resp.Message = fmt.Sprintf("查询在途订单失败: %v", err)
		return resp, nil
	}

	if inProgressOrders >= 2 {
		s.logger.Infof("在途订单数量超限，数量: %d", inProgressOrders)
		resp.CanCreate = false
		resp.Message = "在途订单数量超限"
		return resp, nil
	}

	// 3. 获取用户剩余额度
	reminderQuota, err := s.accountService.GetReminderQuotaByUserID(int64(userID))
	if err != nil {
		s.logger.Errorf("查询用户剩余额度失败: %v", err)
		resp.CanCreate = false
		resp.Message = fmt.Sprintf("查询用户剩余额度失败: %v", err)
		return resp, nil
	}

	s.logger.Infof("用户剩余额度: %.2f", reminderQuota)

	// 4. 检查申请额度是否小于等于用户剩余额度
	if loanAmount > reminderQuota {
		s.logger.Infof("申请额度超过剩余额度，申请额度: %.2f, 剩余额度: %.2f", loanAmount, reminderQuota)
		resp.CanCreate = false
		resp.Message = "申请额度超过剩余额度"
		return resp, nil
	}

	// 检查在途订单限制
	err = businessorder.ValidateUserOrderLimit(userID, loanAmount)
	if err != nil {
		s.logger.Errorf("检查在途订单限制失败: %v", err)
		resp.CanCreate = false
		resp.Message = fmt.Sprintf("借款金额%.2f元已超过限制额度", loanAmount)
		return resp, nil
	}

	s.logger.Infof("检查通过，用户可以创建订单")
	return resp, nil
}

// GetUserOrderHistory 获取用户待放款和放款中的订单
func (s *OrderService) GetUserOrderHistory(userID int) (resp *UserOrderHistoryResponse, err error) {
	// 记录开始日志
	s.logger.Infof("开始获取用户待放款和放款中的订单，用户ID: %d", userID)

	// 1. 参数验证
	if userID <= 0 {
		s.logger.Errorf("用户ID无效: %d", userID)
		return nil, fmt.Errorf("用户ID无效")
	}

	// 2. 调用Repository层获取数据
	orderHistoryData, err := GetUserPendingOrders(userID)
	if err != nil {
		s.logger.Errorf("获取用户待放款和放款中订单失败，用户ID: %d, 错误: %v", userID, err)
		return nil, fmt.Errorf("获取用户待放款和放款中订单失败: %v", err)
	}

	s.logger.Infof("成功获取用户待放款和放款中订单数据，用户ID: %d, 订单数量: %d", userID, len(orderHistoryData))

	// 3. 转换为响应格式
	resp = &UserOrderHistoryResponse{
		Orders: make([]UserOrderHistoryItem, 0, len(orderHistoryData)),
	}

	for _, orderData := range orderHistoryData {
		item := UserOrderHistoryItem{
			OrderID:              orderData.OrderID,
			OrderNo:              orderData.OrderNo,
			Status:               orderData.Status,
			StatusText:           s.getOrderStatusText(orderData.Status),
			CreatedAt:            orderData.CreatedAt,
			TotalRepayableAmount: orderData.TotalRepayableAmount,
		}

		resp.Orders = append(resp.Orders, item)
	}

	s.logger.Infof("成功构建用户待放款和放款中订单响应，用户ID: %d, 处理订单数量: %d", userID, len(resp.Orders))
	return
}

// getOrderStatusText 获取订单状态文本（订单状态只有0-3四种）
func (s *OrderService) getOrderStatusText(status int) string {
	switch status {
	case 0:
		return "待放款"
	case 1:
		return "放款中"
	case 2:
		return "交易关闭"
	case 3:
		return "交易完成"
	default:
		s.logger.Warnf("未知的订单状态: %d", status)
		return "未知状态"
	}
}
