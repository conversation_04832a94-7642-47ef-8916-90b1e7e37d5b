# 锁模块

## 概述

这是一个通用锁模块，专注于提供简洁、高效、业务无关的锁功能。支持链式操作、自动过期、通过key解锁等特性。

## 特性

- ✅ **链式操作**：支持流畅的链式调用语法
- ✅ **自动过期**：锁支持自动过期机制，防止死锁
- ✅ **通过key解锁**：可以通过锁的key直接解锁
- ✅ **业务无关**：完全通用的设计，不包含任何业务逻辑
- ✅ **并发安全**：基于sync.Mutex实现，保证并发安全
- ✅ **统计监控**：提供锁使用情况的统计信息
- ✅ **超时控制**：支持加锁超时设置
- ✅ **上下文日志**：支持注入上下文，记录详细的加锁解锁日志
- ✅ **请求链路追踪**：支持请求ID追踪，便于问题排查

## 文件结构

```
go/src/utils/lock/
├── simple_lock.go              # 核心实现
├── simple_lock_example.go      # 使用示例
├── simple_lock_test.go         # 测试文件
└── README.md                   # 说明文档
```

## 核心接口

### Lock 接口

```go
type Lock interface {
    Lock() Lock                              // 加锁，返回自身支持链式调用
    Unlock() Lock                            // 解锁，返回自身支持链式调用
    TryLock() (bool, Lock)                  // 尝试加锁，返回是否成功和自身
    WithTimeout(timeout time.Duration) Lock // 设置超时时间，支持链式调用
    WithContext(ctx context.Context) Lock   // 设置上下文，支持链式调用
    WithLogger(logger *log.Logger) Lock     // 设置日志对象，支持链式调用
    GetKey() string                         // 获取锁的key
}
```

### LockManager 接口

```go
type LockManager interface {
    GetLock(key string, expiration ...time.Duration) Lock // 获取指定key的锁，可选过期时间
    GetLockWithLogger(key string, logger *log.Logger, expiration ...time.Duration) Lock // 获取指定key的锁，使用指定日志对象
    UnlockByKey(key string) error                         // 通过key解锁
    CleanExpiredLocks() int                               // 清理过期锁
    GetStats() map[string]LockStats                       // 获取锁统计信息
}
```

## 基本使用

### 1. 简单加锁解锁

```go
import "fincore/utils/lock"

// 获取锁（默认5分钟过期）
lock := lock.GetLock("user:1001:operation")
lock.Lock()
defer lock.Unlock()

// 执行业务逻辑
// ...
```

### 2. 链式操作

```go
// 流畅的链式调用
lock.GetLock("order:2001", 10*time.Minute).
    WithTimeout(30*time.Second).
    Lock().
    Unlock()
```

### 3. 上下文日志

```go
import "fincore/utils/log"

// 创建带请求ID的上下文
ctx := context.Background()
ctx = log.SetRequestIDToContext(ctx, "req-12345")

// 使用上下文进行锁操作（会记录详细日志）
lock.GetLock("user:1001:operation", 5*time.Minute).
    WithContext(ctx).
    Lock().
    Unlock()
```

### 4. 尝试加锁

```go
lock := lock.GetLock("resource:3001")
if success, _ := lock.TryLock(); success {
    defer lock.Unlock()
    // 执行业务逻辑
} else {
    // 资源被占用，处理失败情况
}
```

### 5. 通过key解锁

```go
key := "payment:4001"
lock := lock.GetLock(key)
lock.Lock()

// 在其他地方通过key解锁
err := lock.UnlockByKey(key)
if err != nil {
    log.Printf("解锁失败: %v", err)
}
```

### 6. 设置过期时间

```go
// 设置锁的过期时间为10分钟
lock := lock.GetLock("session:5001", 10*time.Minute)
lock.Lock()
defer lock.Unlock()
```

### 7. 日志对象注入

```go
import "fincore/utils/log"

// 创建自定义日志对象
customLogger := log.RegisterModule("custom_lock", "自定义锁模块")

// 方式1：直接使用指定日志对象（会记录日志）
lock := lock.GetLockWithLogger("user:1001", customLogger, 5*time.Minute)
lock.Lock()
defer lock.Unlock()

// 方式2：链式操作注入日志对象（会记录日志）
lock.GetLock("order:2001", 10*time.Minute).
    WithLogger(customLogger).
    Lock().
    Unlock()

// 方式3：不注入日志对象（不会记录日志）
lock := lock.GetLock("no_log:3001", 5*time.Minute) // 不会记录任何日志
lock.Lock()
defer lock.Unlock()

// 方式4：结合上下文和自定义日志（会记录日志）
ctx := log.SetRequestIDToContext(context.Background(), "req-12345")
lock.GetLockWithLogger("complex:4001", customLogger, 5*time.Minute).
    WithContext(ctx).
    Lock().
    Unlock()
```

## 高级功能

### 1. 锁统计信息

```go
// 获取所有锁的统计信息
stats := lock.GetStats()
for key, stat := range stats {
    fmt.Printf("锁: %s, 创建时间: %s, 过期时间: %s, 是否锁定: %v\n",
        key, stat.CreatedAt, stat.ExpiresAt, stat.IsLocked)
}
```

### 2. 清理过期锁

```go
// 手动清理过期锁
cleaned := lock.CleanExpiredLocks()
fmt.Printf("清理了 %d 个过期锁\n", cleaned)

// 定期清理（推荐在后台goroutine中运行）
go func() {
    ticker := time.NewTicker(1 * time.Hour)
    defer ticker.Stop()
    
    for range ticker.C {
        cleaned := lock.CleanExpiredLocks()
        log.Printf("清理了 %d 个过期锁", cleaned)
    }
}()
```

### 3. 超时控制

```go
lock := lock.GetLock("timeout:test").
    WithTimeout(5*time.Second) // 设置5秒超时

lock.Lock() // 如果5秒内无法获取锁，会继续等待但有超时控制
defer lock.Unlock()
```

## 真实业务场景示例

### 用户操作防重复

```go
func ProcessUserOrder(userID, orderID string) error {
    key := fmt.Sprintf("user:%s:order:%s", userID, orderID)
    
    lock := lock.GetLock(key, 10*time.Minute)
    if success, _ := lock.TryLock(); !success {
        return errors.New("订单正在处理中，请稍后重试")
    }
    defer lock.Unlock()
    
    // 执行订单处理逻辑
    return processOrder(orderID)
}
```

### 支付处理

```go
func ProcessPayment(paymentID string) error {
    key := fmt.Sprintf("payment:%s", paymentID)
    
    lock.GetLock(key, 5*time.Minute).
        WithTimeout(30*time.Second).
        Lock()
    
    defer lock.UnlockByKey(key) // 使用key解锁
    
    // 执行支付逻辑
    return executePayment(paymentID)
}
```

## 性能特点

- **高并发**：基于sync.Map实现，支持高并发访问
- **低延迟**：内存锁，无网络开销
- **自动清理**：支持过期锁的自动清理，防止内存泄漏
- **链式优化**：链式操作不会产生额外的性能开销

## 测试

运行测试：
```bash
cd go/src/utils/lock
go test -v
```

运行基准测试：
```bash
go test -bench=. -v
```


### 从旧版本迁移

**旧版本代码：**
```go
orderLock := lockutils.GetOrderLock(orderID)
orderLock.Lock()
defer orderLock.Unlock()
```

**新版本代码：**
```go
lock := lock.GetLock(fmt.Sprintf("order:%v", orderID), 5*time.Minute)
lock.Lock()
defer lock.Unlock()
```

### 主要变化

1. **移除业务相关方法**：不再提供GetOrderLock、GetUserLock等方法
2. **统一接口**：所有锁都通过GetLock(key)获取
3. **添加过期时间**：所有锁都支持过期时间设置
4. **支持链式操作**：提供流畅的API体验

## 注意事项

1. **避免死锁**：使用defer确保锁被释放
2. **合理设置过期时间**：根据业务需求设置合适的过期时间
3. **定期清理**：在长期运行的服务中定期清理过期锁
4. **key命名规范**：使用有意义的key命名，建议格式：`模块:ID:操作`

## 总结

重构后的锁模块提供了简洁、高效、业务无关的锁功能，支持链式操作和自动过期，适用于各种并发控制场景。
