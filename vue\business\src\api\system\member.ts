import { defHttp } from '@/utils/http';
import type { BaseResponse } from '@/api/base';

// 成员账户信息接口
export interface BusinessAccount {
  id: number;
  username: string;
  name: string;
  nickname?: string;
  avatar?: string;
  tel?: string;
  mobile?: string;
  email?: string;
  status: number; // 账户状态，0-正常，1-禁用
  dept_id?: number;
  depname?: string;
  roleid?: number[];
  rolename?: string[];
  createtime?: number;
  updatetime?: number;
  remark?: string;
  company?: string;
  city?: string;
  address?: string;
  province?: string;
  area?: string;
}

// 成员列表响应接口
export interface MemberListResponse {
  code: number;
  message: string;
  data: BusinessAccount[];
  time: number;
}

// API接口枚举
enum Api {
  GetList = '/system/member/get_list',
}

// 获取成员列表
export function getMemberList() {
  return defHttp.get<MemberListResponse>({
    url: Api.GetList,
  }, {
    isTransformResponse: false // 禁用响应转换，确保返回完整的响应对象
  });
} 