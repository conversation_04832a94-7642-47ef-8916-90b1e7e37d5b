package gf

import (
	"math/rand"
)

var (
	chars = []byte("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ")
)

// 获取随机数
func RandString(length int) string {
	bs := []byte{}
	for i := 0; i < length; i++ {
		bs = append(bs, chars[rand.Intn(len(chars))])
	}
	return string(bs)
}

// 获取只有数字的随机数
func RandIntString(length int) string {
	bs := []byte{}
	for i := 0; i < length; i++ {
		bs = append(bs, chars[rand.Intn(9)])
	}
	return string(bs)
}
