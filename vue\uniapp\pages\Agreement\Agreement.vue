<template>
	<view class="contract-page">
		<!-- 主要内容区域 -->
		<view class="content-card">
			<text class="confirm-text">请确认以下协议</text>
			<text class="agreement-list">
				共5份协议，包含贷款告知书、担保协议、借款合同、承诺书、债权转让通知书
			</text>
		</view>
		<!-- 底部同意按钮 -->
		<view class="agree-button" @click="handleAgreement">
			<text>我已阅读并同意以上协议</text>
		</view>
	</view>
</template>

<script setup>
	import { ref } from "vue";
	import {
		onLoad,
		onShow
	} from "@dcloudio/uni-app";
	import userApi from '@/api/user.js';
	import {
		storeToRefs
	} from 'pinia';
	import user from '@/store/user.js';
	const userStore = user();
	
	const { userInfo, cid } = storeToRefs(userStore);
	const product_id = ref('');
	const loan_amount = ref('');
	const bank_card_id = ref('');
	onLoad((opt) => {
		product_id.value = opt.product_id || '';
		uni.setStorageSync('product_id', opt.product_id);
		loan_amount.value = opt.loan_amount || '';
		uni.setStorageSync('loan_amount', opt.loan_amount);
		bank_card_id.value = opt.bank_card_id;
	})
	// 模拟后端接口（固定返回你的目标链接）
	const mockGenerateContract = () => {
		return new Promise((resolve) => {
			// 模拟网络延迟（200ms）
			setTimeout(() => {
				resolve({
					code: 0,
					msg: "合同生成成功",
					data: {
						// 直接返回你指定的固定链接
						contractLink: "https://h5.asign.cn/web/short/AjiM3a272232"
					}
				});
			}, 200);
		});
	};
	
	
	
	const handleAgreement = async () => {
		// 1. 显示确认弹窗
		uni.showModal({
			title: "确认提示",
			content: "您确定同意所有协议条款吗？",
			cancelText: "取消",
			confirmText: "确认",
			success: async (res) => {
				if (res.confirm) {
					// uid-用户id user_name-用户姓名 product_id-产品id channel_id 渠道id
					let params = {
						user_id: userInfo.value.uid,
						user_name: userInfo.value.name,
						channel_id: cid.value,
						product_id: parseInt(product_id.value),
						bank_card_id: Number(bank_card_id.value)
					}
					// console.log(params)
					uni.showLoading({ title: "合同生成中..." });
					try{
						const res = await userApi.createContract(params);
						if (res.code === 0) {
							uni.showToast({ title: "合同生成成功", icon: "success" });
							// 跳转页面
							// #ifdef WEB
							location.href = res.data;
							// #endif
							// #ifndef WEB
							const appAuthorizeSetting = uni.getAppAuthorizeSetting();
							if(appAuthorizeSetting.cameraAuthorized == 'authorized') {
								uni.navigateTo({
									url: `/pages/webview/htWebView?url=${encodeURIComponent(res.data)}`
								})
							}else{
								uni.showModal({
									title: "提示",
									content: "摄像头未授权开启，是否跳转设置页'权限管理->相机'设置允许",
									success: () => {
										uni.openAppAuthorizeSetting()
									}
								})
							}
							// #endif
						}else {
							uni.showToast({ title: res.message || "合同生成失败", icon: "none" });
						}
					}catch (error) {
						uni.showToast({ title: "网络异常，请重试", icon: "none" });
					}finally {
						uni.hideLoading();
					}
					
					// 2. 显示加载状态
					// uni.showLoading({ title: "合同生成中..." });

					// try {
					//   // 3. 调用模拟接口（固定返回目标链接）
					//   const res = await mockGenerateContract();

					//   // 4. 处理响应（直接跳转）
					//   if (res.code === 0) {
					//     uni.showToast({ title: "合同生成成功", icon: "success" });

					//     // 5. 跳转到指定链接（使用 web-view 组件）
					//     uni.navigateTo({
					//       url: `/pages/webview/webview?url=${encodeURIComponent(res.data.contractLink)}`
					//     });
					//   } else {
					//     uni.showToast({ title: res.msg || "合同生成失败", icon: "none" });
					//   }
					// } catch (error) {
					//   console.error("合同生成失败:", error);
					//   uni.showToast({ title: "网络异常，请重试", icon: "none" });
					// } finally {
					//   // 6. 关闭加载状态
					//   uni.hideLoading();
					// }
				}
			}
		});
	};
</script>

<style lang="scss">
	.contract-page {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding: 20rpx;
		display: flex;
		flex-direction: column;

		.content-card {
			background-color: #fff;
			border-radius: 16rpx;
			padding: 40rpx 30rpx;
			margin-bottom: 30rpx;

			.confirm-text {
				display: block;
				font-size: 32rpx;
				font-weight: bold;
				margin-bottom: 30rpx;
				color: #000;
			}

			.agreement-list {
				display: block;
				font-size: 28rpx;
				line-height: 1.6;
				margin-bottom: 50rpx;
				color: #333;
			}
		}

		.agree-button {
			position: fixed;
			bottom: 40rpx;
			left: 30rpx;
			right: 30rpx;
			background-color: #007aff;
			border-radius: 50rpx;
			padding: 24rpx;
			text-align: center;

			text {
				color: #fff;
				font-size: 32rpx;
			}
		}
	}
</style>