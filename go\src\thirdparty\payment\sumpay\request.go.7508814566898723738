package sumpay

import (
	"encoding/json"
	"fincore/global"
	"fincore/utils/config"
	"fmt"
	"time"
)

const (
	RespCodeSuccess          = "000000" //请求成功
	RespCodePayCommitSuccess = "01"     //支付渠道成功
)

// Request 商盟支付请求结构
type Request struct {
	// HTTP相关配置
	URL     string `json:"url"`
	Charset string `json:"charset"`

	// 证书相关配置
	CertType       string `json:"certType"`
	PrivateKeyPath string `json:"privateKeyPath"`
	PublicKeyPath  string `json:"publicKeyPath"`
	Password       string `json:"password"`

	// 业务数据
	Content string `json:"content"`

	// 编码配置
	AesEncodedWords    []string `json:"aesEncodedWords"`
	Base64EncodedWords []string `json:"base64EncodedWords"`
	CharsetChangeWords []string `json:"charsetChangeWords"`
}

// NewGateWayRequest 创建商盟支付请求公共参数
func NewGateWayRequest(data interface{}, pfx, password string) (*Request, error) {
	content, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("序列化请求参数失败: %v", err)
	}
	cfg := global.App.Config
	url := cfg.SumPay.TestUrl
	if cfg.SumPay.Environment == "prod" {
		url = cfg.SumPay.ProdUrl
	}
	return &Request{
		URL:            url,
		Charset:        "UTF-8",
		CertType:       "CERT",
		PrivateKeyPath: pfx,
		PublicKeyPath:  cfg.SumPay.Cert.PublicKeyPath,
		Password:       password,
		Content:        string(content),
	}, nil
}

// BaseRequest 基础请求参数
type BaseRequest struct {
	Service      string `json:"service" validate:"required"`   // 接口名称
	AppID        string `json:"app_id" validate:"required"`    // 应用ID
	MerNo        string `json:"mer_no" validate:"required"`    // 商户号
	Format       string `json:"format"`                        // 数据格式，默认JSON
	Timestamp    string `json:"timestamp" validate:"required"` // 时间戳
	Version      string `json:"version"`                       // 版本号，默认1.0
	TerminalType string `json:"terminal_type"`                 // 终端类型，默认API
}

// NewBaseRequest 创建基础请求参数
func NewBaseRequest(service, appID, merNo string) *BaseRequest {
	return &BaseRequest{
		Service:      service,
		AppID:        appID,
		MerNo:        merNo,
		Format:       "JSON",
		Timestamp:    time.Now().Format("20060102150405"),
		Version:      "1.0",
		TerminalType: "API",
	}
}

type BaseResponse struct {
	RespCode string `json:"resp_code"`
	RespMsg  string `json:"resp_msg"`
	SignType string `json:"sign_type"`
	Sign     string `json:"sign"`
}

func (r *BaseResponse) IsSuccess() bool {
	return r.RespCode == RespCodeSuccess
}

// NewBaseRequestWithTimestamp 创建基础请求参数（指定时间戳）
func NewBaseRequestWithTimestamp(service, appID, merNo, timestamp string) *BaseRequest {
	return &BaseRequest{
		Service:      service,
		AppID:        appID,
		MerNo:        merNo,
		Format:       "JSON",
		Timestamp:    timestamp,
		Version:      "1.0",
		TerminalType: "API",
	}
}

// ToJSON 将请求转换为JSON字符串
func (r *BaseRequest) ToJSON() (string, error) {
	data, err := json.Marshal(r)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// PrivateAgentPayRequest 对私代付请求
type PrivateAgentPayRequest struct {
	*BaseRequest
	OrderAmount string `json:"order_amount" validate:"required"` // 订单金额
	OrderNo     string `json:"order_no" validate:"required"`     // 订单号
	Realname    string `json:"realname" validate:"required"`     // 真实姓名
	IDNo        string `json:"id_no" validate:"required"`        // 身份证号
	CardNo      string `json:"card_no" validate:"required"`      // 银行卡号
	CardType    string `json:"card_type" validate:"required"`    // 卡类型：0-储蓄卡，1-信用卡
	NeedNotify  string `json:"need_notify"`                      // 是否需要通知：0-否，1-是
	NotifyURL   string `json:"notify_url"`                       // 通知地址
	FeeAmount   string `json:"fee_amount"`                       // 手续费金额
	IDType      string `json:"id_type"`                          // 证件类型：1-身份证
	IDValidDate string `json:"id_valid_date"`                    // 证件有效期
}

type PrivateAgentPayResponse struct {
	*BaseResponse
}

// DisbursementParams 放款参数
type DisbursementParams struct {
	OrderNo     string     `json:"order_no"`      // 订单号
	Realname    string     `json:"realname"`      // 真实姓名
	IDNo        string     `json:"id_no"`         // 身份证号
	CardNo      string     `json:"card_no"`       // 银行卡号
	CardType    string     `json:"card_type"`     // 卡类型
	OrderAmount string     `json:"order_amount"`  // 订单金额
	FeeAmount   string     `json:"fee_amount"`    // 手续费金额
	IDType      string     `json:"id_type"`       // 证件类型
	IDValidDate string     `json:"id_valid_date"` // 证件有效期
	Timestamp   *time.Time `json:"timestamp"`     // 时间戳
	NotifyURL   string     `json:"notify_url"`    // 通知地址
	NeedNotify  string     `json:"need_notify"`   // 是否需要通知
}

type PrivateAgentPaySuccessBodyResponse struct {
	OrderAmount string `json:"order_amount"` // 订单金额
	Status      string `json:"status"`       // 状态
	TradeNo     string `json:"trade_no"`     // 交易流水号
}

type PrivateAgentPaySuccessResponse struct {
	*BaseResponse
	Response PrivateAgentPaySuccessBodyResponse `json:"response"`
}

type PrivateAgentPayFailedResponse struct {
	*BaseResponse
}

// NewPrivateAgentPayRequest 创建对私代付请求
func NewPrivateAgentPayRequest(params DisbursementParams) *PrivateAgentPayRequest {
	return &PrivateAgentPayRequest{
		BaseRequest: NewBaseRequest("fosun.sumpay.api.trade.private.agent.pay", global.App.Config.SumPay.SmallLoanMerchant.AppId, global.App.Config.SumPay.SmallLoanMerchant.MerNo),
		OrderAmount: params.OrderAmount,
		OrderNo:     params.OrderNo,
		Realname:    params.Realname,
		IDNo:        params.IDNo,
		CardNo:      params.CardNo,
		CardType:    params.CardType,
		FeeAmount:   params.FeeAmount,
		IDType:      params.IDType,
		IDValidDate: params.IDValidDate,
		NeedNotify:  params.NeedNotify,
		NotifyURL:   params.NotifyURL,
	}
}

// 放款查询参数
type DisbursementQueryParams struct {
	OrderNo     string `json:"order_no"`     // 订单号
	Rows        int    `json:"rows"`         // 每页条数
	OrderStatus string `json:"order_status"` // 订单状态
	Page        int    `json:"page"`         // 页码
	BeginTime   string `json:"begin_time"`   // 开始时间  格式：YYYYMMDDHHmmSS
	EndTime     string `json:"end_time"`     // 结束时间  格式：YYYYMMDDHHmmSS
}

type DisbursementQueryRequest struct {
	*BaseRequest
	WithholdSign string `json:"withhold_sign"` // 扣款标识 0 商户账户扣款 1 个人账户扣款
	DisbursementQueryParams
}

type AgentPay struct {
	CreateTime  string `json:"create_time"`  // 创建时间
	FeeAmount   string `json:"fee_amount"`   // 手续费金额
	OrderAmount string `json:"order_amount"` // 订单金额
	OrderNo     string `json:"order_no"`     // 订单号
	OrderStatus string `json:"order_status"` // 订单状态
	SuccessTime string `json:"success_time"` // 成功时间
	TradeNo     string `json:"trade_no"`     // 交易流水号
}

type SumpayOrderSearchAgentPayResponse struct {
	AgentPays []AgentPay `json:"agent_pays"`
	RowCount  string     `json:"row_count"` // 总条数
}

type DisbursementQuerySuccessResponse struct {
	*BaseResponse
	SumpayOrderSearchAgentPayResponse SumpayOrderSearchAgentPayResponse `json:"sumpay_order_search_agent_pay_response"`
}

type DisbursementQueryFailedResponse struct {
	*BaseResponse
}

func NewDisbursementQueryRequest(appID, merNo string, params DisbursementQueryParams) *DisbursementQueryRequest {
	return &DisbursementQueryRequest{
		BaseRequest:             NewBaseRequest("fosun.sumpay.api.trade.order.search.agent.pay", appID, merNo),
		WithholdSign:            "0", // 默认商户账户扣款
		DisbursementQueryParams: params,
	}
}

type SignRequest struct {
	*BaseRequest
	MerNo    string `json:"mer_no"`    //商户编号
	UserId   string `json:"user_id"`   //用户编号
	OrderNo  string `json:"order_no"`  //签约订单号
	CardNo   string `json:"card_no"`   //卡号Aes加密
	MobileNo string `json:"mobile_no"` //银行预留手机号
	RealName string `json:"realname"`  //持卡人姓名
	IdType   uint   `json:"id_type"`   //1:身份证
	IdNo     string `json:"id_no"`     //持卡人身份证号
}

// NewSignRequest
//
//	@Description:
//	@param userId
//	@param orderNo
//	@param cardNo
//	@param mobileNo
//	@param realName
//	@param idNo
//	@return *SignRequest
func NewSignRequest(userId, orderNo, cardNo, mobileNo, realName, idNo string) *SignRequest {
	cfg := global.App.Config

	return &SignRequest{MerNo: cfg.SumPay.AssetMerchant.MerNo, UserId: userId, OrderNo: orderNo,
		CardNo: cardNo, MobileNo: mobileNo, RealName: realName, IdType: 1, IdNo: idNo,
		BaseRequest: NewBaseRequest("fosun.sumpay.api.business.entrust.signing", cfg.SumPay.AssetMerchant.AppId, cfg.SumPay.AssetMerchant.MerNo)}
}

type SignResponse struct {
	OrderNo                                   string `json:"order_no"`  //订单号
	RespCode                                  string `json:"resp_code"` //返回码000000标示成功
	RespMsg                                   string `json:"resp_msg"`  //当respCode不为000000时，该参数不能为空，返回操作失败的原因
	SignType                                  string `json:"sign_type"`
	Sign                                      string `json:"sign"` //签名
	SumpayBusinessEntrustValidMessageResponse string `json:"sumpay_business_entrust_valid_message_response"`
}

func (r *SignResponse) IsSuccess() bool {
	return r.RespCode == RespCodeSuccess
}

type ValidSMSRequest struct {
	*BaseRequest
	OrderNo    string `json:"order_no"` //签约订单号，必须和signRequest  中的OrderNo相同
	VerifyCode string `json:"verify_code"`
}

type ValidSMSResponse struct {
	*BaseResponse
	SumpayBusinessEntrustValidMessageResponse string `json:"sumpay_business_entrust_valid_message_response"`
}

// SumpayBusinessEntrustValidMessageResponse 短信验证响应业务数据
type SumpayBusinessEntrustValidMessageResponse struct {
	BindCardId string `json:"bind_card_id"` // 绑卡ID，成功必定返回
}

// IsSuccess 判断短信验证是否成功
func (r *ValidSMSResponse) IsSuccess() bool {
	return r.RespCode == RespCodeSuccess
}

// GetBindCardId 获取绑卡ID
func (r *ValidSMSResponse) GetBindCardId() string {
	var businessResp SumpayBusinessEntrustValidMessageResponse
	if err := json.Unmarshal([]byte(r.SumpayBusinessEntrustValidMessageResponse), &businessResp); err != nil {
		return ""
	}
	return businessResp.BindCardId
}

func NewValidSMSRequest(orderNo, verifyCode string) *ValidSMSRequest {
	return &ValidSMSRequest{OrderNo: orderNo, VerifyCode: verifyCode,
		BaseRequest: NewBaseRequest("fosun.sumpay.api.business.entrust.valid.message",
			global.App.Config.SumPay.AssetMerchant.AppId, global.App.Config.SumPay.AssetMerchant.MerNo)}
}

type PayRequest struct {
	*BaseRequest
	TradeCode    string          `json:"trade_code"`   //交易码	即时交易:T0002
	UserId       string          `json:"user_id"`      //用户id
	UserIpAddr   string          `json:"user_ip_addr"` //支付时用户设备所在公网IP
	OrderNo      string          `json:"order_no"`     //订单号
	OrderTime    string          `json:"order_time"`   //订单创建日期yyyymmddhhmmss,20140313142521
	OrderAmount  string          `json:"order_amount"` //订单金额，单位元，且保留两位小数
	NeedNotify   string          `json:"need_notify"`  //是否需要异步通知	0-否，1-是
	NotifyUrl    string          `json:"notify_url"`   //通知url
	Currency     string          `json:"currency"`     //币种，固定CNY
	GoodsName    string          `json:"goods_name"`   //商品名称
	GoodsNum     string          `json:"goods_num"`    //商品数量
	GoodsType    string          `json:"goods_type"`   //商品类型，1实物商品，2虚拟物品
	BindCardId   string          `json:"bind_card_id"` //绑定银行卡id
	MerchantConf config.Merchant `json:"merchant"`
}

type SumpayBusinessEntrustPayResponse struct {
	OrderNo    string `json:"order_no"`     //订单号
	SerialNo   string `json:"serial_no"`    //流水号
	Status     string `json:"status"`       //状态 0：失败；1：成功；2：处理中；申请支付同步返回结果不能作为订单判断依据，具体结果判断以异步通知，订单查询返回结果为准
	OrderAmout string `json:"order_amount"` //订单金额
}

type PayResponse struct {
	*BaseResponse
	SumpayBusinessEntrustPayResponse string `json:"sumpay_business_entrust_pay_response"`
}

// IsSuccess 判断支付是否成功
func (r *PayResponse) IsSuccess() bool {
	return r.RespCode == RespCodeSuccess
}

// GetPayResponseData 获取支付响应业务数据
func (r *PayResponse) GetPayResponseData() (*SumpayBusinessEntrustPayResponse, error) {
	var businessResp SumpayBusinessEntrustPayResponse
	if err := json.Unmarshal([]byte(r.SumpayBusinessEntrustPayResponse), &businessResp); err != nil {
		return nil, err
	}
	return &businessResp, nil
}

// NewPayRequest
//
//	@Description: 创建一键支付请求对象
//	@param userId	用户id
//	@param userIdAddr	用户支付时IP
//	@param orderNo	订单号
//	@param orderTime	订单创建时间 yyyymmddhhmmss,20140313142521
//	@param goodsName	商品名称
//	@param bindCardId	绑定卡id
//	@param orderAmount	订单金额，单位元
//	@param goodsNum		商品数量
//	@return *PayRequest
func NewPayRequest(userId, userIdAddr, orderNo, orderTime, goodsName, bindCardId string, orderAmount float64, goodsType int, goodsNum int, merchant config.Merchant) *PayRequest {
	//mock 测试
	// Base64编码通知URL
	// notifyUrl := base64.StdEncoding.EncodeToString([]byte(global.App.Config.SumPay.NotifyUrl))
	// 根据业务类型获取对应的商户号
	return &PayRequest{
		BaseRequest: NewBaseRequest("fosun.sumpay.api.business.entrust.pay", merchant.AppId, merchant.MerNo),
		TradeCode:   "T0002",
		UserId:      userId,
		UserIpAddr:  "**************",
		OrderNo:     orderNo,
		OrderTime:   orderTime,
		OrderAmount: fmt.Sprintf("%.2f", orderAmount),
		NeedNotify:  "1",
		// NotifyUrl:   notifyUrl,
		Currency:     "CNY",
		GoodsName:    goodsName,
		GoodsNum:     fmt.Sprintf("%d", goodsNum),
		BindCardId:   bindCardId,
		GoodsType:    fmt.Sprintf("%d", goodsType),
		MerchantConf: merchant,
	}
}

type QueryPayOrderRequest struct {
	*BaseRequest
	OrderNo      string          `json:"order_no"` //订单号
	MerchantConf config.Merchant `json:"merchant"`
}

type QueryPayOrderResponse struct {
	*BaseResponse
	SumpayOrderSearchMerchantResponse string `json:"sumpay_order_search_merchant_response"` // 商盟订单查询响应数据
}

// IsSuccess 判断查询是否成功
func (r *QueryPayOrderResponse) IsSuccess() bool {
	return r.RespCode == RespCodeSuccess
}

// SumpayOrderSearchMerchantResponse 商盟订单查询业务响应数据
type SumpayOrderSearchMerchantResponse struct {
	OrderNo         string  `json:"order_no"`          // 订单号
	OrderTime       string  `json:"order_time"`        // 订单时间
	PayProductCode  string  `json:"pay_product_code"`  // 支付产品代码
	SerialNo        string  `json:"serial_no"`         // 流水号
	ShareBenefitExp string  `json:"share_benefit_exp"` // 分润表达式
	Status          string  `json:"status"`            // 状态 0：失败；1：成功；2：处理中
	SuccessTime     string  `json:"success_time"`      // 成功时间
	TradeAmt        float64 `json:"trade_amt"`         // 交易金额
	TradeCode       string  `json:"trade_code"`        // 交易码
	ErrorCode       string  `json:"error_code"`        // 错误代码（当status为0时）
	ErrorMsg        string  `json:"error_msg"`         // 错误信息（当status为0时）
}

// GetQueryPayOrderResponseData 获取订单查询响应业务数据
func (r *QueryPayOrderResponse) GetQueryPayOrderResponseData() (*SumpayOrderSearchMerchantResponse, error) {
	var businessResp SumpayOrderSearchMerchantResponse
	if err := json.Unmarshal([]byte(r.SumpayOrderSearchMerchantResponse), &businessResp); err != nil {
		return nil, err
	}
	return &businessResp, nil
}

type H5QuickPayRequest struct {
	*BaseRequest
	TradeCode   string  `json:"trade_code"`   //交易码，固定为T0002
	UserId      string  `json:"user_id"`      //用户id
	OrderNo     string  `json:"order_no"`     //订单号
	OrderTime   string  `json:"order_time"`   //订单时间
	OrderAmount float64 `json:"order_amount"` //订单金额，单位元，保留两位小数
	NeedNotify  uint    `json:"need_notify"`  //是否需要异步通知，0-否，1-是
	NotifUrl    string  `json:"notify_url"`   //异步通知url
	Currency    string  `json:"currency"`     //币种，固定为CNY
	GoodsName   string  `json:"goods_name"`   //商品名称
	GoodsNum    int     `json:"goods_num"`    //商品数量
	GoodsType   int     `json:"goods_type"`   //商品类型，1:实物商品,2:虚拟物品
	LogoUrl     string  `json:"mer_logo_url"` //logo图地址
	UserIpAddr  string  `json:"user_ip_addr"` //支付用户ip
}

// RefundRequest 退款请求结构
type RefundRequest struct {
	*BaseRequest
	RefundNo     string          `json:"refund_no"`  // 退款订单号
	OrderNo      string          `json:"order_no"`   // 原订单号
	RefundAmt    string          `json:"refund_amt"` // 退款金额
	NotifyUrl    string          `json:"notify_url"` // 回调通知URL
	MerchantConf config.Merchant `json:"merchant"`
}

// RefundResponse 退款响应结构
type RefundResponse struct {
	*BaseResponse
	RefundResponseData string `json:"sumpay_refund_response"` // 退款响应数据
}

// NewRefundRequest 创建退款请求对象
func NewRefundRequest(refundNo, orderNo, refundAmt, notifyUrl string, merchant config.Merchant) *RefundRequest {
	return &RefundRequest{
		BaseRequest:  NewBaseRequest("fosun.sumpay.api.trade.refund", merchant.AppId, merchant.MerNo),
		RefundNo:     refundNo,
		OrderNo:      orderNo,
		RefundAmt:    refundAmt,
		NotifyUrl:    notifyUrl,
		MerchantConf: merchant,
	}
}

// RefundSearchRequest 退款查询请求结构
type RefundSearchRequest struct {
	*BaseRequest
	RefundNo     string          `json:"refund_no"` // 退款订单号
	MerchantConf config.Merchant `json:"merchant"`
}

// RefundSearchResponse 退款查询响应结构
type RefundSearchResponse struct {
	*BaseResponse
	RefundSearchResponseData string `json:"sumpay_refund_search_response"` // 退款查询响应数据
}

// SumpayRefundSearchResponse 退款查询业务响应数据
// {"mer_no":"s100000040","offset_amount":"0","paid_amount":"10","ref_serial_no":"5455547","refund_no":"RF1754048140084871700680947","refund_time":"20250801193544","status":"2","success_time":"0","trade_amt":"10"}
type SumpayRefundSearchResponse struct {
	MerNo        string `json:"mer_no"`        // 商户号
	RefundNo     string `json:"refund_no"`     // 退款单号
	RefundTime   string `json:"refund_time"`   // 退款时间
	RefSerialNo  string `json:"ref_serial_no"` // 退款流水号
	Status       string `json:"status"`        // 退款状态 0失败1成功2处理中
	TradeAmt     string `json:"trade_amt"`     // 退款金额
	SuccessTime  string `json:"success_time"`  // 成功退款时间
	OffsetAmount string `json:"offset_amount"` // 抵扣金额
	PaidAmount   string `json:"paid_amount"`   // 实付金额
}

// NewRefundSearchRequest 创建退款查询请求对象
func NewRefundSearchRequest(refundNo string, merchant config.Merchant) *RefundSearchRequest {
	return &RefundSearchRequest{
		BaseRequest:  NewBaseRequest("fosun.sumpay.api.trade.refund.search", merchant.AppId, merchant.MerNo),
		RefundNo:     refundNo,
		MerchantConf: merchant,
	}
}

type MerchantQueryAcountBalanceRequest struct {
	*BaseRequest
	MerNo        string          `json:"mer_no"` // 商户号
	MerchantConf config.Merchant `json:"merchant"`
}

type MerchantQueryAcountBalanceResponse struct {
	*BaseResponse
	SumpayMerchantQueryBalanceResponse struct {
		TotalBalance               string `json:"total_balance"`                 // 账户总余额=可提现总额+待结算总额
		WithdrawBalance            string `json:"withdraw_balance"`              // 可提现总额=可提现可用余额+可提现风控冻结余额+可提现业务冻结余额
		WithdrawUsableBalance      string `json:"withdraw_usable_balance"`       // 可提现可用余额,	代付直接可以提的金额
		WithdrawRiskFreezeBalance  string `json:"withdraw_risk_freeze_balance"`  // 可提现风控冻结余额
		WithdrawBusFreezeBalance   string `json:"withdraw_bus_freeze_balance"`   // 可提现业务冻结余额
		UnsettledBalance           string `json:"unsettled_balance"`             // 待结算总金额=待结算可用余额+待结算风控冻结余额+待结算业务冻结余额
		UnsettledUsableBalance     string `json:"unsettled_usable_balance"`      // 待结算可用余额
		UnsettledRiskFreezeBalance string `json:"unsettled_risk_freeze_balance"` // 待结算风控冻结余额
		UnsettledBusFreezeBalance  string `json:"unsettled_bus_freeze_balance"`  // 待结算业务冻结余额
	} `json:"sumpay_merchant_query_balance_response"` // 商户余额查询响应数据
}

// NewMerchantQueryAcountBalanceRequest 创建商户余额查询请求对象
func NewMerchantQueryAcountBalanceRequest(merchant config.Merchant) *MerchantQueryAcountBalanceRequest {
	return &MerchantQueryAcountBalanceRequest{
		BaseRequest:  NewBaseRequest("fosun.sumpay.api.merchant.query.account.balance", merchant.AppId, merchant.MerNo),
		MerNo:        merchant.MerNo,
		MerchantConf: merchant,
	}
}
