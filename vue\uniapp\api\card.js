import request from '@/utils/request';
/** 
 * 银行卡
 */

// 银行卡列表
export default {
	getCardList: (data) => request({
		url: '/bankcard/bankcardcontroller/getBankCardList',
		method: 'GET',
		data,
		custom: {
			showLoading: true
		},
	}),
	postBindBankCardSms: (data) => request({
		url: '/bankcard/bankcardcontroller/postBindBankCardSms',
		method: 'POST',
		data,
		custom: {
			showLoading: true
		},
	}),
	postBindBankCard: (data) => request({
		url: '/bankcard/bankcardcontroller/postBindBankCard',
		method: 'POST',
		data,
		custom: {
			showLoading: true
		},
	}),
}