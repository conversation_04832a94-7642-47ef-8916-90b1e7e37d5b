<template>
	<!-- 页面最外层容器，设置蓝白渐变背景 -->
	<div class="loan-page" style="position: relative;">
		<!-- 顶部信息区域 -->
		<div class="top-info">
			<p class="phone">{{ getShowMobile() }}</p>
			<template v-if="repay.isData && repay.pending_loan">
				<p class="repay-tip" :class="repay.days_to_next_due<0?'yyq':''">{{ repay.days_to_next_due>=0?`距离还款还剩${repay.days_to_next_due}天 请记得按时还款`:`您已逾期${Math.abs(repay.days_to_next_due)}天 按时还款可提高额度哦~` }}</p>
			</template>
			<template v-else>
				<p class="repay-tip">暂时无需还款</p>
			</template>
		</div>
		<div class="service-btn" @click="handleService">
			<image src="/static/icon/kf2.png" class="btn-icon" mode="widthFix" />
			<text>客服</text>
		</div>
		
		<div class="card-container audit-status-dfk" v-if="repay.pending_loan && !repay.isData">
			<view class="sudit-status">待放款</view>
			<view class="audit-pass-hint">
				如需加急请联系客服
			</view>
			<p class="rate-desc">年化利率低至{{ (dayRate/100*365).toFixed(2) }}%起（以审核通过为准）</p>
		</div>
		<template v-else-if="!repay.isData">
			<!-- 中间卡片容器 -->
			<div class="card-container" v-if="riskResult == -1 || riskResult == 0">
				<h3 class="card-title">{{ riskResult == 0?'可用额度(元)':'可借额度(元)' }}</h3>
				<p class="amount">{{ riskResult == 0?availableCreditLimit:amountLimit}}</p>
				<p class="rate-desc">年化利率低至{{ (dayRate/100*365).toFixed(2) }}%起（以审核通过为准）</p>
				<button class="get-money-btn" @click="handleGetMoney">马上拿钱</button>
			</div>
			<div class="card-container audit-pass" v-else-if="riskResult == 1">
				<view class="audit-pass-hint">
					正在审核中
				</view>
				<p class="rate-desc">年化利率低至{{ (dayRate/100*365).toFixed(2) }}%起（以审核通过为准）</p>
			</div>
			<div class="card-container audit-pass" v-else>
				<view class="audit-pass-hint">
					很遗憾审核未通过
				</view>
				<p class="rate-desc">年化利率低至{{ (dayRate/100*365).toFixed(2) }}%起（以审核通过为准）</p>
			</div>
		</template>
		
		
		<!-- 还款 -->
		<view class="hk-container" v-else>
			<view class="hk-box">
				<view class="hk-box-title">
					全部应还（元）
				</view>
				<view class="hk-box-money">
					{{ repay.loan_total_amount }}
				</view>
				<view class="hk-box-hint">
					还款日6:00起将从已绑定银行卡，余额自动扣款
				</view>
			</view>
			<view class="hk-list">
				<view class="hk-list-name">
					还款计划
				</view>
				<view class="hk-list-item" v-for="item in repay.repayment_schedule" :key="item.bill_id">
					<view class="hk-item-name">
						{{ item.loan_date }} 借{{ item.order_loan_amount }}元 {{ item.current_period }}/{{ item.total_periods }}期
					</view>
					<view class="hk-item-box">
						<view class="hk-item-desc">
							<view class="hk-item-status">
								{{ item.bill_status == 0?'待还款':'还款中' }}
							</view>
							<view class="hk-item-money">
								{{ item.pending_amount }}
							</view>
							<view class="hk-item-endtime">
								最后还款日期：{{ item.due_date }}
							</view>
						</view>
						<view class="hk-item-btn" @click="handleToHk(item)">
							立即还款
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 在线客服底部弹框 -->
		<div class="service-modal" v-if="showServiceModal" @click="closeServiceModal">
			<div class="modal-content" @click.stop>
				<div class="modal-header">
					<text class="modal-title" @click="handleCall()">电话客服: ************</text>
					<div class="modal-close" @click="closeServiceModal">
						取消
					</div>
				</div>
			</div>
		</div>
		
		<!-- 选择银行卡还款弹窗 -->
		<uni-popup ref="hkPopup" type="bottom">
			<view class="hk-popup">
				<view class="hk-popup-title">
					订单
				</view>
				<view class="hk-popup-money">
					<text class="hk-popup-symbol">￥</text><text class="hk-popup-price">{{ setItem.pending_amount }}</text>
				</view>
				<view class="hk-popup-name">
					<image src="/static/image/yhkicon.png" mode="widthFix"></image><text>直接支付</text>
				</view>
				
				<view class="hk-popup-card">
					<view class="hk-popup-card-sel" v-if="cardOpt.setCard && cardOpt.setCard.id">
						<text>{{ cardOpt.setCard.bank_name }}{{ cardOpt.setCard.bank_card_no_end }}</text>
						<image src="/static/image/yxz.png" mode="widthFix"></image>
					</view>
					<view class="hk-popup-card-other" @click="handleSelOtherCard()">
						<text>更换其他银行卡</text>
						<image src="/static/image/more.png" mode="widthFix"></image>
					</view>
				</view>
				
				<view class="hk-popup-btn" @click="handleConfirmPay">
					确认交易
				</view>
				<!-- 协议勾选 -->
				<view class="agreement">
					<checkbox-group @change="handleAgreementChange">
						<checkbox color="#258ceb" :checked="agreementChecked" style="transform: scale(0.5)" />
					</checkbox-group>
					<text class="agreement-text">我已阅读并同意<text @click="handleToBook" style="color: blue;">《小额免密扣款协议》</text></text>
				</view>
				
			</view>
		</uni-popup>
		
		<!-- 选择其他银行卡弹窗 -->
		<uni-popup ref="otherCardPopup" type="bottom">
			<view class="other-card-popup">
				<view class="other-card-title">
					<uni-icons class="other-card-title-icon" @click="handleCloseCardPopup" color="#666" type="back" size="20"></uni-icons>
					<text>请选择付款方式</text>
				</view>
				<view class="other-card-list">
					<view class="other-card-item" v-for="(item,index) in cardOpt.list" :key="item.id" @click="handleSetHkCard(item)">
						<view class="other-card-item-info">
							<view class="other-card-item-name">
								{{ item.bank_name }}{{ item.bank_card_no_end }}
							</view>
							<view class="other-card-item-hint">
								{{ item.bank_phone_end }}
							</view>
						</view>
						<uni-icons color="#1a6eff" v-if="cardOpt.setCard.id == item.id" type="checkbox-filled" size="30"></uni-icons>
						<uni-icons color="#ccc" type="checkbox" v-else size="30"></uni-icons>
					</view>
				</view>
				<view class="other-card-add" @click="handleToAddCard">
					<uni-icons type="plusempty" color="#555" size="20"></uni-icons><text>添加银行卡</text>
				</view>
			</view>
		</uni-popup>
		
	</div>
</template>

<script setup>
	import {
		onShow,
		onLoad
	} from '@dcloudio/uni-app';
	import {
		reactive,
		ref
	} from 'vue';
	import { getShowMobile as getShowMobileHelper } from '@/utils/helper';
	import userApi from '@/api/user.js';
	import user from '@/store/user.js';
	import { storeToRefs } from 'pinia';
	const userStore = user();
	import cardApi from '@/api/card.js';
	// userInfo -- 用户信息
	// riskResult -- 是否审核通过 0 通过 1 审核 2 拒绝 3 调用风控模型失败
	// amountLimit 额度
	const { userInfo, riskResult, amountLimit, availableCreditLimit, dayRate, cid } = storeToRefs(userStore);
	
	const showServiceModal = ref(false);
	
	const hkPopup = ref();
	const otherCardPopup = ref();
	const agreementChecked = ref(false);
	
	const cardOpt = reactive({
		list: [], // 银行卡列表
		setCard: {}, // 已选银行卡 / 默认第一个
	})
	
	// 还款计划
	const repay = reactive({
		isData: false,
		days_to_next_due: 0,
		loan_total_amount: 0,
		repayment_schedule: [],
		pending_loan: false, // 待放款
	})
	onShow(async () => {
		
		checkLogin();
		getCardList();
		let uid = userInfo.value?.uid;
		if (!uid) {
			await userStore.getInfo();
			uid = userInfo.value?.uid;
		}
		if(userInfo.value.identityStatus && userInfo.value.identityStatus == 2) { // 实名认证审核通过后再获取
			// 更新贷款是否审核通过状态
			await userStore.getEvaluate(uid);
			// 获取授信额度&贷款产品
			await userStore.getProducts(uid);
			// 获取还款计划
			const orderBills = await userApi.getOrderBills({});
			if(orderBills.code == 0) {
				repay.days_to_next_due = orderBills.data.days_to_next_due || 0;
				repay.loan_total_amount = orderBills.data.loan_total_amount || 0;
				repay.repayment_schedule = orderBills.data.repayment_schedule || [];
				repay.pending_loan = orderBills.data.pending_loan;
				if(orderBills.data.loan_total_amount && orderBills.data.repayment_schedule.length > 0) {
					repay.isData = true;
				}else{
					repay.isData = false;
				}
			}
		}
		
		
	});
	
	
	
	// 检查登录状态
	function checkLogin() {
		if (!userStore.isLogin) {
			uni.showToast({
				title: '请先登录',
				icon: 'none'
			});
			setTimeout(() => {
				uni.navigateTo({
					url: '/pages/login/login'
				});
			}, 1500);
			return false;
		}
		return true;
	}

	// 获取显示的手机号 - 使用统一工具函数
	function getShowMobile() {
		return getShowMobileHelper(userStore);
	}

	// 客服按钮点击
	function handleService() {
		if (!checkLogin()) return;
		showServiceModal.value = true;
	}
	function handleCall() {
		uni.makePhoneCall({
			phoneNumber: '************'
		})
	}
	function closeServiceModal() {
		showServiceModal.value = false;
	}

	// 马上拿钱按钮点击
	function handleGetMoney() {
		if (!checkLogin()) return;
		
		userStore.nextCallBack().then(res => {
			uni.navigateTo({
				url: "/pages/BorrowMoney/BorrowMoney",
			});
		}).catch(err => {
			if(err.url) {
				uni.navigateTo({
					url: err.url
				});
			}
			if(err.message) {
				uni.showToast({
					title: err.message,
					icon: "none"
				})
			}
		});
	}
	// 协议勾选状态变更
	function handleAgreementChange(e) {
		agreementChecked.value = e.detail.value.length > 0;
	}
	
	const cardList = ref([]);
	async function getCardList() {
		const cardData = await cardApi.getCardList();
		cardList.value = cardData.data.list.filter(item => item.card_status == 1);
		cardList.value.forEach(item => {
			let bank_card_no = item.bank_card_no;
			let bank_phone = item.bank_phone;
			item.bank_card_no_end = bank_card_no.slice(bank_card_no.length - 4, bank_card_no.length);
			item.bank_card_no_end_txt =
				`(尾号${bank_card_no.slice(bank_card_no.length-4,bank_card_no.length)})`;
			item.bank_phone_end = `手机尾号${bank_phone.slice(bank_phone.length-4,bank_phone.length)}`
		})
		cardOpt.list = cardList.value;
	}
	// 当前选择的待还
	const setItem = ref();
	const handleToHk = async (item)=> {
		if(item.bill_status == 1) {
			return uni.showToast({
				title: "还款中,请稍后",
				icon: "none"
			})
		}
		setItem.value = item;
		// otherCardPopup.value.open()
		if(cardList.value.length>0 && Object.keys(cardOpt.setCard).length === 0) {
			cardOpt.setCard = cardList.value[0];
		}
		hkPopup.value.open()
	}
	
	function handleToBook() {
		uni.navigateTo({
			url: "/pages/withoutCodeGgreement/withoutCodeGgreement"
		})
	}
	function handleToAddCard() {
		uni.navigateTo({
			url: "/pages/CollectionCard/CollectionCard"
		})
	}
	
	function handleSelOtherCard() {
		otherCardPopup.value.open();
	}
	function handleSetHkCard(item) {
		cardOpt.setCard = item;
		otherCardPopup.value.close();
	}
	function handleCloseCardPopup() {
		otherCardPopup.value.close();
	}
	
	function handleConfirmPay() {
		if(!agreementChecked.value) {
			return uni.showToast({
				title: "请阅读并同意《小额免密扣款协议》",
				icon: "none"
			})
		}
		if(!cardOpt.setCard.id) {
			return uni.showToast({
				title: "请选择支付银行卡",
				icon: "none"
			})
		}
		// console.log(setItem.value)
		// console.log(cardOpt.setCard)
		userApi.createRepayment({
			bill_id: setItem.value.bill_id,
			bank_card_id: cardOpt.setCard.id
		}).then(res => {
			console.log(res)
			if(res.code == 0) {
				
				hkPopup.value.close();
				let url = "";
				let g_transaction_no = res.data.guarantee_payment && res.data.guarantee_payment.transaction_no;
				let a_transaction_no = res.data.asset_payment && res.data.asset_payment.transaction_no;
				if (a_transaction_no && !g_transaction_no) {
					url = "/pages/repayCountDown/repayCountDown?a="+a_transaction_no
				}else if(g_transaction_no && !a_transaction_no) {
					url = "/pages/repayCountDown/repayCountDown?g="+g_transaction_no
				}else if(g_transaction_no && a_transaction_no) {
					url = "/pages/repayCountDown/repayCountDown?a="+a_transaction_no+"&g="+g_transaction_no
				}
				// 轮询去查是否成功扣款
				uni.navigateTo({
					url: url
				})
			}
		})
	}
	
	
</script>

<style scoped lang="scss">
	
	/* 页面整体样式，设置蓝白垂直渐变背景 */
	.loan-page {
		width: 100%;
		min-height: 100vh;
		/* 蓝白渐变，可根据实际设计微调色值 */
		background: linear-gradient(to bottom, #1a6eff 20%, #eff2f7 45%);
		padding-top: 120px;
		box-sizing: border-box;
	}

	/* 顶部信息样式 */
	.top-info {
		padding: 0 20px;
		color: #fff;
	}

	.phone {
		font-size: 18px;
		margin-bottom: 8px;
	}

	.repay-tip {
		font-size: 18px;
		&.yyq{
			color: #f00;
		}
	}

	/* 卡片容器样式，模拟圆角、白背景、阴影 */
	.card-container {
		margin: 30rpx 30rpx 0;
		background-color: #fff;
		border-radius: 8px;
		padding: 20px;
		box-sizing: border-box;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		text-align: center;
	}

	.card-title {
		font-size: 16px;
		color: #333;
		margin-bottom: 12px;
	}

	.amount {
		font-size: 24px;
		color: #333;
		font-weight: bold;
		margin-bottom: 8px;
	}

	.rate-desc {
		font-size: 12px;
		color: #999;
		margin-bottom: 16px;
	}

	/* 按钮样式 */
	.get-money-btn {
		width: 150px;
		border-radius: 30px;
		background: linear-gradient(to bottom, #1a6eff 20%, #4781e3 45%);
		color: #fff;
		padding: 10rpx;
		border: none;
		font-size: 14px;
		cursor: pointer;
		box-shadow: 0 2px 10px #ccc;
	}

	.get-money-btn:active {
		opacity: 0.8;
	}

	/* 客服弹框样式 */
	.service-modal {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		top: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: flex-end;
		align-items: flex-end;
		z-index: 999;
	}
	
	.modal-content {
		width: 100%;
		height: 110px;
		background-color: #fff;
		border-top-left-radius: 8px;
		border-top-right-radius: 8px;
		overflow: hidden;
		position: relative;
		box-shadow: none;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 24px;
	}
	
	.modal-header {
		width: 100%;
		text-align: center;
		padding: 0;
		border-bottom: none;
	}
	
	.modal-title {
		display: block;
		font-size: 16px;
		font-weight: normal;
		color: #333;
		margin-bottom: 24px;
		margin-top: 0;
		line-height: 1.5;
	}
	
	.modal-close {
		margin-top: 10px;
		font-size: 14px;
		color: #bbb;
		cursor: pointer;
		text-align: center;
		font-weight: normal;
	}
	.service-btn{
		position: absolute;
		right: 60rpx;
		top: 120rpx;
		z-index: 1;
		color: #fff;
		display: flex;
		flex-direction: column;
		text-align: center;
		align-items: center;
		justify-content: center;
		image{
			width: 40rpx;
			height: auto;
		}
		text{
			font-size: 22rpx;
		}
	}
	.audit-pass{
		padding: 100rpx 0 80rpx;
		.audit-pass-hint{
			font-size: 40rpx;
			margin-bottom: 20rpx;
		}
	}
	.audit-status-dfk{
		
		.sudit-status{
			color: #333;
			font-size: 26rpx;
			font-weight: bold;
			margin-bottom: 20rpx;
		}
		.audit-pass-hint{
			font-size: 40rpx;
			margin-bottom: 20rpx;
		}
	}
	
	.hk-container{
		padding: 30rpx;
		.hk-box{
			background-color: #fff;
			border-radius: 8px;
			padding: 20px;
			box-sizing: border-box;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
			text-align: center;
			.hk-box-title{
				color: #666;
				font-size: 26rpx;
			}
			.hk-box-money{
				font-size: 50rpx;
				font-weight: bold;
				margin: 25rpx;
			}
			.hk-box-hint{
				background-color: #eee;
				font-size: 24rpx;
				padding: 15rpx;
				border-radius: 50rpx;
				color: #888;
			}
		}
		
		.hk-list{
			padding-bottom: 100rpx;
			.hk-list-name{
				color: #222;
				font-size: 30rpx;
				padding: 40rpx 0 30rpx;
			}
			.hk-list-item{
				background-color: #fff;
				border-radius: 8px;
				padding: 0 30rpx;
				box-sizing: border-box;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
				margin-bottom: 20rpx;
				font-size: 26rpx;
				color: #444;
				.hk-item-name{
					padding-top: 20rpx;
				}
				.hk-item-box{
					padding: 20rpx 0;
					display: flex;
					align-items: center;
					.hk-item-desc{
						flex: 1;
						.hk-item-money{
							font-size: 36rpx;
							font-weight: bold;
							margin: 15rpx 0;
						}
						.hk-item-endtime{
							font-size: 22rpx;
							color: #888;
						}
					}
					.hk-item-btn{
						width: 180rpx;
						flex-shrink: 0;
						height: 80rpx;
						line-height: 80rpx;
						background: linear-gradient(to bottom, #1a6eff 20%, #4781e3 45%);
						color: #fff;
						border: none;
						border-radius: 22px;
						font-size: 13px;
						cursor: pointer;
						margin-right: 0;
						margin-top: 0;
						padding: 0;
						box-sizing: border-box;
						text-align: center;
						white-space: nowrap;
					}
				}
			}
		}
	}
	
	.hk-popup{
		background-color: #fff;
		padding-bottom: 140rpx;
		height: 55vh;
		box-sizing: border-box;
		image{
			width: 30rpx;
		}
		.hk-popup-title{
			padding: 25rpx;
			text-align: center;
			color: #333;
		}
		.hk-popup-money{
			padding: 15rpx 0 30rpx;
			text-align: center;
			color: #444;
			.hk-popup-symbol{
				font-size: 28rpx;
			}
			.hk-popup-price{
				font-size: 50rpx;
				font-weight: bold;
			}
		}
		.hk-popup-name{
			padding: 0 25rpx;
			display: flex;
			align-items: center;
			image{
				width: 35rpx;
			}
			text{
				margin-left: 15rpx;
				color: #666;
				font-size: 26rpx;
			}
		}
		.hk-popup-card{
			margin: 0 20rpx;
			padding: 0 20rpx;
			background-color: #eee;
			border-radius: 15rpx;
			margin-top: 20rpx;
			.hk-popup-card-sel{
				padding: 30rpx 0;
				border-bottom: 1px solid #ddd;
				display: flex;
				align-items: center;
				text{
					flex: 1;
					color: #333;
				}
			}
			.hk-popup-card-other{
				padding: 30rpx 0;
				display: flex;
				align-items: center;
				text{
					flex: 1;
					color: #666;
					font-size: 24rpx;
				}
			}
		}
		
		.hk-popup-btn{
			margin: 20rpx;
			border-radius: 30px;
			background: linear-gradient(to bottom, #1a6eff 20%, #4781e3 45%);
			color: #fff;
			padding: 25rpx 50rpx;
			border: none;
			font-size: 30rpx;
			cursor: pointer;
			text-align: center;
		}
	}
	.agreement {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #666;
		white-space: nowrap;
		justify-content: center;
		.agreement-text {
			line-height: 1.6;
			white-space: nowrap;
		}
	}
	
	.other-card-popup{
		height: 55vh;
		background-color: #fff;
		.other-card-title{
			padding: 25rpx;
			text-align: center;
			position: relative;
			color: #333;
			.other-card-title-icon{
				position: absolute;
				left: 25rpx;
				top: 25rpx;
			}
		}
		
		.other-card-list{
			padding: 0 25rpx;
			max-height: 50%;
			overflow-y: auto;
			.other-card-item{
				padding: 25rpx 0;
				border-bottom: 1px solid #efefef;
				display: flex;
				align-items: center;
				&:last-child{
					border-bottom: 0;
				}
				.other-card-item-info{
					flex: 1;
					.other-card-item-name{
						color: #333;
						font-size: 32rpx;
					}
					.other-card-item-hint{
						color: #666;
						font-size: 28rpx;
						margin-top: 15rpx;
					}
				}
				
			}
		}
		.other-card-add{
			display: flex;
			align-items: center;
			padding: 30rpx 25rpx;
			border-top: 1px solid #efefef;
			border-bottom: 1px solid #efefef;
			text{
				color: #444;
				margin-left: 15rpx;
			}
		}
	}
</style>