import request from '@/utils/request';
import md5 from 'js-md5';
import {
	baseUrl,
	apiPath,
	apiModel
} from '@/utils/config';
import user from '@/store/user.js';
export default {
	// 文件上传（允许重复上传）
	uploadFile: (file) => {
		return new Promise((resolve, reject) => {
			const token = uni.getStorageSync('token');
			const timestamp = Date.parse(new Date().toString()) / 1000;
			const passstr = md5(import.meta.env.GF_API_SECRET + timestamp);
			let uploadUrl = baseUrl + apiModel + '/common/uploadfile/onefile';
			// let uploadUrl = apiModel + '/common/uploadfile/onefile';
			if (typeof file === 'string') {
				uploadUrl += '?_t=' + Date.now() + Math.random();
			} else if (file && file.path) {
				uploadUrl += '?_t=' + Date.now() + Math.random();
			}
			uni.uploadFile({
				url: uploadUrl,
				filePath: file.path || file,
				name: 'file',
				header: {
					'Authorization': token,
					'Businessid': import.meta.env.GF_BUSINESUSSID,
					// #ifndef APP
					'apiverify': window.btoa(passstr + "#" + timestamp)
					// #endif
					// #ifdef APP
					'apiverify': uni.arrayBufferToBase64(passstr + "#" + timestamp)
					// #endif
				},
				success: (uploadFileRes) => {
					try {
						const data = JSON.parse(uploadFileRes.data);
						
						// 检查token超时
						if (data.code === 401 || data.code === 'TOKEN_INVALID' || data.code === 'TOKEN_EXPIRED') {
							// 处理token超时，直接跳转登录页
							const userStore = user();
							userStore.logout(true);
							uni.removeStorageSync('mobile');
							uni.reLaunch({
								url: '/pages/login/login'
							});
							reject(new Error('token超时'));
							return;
						}
						
						// 仅校验基本成功条件（原逻辑更严格）
						if (data.status === 'done') {
							resolve({
								code: 0,
								data: data,
								message: '上传成功'
							});
						} 
						else {
							// 移除文件重复的判断逻辑
							resolve({ // 修改为成功处理
								code: 0,
								data: data,
								message: data.response || '文件已存在'
							});
						}
					} catch (error) {
						reject(new Error('解析响应数据失败'));
					}
				},
				fail: (error) => {
					// 检查是否是401错误
					if (error.statusCode === 401) {
						// 处理token超时，直接跳转登录页
						const userStore = user();
						userStore.logout(true);
						uni.removeStorageSync('mobile');
						uni.reLaunch({
							url: '/pages/login/login'
						});
						reject(new Error('token超时'));
						return;
					}
					reject(error);
				}
			});
		});
	},
	
	// 新增人脸活体检验接口（保持不变）
	postFaceAuth(params) {
		return request({
			url: '/identity/postFaceAuth',
			method: 'POST',
			data: params,
			custom: {
				auth: true,
			},
		});
	}
}