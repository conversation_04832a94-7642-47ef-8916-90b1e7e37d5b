# fincore配置管理工具

## 概述

Fincore安全配置管理工具，提供配置文件的加密存储、OSS 云端管理和安全传输功能。主要是用于生产环境的配置管理工作，开发环境的配置保持原有使用方式不变。生产环境整个项目配置不会落地，按需从OSS中下载解密后使用。

### 配置加密上传
1、配置有2重加密，
* 首先加密配置文件中指定的配置项，比如密码，在对应的配置项前添加ENC:前缀，比如：
```
password: ENC:123456
```
* 然后对整个配置文件进行AES-256-GCM加密，加密后上传到OSS中
2、OSS凭证配置文件保存在resource/fincore.yml，这个配置也被加密，默认带到项目中
 * 对应OSS的bucket为fincore-config-new, 注意，为了确保安全性，这个bucket权限配置仅我们生产VPC可以访问，因此上传配置时，最好是在生产环境操作；测试时，可以放开临时放开权限；

### 配置下载
1、从OSS下载配置文件
2、解密配置文件
3、保存到本地
主要是用于测试验证，查看解密后的配置是否正确


## OSS 凭证配置

在使用工具前，需要准备 OSS 凭证文件 `fincore.yml`：

```yaml
# OSS 访问端点
endpoint: oss-cn-hangzhou.aliyuncs.com
# 访问密钥 ID
access_key_id: your-access-key-id
# 访问密钥 Secret
access_key_secret: your-access-key-secret
# 存储桶名称
bucket_name: your-bucket-name
# 加密密钥（32字符）
encryption_key: your-32-character-encryption-key
```

### 环境变量

可以通过环境变量指定 OSS 凭证文件路径：

```bash
# Windows PowerShell
$env:OSS_CREDENTIALS_PATH = "./resource/fincore.yml"

# Linux/macOS
export OSS_CREDENTIALS_PATH="./resource/fincore.yml"
```

如果不设置，默认使用 `./resource/fincore.yml`

## 使用方法

### 基本语法

```bash
secure-config-tool -action=<操作> [选项]
```

### 命令行参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `-action` | 操作类型（必需） | `upload`, `download`, `validate` 等 |
| `-local` | 本地配置文件路径 | `config.yml` |
| `-oss` | OSS 配置文件路径 | `config/prod/config.yml` |
| `-backup` | 备份文件路径 | `backup.yml` |
| `-prefix` | OSS 文件前缀 | `config/` |
| `-help` | 显示帮助信息 | - |

## 操作详解

### 1. 上传配置到 OSS

将本地配置文件加密后上传到 OSS：

```bash
# 上传生产环境配置
secure-config-tool -action=upload -local=config.yml -oss=config/prod/config.yml

# 上传开发环境配置
secure-config-tool -action=upload -local=config_dev.yml -oss=config/dev/config.yml

# 上传测试环境配置
secure-config-tool -action=upload -local=config_test.yml -oss=config/test/config.yml
```

**功能说明：**
- 自动识别并加密敏感字段（密码、密钥等）
- 验证配置文件完整性
- 加密后上传到指定的 OSS 路径

### 2. 从 OSS 下载配置

从 OSS 下载配置文件并自动解密：

```bash
# 下载生产环境配置
secure-config-tool -action=download -oss=config/prod/config.yml -local=config.yml

# 下载到指定目录
secure-config-tool -action=download -oss=config/dev/config.yml -local=./configs/config_dev.yml
```

**功能说明：**
- 从 OSS 下载加密的配置文件
- 自动解密所有敏感字段
- 保存为可直接使用的配置文件

### 3. 验证 OSS 配置

验证 OSS 中配置文件的完整性：

```bash
# 验证生产环境配置
secure-config-tool -action=validate -oss=config/prod/config.yml

# 验证开发环境配置
secure-config-tool -action=validate -oss=config/dev/config.yml
```

**验证内容：**
- 配置文件是否存在
- 文件格式是否正确
- 必要字段是否完整
- 敏感字段是否正确加密

### 4. 加密 OSS 凭证文件

对 OSS 凭证文件进行硬编码加密：

```bash
# 加密凭证文件
secure-config-tool -action=encrypt-oss-config -local=fincore.yml -oss=fincore.yml.encrypted

# 直接覆盖原文件
secure-config-tool -action=encrypt-oss-config -local=fincore.yml
```

**安全特性：**
- 使用内置硬编码密钥加密
- 防止凭证信息泄露
- 支持自动解密使用

## 使用场景

### 开发环境配置管理

```bash
# 1. 加密指定字段
修改项目配置文件，通过前缀ENC:指定敏感字段加密，比如：
password: ENC:123456

# 2. 上传到 OSS
secure-config-tool -action=upload -local=config.yml -oss=config/proc/config.yml
```

### 生产环境部署

```bash
# 1. 验证生产配置，确认配置是否正常
secure-config-tool -action=download -oss=config/prod/config.yml -local=config.yml

# 2. 如果是使用deploy.sh脚本部署，可以忽略这一步
是否启用oss配置，使用的是环境变量来区分：USE_OSS_CONFIG

# 3. 确认配置是否正常
服务启动后，检测config.yml配置文件已经被清理
```

## 安全特性

### 多层加密保护
- **传输加密**：使用 HTTPS 协议传输
- **存储加密**：OSS 服务端加密存储
- **字段加密**：敏感字段单独加密
- **凭证加密**：OSS 凭证文件硬编码加密

### 访问控制
- **凭证隔离**：OSS 凭证与配置文件分离存储
- **权限最小化**：仅授予必要的 OSS 访问权限，仅vpc内访问

### 审计追踪
- **操作日志**：记录所有配置操作
- **时间戳**：备份文件包含时间信息
- **版本管理**：支持配置文件版本控制
