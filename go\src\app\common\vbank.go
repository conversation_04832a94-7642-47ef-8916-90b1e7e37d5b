package common

import (
	"encoding/json"
	"errors"
	"fincore/global"
	"io"
	"net/http"
	"strings"
)

type VbankRequest struct {
	ApiKey     string `json:"apikey"`
	BankNumber string `json:"bank_number"`
	Name       string `json:"name"`
	Mobile     string `json:"mobile"`
	IdCard     string `json:"id_card"`
}

func ValidBank(Name, BankNumber, Mobile, IdCard string) error {

	// 接口请求入参配置

	apiKey := global.App.Config.Vbank.Apikey
	apiurl := global.App.Config.Vbank.Apiurl
	// fmt.Println("apiKey=", apiKey, "apiurl=", apiurl)
	reqData := VbankRequest{
		ApiKey:     apiKey,
		BankNumber: BankNumber,
		Name:       Name,
		Mobile:     Mobile,
		IdCard:     IdCard,
	}
	reqBody, err := json.Marshal(reqData)
	if err != nil {
		return err
	}
	// 创建请求
	req, err := http.NewRequest("POST", apiurl, strings.NewReader(string(reqBody)))
	if err != nil {
		return err
	}
	defer req.Body.Close()
	// 发送HTTP请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	// 解析响应
	var result map[string]interface{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		return err
	}
	if result["result"] == "0" {
		return nil
	} else {
		msg, _ := result["desc"].(string)
		return errors.New(msg)
	}

}
