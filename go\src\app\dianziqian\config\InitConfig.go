package config

// 此处为官方sdk，能不能就不改。
//通过单例模式，生成一个通用的initConfig对象

type InitConfig struct {
	host string
	appId string
	privateKey string
}




var initConfig *InitConfig = new(InitConfig)

 func GetInitConfig() *InitConfig {
	return initConfig
}

func (e *InitConfig) Host() string {
	return e.host
}

func (e *InitConfig) SetHost(host string)  {
	e.host = host
}

func (e *InitConfig) AppId() string  {
	return e.appId
}

func (e *InitConfig) SetAppId(appId string)  {
	e.appId = appId
}

func (e *InitConfig) PrivateKey() string  {
	return e.privateKey
}

func (e *InitConfig) SetPrivateKey(privateKey string)  {
	e.privateKey = privateKey
}
