---
description: go/*
alwaysApply: false
---
# 应用模块结构

FinCore 应用采用模块化设计，所有业务模块都位于 [app/](mdc:src/app) 目录下，按功能进行划分。

## 主要模块

- [admin/](mdc:src/app/admin) - 后台管理模块
  - 提供管理员相关功能和后台管理接口
  - 包含管理员账户、权限、系统设置等功能

- [business/](mdc:src/app/business) - 业务端模块
  - 处理业务流程和业务逻辑

- [dianziqian/](mdc:src/app/dianziqian) - 电子签名模块
  - 处理电子签名相关功能
  - 提供文档签署、验证等服务

- [fengkong/](mdc:src/app/fengkong) - 风控模块
  - 处理风险控制相关功能
  - 提供风险评估、监控等服务

- [uniapp/](mdc:src/app/uniapp) - UniApp 应用模块
  - 提供移动端应用接口服务

- [common/](mdc:src/app/common) - 公共功能模块
  - 提供跨模块共享的功能
  - 包含通用的控制器、服务和助手函数

## 模块开发

每个模块通常包含以下组件：

1. **Controller** - 处理 HTTP 请求和响应
2. **Service** - 处理业务逻辑
3. **Model** - 数据存取和模型定义
4. **Validation** - 请求验证逻辑

开发新功能时，应在相应模块中添加对应的组件，遵循模块化和职责分离原则。

## 应用控制器

[app/controller.go](mdc:src/app/controller.go) 定义了基本的控制器结构，为所有模块的控制器提供基础功能。
