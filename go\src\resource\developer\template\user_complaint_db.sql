-- ----------------------------
-- Table structure for user_complaint
-- ----------------------------
DROP TABLE IF EXISTS `user_complaint`;
CREATE TABLE `user_complaint` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '姓名',
  `mobile` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号码',
  `idCard` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '身份证号',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '投诉内容',
  `complaintTime` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '投诉时间',
  `photoUrls` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '投诉照片保存路径，多个用逗号分隔',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `feedback` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '反馈内容',
  `updatetime` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户投诉表' ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- 初始化页面菜单
-- ----------------------------
INSERT INTO `fincore`.`business_auth_rule` (`id`, `uid`, `title`, `locale`, `orderNo`, `type`, `pid`, `icon`, `routePath`, `routeName`, `component`, `redirect`, `permission`, `status`, `isExt`, `keepalive`, `requiresAuth`, `hideInMenu`, `hideChildrenInMenu`, `activeMenu`, `noAffix`, `createtime`) VALUES (437, 1, '建议反馈管理', '', 437, 0, 0, 'icon-phone', '/suggestions', 'suggestions', 'LAYOUT', 'suggestions/complaint', '', 0, 0, 0, 1, 0, 0, 0, 0, 1750214853);
INSERT INTO `fincore`.`business_auth_rule` (`id`, `uid`, `title`, `locale`, `orderNo`, `type`, `pid`, `icon`, `routePath`, `routeName`, `component`, `redirect`, `permission`, `status`, `isExt`, `keepalive`, `requiresAuth`, `hideInMenu`, `hideChildrenInMenu`, `activeMenu`, `noAffix`, `createtime`) VALUES (438, 1, '投诉管理', '', 438, 1, 437, 'icon-bug', 'complaint', 'complaint', 'suggestions/complaint/index', '', '', 0, 0, 0, 1, 0, 0, 0, 0, 1750214933);
