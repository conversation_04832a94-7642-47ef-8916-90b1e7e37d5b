package log

import (
	"crypto/rand"
	"encoding/hex"
	"time"

	"github.com/gin-gonic/gin"
)

// RequestIDMiddleware 请求ID中间件
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 尝试从请求头中获取请求ID
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			// 如果没有，则生成新的请求ID
			requestID = generateRequestID()
		}

		// 设置到Gin上下文
		c.Set(string(RequestIDKey), requestID)

		// 设置到请求上下文
		ctx := SetRequestIDToContext(c.Request.Context(), requestID)
		c.Request = c.Request.WithContext(ctx)

		// 设置响应头
		c.Header("X-Request-ID", requestID)

		// 记录请求开始日志
		startTime := time.Now()
		logger := NewLogger("http").WithGinContext(c)
		logger.Info("请求开始",
			String("path", c.Request.URL.Path),
			String("query", c.Request.URL.RawQuery),
		)

		// 继续处理请求
		c.Next()

		// 记录请求结束日志
		duration := time.Since(startTime)
		status := c.Writer.Status()

		logLevel := "info"
		if status >= 400 && status < 500 {
			logLevel = "warn"
		} else if status >= 500 {
			logLevel = "error"
		}

		logger = logger.WithFields(
			StatusCode(status),
			ResponseTime(duration),
			Int("response_size", c.Writer.Size()),
		)

		switch logLevel {
		case "warn":
			logger.Warn("请求完成")
		case "error":
			logger.Error("请求完成")
		default:
			logger.Info("请求完成")
		}
	}
}

// LoggingMiddleware 通用日志中间件（简化版）
func LoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 确保有请求ID
		requestID := c.GetString(string(RequestIDKey))
		if requestID == "" {
			requestID = generateRequestID()
			c.Set(string(RequestIDKey), requestID)
			ctx := SetRequestIDToContext(c.Request.Context(), requestID)
			c.Request = c.Request.WithContext(ctx)
			c.Header("X-Request-ID", requestID)
		}

		c.Next()
	}
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	// 使用时间戳 + 随机数生成唯一ID
	timestamp := time.Now().UnixNano()

	// 生成4字节随机数
	randomBytes := make([]byte, 4)
	rand.Read(randomBytes)

	// 组合时间戳和随机数
	return hex.EncodeToString(append(
		[]byte{
			byte(timestamp >> 56),
			byte(timestamp >> 48),
			byte(timestamp >> 40),
			byte(timestamp >> 32),
			byte(timestamp >> 24),
			byte(timestamp >> 16),
			byte(timestamp >> 8),
			byte(timestamp),
		},
		randomBytes...,
	))
}

// GetRequestID 从Gin上下文中获取请求ID
func GetRequestID(c *gin.Context) string {
	if requestID, exists := c.Get(string(RequestIDKey)); exists {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}

// SetRequestID 设置请求ID到Gin上下文
func SetRequestID(c *gin.Context, requestID string) {
	c.Set(string(RequestIDKey), requestID)
	ctx := SetRequestIDToContext(c.Request.Context(), requestID)
	c.Request = c.Request.WithContext(ctx)
	c.Header("X-Request-ID", requestID)
}

// WithRequestContext 为日志添加请求上下文
func WithRequestContext(c *gin.Context) *Logger {
	return NewLogger("app").WithGinContext(c)
}

// AccessLogMiddleware 访问日志中间件
func AccessLogMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录开始时间
		startTime := time.Now()

		// 处理请求
		c.Next()

		// 计算处理时间
		duration := time.Since(startTime)

		// 记录访问日志
		logger := NewLogger("access").WithFields(
			RequestID(GetRequestID(c)),
			Method(c.Request.Method),
			URL(c.Request.URL.Path),
			String("query", c.Request.URL.RawQuery),
			IP(c.ClientIP()),
			UserAgent(c.GetHeader("User-Agent")),
			StatusCode(c.Writer.Status()),
			ResponseTime(duration),
			Int("request_size", int(c.Request.ContentLength)),
			Int("response_size", c.Writer.Size()),
		)

		// 根据状态码选择日志级别
		status := c.Writer.Status()
		if status >= 500 {
			logger.Error("HTTP请求")
		} else if status >= 400 {
			logger.Warn("HTTP请求")
		} else {
			logger.Info("HTTP请求")
		}
	}
}

// ErrorLogMiddleware 错误日志中间件
func ErrorLogMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 检查是否有错误
		if len(c.Errors) > 0 {
			logger := NewLogger("error").WithGinContext(c)

			for _, err := range c.Errors {
				logger.WithError(err.Err).Error("请求处理错误",
					Any("error_type", err.Type),
					Any("error_meta", err.Meta),
				)
			}
		}
	}
}
