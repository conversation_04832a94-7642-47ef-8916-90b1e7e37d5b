package captcha

import (
	"fincore/app/uniapp/captcha"
	"fincore/utils/gf"
	"reflect"

	"github.com/gin-gonic/gin"
)

// 验证码页面
/**
* 使用 Index 是省略路径中的index
* 本路径为： /admin/user/login -省去了index
 */
type Index struct {
	NoNeedLogin []string //忽略登录接口配置-忽略全部传[*]
	NoNeedAuths []string //忽略RBAC权限认证接口配置-忽略全部传[*]
}

func init() {
	fpath := Index{NoNeedLogin: []string{"GetCaptcha"}, NoNeedAuths: []string{"*"}}
	gf.Register(&fpath, reflect.TypeOf(fpath).PkgPath())
}

/**
* 1 获取图形验证码, 复用uniapp逻辑
 */
func (api *Index) GetCaptcha(c *gin.Context) {
	uniIndex := captcha.Index{}
	uniIndex.GetCaptcha(c)
}
