<template>
	<div class="deduction-notice">
		<p class="salutation">尊敬的客户：</p>
		<p class="content">
			根据我们公司的相关规定，我们发现您的账户上存在未按时缴纳的款项。为了维护公司的正常经营秩序，特向您发出此扣款通知函。具体扣款事项如下：
		</p>
		<ol class="items">
			<li>
				扣款原因:您的账户余额存在逾期未支付的费用，已影响公司财务正常运作。
			</li>
			<li>
				扣款金额:根据相关协议约定，将从您的账户中扣除您在本平台贷款应还所有费用。
			</li>
			<li>
				扣款方式:我们将通过协议约定的扣款方式进行操作，请确保账户资金充足以完成本次扣款。
			</li>
			<li>
				扣款时间:扣款将在您还款日当日凌晨六点执行，具体时间请留意您的账户变动通知消息
			</li>
		</ol>
		<p class="reminder">
			友情提醒:为了避免产生不必要的逾期费用及征信记录，请您在今后务必按时支付相关费用，并保持良好的资金流动性。
		</p>
		<p class="conclusion">特此通知。</p>
	</div>
</template>

<script setup>
	// 可在此处添加逻辑，比如从接口获取通知内容等，当前示例无额外逻辑
</script>

<style scoped>
	.deduction-notice {
		width: 90%;
		height: 100%;
		padding: 20px;
		background-color: #eff2f7;
		border-radius: 8px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		font-size: 14px;
		line-height: 1.8;
		color: #333;
	}

	.salutation {
		margin-bottom: 12px;
	}

	.content {
		text-indent: 2em;
		/* 首行缩进2个字符，模拟公文排版 */
		margin-bottom: 12px;
	}

	.items {
		margin-left: 20px;
		margin-bottom: 12px;
	}

	.reminder {
		text-indent: 2em;
		margin-bottom: 12px;
	}

	.conclusion {
		text-align: right;
		/* 特此通知右对齐 */
	}
</style>