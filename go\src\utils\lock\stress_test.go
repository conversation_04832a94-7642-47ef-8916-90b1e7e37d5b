package lock

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"testing"
	"time"

	"fincore/utils/log"
)

// TestStressLockCreation 压力测试：大量锁创建
func TestStressLockCreation(t *testing.T) {
	if testing.Short() {
		t.<PERSON><PERSON>("跳过压力测试")
	}

	manager := NewSimpleLockManager()
	const numLocks = 100000

	startTime := time.Now()

	// 创建大量锁
	for i := 0; i < numLocks; i++ {
		key := fmt.Sprintf("stress:creation:%d", i)
		lock := manager.GetLock(key, 1*time.Minute)
		lock.Lock()
		lock.Unlock()
	}

	duration := time.Since(startTime)

	t.Logf("压力测试 - 锁创建:")
	t.Logf("- 创建锁数量: %d", numLocks)
	t.Logf("- 总耗时: %v", duration)
	t.Logf("- 平均每锁耗时: %v", duration/time.Duration(numLocks))

	// 检查内存使用
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	t.Logf("- 当前内存使用: %d KB", m.Alloc/1024)

	// 清理测试
	stats := manager.GetStats()
	t.Logf("- 当前锁数量: %d", len(stats))
}

// TestStressHighConcurrency 压力测试：极高并发
func TestStressHighConcurrency(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过压力测试")
	}

	manager := NewSimpleLockManager()
	const numGoroutines = 10000
	const numOperations = 10

	var wg sync.WaitGroup
	var totalOperations int64
	var successOperations int64
	var mu sync.Mutex

	startTime := time.Now()

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			localTotal := 0
			localSuccess := 0

			for j := 0; j < numOperations; j++ {
				key := fmt.Sprintf("stress:concurrent:%d", j%100) // 100个共享资源
				lock := manager.GetLock(key, 1*time.Minute)

				localTotal++
				if success, _ := lock.TryLock(); success {
					localSuccess++
					// 模拟极短的工作
					runtime.Gosched()
					lock.Unlock()
				}
			}

			mu.Lock()
			totalOperations += int64(localTotal)
			successOperations += int64(localSuccess)
			mu.Unlock()
		}(i)
	}

	wg.Wait()
	duration := time.Since(startTime)

	t.Logf("压力测试 - 极高并发:")
	t.Logf("- Goroutine数量: %d", numGoroutines)
	t.Logf("- 总操作数: %d", totalOperations)
	t.Logf("- 成功操作数: %d", successOperations)
	t.Logf("- 成功率: %.2f%%", float64(successOperations)/float64(totalOperations)*100)
	t.Logf("- 总耗时: %v", duration)
	t.Logf("- 吞吐量: %.0f ops/sec", float64(totalOperations)/duration.Seconds())
}

// TestStressMemoryUsage 压力测试：内存使用
func TestStressMemoryUsage(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过压力测试")
	}

	manager := NewSimpleLockManager()

	// 记录初始内存
	runtime.GC()
	var m1 runtime.MemStats
	runtime.ReadMemStats(&m1)

	const cycles = 10
	const locksPerCycle = 10000

	for cycle := 0; cycle < cycles; cycle++ {
		t.Logf("内存测试周期 %d/%d", cycle+1, cycles)

		// 创建大量锁
		for i := 0; i < locksPerCycle; i++ {
			key := fmt.Sprintf("stress:memory:%d:%d", cycle, i)
			lock := manager.GetLock(key, 100*time.Millisecond) // 短过期时间
			lock.Lock()
			lock.Unlock()
		}

		// 等待过期
		time.Sleep(200 * time.Millisecond)

		// 清理过期锁
		cleaned := manager.CleanExpiredLocks()
		t.Logf("- 清理了 %d 个过期锁", cleaned)

		// 强制垃圾回收
		runtime.GC()

		// 检查内存
		var m runtime.MemStats
		runtime.ReadMemStats(&m)
		memoryGrowth := int64(m.Alloc) - int64(m1.Alloc)
		t.Logf("- 内存增长: %d KB", memoryGrowth/1024)

		// 如果内存增长过多，可能有问题
		if memoryGrowth > 10*1024*1024 { // 10MB
			t.Errorf("内存增长过多: %d KB", memoryGrowth/1024)
		}
	}

	// 最终内存检查
	runtime.GC()
	runtime.GC()
	var m2 runtime.MemStats
	runtime.ReadMemStats(&m2)

	finalGrowth := int64(m2.Alloc) - int64(m1.Alloc)
	t.Logf("最终内存增长: %d KB", finalGrowth/1024)
}

// TestStressLongRunning 压力测试：长时间运行
func TestStressLongRunning(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过压力测试")
	}

	manager := NewSimpleLockManager()
	const duration = 30 * time.Second // 运行30秒
	const numWorkers = 100

	ctx, cancel := context.WithTimeout(context.Background(), duration)
	defer cancel()

	var wg sync.WaitGroup
	var totalOps int64
	var mu sync.Mutex

	// 启动清理goroutine
	wg.Add(1)
	go func() {
		defer wg.Done()
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				cleaned := manager.CleanExpiredLocks()
				t.Logf("定期清理了 %d 个过期锁", cleaned)
			}
		}
	}()

	// 启动工作goroutines
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			localOps := 0
			requestCtx := log.SetRequestIDToContext(context.Background(), fmt.Sprintf("worker-%d", id))

			for {
				select {
				case <-ctx.Done():
					mu.Lock()
					totalOps += int64(localOps)
					mu.Unlock()
					return
				default:
					key := fmt.Sprintf("longrun:%d:%d", id, localOps%10)
					lock := manager.GetLock(key, 1*time.Second).WithContext(requestCtx)

					if success, _ := lock.TryLock(); success {
						// 模拟工作
						time.Sleep(time.Microsecond * 100)
						lock.Unlock()
					}

					localOps++

					// 偶尔休息一下
					if localOps%1000 == 0 {
						time.Sleep(time.Millisecond)
					}
				}
			}
		}(i)
	}

	wg.Wait()

	t.Logf("长时间运行测试:")
	t.Logf("- 运行时间: %v", duration)
	t.Logf("- 工作线程数: %d", numWorkers)
	t.Logf("- 总操作数: %d", totalOps)
	t.Logf("- 平均吞吐量: %.0f ops/sec", float64(totalOps)/duration.Seconds())

	// 最终清理和内存检查
	cleaned := manager.CleanExpiredLocks()
	t.Logf("- 最终清理了 %d 个过期锁", cleaned)

	stats := manager.GetStats()
	t.Logf("- 剩余锁数量: %d", len(stats))
}

// TestStressEdgeCases 压力测试：边界情况
func TestStressEdgeCases(t *testing.T) {
	if testing.Short() {
		t.Skip("跳过压力测试")
	}

	manager := NewSimpleLockManager()

	t.Run("极短过期时间", func(t *testing.T) {
		const numLocks = 1000
		for i := 0; i < numLocks; i++ {
			key := fmt.Sprintf("edge:short:%d", i)
			lock := manager.GetLock(key, time.Nanosecond) // 极短过期时间
			lock.Lock()
			lock.Unlock()
		}

		time.Sleep(10 * time.Millisecond)
		cleaned := manager.CleanExpiredLocks()
		t.Logf("极短过期时间测试: 清理了 %d 个锁", cleaned)
	})

	t.Run("重复key操作", func(t *testing.T) {
		const sameKey = "edge:same:key"
		const numOps = 10000

		for i := 0; i < numOps; i++ {
			lock := manager.GetLock(sameKey, 1*time.Minute)
			lock.Lock()
			lock.Unlock()
		}

		stats := manager.GetStats()
		if lockStat, exists := stats[sameKey]; exists {
			t.Logf("重复key测试: 使用次数 %d", lockStat.UseCount)
		}
	})

	t.Run("大量不同key", func(t *testing.T) {
		const numKeys = 50000

		for i := 0; i < numKeys; i++ {
			key := fmt.Sprintf("edge:unique:%d", i)
			lock := manager.GetLock(key, 1*time.Minute)
			lock.Lock()
			lock.Unlock()
		}

		stats := manager.GetStats()
		t.Logf("大量不同key测试: 创建了 %d 个锁", len(stats))
	})
}
