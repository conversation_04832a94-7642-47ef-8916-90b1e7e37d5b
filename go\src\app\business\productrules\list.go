package productrules

import (
	"bytes"
	"encoding/json"
	"fincore/model"
	"fincore/utils/gf"
	"fincore/utils/log"
	"fincore/utils/results"
	"fmt"
	"io"
	"reflect"
	"strconv"

	"github.com/gin-gonic/gin"
)

// 用于自动注册路由
type List struct {
}

// 初始化生成路由
func init() {
	fpath := List{}
	gf.Register(&fpath, reflect.TypeOf(fpath).PkgPath())
}

// 产品规则结构体
type ProductRule struct {
	ID                 uint    `gorm:"primaryKey;column:id" json:"id"`
	RuleName           string  `gorm:"column:rule_name" json:"rule_name" binding:"required"`
	LoanAmount         float64 `gorm:"column:loan_amount" json:"loan_amount" binding:"required,min=0"`
	LoanPeriod         int     `gorm:"column:loan_period" json:"loan_period" binding:"required,min=1"`
	TotalPeriods       int     `gorm:"column:total_periods" json:"total_periods" binding:"required,min=1"`
	GuaranteeFee       float64 `gorm:"column:guarantee_fee" json:"guarantee_fee" binding:"min=0"`
	AnnualInterestRate float64 `gorm:"column:annual_interest_rate" json:"annual_interest_rate" binding:"min=0"`
	OtherFees          float64 `gorm:"column:other_fees" json:"other_fees" binding:"min=0"`
	RuleCategory       string  `gorm:"column:rule_category" json:"rule_category" binding:"required"`
	RepaymentMethod    string  `gorm:"column:repayment_method" json:"repayment_method" binding:"required"`
}

func (api *List) CreateRule(c *gin.Context) {
	body, _ := c.GetRawData()
	log.Info("创建产品规则时Request Body:", string(body))
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
	var rule ProductRule
	if err := c.ShouldBindJSON(&rule); err != nil {
		log.Error(fmt.Sprintf("创建产品规则时，传入的参数不符合要求：%s", err.Error()))
		results.Failed(c, "创建产品规则失败", nil)
		return
	}

	_, err := model.DB().Table("product_rules").Data(map[string]interface{}{
		"rule_name":            rule.RuleName,
		"loan_amount":          rule.LoanAmount,
		"loan_period":          rule.LoanPeriod,
		"total_periods":        rule.TotalPeriods,
		"guarantee_fee":        rule.GuaranteeFee,
		"annual_interest_rate": rule.AnnualInterestRate,
		"other_fees":           rule.OtherFees,
		"rule_category":        rule.RuleCategory,
		"repayment_method":     rule.RepaymentMethod}).Insert()
	if err != nil {
		log.Error(fmt.Sprintf("创建产品规则时插入数据库失败，原因：%s", err))
		results.Failed(c, "创建产品规则失败", nil)
		return
	} else {
		results.Success(c, "创建产品规则成功", nil, nil)
		return
	}
}

// 查询产品规则列表
func (api *List) GetRules(c *gin.Context) {
	// 处理查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	name := c.DefaultQuery("rule_name", "")
	period := c.DefaultQuery("loan_period", "")
	category := c.DefaultQuery("rule_category", "")
	repaymentMethod := c.DefaultQuery("repayment_method", "")

	query := model.DB().Table("product_rules")

	// 动态添加条件
	if name != "" {
		query = query.Where("rule_name", name)
	}

	if period != "" {
		query = query.Where("loan_period", period)
	}

	if category != "" {
		query = query.Where("rule_category", category)
	}

	if repaymentMethod != "" {
		query = query.Where("repayment_method", repaymentMethod)
	}

	// 分页查询
	var total int64
	total, _ = query.Count("*")

	offset := (page - 1) * limit
	list, err := query.Fields("id, rule_name, loan_amount, loan_period, total_periods, guarantee_fee, annual_interest_rate, other_fees, rule_category, repayment_method").Limit(limit).Offset(offset).Order("id desc").Get()

	if err != nil {
		log.Error(fmt.Sprintf("查询产品规则失败：%s", err))
		results.Failed(c, "查询产品规则失败", nil)
		return
	} else {
		log.Info(fmt.Sprintf("查询产品规则成功："))
	}

	results.Success(c, "查询产品规则成功", map[string]interface{}{
		"page":     page,
		"pageSize": limit,
		"total":    total,
		"items":    list}, nil)
}

// 删除产品规则
func (api *List) DelRule(c *gin.Context) {
	body, _ := io.ReadAll(c.Request.Body)
	var parameter map[string]interface{}
	_ = json.Unmarshal(body, &parameter)
	ids := parameter["ids"]
	res2, err := model.DB().Table("product_rules").WhereIn("id", ids.([]interface{})).Delete()
	if err != nil {
		log.Error(fmt.Sprintf("删除产品规则失败：%s", err))
		results.Failed(c, "删除失败", err)
	} else {
		results.Success(c, "删除成功！", res2, nil)
	}
}

// 编辑产品规则
func (api *List) UpdateRule(c *gin.Context) {
	// 2. 记录原始请求体
	body, _ := c.GetRawData()
	log.Info("更新产品规则时Request Body:", string(body))
	c.Request.Body = io.NopCloser(bytes.NewBuffer(body))

	// 3. 解析请求参数
	var rule ProductRule
	if err := c.ShouldBindJSON(&rule); err != nil {
		log.Error(fmt.Sprintf("编辑产品规则时，传入的参数不符合要求：%s", err.Error()))
		results.Failed(c, "编辑产品规则失败", nil)
		return
	}

	// 4. 检查规则是否存在
	id := rule.ID
	found, err := model.DB().Table("product_rules").Where("id", id).Get()
	if err != nil {
		log.Error(fmt.Sprintf("检查规则是否存在时出错: %s", err))
		results.Failed(c, "编辑产品规则失败", nil)
		return
	} else {
		if len(found) == 0 {
			log.Error(fmt.Sprintf("要编辑的产品规则不存在"))
			results.Failed(c, "编辑产品规则失败", nil)
			return
		}
	}

	// 5. 构建更新数据
	updateData := map[string]interface{}{
		"rule_name":            rule.RuleName,
		"loan_amount":          rule.LoanAmount,
		"loan_period":          rule.LoanPeriod,
		"total_periods":        rule.TotalPeriods,
		"guarantee_fee":        rule.GuaranteeFee,
		"annual_interest_rate": rule.AnnualInterestRate,
		"other_fees":           rule.OtherFees,
		"rule_category":        rule.RuleCategory,
		"repayment_method":     rule.RepaymentMethod,
	}

	// 6. 执行更新操作
	_, err2 := model.DB().Table("product_rules").Data(updateData).Where("id", id).Update()
	if err2 != nil {
		log.Error(fmt.Sprintf("编辑产品规则时更新数据库失败，原因：%s", err))
		results.Failed(c, "编辑产品规则失败", nil)
		return
	}

	// 7. 更新成功响应
	results.Success(c, "产品规则更新成功", gin.H{
		"id":   id,
		"data": rule,
	}, nil)
}
