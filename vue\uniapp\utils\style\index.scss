$base-color: #1a6eff;


page {
  -webkit-overflow-scrolling: touch; // 解决ios滑动不流畅
  height: 100%;
  width: 100%;
  word-break: break-all; //英文文本不换行
  white-space: normal;
}
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
  display: none;
}
/*flex布局(colorui里面也有相关基础样式)*/
	/* x水平排列*/
	.x-f {
		display: flex;
		align-items: center;
	}

	/*x两端且水平居中*/
	.x-bc {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	/*x平分且水平居中*/
	.x-ac {
		display: flex;
		justify-content: space-around;
		align-items: center;
	}

	/*x水平靠上对齐*/
	.x-start {
		display: flex;
		align-items: flex-start;
	}

	/*x水平靠下对齐*/
	.x-end {
		display: flex;
		align-items: flex-end;
	}

	/*上下左右居中*/
	.x-c {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	/*y竖直靠左*/
	.y-start {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
	}

	/*y竖直靠右*/
	.y-end {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
	}

	/*y竖直居中*/
	.y-f {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	/* y竖直两端*/
	.y-b {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	/*y竖直两端居中*/
	.y-bc {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
	}
