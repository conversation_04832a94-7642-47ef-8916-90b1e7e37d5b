package examples

import (
	"context"
	"fincore/app/scheduler/tasks"
	"fincore/utils/log"
	"time"
)

// 使用示例：
// 这是一个独立的示例任务，不会自动注册为内置任务
// 如需使用，请在您的代码中手动注册：
//
// import "fincore/app/scheduler"
// import "fincore/app/scheduler/tasks/examples"
//
// func main() {
//     // 初始化调度器
//     scheduler.Initialize()
//
//     // 手动注册任务
//     task := examples.NewHelloTimeTask()
//     scheduler.RegisterTask(task)
//
//     // 启动调度器
//     scheduler.Start()
// }

// HelloTimeTask 时间问候任务
type HelloTimeTask struct {
	*tasks.BaseTask
	logger *log.Logger
}

// NewHelloTimeTask 创建时间问候任务
func NewHelloTimeTask() *HelloTimeTask {
	baseTask := tasks.NewBaseTask(
		"hello_time_task", // 任务名称
		"每5秒打印时间和问候语",     // 任务描述
		"*/5 * * * * *",   // 每5秒执行一次
		1*time.Second,     // 超时时间1秒
	)

	// 设置任务属性
	baseTask.SetRetryCount(1). // 重试1次
					SetRetryInterval(1 * time.Second).                // 重试间隔1秒
					SetConcurrencyMode(tasks.ConcurrencyModeParallel) // 并行模式

	return &HelloTimeTask{
		BaseTask: baseTask,
		logger:   log.GetModule("scheduler"),
	}
}

// Execute 执行时间问候任务
func (t *HelloTimeTask) Execute(ctx context.Context) error {
	// 检查上下文是否被取消
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	// 获取当前时间并格式化
	currentTime := time.Now().Format("2006-01-02 15:04:05")

	// 打印时间和问候语
	t.logger.Info("定时任务执行",
		log.String("current_time", currentTime),
		log.String("message", "hello"),
		log.String("task_name", t.GetName()),
	)

	return nil
}

// OnStart 任务开始前的准备工作
func (t *HelloTimeTask) OnStart(ctx context.Context) error {
	t.logger.Info("时间问候任务准备开始",
		log.String("task_name", t.GetName()),
		log.String("schedule", t.GetSchedule()),
	)
	return nil
}

// OnSuccess 任务成功后的处理
func (t *HelloTimeTask) OnSuccess(ctx context.Context) error {
	// 成功执行后不需要特殊处理，保持简洁
	return nil
}

// OnError 任务失败后的处理
func (t *HelloTimeTask) OnError(ctx context.Context, err error) error {
	t.logger.Error("时间问候任务执行失败",
		log.ErrorField(err),
		log.String("task_name", t.GetName()),
	)
	return nil
}

// OnComplete 任务完成后的清理工作
func (t *HelloTimeTask) OnComplete(ctx context.Context) error {
	// 完成后不需要特殊清理，保持简洁
	return nil
}
