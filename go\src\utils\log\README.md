# 日志系统使用指南

## 概述

本项目的日志系统基于 zap 构建，提供了强大的结构化日志功能，支持多业务模块分文件记录、请求链路追踪、业务上下文等特性。

## 主要特性

- ✅ **向后兼容**：完全兼容现有的 `log.Info()` 等调用方式
- ✅ **多模块支持**：不同业务模块可以记录到不同的日志文件
- ✅ **请求链路追踪**：自动生成请求ID，支持全链路日志追踪
- ✅ **结构化日志**：支持字段化日志，便于日志分析和检索
- ✅ **业务上下文**：支持添加业务相关的上下文信息
- ✅ **中间件支持**：提供 Gin 中间件，自动记录请求日志
- ✅ **性能优化**：基于 zap 的高性能日志库

## 快速开始

### 基础用法（向后兼容）

```go
import "fincore/utils/log"

// 现有代码无需修改，完全兼容
log.Info("用户 %s 登录成功", username)
log.Error("处理请求失败: %v", err)
log.Debug("当前状态: %+v", status)
log.Warn("警告信息: %s", warning)
```

### 模块化日志

#### 动态模块注册（推荐方式）

```go
// 注册新的业务模块
paymentLogger := log.RegisterModule("payment", "支付处理模块")
paymentLogger.Info("支付模块初始化完成")

// 获取模块日志器（不存在会自动创建）
log.GetModule("payment").Info("处理支付请求",
    log.String("order_id", "ORD123456"),
    log.Float64("amount", 99.99),
)

// 获取不存在的模块（自动创建）
log.GetModule("notification").Info("发送通知")

// 查看所有已注册的模块
modules := log.ListRegisteredModules()
```

#### 预定义模块（向后兼容）

```go
// 使用预定义的模块日志器
log.Customer().Info("用户注册成功")
log.Order().Info("订单创建完成")
log.BankCard().Info("银行卡处理成功")

// 自定义模块
log.WithModule("custom_module").Info("自定义模块日志")
```

### 结构化日志

```go
// 单个字段
log.WithField("user_id", 12345).Info("用户操作")

// 多个字段
log.WithFields(
    log.String("user_id", "12345"),
    log.String("action", "login"),
    log.String("ip", "***********"),
).Info("用户登录")

// 订单相关日志
log.Order().WithFields(
    log.OrderID(order.ID),
    log.Amount(order.Amount),
    log.Currency("CNY"),
    log.String("status", "created"),
).Info("订单创建成功")
```

### 上下文日志

```go
// 在 Gin 处理器中使用
func CreateOrder(c *gin.Context) {
    logger := log.Order().WithGinContext(c)
    
    logger.Info("开始创建订单")
    
    // 业务逻辑...
    
    logger.WithFields(
        log.OrderID(orderID),
        log.Amount(amount),
    ).Info("订单创建成功")
}

// 使用请求ID
logger := log.WithRequestID("req-123").Order()
logger.Info("处理订单请求")
```

### 错误日志

```go
// 记录错误
if err != nil {
    log.Order().WithError(err).WithFields(
        log.OrderID(orderID),
        log.String("operation", "create"),
    ).Error("订单创建失败")
    return
}

// 业务错误
log.Order().WithFields(
    log.OrderID(orderID),
    log.String("reason", "insufficient_balance"),
).Warn("订单创建失败：余额不足")
```

## 业务场景示例

### 支付流程日志

```go
func ProcessPayment(orderID string, amount float64) error {
    logger := log.GetModule("payment")

    logger.Info("开始支付流程",
        log.String("order_id", orderID),
        log.Float64("amount", amount),
    )

    // 支付验证
    if err := validatePayment(orderID, amount); err != nil {
        logger.WithError(err).Error("支付验证失败",
            log.String("order_id", orderID),
        )
        return err
    }

    logger.Info("支付验证通过",
        log.String("order_id", orderID),
    )

    // 处理支付
    txnID, err := processPayment(orderID, amount)
    if err != nil {
        logger.WithError(err).Error("支付处理失败",
            log.String("order_id", orderID),
        )
        return err
    }

    logger.Info("支付完成",
        log.String("order_id", orderID),
        log.String("transaction_id", txnID),
        log.String("status", "success"),
    )

    return nil
}
```

### 多模块协作日志

```go
func UserRegistration(mobile, email string) error {
    userLogger := log.GetModule("user")
    smsLogger := log.GetModule("sms")
    emailLogger := log.GetModule("email")

    userID := generateUserID()

    userLogger.Info("用户注册开始",
        log.String("user_id", userID),
        log.String("mobile", mobile),
    )

    // 发送短信验证码
    code := generateSMSCode()
    if err := sendSMS(mobile, code); err != nil {
        smsLogger.WithError(err).Error("短信发送失败",
            log.String("user_id", userID),
            log.String("mobile", mobile),
        )
        return err
    }

    smsLogger.Info("短信验证码发送成功",
        log.String("user_id", userID),
        log.String("mobile", mobile),
    )

    // 发送欢迎邮件
    if err := sendWelcomeEmail(email); err != nil {
        emailLogger.WithError(err).Warn("欢迎邮件发送失败",
            log.String("user_id", userID),
            log.String("email", email),
        )
        // 邮件失败不影响注册流程
    } else {
        emailLogger.Info("欢迎邮件发送成功",
            log.String("user_id", userID),
            log.String("email", email),
        )
    }

    userLogger.Info("用户注册完成",
        log.String("user_id", userID),
        log.String("status", "active"),
    )

    return nil
}
```

## 业务模块日志器

### 动态模块管理

推荐使用动态模块注册方式，无需修改日志模块代码即可扩展新的业务模块：

```go
// 在应用启动时注册常用模块
func InitBusinessModules() {
    log.RegisterModule("payment", "支付处理模块")
    log.RegisterModule("sms", "短信发送模块")
    log.RegisterModule("email", "邮件发送模块")
    log.RegisterModule("user", "用户管理模块")
    log.RegisterModule("inventory", "库存管理模块")
    log.RegisterModule("logistics", "物流管理模块")
}

// 在业务代码中使用
func SomeBusinessFunction() {
    // 获取已注册的模块
    log.GetModule("payment").Info("处理支付")

    // 获取未注册的模块（自动创建）
    log.GetModule("new_feature").Info("新功能日志")
}
```

### 预定义模块（向后兼容）

系统预定义了以下业务模块日志器：

- `log.Order()` - 订单管理模块
- `log.Customer()` - 客户管理模块
- `log.Channel()` - 渠道管理模块
- `log.Risk()` - 风控模块
- `log.BankCard()` - 银行卡模块
- `log.Identity()` - 身份认证模块
- `log.Complaint()` - 投诉建议模块
- `log.System()` - 系统管理模块

## 中间件使用

```go
import "fincore/utils/log"

// 在路由中使用
r := gin.Default()

// 请求ID中间件（推荐）
r.Use(log.RequestIDMiddleware())

// 访问日志中间件
r.Use(log.AccessLogMiddleware())

// 错误日志中间件
r.Use(log.ErrorLogMiddleware())
```

## 配置说明

日志系统支持通过配置文件进行配置：

```yaml
log:
  root_dir: "./log"          # 日志文件根目录
  level: "info"              # 日志级别
  format: "json"             # 日志格式
  show_line: true            # 是否显示行号
  max_backups: 10            # 保留的日志文件数量
  max_size: 100              # 单个日志文件最大大小(MB)
  max_age: 30                # 日志文件保留天数
  compress: true             # 是否压缩旧日志文件
```

## 日志文件结构

```
go/src/log/
├── app/                   # 应用日志
│   └── 2025-07-17_app.log
├── order/                 # 订单模块日志
│   └── 2025-07-17_order.log
├── customer/              # 客户模块日志
│   └── 2025-07-17_customer.log
├── http/                  # HTTP请求日志
│   └── 2025-07-17_http.log
├── access/                # 访问日志
│   └── 2025-07-17_access.log
└── error/                 # 错误日志
    └── 2025-07-17_error.log
```

## 最佳实践

### 1. 选择合适的日志级别

- `Debug`: 调试信息，仅在开发环境使用
- `Info`: 一般信息，记录正常的业务流程
- `Warn`: 警告信息，记录可能的问题但不影响功能
- `Error`: 错误信息，记录需要关注的错误

### 2. 使用结构化字段

```go
// 好的做法
log.Order().WithFields(
    log.OrderID(orderID),
    log.Amount(amount),
    log.String("status", status),
).Info("订单状态更新")

// 避免这样做
log.Order().Infof("订单 %d 状态更新为 %s，金额 %.2f", orderID, status, amount)
```

### 3. 添加上下文信息

```go
// 在业务方法中添加上下文
func (s *OrderService) CreateOrder(ctx context.Context, req *CreateOrderRequest) error {
    logger := log.Order().WithContext(ctx).WithFields(
        log.UserID(req.UserID),
        log.Amount(req.Amount),
    )
    
    logger.Info("开始创建订单")
    
    // 业务逻辑...
    
    logger.WithField("order_id", orderID).Info("订单创建成功")
    return nil
}
```

### 4. 错误处理

```go
// 记录错误时提供足够的上下文
if err := s.orderRepo.Create(order); err != nil {
    log.Order().WithError(err).WithFields(
        log.OrderID(order.ID),
        log.UserID(order.UserID),
        log.String("operation", "create"),
    ).Error("订单创建失败")
    return err
}
```

## 性能考虑

1. **字段预分配**：日志系统已优化字段分配，避免频繁的内存分配
2. **异步写入**：使用 lumberjack 进行异步日志写入
3. **级别过滤**：只有满足级别要求的日志才会被处理
4. **结构化输出**：使用 JSON 格式便于日志分析工具处理

## 故障排查

### 1. 日志文件未生成

检查配置文件中的 `root_dir` 路径是否正确，确保应用有写入权限。

### 2. 日志级别不生效

确认配置文件中的 `level` 设置，以及是否正确重启了应用。

### 3. 性能问题

如果日志写入影响性能，可以：
- 调整日志级别，减少不必要的日志
- 增加 `max_size` 减少文件切换频率
- 启用 `compress` 节省磁盘空间
