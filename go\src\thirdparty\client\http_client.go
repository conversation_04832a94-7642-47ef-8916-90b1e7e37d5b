package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"fincore/thirdparty/auth"
	"fincore/thirdparty/error_handler"
	"fincore/thirdparty/types"
)

// HTTPClient 统一HTTP客户端
type HTTPClient struct {
	client        *http.Client
	authenticator auth.Authenticator
	baseURL       string
	timeout       time.Duration
	retryConfig   *RetryConfig
	errorMapper   error_handler.ErrorMapper
}

// ClientConfig HTTP客户端配置
type ClientConfig struct {
	BaseURL       string
	Timeout       time.Duration
	Authenticator auth.Authenticator
	RetryConfig   *RetryConfig
	ErrorMapper   error_handler.ErrorMapper
	ProxyURL      string // 代理地址
}

// RetryConfig 重试配置
type RetryConfig struct {
	MaxRetries int           `json:"maxRetries"`
	Interval   time.Duration `json:"interval"`
	BackOff    bool          `json:"backOff"`
}

// Request 统一请求结构
type Request struct {
	Method      string                 `json:"method"`
	Path        string                 `json:"path"`
	Headers     map[string]string      `json:"headers"`
	Data        map[string]interface{} `json:"data"`
	QueryParams map[string]string      `json:"queryParams"`
	Files       []FileUpload           `json:"files,omitempty"`
}

// FileUpload 文件上传结构
type FileUpload struct {
	FieldName string `json:"fieldName"`
	FileName  string `json:"fileName"`
	FilePath  string `json:"filePath"`
	Content   []byte `json:"content"`
}

// Response 统一响应结构
type Response struct {
	Success    bool                   `json:"success"`
	Code       string                 `json:"code"`
	Message    string                 `json:"message"`
	Data       map[string]interface{} `json:"data"`
	RawData    []byte                 `json:"-"`
	StatusCode int                    `json:"statusCode"`
}

// NewHTTPClient 创建HTTP客户端
func NewHTTPClient(config *ClientConfig) *HTTPClient {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	if config.RetryConfig == nil {
		config.RetryConfig = &RetryConfig{
			MaxRetries: 3,
			Interval:   time.Second,
			BackOff:    true,
		}
	}

	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: config.Timeout,
	}

	// 设置代理
	if config.ProxyURL != "" {
		if proxyURL, err := url.Parse(config.ProxyURL); err == nil {
			httpClient.Transport = &http.Transport{
				Proxy: http.ProxyURL(proxyURL),
			}
		}
	}

	return &HTTPClient{
		client:        httpClient,
		authenticator: config.Authenticator,
		baseURL:       config.BaseURL,
		timeout:       config.Timeout,
		retryConfig:   config.RetryConfig,
		errorMapper:   config.ErrorMapper,
	}
}

// Do 执行HTTP请求
func (c *HTTPClient) Do(ctx context.Context, req *types.Request) (*types.Response, error) {
	var lastErr error

	for i := 0; i <= c.retryConfig.MaxRetries; i++ {
		response, err := c.doRequest(ctx, req)
		if err == nil {
			return response, nil
		}

		lastErr = err

		// 检查是否需要刷新token
		if c.authenticator != nil && c.authenticator.IsTokenExpired(err) {
			if refreshErr := c.authenticator.RefreshToken(ctx); refreshErr != nil {
				return nil, fmt.Errorf("刷新token失败: %v", refreshErr)
			}
			continue // 重试当前请求
		}

		// 如果是最后一次重试，不再等待
		if i < c.retryConfig.MaxRetries {
			interval := c.retryConfig.Interval
			if c.retryConfig.BackOff {
				interval = time.Duration(i+1) * interval
			}
			time.Sleep(interval)
		}
	}

	return nil, lastErr
}

// doRequest 执行单次请求
func (c *HTTPClient) doRequest(ctx context.Context, req *types.Request) (*types.Response, error) {
	// 构建完整URL
	fullURL := c.baseURL + req.Path
	if len(req.QueryParams) > 0 {
		fullURL += "?" + c.buildQueryString(req.QueryParams)
	}

	// 准备请求体
	var body []byte
	var err error
	if req.Data != nil {
		body, err = json.Marshal(req.Data)
		if err != nil {
			return nil, fmt.Errorf("序列化请求数据失败: %v", err)
		}
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, req.Method, fullURL, bytes.NewBuffer(body))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置默认请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	// 设置自定义请求头
	for key, value := range req.Headers {
		httpReq.Header.Set(key, value)
	}

	// 执行认证
	if c.authenticator != nil {
		if err := c.authenticator.Authenticate(ctx, httpReq, body); err != nil {
			return nil, fmt.Errorf("认证失败: %v", err)
		}
	}

	// 发送请求
	resp, err := c.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}

	// 构建响应对象
	response := &types.Response{
		StatusCode: resp.StatusCode,
		RawData:    respBody,
	}

	// 解析响应
	if err := c.parseResponse(response); err != nil {
		return nil, err
	}

	// 映射错误码
	if c.errorMapper != nil {
		response = c.errorMapper.MapError(response)
	}

	return response, nil
}

// parseResponse 解析响应
func (c *HTTPClient) parseResponse(response *types.Response) error {
	if len(response.RawData) == 0 {
		response.Success = response.StatusCode >= 200 && response.StatusCode < 300
		return nil
	}

	var respData map[string]interface{}
	if err := json.Unmarshal(response.RawData, &respData); err != nil {
		// 如果无法解析为JSON，将原始数据作为字符串返回
		response.Data = map[string]interface{}{
			"raw": string(response.RawData),
		}
		response.Success = response.StatusCode >= 200 && response.StatusCode < 300
		return nil
	}

	response.Data = respData

	// 尝试提取标准字段
	if code, ok := respData["code"].(string); ok {
		response.Code = code
	} else if code, ok := respData["code"].(float64); ok {
		response.Code = fmt.Sprintf("%.0f", code)
	}

	if message, ok := respData["message"].(string); ok {
		response.Message = message
	}

	if success, ok := respData["success"].(bool); ok {
		response.Success = success
	} else {
		// 根据响应码判断是否成功
		if response.Code != "" {
			// 如果code为100000或0000，认为成功
			response.Success = response.Code == "100000" || response.Code == "0000"
		} else {
			// 如果没有success字段，根据HTTP状态码判断
			response.Success = response.StatusCode >= 200 && response.StatusCode < 300
		}
	}

	return nil
}

// buildQueryString 构建查询字符串
func (c *HTTPClient) buildQueryString(params map[string]string) string {
	var parts []string
	for key, value := range params {
		parts = append(parts, fmt.Sprintf("%s=%s", key, value))
	}
	return strings.Join(parts, "&")
}
