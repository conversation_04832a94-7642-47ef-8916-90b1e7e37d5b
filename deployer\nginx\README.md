# Fincore Nginx 配置

## 概述

本目录包含Fincore项目的完整Nginx配置，用于托管前端应用和代理后端API。

## 配置文件说明

### 1. business.conf
- **Business管理后台**：Vue.js开发的管理系统
- **功能**：业务管理、数据分析、系统配置
- **域名**：business.fincore.local
- **目录**：/opt/fincore/build/business

### 2. uniapp.conf
- **UniApp移动端**：跨平台移动应用
- **功能**：移动端用户界面
- **域名**：app.fincore.local
- **目录**：/opt/fincore/build/uniapp

## 部署方式

### 方法1：集成部署（推荐）
```bash
# 部署应用和Nginx配置
sudo ./deploy.sh --start --nginx
```

### 方法2：单独部署Nginx
```bash
# 只部署Nginx配置
sudo ./nginx/deploy-nginx.sh
```

### 方法3：手动部署
```bash
# 复制配置文件
sudo cp nginx/*.conf /etc/nginx/sites-available/
sudo ln -sf /etc/nginx/sites-available/*.conf /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

## 域名配置

### 本地开发环境
在 `/etc/hosts` 文件中添加：
```
127.0.0.1 fincore.local
127.0.0.1 business.fincore.local
127.0.0.1 app.fincore.local
```

### 生产环境
1. 配置DNS解析指向服务器IP
2. 修改配置文件中的域名
3. 如需要可配置HTTPS（当前为HTTP模式）

## HTTP配置说明

### 当前配置
- 所有站点配置为HTTP模式
- 适用于开发环境和内网部署
- 无需SSL证书配置

### 如需HTTPS
如果需要配置HTTPS，可以：
1. 修改nginx配置文件添加SSL配置
2. 获取SSL证书（Let's Encrypt、商业CA等）
3. 配置证书文件路径
4. 添加HTTP到HTTPS重定向

## 目录结构

```
nginx/
├── fincore.conf          # 主配置文件
├── business.conf         # Business前端配置
├── uniapp.conf          # UniApp前端配置
├── deploy-nginx.sh      # 部署脚本
├── ssl/                 # SSL证书目录
│   ├── cert.pem        # SSL证书
│   ├── key.pem         # SSL私钥
│   └── README.md       # 证书说明
└── html/               # 默认HTML页面
    └── index.html      # 服务导航页面
```

## 功能特性

### 1. 前端托管
- **静态文件服务**：高效托管前端资源
- **Gzip压缩**：减少传输大小
- **缓存控制**：优化加载性能
- **SPA路由支持**：处理前端路由

### 2. API代理
- **后端代理**：转发API请求到Go服务
- **负载均衡**：支持多后端服务器
- **超时控制**：防止请求挂起
- **WebSocket支持**：实时通信

### 3. 安全配置
- **安全头**：防止XSS、点击劫持等
- **文件保护**：隐藏敏感文件
- **访问控制**：限制特定路径访问
- **HTTP模式**：适用于内网和开发环境

### 4. 监控支持
- **访问日志**：记录请求信息
- **错误日志**：记录错误信息
- **健康检查**：提供状态检查端点
- **状态页面**：Nginx状态监控

## 配置自定义

### 修改域名
1. 编辑配置文件中的 `server_name`
2. 更新SSL证书（如果域名变更）
3. 重新加载Nginx配置

### 修改后端地址
1. 编辑 `upstream` 配置
2. 修改 `proxy_pass` 地址
3. 重新加载Nginx配置

### 添加新站点
1. 创建新的配置文件
2. 复制到 `/etc/nginx/sites-available/`
3. 创建软链接到 `/etc/nginx/sites-enabled/`
4. 测试并重新加载配置

## 故障排除

### 1. 配置语法错误
```bash
# 测试配置语法
sudo nginx -t

# 查看错误详情
sudo nginx -T
```

### 2. 服务启动失败
```bash
# 查看服务状态
sudo systemctl status nginx

# 查看错误日志
sudo tail -f /var/log/nginx/error.log
```

### 3. SSL证书问题
```bash
# 检查证书有效性
openssl x509 -in /opt/fincore/nginx/ssl/cert.pem -text -noout

# 检查私钥匹配
openssl rsa -in /opt/fincore/nginx/ssl/key.pem -check
```

### 4. 前端文件404
```bash
# 检查文件权限
ls -la /opt/fincore/build/

# 检查Nginx用户权限
sudo -u www-data ls /opt/fincore/build/business/
```

## 性能优化

### 1. 缓存配置
- 静态资源长期缓存
- HTML文件禁用缓存
- API响应适当缓存

### 2. 压缩配置
- Gzip压缩文本文件
- 压缩级别平衡性能和大小
- 排除已压缩文件

### 3. 连接优化
- Keep-alive连接复用
- 适当的超时设置
- 缓冲区大小优化

## 监控和日志

### 访问日志
- 位置：`/var/log/nginx/`
- 格式：标准访问日志格式
- 轮转：系统自动轮转

### 错误日志
- 位置：`/var/log/nginx/`
- 级别：可配置日志级别
- 监控：建议监控错误日志

### 状态监控
- 健康检查：`/health` 端点
- Nginx状态：`/nginx_status` 端点
- 系统状态：`/status` 端点

---

**更新时间**: $(date '+%Y-%m-%d %H:%M:%S')
**版本**: 1.0.0
