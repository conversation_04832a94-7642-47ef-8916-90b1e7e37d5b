{"level":"dev.info","ts":"[2025-08-15 09:10:47.431]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcb83f1925b1c9bb29ab9","method":"GET","url":"/business/statistics/statisticscontroller/getChannelDueStatistics","query":"channel_id&due_date_start&due_date_end&period_number&is_new_user&page_size=10","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0771228,"request_size":0,"response_size":1189}
{"level":"dev.info","ts":"[2025-08-15 09:10:52.291]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"185bcb8512b06d74c2e3b202","method":"GET","url":"/business/statistics/statisticscontroller/getChannelDueStatistics","query":"channel_id&due_date_start&due_date_end&period_number&is_new_user&page_size=10","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0950518,"request_size":0,"response_size":1189}
