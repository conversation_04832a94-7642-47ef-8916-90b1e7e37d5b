<template>
  <view class="legal-agreement-container">
    <!-- 协议标题 -->
    <view class="agreement-header">
      <text class="agreement-title">电子签名协议</text>
    </view>

    <!-- 当事人信息 -->
    <view class="parties-info">
      <text class="party-title">委托人(甲方):</text>
      <text class="party-name">冯方志</text>
      <text class="party-id">身份证号:370829200112210018</text>
      
      <text class="party-title">受托人(乙方):</text>
      <text class="party-name">武汉祥衡科技有限公司</text>
    </view>

    <!-- 协议引言 -->
    <view class="agreement-intro">
      <text>甲乙双方根据《中华人民共和国民法典》《中华人民共和国电子签名法》等有关法律、法规，经双方友好协商，就采用电子签名签订电子合同的有关事项达成如下协议:</text>
    </view>

    <!-- 第一章 电子签名约定 -->
    <view class="agreement-chapter">
      <text class="chapter-title">第一章 电子签名约定</text>
      
      <view class="agreement-article">
        <text class="article-number">1.</text>
        <text class="article-content">
          基于甲方拟通过乙方信息服务向乙方运营的借款服务平台或乙方合作平台(以下统一简称为"乙方平台")合作的资金方申请借款或申请其他服务，甲方与乙方签订本委托授权协议，委托乙方代甲方向乙方合作方的电子签章服务机构【杭州天谷信息科技有限公司(e签宝)及其合作的电子认证机构，包括但不限于:天津 CA、浙江 CA、安徽 CA】申请数字证书用于电子签名。甲方知悉甲方委托乙方申请的数字证书将绑定甲方的身份信息，其作出的电子签名将代表甲方的真实意思表示，数据电文经过电子签名后即代表甲方知悉并认可其中所载内容。甲方同意自签订本协议之日起，授权乙方通过乙方合作的电子签章服务机构，将甲方的主体信息、身份信息【身份证件信息、身份证件照片、人脸识别照片(如需)等】、手机号及签约合同信息等传输至电子认证机构(CA，Certificate Authority)或其他乙方合作的第三方机构，由CA以出具电子签名认证证书之目的或合作第三方机构认证身份之目的使用并保存，且前述信息至少保存至相应签约合同(包括但不限于签约合同电子数据)到期后5年，该等信息不因甲方终止使用乙方平台服务而停止保存。
        </text>
      </view>

      <view class="agreement-article">
        <text class="article-number">2.</text>
        <text class="article-content">
          甲方应当在签署本协议之前登录相应 CA官网(网址详见本协议附件)阅读《数字证书服务协议》《数字证书使用安全提示》《电子认证业务规则》(具体以 CA官网展示的协议名称及内容为准)等协议文件以了解有关数字证书的事项及甲方和CA之间的权利义务内容。一旦甲方接受本条款，则意味着甲方与CA之间成立数字证书服务合同关系。甲方同意当甲方与CA对有关数字证书的服务事项发生争议时，将争议提交至 CA机构相关协议约定的争议解决机构解决。
        </text>
      </view>

      <view class="agreement-article">
        <text class="article-number">3.</text>
        <text class="article-content">
          如甲方在乙方平台签署的相关协议或授权书需使用乙方合作的第三方电子签章服务机构提供电子签章签名服务的，则甲方不可撤销的授权同意:当甲方通过乙方平台向乙方合作的资金方或其他合作方申请借款或其他服务过程中，甲方在乙方平台展示的页面上通过包括但不限于点击、勾选等方式结合确认签署基于申请借款或其他服务而形成的电子合同及相关法律文件(包括不限于借款合同及其他相关协议、授权委托书、承诺文件及其他相关法律文件，具体以甲方申请借款或其他服务流程中页面实际展示的协议名称为准，以下统一简称为"电子合同及相关法律文件")在乙方后台生成时，委托乙方使用该证书代甲方以电子签名的形式进行签署，并视为甲方本人亲自签署。
        </text>
      </view>

      <view class="agreement-article">
        <text class="article-number">4.</text>
        <text class="article-content">
          乙方确认并同意使用电子签名确认同意接受相关电子合同及法律文件的，视为甲方本人亲自签署,与甲方在纸质合同上手写签名或盖章具有同等的法律效力，无需另行签署纸质合同或文书。甲方按照本协议约定一经使用电子签名对上述电子合同及法律文件进行确认及签署，相关电子合同及法律文件即对甲方发生效力，甲方同意接受上述电子合同及法律文件内容的约束，不得变更或撤销，由此所产生的一切后果均由甲方承担。
        </text>
      </view>

      <view class="agreement-article">
        <text class="article-number">5.</text>
        <text class="article-content">
          当本协议第一条列举的使用电子签名所蕴含的风险所指的事项发生时，由此导致甲方损失的，乙方不承担任何赔偿责任。
        </text>
      </view>

      <view class="agreement-article">
        <text class="article-number">6.</text>
        <text class="article-content">
          本协议适用于甲方通过乙方平台向乙方合作的资金方申请借款或向乙方合作方申请其他服务的流程中在乙方平台签署的所有电子合同及法律文件。
        </text>
      </view>

      <view class="agreement-article">
        <text class="article-number">7.</text>
        <text class="article-content">
          甲方同意:甲方在通过乙方平台申请借款或其他服务过程中，如需使用甲方数字证书进行电子签名时，乙方可基于本协议自动调用甲方委托乙方代甲方向电子签章服务机构申请的数字证书进行电子签名而无需另行获得甲方的单独同意。
        </text>
      </view>

      <view class="agreement-article">
        <text class="article-number">8.</text>
        <text class="article-content">
          甲方保证向乙方提供的所有信息的真实性、合法性和有效性，乙方依据本协议进行所有电子合同及法律文件的签署引起的一切法律后果由乙方自行承担。
        </text>
      </view>
    </view>

    <!-- 第二章 生效及其他 -->
    <view class="agreement-chapter">
      <text class="chapter-title">第二章 生效及其他</text>
      
      <view class="agreement-article">
        <text class="article-number">1.</text>
        <text class="article-content">
          甲方在乙方平台展示的页面上通过包括但不限于点击、勾选等方式结合签署本协议的行为，视为甲方不可撤销的同意并确认本协议的所有内容，本协议正式生效。
        </text>
      </view>

      <view class="agreement-article">
        <text class="article-number">2.</text>
        <text class="article-content">
          本协议有效期自本协议生效之日起至甲方注销乙方运营的借款服务平台账户之日为止。
        </text>
      </view>

      <view class="agreement-article">
        <text class="article-number">3.</text>
        <text class="article-content">
          因本协议引起的一切争议，由双方当事人协商解决;协商不成，任意一方可将争议提交至乙方所在地有管辖权的人民法院诉讼解决。
        </text>
      </view>
    </view>

    <!-- 第三章 电子签名风险揭示 -->
    <view class="agreement-chapter">
      <text class="chapter-title">第三章 电子签名风险揭示</text>
      
      <view class="agreement-article">
        <text class="article-content">
          甲方已详细阅读本协议，认识到由于使用电子签名方式签署合同，是指数据电文中以电子形式所含所附用于识别签名人身份并表明签名人认可其中内容的数据的方式签署合同，因此电子签名除具有以书面方式签署合同的所有风险外，还充分了解和认识到其具有以下风险:
        </text>
        <view class="risk-list">
          <text class="risk-item">1.委托人密码泄露或委托人身份可能被仿冒;</text>
          <text class="risk-item">2.由于互联网上存在黑客恶意攻击的可能性，互联网服务器可能会出现故障及其他不可预测的因素合同签署信息可能会出现错误或延迟;</text>
          <text class="risk-item">3.委托人的上网设备及软件系统与所提供的网上交易系统不相匹配，无法签署合同或合同签署失败;</text>
          <text class="risk-item">4.如委托人不具备一定网上交易经验，可能因操作不当造成无法签署合同或合同签署失败;</text>
          <text class="risk-item">5.委托人电脑系统感染电脑病毒或被非法入侵。</text>
        </view>
        <text class="article-content">
          上述风险可能会导致委托人(甲方)发生损失，甲方已经完全知晓并自愿承担上述风险，且承诺不因此而追究乙方的任何责任。
        </text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ElectronicSignatureAgreement'
}
</script>

<style scoped>
.legal-agreement-container {
  padding: 40rpx;
  font-size: 32rpx;
  line-height: 1.8;
  color: #333;
  background-color: #fff;
}

.agreement-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.agreement-title {
  font-size: 44rpx;
  font-weight: bold;
  display: block;
}

.parties-info {
  margin-bottom: 40rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.party-title {
  width: 40%;
  font-weight: bold;
}

.party-name, .party-id {
  width: 60%;
}

.agreement-intro {
  margin-bottom: 40rpx;
  text-align: justify;
}

.agreement-chapter {
  margin-bottom: 50rpx;
  padding-bottom: 30rpx;
  border-bottom: 1px solid #eee;
}

.chapter-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 30rpx;
  color: #222;
  border-left: 10rpx solid #258ceb;
  padding-left: 20rpx;
}

.agreement-article {
  margin-bottom: 30rpx;
}

.article-number {
  font-weight: bold;
  margin-right: 10rpx;
}

.article-content {
  display: block;
  text-align: justify;
}

.risk-list {
  margin-left: 40rpx;
  margin-top: 20rpx;
}

.risk-item {
  display: block;
  margin-bottom: 15rpx;
}

.agreement-footer {
  margin-top: 60rpx;
  padding-top: 30rpx;
  border-top: 1px solid #eee;
  display: flex;
  flex-direction: column;
}

.signature-line, .date-line {
  border-bottom: 1px solid #333;
  width: 200rpx;
  margin: 0 20rpx;
}

/* 响应式设计 */
@media screen and (max-width: 768rpx) {
  .legal-agreement-container {
    padding: 30rpx;
    font-size: 28rpx;
  }
  
  .agreement-title {
    font-size: 40rpx;
  }
  
  .chapter-title {
    font-size: 32rpx;
  }
  
  .risk-item {
    font-size: 26rpx;
  }
}
</style>