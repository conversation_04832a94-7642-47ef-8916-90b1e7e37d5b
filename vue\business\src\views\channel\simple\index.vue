<template>
  <div class="channel-simple-page">
    <div class="page-header">
      <h3>渠道管理</h3>
      <p>管理系统中的所有渠道信息，包括渠道配置、状态管理和放款规则设置。</p>
    </div>
    
    <!-- 搜索区域 -->
    <div class="search-section">
      <a-card title="搜索筛选" :bordered="false">
        <a-form layout="inline" class="search-form">
          <a-form-item label="渠道名称">
            <a-input 
              v-model:value="searchForm.channel_name" 
              placeholder="请输入渠道名称" 
              style="width: 200px"
            />
          </a-form-item>
          <a-form-item label="渠道编码">
            <a-input 
              v-model:value="searchForm.channel_code" 
              placeholder="请输入渠道编码" 
              style="width: 200px"
            />
          </a-form-item>
          <a-form-item label="渠道状态">
            <a-select 
              v-model:value="searchForm.channel_status" 
              placeholder="请选择状态"
              style="width: 120px"
              allow-clear
            >
              <a-option :value="1">启用</a-option>
              <a-option :value="0">禁用</a-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleSearch">
                查询
              </a-button>
              <a-button @click="handleReset">
                重置
              </a-button>
              <a-button type="primary" @click="handleCreate">
                新增渠道
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <a-card title="渠道列表" :bordered="false">
        <a-table
          :columns="columns"
          :data-source="tableData"
          :pagination="pagination"
          :loading="loading"
          row-key="id"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'channel_status'">
              <a-tag :color="record.channel_status === 1 ? 'green' : 'red'">
                {{ record.channel_status === 1 ? '启用' : '禁用' }}
              </a-tag>
            </template>
            
            <template v-else-if="column.key === 'channel_usage'">
              <a-tag :color="record.channel_usage === 1 ? 'blue' : 'default'">
                {{ record.channel_usage === 1 ? '使用中' : '未使用' }}
              </a-tag>
            </template>
            
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small">查看</a-button>
                <a-button type="link" size="small">编辑</a-button>
                <a-button type="link" size="small">规则</a-button>
                <a-button type="link" size="small" danger>删除</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 渠道详情弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      width="800px"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="渠道名称" required>
              <a-input v-model:value="formData.channel_name" placeholder="请输入渠道名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="渠道编码">
              <a-input-group compact>
                <a-input 
                  v-model:value="formData.channel_code" 
                  placeholder="请输入或自动生成"
                  style="width: calc(100% - 80px)"
                />
                <a-button style="width: 80px" @click="generateCode">生成</a-button>
              </a-input-group>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="访问域名">
              <a-input v-model:value="formData.domain" placeholder="请输入域名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系手机">
              <a-input v-model:value="formData.mobile" placeholder="请输入手机号" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="渠道状态">
              <a-select v-model:value="formData.channel_status" placeholder="请选择状态">
                <a-option :value="1">启用</a-option>
                <a-option :value="0">禁用</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="使用状态">
              <a-select v-model:value="formData.channel_usage" placeholder="请选择状态">
                <a-option :value="1">使用中</a-option>
                <a-option :value="0">未使用</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="备注信息">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
// 导入必要的组合式API
const searchForm = reactive({
  channel_name: '',
  channel_code: '',
  channel_status: undefined,
});

const formData = reactive({
  channel_name: '',
  channel_code: '',
  domain: '',
  mobile: '',
  channel_status: 1,
  channel_usage: 1,
  remark: '',
});

const tableData = ref([
  {
    id: 1,
    channel_name: '测试渠道1',
    channel_code: 'TC001',
    domain: 'https://example1.com',
    mobile: '13800138001',
    channel_status: 1,
    channel_usage: 1,
    create_time: '2024-01-01 10:00:00',
  },
  {
    id: 2,
    channel_name: '测试渠道2',
    channel_code: 'TC002',
    domain: 'https://example2.com',
    mobile: '13800138002',
    channel_status: 0,
    channel_usage: 0,
    create_time: '2024-01-02 10:00:00',
  },
]);

const loading = ref(false);
const modalVisible = ref(false);
const modalTitle = ref('新增渠道');

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 2,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`,
});

const columns = [
  {
    title: '渠道名称',
    dataIndex: 'channel_name',
    key: 'channel_name',
  },
  {
    title: '渠道编码',
    dataIndex: 'channel_code',
    key: 'channel_code',
  },
  {
    title: '访问域名',
    dataIndex: 'domain',
    key: 'domain',
  },
  {
    title: '联系手机',
    dataIndex: 'mobile',
    key: 'mobile',
  },
  {
    title: '渠道状态',
    dataIndex: 'channel_status',
    key: 'channel_status',
  },
  {
    title: '使用状态',
    dataIndex: 'channel_usage',
    key: 'channel_usage',
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    key: 'create_time',
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
  },
];

// 搜索
const handleSearch = () => {
  console.log('搜索参数:', searchForm);
  // TODO: 实际搜索逻辑
};

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'channel_status' ? undefined : '';
  });
};

// 新增渠道
const handleCreate = () => {
  modalTitle.value = '新增渠道';
  // 重置表单
  Object.keys(formData).forEach(key => {
    formData[key] = key === 'channel_status' ? 1 : key === 'channel_usage' ? 1 : '';
  });
  modalVisible.value = true;
};

// 生成渠道编码
const generateCode = () => {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).slice(-4).toUpperCase();
  formData.channel_code = `CH${timestamp}${random}`;
};

// 表格变化
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  handleSearch();
};

// 弹窗确认
const handleModalOk = () => {
  console.log('表单数据:', formData);
  // TODO: 提交逻辑
  modalVisible.value = false;
};

// 弹窗取消
const handleModalCancel = () => {
  modalVisible.value = false;
};
</script>

<style scoped lang="less">
.channel-simple-page {
  padding: 16px;
  
  .page-header {
    margin-bottom: 24px;
    
    h3 {
      margin-bottom: 8px;
      font-size: 24px;
      font-weight: 600;
      color: #1a1a1a;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }
  
  .search-section {
    margin-bottom: 16px;
    
    .search-form {
      .ant-form-item {
        margin-bottom: 16px;
      }
    }
  }
  
  .table-section {
    .ant-table {
      .ant-table-tbody > tr > td {
        padding: 12px 16px;
      }
    }
  }
}
</style> 