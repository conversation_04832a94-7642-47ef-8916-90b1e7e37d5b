<template>
  <view class="auth-agreement-container">
    <!-- 协议头部 -->
    <view class="agreement-header">
      <text class="agreement-subtitle">《实名认证服务协议》</text>
      <text class="agreement-note">(以下简称:本协议)</text>
    </view>

    <!-- 协议简介 -->
    <view class="agreement-intro">
      <text>本协议是江西致而远网络科技有限公司与用户(以下简称"您")所订立的有效合约。请您先仔细阅读本协议内容。如您对本协议内容有疑问，请勿进行下一步操作，如您通过页面点击的方式确认本协议即表示您已同意本协议。</text>
    </view>

    <!-- 协议正文 -->
    <view class="agreement-content">
      <!-- 第一部分：定义 -->
      <view class="agreement-section">
        <text class="section-title">一、定义</text>
        <text class="section-content">
          实名认证服务:本服务由e签宝和江西致而远网络科技有限公司提供技术支持，通过手机号核验、身份信息核验、人脸核验等方式确认您身份的真实性，并将认证结果反馈给悦心易的服务。
        </text>
      </view>

      <!-- 第二部分：授权与许可 -->
      <view class="agreement-section">
        <text class="section-title">二、授权与许可</text>
        <view class="subsection">
          <text class="subsection-content">
            1. 为了向您提供认证服务，我们需要将您填写的"姓名、身份证号、手机号"提供给江西致而远网络科技有限公司和e签宝。
          </text>
        </view>
        <view class="subsection">
          <text class="subsection-content">
            2. 为了准确核验您的身份，尽力防止您的身份被冒用，您将授权e签宝人脸核身SDK采集您的设备信息以验证您在进行认证时是否处于可信环境并收集您的人脸照片。
          </text>
        </view>
        <view class="subsection">
          <text class="subsection-content">
            3. 点击"我已阅读《实名认证服务协议》并同意本次授权"即视为您同意并授权使用你所提交的个人身份信息及人脸影像数据用于实现验证身份功能。
          </text>
        </view>
        <view class="subsection">
          <text class="subsection-content">
            4. 在使用到身份证号时，会另外征得您的同意和授权;在使用到人脸信息时，会另外征得您的同意和授权。授权均仅当次有效，下次使用到对应功能时，会再次征得您的同意和授权。
          </text>
        </view>
      </view>

      <!-- 第三部分：您的权利义务 -->
      <view class="agreement-section">
        <text class="section-title">三、您的权利义务</text>
        <view class="subsection">
          <text class="subsection-content">
            1. 目前，悦心易就本服务不收取任何服务费，如后续需要收费，我们将按照法律法规的相关要求提前通过公告、客户端通知、短信等形式告知您收费时间及收费标准。
          </text>
        </view>
        <view class="subsection">
          <text class="subsection-content">
            2. 您不得将本服务用于中国法律法规所禁止或限制、以及违背道德风俗的领域。否则因您导致悦心易、江西致而远网络科技有限公司、e签宝遭受损失的，您应承担相应赔偿责任。
          </text>
        </view>
      </view>

      <!-- 第四部分：悦心易的权利义务 -->
      <view class="agreement-section">
        <text class="section-title">四、悦心易的权利义务</text>
        <view class="subsection">
          <text class="subsection-content">
            1. 悦心易应按本协议约定向您提供本服务。
          </text>
        </view>
        <view class="subsection">
          <text class="subsection-content">
            2. 我们会根据认证技术的发展及市场风险环境的需要，不断完善本服务相应的内容及形式。
          </text>
        </view>
      </view>

      <!-- 第五部分：其他 -->
      <view class="agreement-section">
        <text class="section-title">五、其他</text>
        <text class="section-content">
          本协议之效力、解释、变更、执行与争议解决均适用中华人民共和国法律。本协议项下纠纷，双方应努力友好协商解决，如协商不成，均应依照中华人民共和国法律予以处理，并由被告住所地人民法院管辖。
        </text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AuthAgreement'
}
</script>

<style scoped>
.auth-agreement-container {
  padding: 40rpx;
  font-size: 30rpx;
  line-height: 1.8;
  color: #333;
  background-color: #f9f9f9;
}

.agreement-header {
  margin-bottom: 40rpx;
  text-align: center;
}

.agreement-title {
  font-size: 44rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
  color: #222;
}

.agreement-subtitle {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
  color: #222;
}

.agreement-note {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.agreement-intro {
  margin-bottom: 40rpx;
  text-align: justify;
}

.agreement-intro text {
  display: block;
  margin-bottom: 20rpx;
}

.agreement-content {
  margin-bottom: 60rpx;
}

.agreement-section {
  margin-bottom: 50rpx;
  padding-bottom: 30rpx;
  border-bottom: 1px dashed #eee;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 30rpx;
  color: #222;
  border-left: 10rpx solid #258ceb;
  padding-left: 20rpx;
}

.section-content {
  display: block;
  margin-bottom: 30rpx;
  text-align: justify;
}

.subsection {
  margin-left: 30rpx;
  margin-bottom: 30rpx;
}

.subsection-content {
  display: block;
  margin-bottom: 20rpx;
  text-align: justify;
}

.agreement-footer {
  text-align: right;
  margin-top: 50rpx;
  padding-top: 30rpx;
  border-top: 1px solid #eee;
  font-size: 28rpx;
  color: #666;
}

.footer-date {
  font-size: 26rpx;
  margin-top: 10rpx;
  color: #999;
}

/* 响应式设计 */
@media screen and (max-width: 768rpx) {
  .auth-agreement-container {
    padding: 30rpx;
    font-size: 28rpx;
  }
  
  .agreement-title {
    font-size: 40rpx;
  }
  
  .section-title {
    font-size: 32rpx;
  }
  
  .subsection-content {
    font-size: 28rpx;
  }
}
</style>