{"level":"dev.info","ts":"[2025-08-14 09:36:24.799]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.801]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.801]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.801]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.802]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.804]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.804]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.804]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.804]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.804]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.804]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.804]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.787]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.788]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.788]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.788]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.789]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.790]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.790]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.790]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.790]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.790]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.790]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"scheduler/manager.go:196","msg":"配置为等待正在运行的任务完成"}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":60}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"24.1秒"}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.065]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.066]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.066]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.066]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.067]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.068]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.069]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.303]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.303]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.304]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.304]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.305]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.305]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.305]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.305]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.305]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.306]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.306]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.306]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.306]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.306]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.306]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.306]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.306]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.307]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.308]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.308]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.308]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.308]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.309]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.309]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.309]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.309]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.309]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.259]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.2580494,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.259]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.2580494,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.259]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.259]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.778]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":2,"duration":0.777116,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.778]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.7754087,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.778]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.778]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.779]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.777191,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.779]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.7776981,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.779]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.779]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.781]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.7800727,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.781]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.781]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":4,"duration":0.7784228,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:40:00.781]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.520]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.520]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.520]","caller":"scheduler/manager.go:191","msg":"配置为强制取消正在运行的任务"}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.520]","caller":"scheduler/manager.go:219","msg":"没有正在运行的任务需要取消"}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.520]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":30}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.520]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.521]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"54.2秒"}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.521]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.686]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.688]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.688]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.689]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.691]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.691]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.692]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.693]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.693]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.693]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.694]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.694]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.694]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.694]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.694]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.694]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.695]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.695]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.695]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.695]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.695]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.696]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.696]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.696]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.696]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.696]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.697]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.697]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.697]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.697]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.024]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0238934,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.024]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.058]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":6,"duration":0.0579482,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.058]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.072]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0718154,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.073]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.073]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":5,"duration":0.0729312,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:41:00.073]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.428]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.428]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.429]","caller":"scheduler/manager.go:191","msg":"配置为强制取消正在运行的任务"}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.429]","caller":"scheduler/manager.go:219","msg":"没有正在运行的任务需要取消"}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.429]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":30}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.429]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.429]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"55.7秒"}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.429]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.037]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":7,"duration":0.0363297,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.037]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.037]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":8,"duration":0.0357122,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.037]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.044]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":9,"duration":0.0418361,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.044]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.044]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":10,"duration":0.0412637,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:42:00.044]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.259]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.260]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.260]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.260]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.264]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.266]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.267]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.268]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.268]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.268]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.268]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.268]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.268]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.268]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.269]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.269]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.269]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.269]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:43:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:43:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:43:00.027]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0272155,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:43:00.027]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:43:00.049]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0485711,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:43:00.049]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.116]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.1166747,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.116]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.120]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":6,"duration":0.1196154,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.120]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.120]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.1206256,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.120]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":0.1201211,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.120]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:00.120]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"scheduler/manager.go:191","msg":"配置为强制取消正在运行的任务"}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"scheduler/manager.go:219","msg":"没有正在运行的任务需要取消"}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":30}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"1分钟 33秒"}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.875]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.876]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.876]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.876]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.877]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.878]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.878]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.878]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.878]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.878]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.878]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.370]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.371]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.371]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.371]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.372]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.372]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.373]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.374]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.374]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.374]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.353]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.354]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.354]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.354]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.355]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.356]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.356]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.356]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.016]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0160667,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.016]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.016]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0165708,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.016]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.064]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.065]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.065]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.065]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.080]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.0155957,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.080]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.108]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":6,"duration":0.0429105,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.108]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.108]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":5,"duration":0.0434487,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.108]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.108]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0434487,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.108]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:49:00.002]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:49:00.002]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:49:00.014]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":8,"duration":0.0116054,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:49:00.014]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:49:00.014]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":7,"duration":0.0125942,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:49:00.014]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.923]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.924]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.924]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.924]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.925]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.029]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.0290225,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.029]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.043]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0432369,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.043]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.043]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":2,"duration":0.0432369,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.043]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.048]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0464616,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.048]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:51:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:51:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:51:00.041]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":6,"duration":0.0411344,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:51:00.041]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:51:00.042]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":0.0418292,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:51:00.042]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.081]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":10,"duration":0.0809701,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.081]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.098]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":9,"duration":0.097785,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.098]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.106]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":8,"duration":0.10601,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.106]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":7,"duration":0.10601,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.106]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:52:00.106]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:53:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:53:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:53:00.052]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":12,"duration":0.0521,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:53:00.052]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:53:00.054]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":11,"duration":0.053853,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:53:00.054]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.054]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":14,"duration":0.0542266,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.054]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.064]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":16,"duration":0.0630952,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.064]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.066]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":13,"duration":0.065303,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.066]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.066]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":15,"duration":0.065303,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:54:00.066]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:55:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:55:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:55:00.077]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":18,"duration":0.0762922,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:55:00.077]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:55:00.077]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":17,"duration":0.0767995,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:55:00.077]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.069]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":19,"duration":0.0689594,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.069]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.074]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":21,"duration":0.0738538,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.074]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.081]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":20,"duration":0.0812061,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.081]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.081]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":22,"duration":0.0807004,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:56:00.081]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:57:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:57:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:57:00.082]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":24,"duration":0.0818799,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:57:00.082]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:57:00.082]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":23,"duration":0.0818799,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 09:57:00.082]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.261]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.262]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.263]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.263]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.265]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.092]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.092]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.092]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.092]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.094]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.116]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.118]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.118]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.118]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.119]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.120]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.121]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.121]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.121]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.121]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.121]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.121]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.121]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.122]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.122]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.122]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.122]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.122]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.001]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"bill-notice-overdue"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.017]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0166756,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.017]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.018]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.0171805,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.018]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.018]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0160614,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.018]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.046]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":6,"duration":0.0440972,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.046]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.047]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"bill-notice-overdue","job_id":5,"duration":0.0451209,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.047]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"bill-notice-overdue"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.133]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"channel_statistics_task","job_id":1,"duration":0.1322637,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.133]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"channel_statistics_task"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.586]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.587]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.587]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.587]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.588]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.589]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 10:11:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:11:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:11:00.025]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0250069,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:11:00.025]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:11:00.025]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0250069,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:11:00.025]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.0534872,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":5,"duration":0.052975,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":6,"duration":0.052975,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":4,"duration":0.0534872,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:12:00.053]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:13:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:13:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:13:00.048]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":7,"duration":0.0481792,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:13:00.048]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:13:00.052]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":8,"duration":0.0520148,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:13:00.052]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.149]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":10,"duration":0.1491443,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.149]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.150]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":11,"duration":0.1501586,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.150]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.157]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":12,"duration":0.1561195,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.157]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.157]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":9,"duration":0.1567083,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:14:00.157]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.550]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.551]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.551]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.551]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.553]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.554]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.554]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.554]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.554]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.554]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.554]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.554]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.555]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.555]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.555]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.555]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 10:57:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:57:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:57:00.030]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0304038,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:57:00.030]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:57:00.031]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0309062,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:57:00.031]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:58:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:58:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:58:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:58:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:58:00.048]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":4,"duration":0.0478868,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:58:00.048]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:58:00.048]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":3,"duration":0.0478868,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:58:00.048]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:58:00.054]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":5,"duration":0.0539843,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:58:00.054]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:58:00.054]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":6,"duration":0.0529016,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:58:00.054]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:59:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:59:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:59:00.039]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":7,"duration":0.0390889,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:59:00.039]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 10:59:00.041]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":8,"duration":0.0406801,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 10:59:00.041]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.889]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.889]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.889]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.889]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.891]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.892]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.892]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.892]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.892]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.892]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.892]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.892]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.892]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.173]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.174]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.174]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.174]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.175]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.176]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.176]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.176]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.176]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.176]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.176]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.177]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.177]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.177]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.177]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.177]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:03:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:03:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:03:00.018]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.018036,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:03:00.019]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:03:00.019]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0186499,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:03:00.019]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.982]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.983]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.983]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.983]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.985]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.986]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.986]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.986]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.986]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.986]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.986]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.986]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.986]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.986]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.986]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.986]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.986]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.039]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.040]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.040]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.040]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.042]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.042]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.042]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.042]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.042]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.042]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.042]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.042]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.042]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.042]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.043]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.044]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.044]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.044]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.044]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.044]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.044]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.044]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.044]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:07:02.890]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:07:02.890]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:07:03.024]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.1334335,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:07:03.024]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:07:03.043]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.1529853,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:07:03.043]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:08:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:08:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:08:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:08:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:08:00.028]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.0278066,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:08:00.028]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:08:00.028]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0283107,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:08:00.028]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:08:00.028]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":5,"duration":0.0283107,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:08:00.028]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:08:00.054]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":6,"duration":0.0539557,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:08:00.054]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.236]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.237]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.237]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.237]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.238]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.238]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.238]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.238]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.238]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.238]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.238]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.238]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.238]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.239]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.240]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.240]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.240]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.240]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:09:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:09:00.001]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:09:00.029]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0281415,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:09:00.029]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:09:00.030]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0286677,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:09:00.030]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.509]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.509]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.509]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.509]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.512]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.513]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.513]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.513]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.513]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.514]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.514]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.514]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.514]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.514]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.514]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.514]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.514]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:11:00.457]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:11:00.457]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:11:00.526]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0696057,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:11:00.526]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:11:00.526]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0696057,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:11:00.526]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.543]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.544]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.544]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.544]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.545]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.546]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.546]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.546]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.546]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.546]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.546]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.546]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.546]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.546]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.546]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.546]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:18:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:18:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:18:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:18:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:18:00.087]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0861606,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:18:00.087]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:18:00.087]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":2,"duration":0.0868437,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:18:00.087]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:18:00.111]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.1109964,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:18:00.111]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:18:00.118]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":4,"duration":0.1174794,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:18:00.118]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:19:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:19:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:19:00.040]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":6,"duration":0.0403181,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:19:00.040]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:19:00.044]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":0.0448259,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:19:00.044]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:20:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:20:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:20:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:20:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:20:00.077]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":9,"duration":0.0768338,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:20:00.077]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:20:00.077]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":10,"duration":0.0763229,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:20:00.077]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:20:00.077]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":8,"duration":0.0768338,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:20:00.077]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:20:00.077]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":7,"duration":0.0768338,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:20:00.077]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.794]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.796]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.796]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.796]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.797]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.797]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.798]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.799]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.799]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.799]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.799]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.799]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.799]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.799]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.799]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.799]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.799]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.799]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:24:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:24:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:24:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:24:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:24:00.016]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0164505,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:24:00.016]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:24:00.050]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0504112,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:24:00.050]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:24:00.060]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":3,"duration":0.0606663,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:24:00.060]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:24:00.064]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":4,"duration":0.0631117,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:24:00.064]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:25:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:25:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:25:00.066]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":5,"duration":0.065884,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:25:00.066]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:25:00.066]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":6,"duration":0.065884,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:25:00.066]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.525]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.526]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.526]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.526]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.528]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.529]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.529]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.529]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.529]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.529]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.529]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.529]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.529]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:32:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:32:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:32:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:32:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:32:00.048]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0471963,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:32:00.048]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:32:00.048]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.0471963,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:32:00.048]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:32:00.074]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0731426,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:32:00.074]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:32:00.074]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0725284,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:32:00.074]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.023]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.023]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.024]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.024]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.025]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.026]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.026]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.026]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.026]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.026]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.026]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.026]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.026]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.027]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.027]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.027]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.027]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.963]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.966]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.966]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.966]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.968]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.969]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.969]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.969]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.969]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.969]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.969]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.969]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.969]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.969]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:52:00.017]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":2,"duration":0.0160679,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:52:00.017]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:52:00.028]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":1,"duration":0.027677,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:52:00.028]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:52:00.052]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":4,"duration":0.0510979,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:52:00.052]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:52:00.057]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":3,"duration":0.0563426,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:52:00.057]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.562]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.563]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.563]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.563]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.564]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.565]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.565]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.565]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.565]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.565]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.565]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.565]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.566]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.566]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.566]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.566]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.566]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:55:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:55:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:55:00.045]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0451587,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:55:00.045]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:55:00.046]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0451587,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:55:00.046]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:56:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:56:00.049]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":4,"duration":0.048764,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:56:00.049]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:56:00.056]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":3,"duration":0.0553802,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:56:00.056]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:56:00.068]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":6,"duration":0.0669983,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:56:00.068]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":5,"duration":0.0680074,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:56:00.068]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:56:00.068]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:57:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:57:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:57:00.110]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":8,"duration":0.1095501,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:57:00.110]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:57:00.110]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":7,"duration":0.1100572,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:57:00.110]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:58:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:58:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:58:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:58:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:58:00.063]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":12,"duration":0.0623661,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:58:00.063]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:58:00.070]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":9,"duration":0.0707098,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:58:00.070]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:58:00.071]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":10,"duration":0.0712445,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:58:00.071]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:58:00.071]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":11,"duration":0.0712445,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:58:00.071]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:59:08.788]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:59:08.789]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:59:08.834]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":14,"duration":0.0446595,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:59:08.834]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:59:08.834]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":13,"duration":0.0447409,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 11:59:08.834]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.264]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.266]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.266]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.266]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.268]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.268]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.268]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.268]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.268]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.268]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.268]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.268]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.268]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.268]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.268]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.268]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.269]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.269]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.269]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.269]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.269]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.269]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.269]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.269]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.269]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.269]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.269]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.269]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.269]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.269]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.269]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.270]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.270]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.270]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.270]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.270]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.270]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.270]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.270]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.270]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.612]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.613]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.613]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.613]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.614]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.614]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.614]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.614]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.615]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.616]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.616]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.616]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.616]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.616]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.616]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.616]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.616]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 14:21:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 14:21:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 14:21:00.047]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0476094,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 14:21:00.047]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 14:21:00.047]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0476094,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 14:21:00.047]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.287]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.288]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.288]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.288]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.292]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.292]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.292]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.292]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.292]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.292]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.292]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.292]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.293]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.293]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.293]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.293]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.293]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.293]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.293]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.293]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.293]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.293]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.294]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.294]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.294]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.294]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.294]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.294]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.294]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.294]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.294]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.295]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.295]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.295]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.295]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.295]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.295]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.295]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.295]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.295]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 16:22:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:22:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:22:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:22:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:22:00.024]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":1,"duration":0.0237113,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 16:22:00.024]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:22:00.039]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":4,"duration":0.0371735,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 16:22:00.039]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:22:00.040]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.0387565,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 16:22:00.040]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0392935,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 16:22:00.040]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:22:00.040]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:22:10.430]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 16:22:10.430]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-14 16:22:10.430]","caller":"scheduler/manager.go:191","msg":"配置为强制取消正在运行的任务"}
{"level":"dev.info","ts":"[2025-08-14 16:22:10.430]","caller":"scheduler/manager.go:219","msg":"没有正在运行的任务需要取消"}
{"level":"dev.info","ts":"[2025-08-14 16:22:10.430]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":30}
{"level":"dev.info","ts":"[2025-08-14 16:22:10.430]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-14 16:22:10.430]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"1分钟 3秒"}
{"level":"dev.info","ts":"[2025-08-14 16:22:10.430]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.836]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.837]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.837]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.837]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.839]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.839]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.839]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.839]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.839]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.839]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.839]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.839]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.839]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.839]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.839]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.839]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.839]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.840]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.840]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.840]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.840]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.840]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.840]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.840]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.840]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.840]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.840]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.840]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.840]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.841]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.841]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.841]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.841]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.841]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.841]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.841]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.842]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.842]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.842]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.842]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 16:28:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:28:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:28:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:28:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:28:00.040]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":1,"duration":0.0396824,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 16:28:00.040]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:28:00.055]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":4,"duration":0.0534625,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 16:28:00.055]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:28:00.056]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":2,"duration":0.0552021,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 16:28:00.056]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.0557752,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 16:28:00.056]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:28:00.056]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:28:18.511]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 16:28:18.511]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-14 16:28:18.511]","caller":"scheduler/manager.go:191","msg":"配置为强制取消正在运行的任务"}
{"level":"dev.info","ts":"[2025-08-14 16:28:18.511]","caller":"scheduler/manager.go:219","msg":"没有正在运行的任务需要取消"}
{"level":"dev.info","ts":"[2025-08-14 16:28:18.511]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":30}
{"level":"dev.info","ts":"[2025-08-14 16:28:18.511]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-14 16:28:18.511]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"38.7秒"}
{"level":"dev.info","ts":"[2025-08-14 16:28:18.511]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.083]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.084]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.084]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.084]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.085]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.086]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.086]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.086]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.086]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.086]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.086]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.086]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.086]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.086]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.087]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.087]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.087]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.020]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.021]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.021]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.021]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.023]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.023]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.023]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.023]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.023]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.023]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.025]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.025]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.025]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.025]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.025]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.025]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.025]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.025]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.025]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.026]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.026]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.026]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.026]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.026]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.026]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.026]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.026]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.026]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.027]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.027]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.027]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.027]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.028]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.028]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.028]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.028]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.028]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.028]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.028]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.029]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 16:45:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:45:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:45:00.015]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0158647,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 16:45:00.017]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:45:00.019]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0193971,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 16:45:00.019]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:46:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:46:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:46:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:46:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:46:00.039]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0387776,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 16:46:00.039]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:46:00.039]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":0.0382674,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 16:46:00.040]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:46:00.051]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":6,"duration":0.0495506,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 16:46:00.051]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:46:00.051]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.0505774,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 16:46:00.051]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 16:46:44.591]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 16:46:44.591]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-14 16:46:44.591]","caller":"scheduler/manager.go:191","msg":"配置为强制取消正在运行的任务"}
{"level":"dev.info","ts":"[2025-08-14 16:46:44.591]","caller":"scheduler/manager.go:219","msg":"没有正在运行的任务需要取消"}
{"level":"dev.info","ts":"[2025-08-14 16:46:44.592]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":30}
{"level":"dev.info","ts":"[2025-08-14 16:46:44.592]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-14 16:46:44.592]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"2分钟 25秒"}
{"level":"dev.info","ts":"[2025-08-14 16:46:44.593]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.091]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.092]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.092]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.092]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.093]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.093]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.093]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.094]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.095]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.095]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.095]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.095]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.095]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.096]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.096]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.096]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.096]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.096]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.096]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.096]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 16:51:43.412]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 16:51:43.412]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-14 16:51:43.413]","caller":"scheduler/manager.go:191","msg":"配置为强制取消正在运行的任务"}
{"level":"dev.info","ts":"[2025-08-14 16:51:43.413]","caller":"scheduler/manager.go:219","msg":"没有正在运行的任务需要取消"}
{"level":"dev.info","ts":"[2025-08-14 16:51:43.413]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":30}
{"level":"dev.info","ts":"[2025-08-14 16:51:43.413]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-14 16:51:43.413]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"42.3秒"}
{"level":"dev.info","ts":"[2025-08-14 16:51:43.413]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.525]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.526]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.526]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.526]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.528]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.528]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.529]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.529]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.529]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.529]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.529]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.530]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.530]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.530]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.530]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.530]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.530]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.531]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.531]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.531]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.531]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.531]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.531]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.531]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.531]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.531]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.532]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.532]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.533]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.533]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.533]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.533]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.534]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.534]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.534]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.534]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.535]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.535]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.535]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.535]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 17:10:00.001]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:10:00.001]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:10:00.001]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:10:00.001]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:10:00.033]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":2,"duration":0.0316355,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:10:00.034]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:10:00.064]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0625731,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:10:00.064]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:10:00.065]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.0620475,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:10:00.065]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":4,"duration":0.0620475,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:10:00.065]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:10:00.065]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:10:14.046]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 17:10:14.046]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-14 17:10:14.046]","caller":"scheduler/manager.go:191","msg":"配置为强制取消正在运行的任务"}
{"level":"dev.info","ts":"[2025-08-14 17:10:14.046]","caller":"scheduler/manager.go:219","msg":"没有正在运行的任务需要取消"}
{"level":"dev.info","ts":"[2025-08-14 17:10:14.046]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":30}
{"level":"dev.info","ts":"[2025-08-14 17:10:14.046]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-14 17:10:14.046]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"35.5秒"}
{"level":"dev.info","ts":"[2025-08-14 17:10:14.047]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.447]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.448]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.448]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.448]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.451]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.451]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.451]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.451]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.451]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.452]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.452]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.452]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.452]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.452]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.452]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.452]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.452]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.452]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.452]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.452]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.452]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.453]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.453]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.453]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.453]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.453]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.453]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.453]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.453]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.453]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.454]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.454]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.454]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.454]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.454]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.454]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.454]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.455]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.455]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.455]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 17:29:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:29:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:29:00.020]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0191854,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:29:00.020]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:29:00.067]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0666737,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:29:00.068]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:29:13.039]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 17:29:13.039]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-14 17:29:13.040]","caller":"scheduler/manager.go:191","msg":"配置为强制取消正在运行的任务"}
{"level":"dev.info","ts":"[2025-08-14 17:29:13.041]","caller":"scheduler/manager.go:219","msg":"没有正在运行的任务需要取消"}
{"level":"dev.info","ts":"[2025-08-14 17:29:13.041]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":30}
{"level":"dev.info","ts":"[2025-08-14 17:29:13.041]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-14 17:29:13.041]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"38.6秒"}
{"level":"dev.info","ts":"[2025-08-14 17:29:13.041]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.800]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.800]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.800]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.800]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.802]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.803]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.803]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.803]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.803]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 17:35:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:35:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:35:00.025]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0252044,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:35:00.025]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:35:00.026]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0257234,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:35:00.026]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:36:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:36:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:36:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:36:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:36:00.061]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":4,"duration":0.0614639,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:36:00.061]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:36:00.061]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":5,"duration":0.0614639,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:36:00.061]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:36:00.071]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":3,"duration":0.0709715,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:36:00.071]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:36:00.071]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":6,"duration":0.0704595,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:36:00.071]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.503]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.505]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.505]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.505]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.507]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.507]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.507]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.507]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.507]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.507]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.508]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.508]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.508]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.508]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.508]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.508]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.508]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.508]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.509]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.509]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.509]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.509]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.509]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.509]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.510]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.510]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.510]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.510]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.510]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.510]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.510]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.510]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.511]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.511]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.511]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.511]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.511]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.511]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.511]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.511]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 17:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:40:00.023]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.022006,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:40:00.024]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:40:00.054]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.053983,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:40:00.054]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:40:00.058]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0562816,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:40:00.058]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:40:00.059]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":2,"duration":0.0596066,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:40:00.059]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:40:14.657]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 17:40:14.657]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-14 17:40:14.658]","caller":"scheduler/manager.go:191","msg":"配置为强制取消正在运行的任务"}
{"level":"dev.info","ts":"[2025-08-14 17:40:14.658]","caller":"scheduler/manager.go:219","msg":"没有正在运行的任务需要取消"}
{"level":"dev.info","ts":"[2025-08-14 17:40:14.658]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":30}
{"level":"dev.info","ts":"[2025-08-14 17:40:14.658]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-14 17:40:14.658]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"38.1秒"}
{"level":"dev.info","ts":"[2025-08-14 17:40:14.658]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.423]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.426]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.426]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.426]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.428]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.429]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.429]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.429]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.429]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.429]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.429]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.429]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.429]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.429]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.429]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 17:41:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:41:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:41:00.030]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.0303471,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:41:00.030]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:41:00.030]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0303471,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:41:00.031]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.237]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.238]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.238]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.238]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.239]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.239]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.239]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.240]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.240]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.240]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.240]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.240]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.240]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.240]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.240]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.240]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.240]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.241]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.241]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.241]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.241]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.241]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.242]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.242]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.242]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.242]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.242]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.242]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.242]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.242]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.242]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.242]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.243]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.243]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.243]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.243]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.243]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.243]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.243]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.243]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 17:46:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:46:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:46:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:46:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:46:00.013]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":4,"duration":0.012874,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:46:00.014]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:46:00.019]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.0184395,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:46:00.019]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0189794,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:46:00.019]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":1,"duration":0.0195697,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:46:00.019]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:46:00.019]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:46:00.019]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:46:02.715]","caller":"engine/scheduler.go:99","msg":"正在停止调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 17:46:02.716]","caller":"engine/scheduler.go:117","msg":"调度引擎已停止"}
{"level":"dev.info","ts":"[2025-08-14 17:46:02.717]","caller":"scheduler/manager.go:191","msg":"配置为强制取消正在运行的任务"}
{"level":"dev.info","ts":"[2025-08-14 17:46:02.717]","caller":"scheduler/manager.go:219","msg":"没有正在运行的任务需要取消"}
{"level":"dev.info","ts":"[2025-08-14 17:46:02.717]","caller":"scheduler/manager.go:249","msg":"等待任务完成","配置超时时间：":30}
{"level":"dev.info","ts":"[2025-08-14 17:46:02.717]","caller":"scheduler/manager.go:257","msg":"所有任务已完成"}
{"level":"dev.info","ts":"[2025-08-14 17:46:02.718]","caller":"scheduler/manager.go:208","msg":"调度器管理器已停止","uptime":"40.5秒"}
{"level":"dev.info","ts":"[2025-08-14 17:46:02.718]","caller":"scheduler/scheduler.go:100","msg":"任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.101]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.102]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.102]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.102]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.107]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.108]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.108]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.108]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.108]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.108]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.108]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.109]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.109]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.109]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.110]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.110]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.110]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.110]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.111]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.111]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.111]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.111]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 17:48:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:48:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:48:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:48:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:48:00.019]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":1,"duration":0.018855,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:48:00.020]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:48:00.020]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.0193625,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:48:00.020]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:48:00.020]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0193113,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:48:00.020]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:48:00.021]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.0204677,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:48:00.021]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:49:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:49:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:49:00.040]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":6,"duration":0.0401891,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:49:00.040]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:49:00.040]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":5,"duration":0.0401891,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 17:49:00.040]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.863]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.864]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.864]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.864]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.867]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.868]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.868]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.868]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.868]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.868]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.868]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.868]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.869]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.869]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.869]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.869]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.869]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.870]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.870]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.870]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.870]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.221]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.223]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler","timezone":"Asia/Shanghai","max_concurrent_jobs":10}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.223]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.223]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 * * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.225]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.226]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.226]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.226]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.226]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.226]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.226]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.226]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 * * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.226]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.227]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.227]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.227]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.227]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-14 18:01:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 18:01:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 18:01:00.121]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.1206569,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 18:01:00.121]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 18:01:00.122]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.1217476,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 18:01:00.122]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 18:02:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 18:02:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 18:02:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 18:02:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 18:02:00.134]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":5,"duration":0.1338406,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 18:02:00.134]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 18:02:00.134]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":3,"duration":0.1338406,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 18:02:00.134]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-14 18:02:00.134]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":4,"duration":0.1338406,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 18:02:00.134]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-14 18:02:00.201]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":6,"duration":0.2008111,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-14 18:02:00.201]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
