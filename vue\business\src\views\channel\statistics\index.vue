<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { IconRefresh, IconSearch } from '@arco-design/web-vue/es/icon';
import { Message } from '@arco-design/web-vue';
import { getChannelStatistics } from '@/api/channel';
import type { ChannelStatisticsItem } from '@/api/channel';

const queryFormRef = ref();
const queryForm = reactive({
  date: formattedDate()
})
const loading = ref(false);
function formattedDate() {
  const today = new Date()
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}
onMounted(() => {


  fetchData();
})
// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
});

const tableData = ref<ChannelStatisticsItem[]>([]);

async function fetchData() {
  try {
    loading.value = true;
    const params = {
      ...queryForm,
      page: pagination.current,
      pageSize: pagination.pageSize,
    };
    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === undefined || params[key] === null) {
        delete params[key];
      }
    });
    const response = await getChannelStatistics(params);
    if (response && response.data) {
      const { data: listData, total: totalCount } = response;
      tableData.value = Array.isArray(listData) ? listData : [];
      pagination.total = totalCount || 0;
    } else {
      console.error('响应格式错误:', response);
      tableData.value = [];
      pagination.total = 0;
    }

  }catch (error) {
    console.error('获取渠道列表失败:', error);
    Message.error('获取渠道列表失败');
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }

}
// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};
// 重置
const handleReset = () => {
  queryFormRef.value?.resetFields();
  fetchData();
};
// 刷新
const handleRefresh = () => {
  fetchData();
};
// 分页变化
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchData();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchData();
};
</script>

<template>
  <div class="container">
    <!--  搜索条件  -->
    <a-card class="general-card" title="搜索条件">
      <a-form
        ref="queryFormRef"
        :model="queryForm"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 18 }"
        label-align="left"
        auto-label-width
        layout="inline"
        @submit="handleSearch"
      >
        <a-form-item field="date" label="统计日期">
          <a-date-picker v-model="queryForm.date"/>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
            <a-button @click="handleReset">
              <template #icon>
                <icon-refresh />
              </template>
              重置
            </a-button>
          </a-space>
        </a-form-item>

      </a-form>
    </a-card>


    <!-- 表格区域 -->
    <a-card class="general-card" title="渠道统计列表">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleRefresh">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <div class="table-box" style="height: 60vh">
        <a-table
          row-key="id"
          :loading="loading"
          :pagination="pagination"
          :data="tableData"
          :bordered="false"
          :scroll="{ y: '100%' }"
          size="medium"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        >
          <template #columns>
            <a-table-column title="No" :width="60" align="center" fixed="left">
              <template #cell="{ rowIndex }">
                {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column title="渠道ID" data-index="channel_id" :width="120" />
            <a-table-column title="渠道名称" data-index="channel_name" :width="120" />
            <a-table-column title="渠道编码" data-index="channel_code" :width="120" />
            <a-table-column title="新用户注册数" data-index="new_customer_reg_num" :width="120" />
            <a-table-column title="实名认证数" data-index="real_name_num" :width="120" />
            <a-table-column title="交易笔数" data-index="number_of_transactions" :width="120" />
            <a-table-column title="统计时间" data-index="created_at" :width="120" />
          </template>
        </a-table>
      </div>

    </a-card>

  </div>
</template>

<style scoped lang="less">
.container {
  padding: 10px;
}
.general-card {
  margin-bottom: 16px;
}
</style>