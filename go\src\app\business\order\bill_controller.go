package order

import (
	"fincore/utils/results"

	"github.com/gin-gonic/gin"
)

// ListDueBills 获取到期账单列表
func (c *Manager) ListDueBills(ctx *gin.Context) {

	// 参数校验
	validationResult := ParamsValidateAndGet(ctx, GetDueBillListSchema)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 业务逻辑处理
	service := NewOrderService(ctx)
	result, err := service.GetDueBillList(ctx, validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取到期订单列表失败", err.Error())
		return
	}
	if result.Total > 0 {
		statistics, err := service.GetDueBillStatistics(ctx, validationResult.Data)
		if err != nil {
			results.Failed(ctx, "获取到期订单统计失败", err.Error())
			return
		}
		results.Success(ctx, "获取到期订单列表成功", result, statistics)
	} else {
		results.Success(ctx, "获取到期订单列表成功", result, nil)
	}
}

// ListOverdueBills 获取逾期账单列表
func (c *Manager) ListOverdueBills(ctx *gin.Context) {

	// 参数校验
	validationResult := ParamsValidateAndGet(ctx, GetOverdueBillListSchema)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 业务逻辑处理
	service := NewOrderService(ctx)
	result, err := service.GetOverdueBillList(ctx, validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取逾期订单列表失败", err.Error())
		return
	}

	results.Success(ctx, "获取逾期订单列表成功", result, nil)
}

// DistributeCollection 分配催收
func (c *Manager) DistributeCollection(ctx *gin.Context) {
	// 参数校验
	validationResult := ParamsValidateAndGet(ctx, GetDistributeCollectionSchema)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}
	// 业务逻辑处理
	service := NewOrderService(ctx)
	err := service.DistributeCollection(ctx, validationResult.Data)
	if err != nil {
		results.Failed(ctx, "分配催收失败", err.Error())
		return
	}

	results.Success(ctx, "分配催收成功", nil, nil)
}

// CollectionList 催收详情
func (c *Manager) ListCollection(ctx *gin.Context) {
	// 参数校验
	validationResult := ParamsValidateAndGet(ctx, GetListCollectionSchema)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}
	// 业务逻辑处理
	service := NewOrderService(ctx)
	result, err := service.GetCollectionList(ctx, validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取催收详情失败", err.Error())
		return
	}

	results.Success(ctx, "获取催收详情成功", result, nil)
}

// RecordCollection 记录催收
func (c *Manager) RecordCollection(ctx *gin.Context) {
	// 参数校验
	validationResult := ParamsValidateAndGet(ctx, GetRecordCollectionSchema)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}
	// 业务逻辑处理
	service := NewOrderService(ctx)
	err := service.RecordCollection(ctx, validationResult.Data)
	if err != nil {
		results.Failed(ctx, "记录催收失败", err.Error())
		return
	}

	results.Success(ctx, "记录催收成功", nil, nil)
}
