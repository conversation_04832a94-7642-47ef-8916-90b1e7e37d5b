package log

import (
	"fincore/global"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// LogManager 日志管理器
type LogManager struct {
	loggers map[string]*zap.Logger
	mutex   sync.RWMutex
	config  *LogConfig
}

// LogConfig 日志配置
type LogConfig struct {
	RootDir    string
	Level      string
	Format     string
	Output     string // 输出方式：file/console/both
	Timezone   string // 时区：Asia/Shanghai, UTC, America/New_York 等
	ShowLine   bool
	MaxBackups int
	MaxSize    int
	MaxAge     int
	Compress   bool
}

var (
	manager      *LogManager
	once         sync.Once
	managerMutex sync.RWMutex
)

// GetManager 获取日志管理器单例
func GetManager() *LogManager {
	managerMutex.RLock()
	if manager != nil {
		managerMutex.RUnlock()
		return manager
	}
	managerMutex.RUnlock()

	managerMutex.Lock()
	defer managerMutex.Unlock()

	// 双重检查
	if manager != nil {
		return manager
	}

	config := &LogConfig{
		RootDir:    global.App.Config.Log.RootDir,
		Level:      global.App.Config.Log.Level,
		Format:     global.App.Config.Log.Format,
		Output:     global.App.Config.Log.Output,
		Timezone:   global.App.Config.Log.Timezone,
		ShowLine:   global.App.Config.Log.ShowLine,
		MaxBackups: global.App.Config.Log.MaxBackups,
		MaxSize:    global.App.Config.Log.MaxSize,
		MaxAge:     global.App.Config.Log.MaxAge,
		Compress:   global.App.Config.Log.Compress,
	}
	manager = NewLogManager(config)
	return manager
}

// ResetManager 重置日志管理器（用于配置更新）
func ResetManager() {
	managerMutex.Lock()
	defer managerMutex.Unlock()

	if manager != nil {
		manager.Close()
		manager = nil
	}

	// 清理模块日志器缓存
	clearAllModules()
}

// NewLogManager 创建日志管理器
func NewLogManager(config *LogConfig) *LogManager {
	return &LogManager{
		loggers: make(map[string]*zap.Logger),
		config:  config,
	}
}

// GetLogger 获取指定模块的日志器
func (m *LogManager) GetLogger(module string) *zap.Logger {
	m.mutex.RLock()
	logger, exists := m.loggers[module]
	m.mutex.RUnlock()

	if exists {
		return logger
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 双重检查
	if logger, exists := m.loggers[module]; exists {
		return logger
	}

	// 创建新的日志器
	logger = m.createLogger(module)
	m.loggers[module] = logger
	return logger
}

// createLogger 创建指定模块的日志器
func (m *LogManager) createLogger(module string) *zap.Logger {
	// 使用根目录，不为每个模块创建子目录
	logDir := m.config.RootDir
	if err := os.MkdirAll(logDir, os.ModePerm); err != nil {
		// 如果创建失败，使用当前目录
		logDir = "."
	}

	// 设置日志级别
	level := m.getLogLevel()

	// 根据输出配置创建不同的core
	var cores []zapcore.Core

	// 文件输出
	if m.config.Output == "file" || m.config.Output == "both" || m.config.Output == "" {
		// 创建文件编码器配置
		fileEncoderConfig := m.getFileEncoderConfig()
		var fileEncoder zapcore.Encoder
		if m.config.Format == "json" {
			fileEncoder = zapcore.NewJSONEncoder(fileEncoderConfig)
		} else {
			fileEncoder = zapcore.NewConsoleEncoder(fileEncoderConfig)
		}

		// 创建日志文件写入器
		fileWriter := m.createLogWriter(logDir, module)
		cores = append(cores, zapcore.NewCore(fileEncoder, fileWriter, level))
	}

	// 控制台输出
	if m.config.Output == "console" || m.config.Output == "both" {
		// 创建控制台编码器配置
		consoleEncoderConfig := m.getConsoleEncoderConfig()
		consoleEncoder := zapcore.NewConsoleEncoder(consoleEncoderConfig)

		// 创建控制台写入器
		consoleWriter := zapcore.Lock(os.Stdout)
		cores = append(cores, zapcore.NewCore(consoleEncoder, consoleWriter, level))
	}

	// 如果没有配置输出方式，默认输出到文件
	if len(cores) == 0 {
		fileEncoderConfig := m.getFileEncoderConfig()
		var fileEncoder zapcore.Encoder
		if m.config.Format == "json" {
			fileEncoder = zapcore.NewJSONEncoder(fileEncoderConfig)
		} else {
			fileEncoder = zapcore.NewConsoleEncoder(fileEncoderConfig)
		}
		fileWriter := m.createLogWriter(logDir, module)
		cores = append(cores, zapcore.NewCore(fileEncoder, fileWriter, level))
	}

	// 合并多个core
	var core zapcore.Core
	if len(cores) == 1 {
		core = cores[0]
	} else {
		core = zapcore.NewTee(cores...)
	}

	// 设置选项
	var options []zap.Option
	if m.config.ShowLine {
		options = append(options, zap.AddCaller())
		// 跳过一层调用栈，显示真正的调用者而不是 Logger.Info 方法
		options = append(options, zap.AddCallerSkip(1))
	}

	// 为不同级别添加堆栈跟踪
	if level <= zapcore.ErrorLevel {
		options = append(options, zap.AddStacktrace(zapcore.ErrorLevel))
	}

	return zap.New(core, options...)
}

// getLogLevel 获取日志级别
func (m *LogManager) getLogLevel() zapcore.Level {
	switch m.config.Level {
	case "debug":
		return zap.DebugLevel
	case "info":
		return zap.InfoLevel
	case "warn":
		return zap.WarnLevel
	case "error":
		return zap.ErrorLevel
	case "dpanic":
		return zap.DPanicLevel
	case "panic":
		return zap.PanicLevel
	case "fatal":
		return zap.FatalLevel
	default:
		return zap.InfoLevel
	}
}

// getFileEncoderConfig 获取文件编码器配置
func (m *LogManager) getFileEncoderConfig() zapcore.EncoderConfig {
	config := zap.NewProductionEncoderConfig()

	// 获取时区
	location := m.getTimezone()

	config.EncodeTime = func(t time.Time, encoder zapcore.PrimitiveArrayEncoder) {
		// 转换到指定时区
		localTime := t.In(location)
		encoder.AppendString(localTime.Format("[2006-01-02 15:04:05.000]"))
	}
	config.EncodeLevel = func(l zapcore.Level, encoder zapcore.PrimitiveArrayEncoder) {
		encoder.AppendString(global.App.Config.App.Env + "." + l.String())
	}

	// 根据 show_line 配置决定是否显示调用者信息
	if m.config.ShowLine {
		config.EncodeCaller = zapcore.ShortCallerEncoder
	} else {
		config.CallerKey = zapcore.OmitKey // 不显示调用者信息
	}

	return config
}

// getConsoleEncoderConfig 获取控制台编码器配置
func (m *LogManager) getConsoleEncoderConfig() zapcore.EncoderConfig {
	config := zap.NewDevelopmentEncoderConfig()

	// 获取时区
	location := m.getTimezone()

	config.EncodeTime = func(t time.Time, encoder zapcore.PrimitiveArrayEncoder) {
		// 转换到指定时区
		localTime := t.In(location)
		encoder.AppendString(localTime.Format("15:04:05.000"))
	}

	// 使用固定宽度的级别编码器，确保对齐
	config.EncodeLevel = func(level zapcore.Level, encoder zapcore.PrimitiveArrayEncoder) {
		encoder.AppendString(fmt.Sprintf("%-5s", level.CapitalString()))
	}

	// 设置控制台输出格式，确保字段之间有适当的分隔符
	config.ConsoleSeparator = "\t" // 使用制表符分隔字段

	// 根据 show_line 配置决定是否显示调用者信息
	if m.config.ShowLine {
		// 使用固定宽度的调用者编码器，确保对齐
		config.EncodeCaller = func(caller zapcore.EntryCaller, encoder zapcore.PrimitiveArrayEncoder) {
			encoder.AppendString(fmt.Sprintf("%-30s", caller.TrimmedPath()))
		}
	} else {
		config.CallerKey = zapcore.OmitKey // 不显示调用者信息
	}

	return config
}

// getTimezone 获取时区配置
func (m *LogManager) getTimezone() *time.Location {
	timezone := m.config.Timezone

	// 如果没有配置时区，默认使用上海时区
	if timezone == "" {
		timezone = "Asia/Shanghai"
	}

	// 尝试加载时区
	location, err := time.LoadLocation(timezone)
	if err != nil {
		// 如果加载失败，使用上海时区作为默认值
		location, _ = time.LoadLocation("Asia/Shanghai")
		if location == nil {
			// 如果上海时区也加载失败，使用UTC
			location = time.UTC
		}
	}

	return location
}

// createLogWriter 创建日志写入器
func (m *LogManager) createLogWriter(logDir, module string) zapcore.WriteSyncer {
	// 按月创建目录结构：log/2025-07/2025-07-17_module.log
	now := time.Now()
	monthDir := now.Format("2006-01")   // 月份目录，如：2025-07
	timeStr := now.Format("2006-01-02") // 日期字符串，如：2025-07-17

	// 创建月份目录路径
	monthPath := filepath.Join(logDir, monthDir)

	// 确保月份目录存在
	if err := os.MkdirAll(monthPath, os.ModePerm); err != nil {
		// 如果创建失败，使用根目录
		monthPath = logDir
	}

	// 生成日志文件路径：log/2025-07/2025-07-17_module.log
	filename := filepath.Join(monthPath, fmt.Sprintf("%s_%s.log", timeStr, module))

	file := &lumberjack.Logger{
		Filename:   filename,
		MaxSize:    m.config.MaxSize,
		MaxBackups: m.config.MaxBackups,
		MaxAge:     m.config.MaxAge,
		Compress:   m.config.Compress,
	}

	return zapcore.AddSync(file)
}

// GetDefaultLogger 获取默认日志器（兼容现有代码）
func (m *LogManager) GetDefaultLogger() *zap.Logger {
	return m.GetLogger("app")
}

// Close 关闭所有日志器
func (m *LogManager) Close() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	for _, logger := range m.loggers {
		logger.Sync()
	}

	// 清理缓存的日志器，确保重新初始化时使用新配置
	m.loggers = make(map[string]*zap.Logger)
}
