

[31m2025/07/29 15:02:04 [Recovery] 2025/07/29 - 15:02:04 panic recovered:
GET /business/user/get_userinfo?_t=**********602 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: ddfd4af3c24c07f3444fdc5b47402aa7
Verify-Time: **********


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/index.go:139 (0x14bf364)
	(*Index).Get_userinfo: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:04 [Recovery] 2025/07/29 - 15:02:04 panic recovered:
GET /business/user/account/get_menu?_t=************* HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: ddfd4af3c24c07f3444fdc5b47402aa7
Verify-Time: **********


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/account.go:38 (0x14ba173)
	(*Account).Get_menu: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:04 [Recovery] 2025/07/29 - 15:02:04 panic recovered:
GET /business/user/get_userinfo?_t=**********602 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: ddfd4af3c24c07f3444fdc5b47402aa7
Verify-Time: **********


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/index.go:139 (0x14bf364)
	(*Index).Get_userinfo: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:04 [Recovery] 2025/07/29 - 15:02:04 panic recovered:
GET /business/user/account/get_menu?_t=************* HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: ddfd4af3c24c07f3444fdc5b47402aa7
Verify-Time: **********


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/account.go:38 (0x14ba173)
	(*Account).Get_menu: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:04 [Recovery] 2025/07/29 - 15:02:04 panic recovered:
GET /business/user/account/get_menu?_t=************* HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: ddfd4af3c24c07f3444fdc5b47402aa7
Verify-Time: **********


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/account.go:38 (0x14ba173)
	(*Account).Get_menu: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:04 [Recovery] 2025/07/29 - 15:02:04 panic recovered:
GET /business/user/get_userinfo?_t=**********602 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: ddfd4af3c24c07f3444fdc5b47402aa7
Verify-Time: **********


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/index.go:139 (0x14bf364)
	(*Index).Get_userinfo: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:04 [Recovery] 2025/07/29 - 15:02:04 panic recovered:
GET /business/user/get_userinfo?_t=**********602 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: c3c1802b462565743ca4091ac9023ea0
Verify-Time: **********


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/index.go:139 (0x14bf364)
	(*Index).Get_userinfo: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:04 [Recovery] 2025/07/29 - 15:02:04 panic recovered:
GET /business/user/account/get_menu?_t=************* HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: ddfd4af3c24c07f3444fdc5b47402aa7
Verify-Time: **********


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/account.go:38 (0x14ba173)
	(*Account).Get_menu: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:04 [Recovery] 2025/07/29 - 15:02:04 panic recovered:
GET /business/user/account/get_menu?_t=************* HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: c3c1802b462565743ca4091ac9023ea0
Verify-Time: **********


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/account.go:38 (0x14ba173)
	(*Account).Get_menu: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:04 [Recovery] 2025/07/29 - 15:02:04 panic recovered:
GET /business/user/get_userinfo?_t=**********602 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: c3c1802b462565743ca4091ac9023ea0
Verify-Time: **********


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/index.go:139 (0x14bf364)
	(*Index).Get_userinfo: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:04 [Recovery] 2025/07/29 - 15:02:04 panic recovered:
GET /business/user/account/get_menu?_t=************* HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: c3c1802b462565743ca4091ac9023ea0
Verify-Time: **********


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/account.go:38 (0x14ba173)
	(*Account).Get_menu: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:04 [Recovery] 2025/07/29 - 15:02:04 panic recovered:
GET /business/user/get_userinfo?_t=**********602 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: c3c1802b462565743ca4091ac9023ea0
Verify-Time: **********


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/index.go:139 (0x14bf364)
	(*Index).Get_userinfo: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:11 [Recovery] 2025/07/29 - 15:02:11 panic recovered:
GET /business/user/get_userinfo?_t=1753772531908 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 3054e123bb13fd2637bf9203721d74c2
Verify-Time: 1753772531


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/index.go:139 (0x14bf364)
	(*Index).Get_userinfo: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:11 [Recovery] 2025/07/29 - 15:02:11 panic recovered:
GET /business/user/get_userinfo?_t=1753772531908 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: a45382433895ebe63ec5866d3d48542c
Verify-Time: 1753772532


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/index.go:139 (0x14bf364)
	(*Index).Get_userinfo: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:11 [Recovery] 2025/07/29 - 15:02:11 panic recovered:
GET /business/user/get_userinfo?_t=1753772531908 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: a45382433895ebe63ec5866d3d48542c
Verify-Time: 1753772532


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/index.go:139 (0x14bf364)
	(*Index).Get_userinfo: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:11 [Recovery] 2025/07/29 - 15:02:11 panic recovered:
GET /business/user/get_userinfo?_t=1753772531908 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: a45382433895ebe63ec5866d3d48542c
Verify-Time: 1753772532


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/index.go:139 (0x14bf364)
	(*Index).Get_userinfo: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:12 [Recovery] 2025/07/29 - 15:02:12 panic recovered:
GET /business/user/get_userinfo?_t=1753772531908 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: a45382433895ebe63ec5866d3d48542c
Verify-Time: 1753772532


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/index.go:139 (0x14bf364)
	(*Index).Get_userinfo: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:12 [Recovery] 2025/07/29 - 15:02:12 panic recovered:
GET /business/user/get_userinfo?_t=1753772531908 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: a45382433895ebe63ec5866d3d48542c
Verify-Time: 1753772532


interface conversion: interface {} is nil, not *middleware.UserClaims
C:/Users/<USER>/scoop/apps/go/current/src/runtime/iface.go:275 (0x936524)
	panicdottypeE: panic(&TypeAssertionError{iface, have, want, ""})
D:/work/code/fincore/go/src/app/business/user/index.go:139 (0x14bf364)
	(*Index).Get_userinfo: user := getuser.(*middleware.UserClaims)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xa36d25)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xa35e38)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x100c636)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x105b34d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x105b173)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x14f0569)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x14efb88)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe53e8a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x14ef765)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe6098e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0xe6097b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5fadc)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0xe5fac3)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0xe5f06d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0xe5ed04)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0xe5e849)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xc7080d)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xc4f3a4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x99bda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:51 [Recovery] 2025/07/29 - 15:02:51 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=**********625 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 62ad1ddd7702726a973d4af14deed221
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:51 [Recovery] 2025/07/29 - 15:02:51 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=**********625 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 62ad1ddd7702726a973d4af14deed221
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:51 [Recovery] 2025/07/29 - 15:02:51 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=**********625 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 62ad1ddd7702726a973d4af14deed221
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:51 [Recovery] 2025/07/29 - 15:02:51 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=**********625 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 6ca244197f8910ad019af9d87bd6caaf
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:51 [Recovery] 2025/07/29 - 15:02:51 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=**********625 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 6ca244197f8910ad019af9d87bd6caaf
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:02:51 [Recovery] 2025/07/29 - 15:02:51 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=**********625 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 6ca244197f8910ad019af9d87bd6caaf
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:03:44 [Recovery] 2025/07/29 - 15:03:44 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=************* HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 1c186b91408f45919ba1c29417765c97
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:03:44 [Recovery] 2025/07/29 - 15:03:44 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=************* HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 1c186b91408f45919ba1c29417765c97
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:03:44 [Recovery] 2025/07/29 - 15:03:44 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=************* HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 0b00711d5d67697f705b3d9bc14e3605
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:03:44 [Recovery] 2025/07/29 - 15:03:44 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=************* HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 0b00711d5d67697f705b3d9bc14e3605
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:03:45 [Recovery] 2025/07/29 - 15:03:45 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=************* HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 0b00711d5d67697f705b3d9bc14e3605
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:03:45 [Recovery] 2025/07/29 - 15:03:45 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=************* HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 0b00711d5d67697f705b3d9bc14e3605
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:07:20 [Recovery] 2025/07/29 - 15:07:20 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=**********881 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 5205f72d7a0dd1ae1bae4a16b70376cb
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:07:20 [Recovery] 2025/07/29 - 15:07:20 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=**********881 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 68d06f0a6e72ccf8093fc8efdfcde7ae
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:07:20 [Recovery] 2025/07/29 - 15:07:20 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=**********881 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 68d06f0a6e72ccf8093fc8efdfcde7ae
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:07:20 [Recovery] 2025/07/29 - 15:07:20 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=**********881 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 68d06f0a6e72ccf8093fc8efdfcde7ae
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:07:20 [Recovery] 2025/07/29 - 15:07:20 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=**********881 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 68d06f0a6e72ccf8093fc8efdfcde7ae
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:07:21 [Recovery] 2025/07/29 - 15:07:21 panic recovered:
GET /business/bankcard/bankcardcontroller/getBankCardList?customer_id=86&_t=**********881 HTTP/1.1
Host: ************:8109
Accept: application/json, text/plain, */*
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Authorization: *
Connection: close
Referer: http://************:9106/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Verify-Encrypt: 68d06f0a6e72ccf8093fc8efdfcde7ae
Verify-Time: **********


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x2d7777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x2d7747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xab88fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/business/bankcard/controller.go:197 (0xab5e85)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x396d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x395e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x96c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:46 (0xe51153)
	InitRouter.ValidityAPi.func6: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x9bb76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x9bb593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe509e9)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe50008)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7b3f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe4fbe5)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7c0a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x7c0a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bfb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x7bfb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x7bf12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x7bedc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x7be909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5d08cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x5af464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2fbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:08:45 [Recovery] 2025/07/29 - 15:08:45 panic recovered:
GET /uniapp/bankcard/bankcardcontroller/getBankCardList HTTP/1.1
Host: ************:8109
Accept: text/json
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Apiverify: ************************************************************
Authorization: *
Businessid: 1
Connection: close
Content-Type: application/json;charset=UTF-8
Referer: http://************:6020/
User-Agent: Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x297777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x297747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xa788fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/uniapp/bankcard/controller.go:165 (0xdf04b8)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, userID)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x356d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x355e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x92c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x773f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:40 (0xe10fb7)
	InitRouter.ValidityAPi.func6: c.Next() //不需验证
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x97b76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x97b593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x773f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe10a69)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x780a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x780a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x77fb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x77fb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x773f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe10088)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x773f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe0fc65)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x780a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x780a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x77fb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x77fb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x77f12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x77edc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x77e909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5908cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x56f464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2bbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:08:48 [Recovery] 2025/07/29 - 15:08:48 panic recovered:
GET /uniapp/bankcard/bankcardcontroller/getBankCardList HTTP/1.1
Host: ************:8109
Accept: text/json
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Apiverify: ************************************************************
Authorization: *
Businessid: 1
Connection: close
Content-Type: application/json;charset=UTF-8
Referer: http://************:6020/
User-Agent: Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x297777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x297747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xa788fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/uniapp/bankcard/controller.go:165 (0xdf04b8)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, userID)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x356d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x355e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x92c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x773f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:40 (0xe10fb7)
	InitRouter.ValidityAPi.func6: c.Next() //不需验证
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x97b76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x97b593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x773f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe10a69)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x780a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x780a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x77fb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x77fb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x773f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe10088)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x773f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe0fc65)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x780a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x780a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x77fb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x77fb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x77f12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x77edc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x77e909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5908cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x56f464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2bbda0)
	goexit: BYTE	$0x90	// NOP
[0m


[31m2025/07/29 15:09:43 [Recovery] 2025/07/29 - 15:09:43 panic recovered:
GET /uniapp/bankcard/bankcardcontroller/getBankCardList HTTP/1.1
Host: ************:8109
Accept: text/json
Accept-Encoding: gzip, deflate
Accept-Language: en,zh-CN;q=0.9,zh-TW;q=0.8,zh;q=0.7
Apiverify: ************************************************************
Authorization: *
Businessid: 1
Connection: close
Content-Type: application/json;charset=UTF-8
Referer: http://************:6020/
User-Agent: Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36


runtime error: invalid memory address or nil pointer dereference
C:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262 (0x297777)
	panicmem: panic(memoryError)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401 (0x297747)
	sigpanic: panicmem()
D:/work/code/fincore/go/src/app/business/bankcard/service.go:283 (0xa788fd)
	(*BankCardService).GetBankCardList: bankCards, err := s.bankCardModel.GetBankCardsByUserID(int(userID))
D:/work/code/fincore/go/src/app/uniapp/bankcard/controller.go:165 (0xdf04b8)
	(*BankCardController).GetBankCardList: result, err := getBankCardService().GetBankCardList(ctx, userID)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0x356d85)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0x355e98)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x92c6f6)
	Bind.match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x773f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/validityAPi.go:40 (0xe10fb7)
	InitRouter.ValidityAPi.func6: c.Next() //不需验证
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x97b76d)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x97b593)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x773f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0xe10a69)
	InitRouter.ErrorLogMiddleware.func5: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x780a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x780a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x77fb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x77fb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x773f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0xe10088)
	InitRouter.AccessLogMiddleware.func4: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x773f4a)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0xe0fc65)
	InitRouter.RequestIDMiddleware.func3: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x780a4e)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x780a3b)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x77fb9c)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x77fb83)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x77f12d)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x77edc4)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x77e909)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0x5908cd)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0x56f464)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x2bbda0)
	goexit: BYTE	$0x90	// NOP
[0m
