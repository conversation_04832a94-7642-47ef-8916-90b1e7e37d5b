// @/api/complaint.js
import request from '@/utils/request';

export default {
	// 提交投诉内容
	uploadComplaintContent: (data) =>
		request({
			url: '/user/complaint/uploadComplaintContent',
			method: 'POST',
			data,
			custom: {
				loadingMsg: '提交中...',
				auth: true
			}
		}),

	// 获取投诉用户信息（根据您之前的代码添加）
	getComplaintUserInfo: (uid) =>
		request({
			url: '/user/complaint/getComplaintUserInfo',
			method: 'GET',
			params: {
				"uid": uid
			},
			custom: {
				auth: true
			}
		}),
	// 获取投诉返回信息
	getComplaintRes: (uid) =>
		request({
			url: '/user/complaint/getComplaintRes',
			method: 'GET',
			params: {
				"uid": uid
			},
			custom: {
				auth: true
			}
		})
};