# This file is a template, and might need editing before it works on your project.
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml

# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
before_script:
  - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
  - eval $(ssh-agent -s)
  - ssh-add <(echo "$SSH_PRIVATE_KEY")
  - mkdir -p ~/.ssh
  - 'echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'


stages:          # List of stages for jobs, and their order of execution
  # - build
  # - test
  - deploy

# cache:
#    paths:
#      - deploy

# build-job:       # This job runs in the build stage, which runs first.
#   tags:
#     - deploy
#   stage: build
#   script:
#     - echo "Compiling the code..."
#     - source ~/.profile
#     - ./build.sh
#     - echo "Compile complete."

# unit-test-job:   # This job runs in the test stage.
#   tags:
#     - deploy
#   stage: test    # It only starts when the job in the build stage completes successfully.
#   script:
#     - echo "Running unit tests... This will take about 60 seconds."
#     - echo "test success"

# lint-test-job:   # This job also runs in the test stage.
#   stage: test    # It can run at the same time as unit-test-job (in parallel).
#   script:
#     - echo "Linting code... This will take about 10 seconds."
#     - sleep 10
#     - echo "No lint issues found."

deploy-job:      # This job runs in the deploy stage.
  tags:
    - deploy
  when: manual
  stage: deploy  # It only runs when *both* jobs in the test stage complete successfully.
  script:
    - echo "Compiling the code..."
    - source ~/.profile
    - ./build.sh --skip-uniapp
    - echo "Compile complete."
    - echo "Deploying application..."
    - rsync -r deployer root@*************:/root
    - ssh root@************* "cd /root/deployer; chmod +x deploy.sh; chmod +x nginx/deploy-nginx.sh; sudo ./deploy.sh -s"
    - echo "Application successfully deployed."
