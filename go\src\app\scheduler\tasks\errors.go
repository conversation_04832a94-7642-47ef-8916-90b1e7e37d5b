package tasks

import "errors"

// 任务相关错误定义
var (
	// ErrTaskNameEmpty 任务名称为空
	ErrTaskNameEmpty = errors.New("任务名称不能为空")

	// ErrTaskScheduleEmpty 任务调度规则为空
	ErrTaskScheduleEmpty = errors.New("任务调度规则不能为空")

	// ErrTaskTimeoutInvalid 任务超时时间无效
	ErrTaskTimeoutInvalid = errors.New("任务超时时间必须大于0")

	// ErrTaskRetryCountInvalid 任务重试次数无效
	ErrTaskRetryCountInvalid = errors.New("任务重试次数不能小于0")

	// ErrTaskRetryIntervalInvalid 任务重试间隔无效
	ErrTaskRetryIntervalInvalid = errors.New("任务重试间隔不能小于0")

	// ErrTaskNotImplemented 任务未实现
	ErrTaskNotImplemented = errors.New("任务Execute方法未实现")

	// ErrTaskAlreadyRegistered 任务已注册
	ErrTaskAlreadyRegistered = errors.New("任务已经注册")

	// ErrTaskNotFound 任务未找到
	ErrTaskNotFound = errors.New("任务未找到")

	// ErrTaskAlreadyRunning 任务已在运行
	ErrTaskAlreadyRunning = errors.New("任务已在运行中")

	// ErrTaskTimeout 任务执行超时
	ErrTaskTimeout = errors.New("任务执行超时")

	// ErrTaskCancelled 任务被取消
	ErrTaskCancelled = errors.New("任务被取消")

	// ErrTaskPanic 任务执行发生panic
	ErrTaskPanic = errors.New("任务执行发生panic")

	// ErrSchedulerNotStarted 调度器未启动
	ErrSchedulerNotStarted = errors.New("调度器未启动")

	// ErrSchedulerAlreadyStarted 调度器已启动
	ErrSchedulerAlreadyStarted = errors.New("调度器已启动")

	// ErrInvalidCronExpression 无效的Cron表达式
	ErrInvalidCronExpression = errors.New("无效的Cron表达式")
)
