<template>
	<view class="callBackContract">
		<view class="contractBox" v-if="isSucc">
			<image src="/static/image/shz.png" mode="widthFix"></image>
			<text class="name">正在加速审核中</text>
			<text class="hint">相关工作人员将在当天处理</text>
			<button @click="backHome">返回首页</button>
		</view>
	</view>
</template>

<script>
	import userApi from '@/api/user.js';

	import user from '@/store/user.js';
	import {
		storeToRefs
	} from 'pinia';

	export default {
		data() {
			return {
				isSucc: false,
				product_id: '',
				channel_code: '',
				loan_amount: ''
			}
		},
		onLoad(opt) {
			uni.webView.postMessage({
				type: 'message',
				data: opt.contract_no
			}, '*');
			

			// https 后删除
			
			this.channel_code = uni.getStorageSync('cid');
			this.product_id = uni.getStorageSync('product_id');
			this.loan_amount = uni.getStorageSync('loan_amount');
			if(!this.channel_code) {
				return false
			}
			if (opt.contract_no) {
				// this.isSucc = true
				// 合同签约成功调用接口
				userApi.queryContractStatus({
					contract_no: opt.contract_no
				}).then(res => {
					if (res.code == 0) {
						if (res.data == 2) {
							this.updateContract(opt.contract_no);
							this.isSucc = true
						} else {
							uni.redirectTo({
								url: '/pages/Agreement/Agreement?product_id=' + uni.getStorageSync(
									'product_id')
							});
						}
					}
				})
			}

			// https 后删除end
		},
		methods: {
			// https 后删除top
			updateContract(c_no) {
				const userStore = user();
				const {
					userInfo
				} = storeToRefs(userStore);
				userApi.updateContract({
					user_id: userInfo.value.uid,
					contract_no: c_no
				}).then(res => {
					userApi.createOrder({
						channel_code: this.channel_code,
						product_rule_id: this.product_id,
						loan_amount: this.loan_amount,
						customer_origin: 'H5'
					}).then(res => {
						console.log('创建订单', res)
					})
				})
				// 创建订单

			},
			backHome() {
				uni.removeStorageSync('product_id');
				uni.removeStorageSync('loan_amount');
				uni.switchTab({
					url: "/pages/index/index"
				})
			}
			// https 后删除end
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background: linear-gradient(180deg, #e6ecf7, #f5faff);
	}

	.callBackContract {
		padding: 200rpx 0;

		.contractBox {
			display: flex;
			justify-content: center;
			flex-direction: column;
			align-items: center;

			image {
				width: 200rpx;
			}

			text.name {
				color: #444;
				font-size: 36rpx;
				font-weight: bold;
				margin: 80rpx 0 20rpx;
			}

			text.hint {
				color: #666;
				font-size: 26rpx;
			}

			button {
				width: 180rpx;
				flex-shrink: 0;
				height: 80rpx;
				line-height: 80rpx;
				background: linear-gradient(to bottom, #1a6eff 20%, #4781e3 45%);
				color: #fff;
				border: none;
				border-radius: 22px;
				font-size: 13px;
				cursor: pointer;
				padding: 0;
				box-sizing: border-box;
				text-align: center;
				white-space: nowrap;
				margin-top: 30rpx;
			}
		}
	}
</style>