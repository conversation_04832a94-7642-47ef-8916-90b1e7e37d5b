<template>
	<view class="repayCountDown">
		<template v-if="!isSucceed">
			<view class="count-down">
				{{ countDown }}
			</view>
			<view class="count-down-hint">
				<view class="count-down-hint-tit">
					正在还款
				</view>
				<view class="count-down-hint-desc">
					请耐心等待
				</view>
			</view>
		</template>
		<template v-else-if="isSucceed == 1 || isSucceed == 2 || isSucceed == 4">
			<view class="error-img">
				<image src="/static/image/succeed.png" mode="widthFix"></image>
			</view>
			<view class="count-down-hint">
				<view class="count-down-hint-tit">
					{{ isSucceed == 1?'还款成功':isSucceed == 2?'部分还款成功':'还款申请已提交，请耐心等待' }}
				</view>
				<view class="contact-customer">
					<button @click="handleToHome">再借一笔</button>
				</view>
			</view>
		</template>
		<template v-else>
			<view class="error-img">
				<image src="/static/image/error.png" mode="widthFix"></image>
			</view>
			<view class="count-down-hint">
				<view class="count-down-hint-tit">
					还款失败
				</view>
				<view class="count-down-hint-desc">
					联系客服询问还款失败原因
				</view>
				<view class="contact-customer">
					<button @click="handleContact">联系客服</button>
				</view>
			</view>
		</template>

		<!-- 在线客服底部弹框 -->
		<div class="service-modal" v-if="showServiceModal" @click="closeServiceModal">
			<div class="modal-content" @click.stop>
				<div class="modal-header">
					<text class="modal-title" @click="handleCall()">电话客服: ************</text>
					<div class="modal-close" @click="closeServiceModal">
						取消
					</div>
				</div>
			</div>
		</div>
	</view>
</template>

<script setup>
	import {
		onMounted,
		ref,
		watch
	} from 'vue';
	import {
		onLoad,
		onShow
	} from "@dcloudio/uni-app";
	import userApi from '@/api/user.js';
	const countDown = ref(60);
	const isSucceed = ref(''); // 1 成功 2 部分成功 3 失败 4 还款申请已提交
	const showServiceModal = ref(false);
	
	function handleContact() {
		showServiceModal.value = true;
	}
	// 关闭客服弹框
	const closeServiceModal = () => {
		showServiceModal.value = false;
	};

	function handleCall() {
		uni.makePhoneCall({
			phoneNumber: '************'
		})
	}
	function handleToHome() {
		uni.reLaunch({
			url: "/pages/index/index"
		})
	}
	const transactions_no = ref([]);
	
	
	let inter = null;
	async function getPaymentStatus() {
		const res = await userApi.getPaymentStatus({
			transactions_no: transactions_no.value
		});
		if(res.code == 0) {
			if(res.data.status_raw == 4) {
				setTimeout(() => {
					getPaymentStatus();
				}, 5000)
			}else{
				clearInterval(inter);
				isSucceed.value = res.data.status_raw;
			}
		}else{
			clearInterval(inter);
			isSucceed.value = 3;
		}
	}
	onLoad((opt) => {
		opt.a?transactions_no.value.push(opt.a):''; 
		opt.g?transactions_no.value.push(opt.g):'';
		inter = setInterval(() => {
			countDown.value = countDown.value - 1;
			if(countDown.value == 1) {
				isSucceed.value = 4;
				clearInterval(inter);
			}
		},1000)
		
		getPaymentStatus();
	})
</script>

<style scoped lang="scss">
	.repayCountDown {
		min-height: 100vh;
		text-align: center;
		background: linear-gradient(180deg, #e6ecf7, #f5faff);

		.count-down {
			font-size: 80rpx;
			margin-bottom: 200rpx;
			padding-top: 200rpx;
		}

		.error-img {
			margin-bottom: 200rpx;
			padding-top: 200rpx;

			image {
				width: 150rpx;
			}
		}

		.count-down-hint {
			.count-down-hint-tit {
				font-size: 35rpx;
				color: #333;
				font-weight: bold;
				margin-bottom: 15rpx;
			}

			.count-down-hint-desc {
				font-size: 28rpx;
				color: #666;
			}

			.contact-customer {
				padding-top: 30rpx;

				button {
					width: 120px;
					height: 90rpx;
					line-height: 90rpx;
					border-radius: 30px;
					background: linear-gradient(to bottom, #1a6eff 20%, #4781e3 45%);
					color: #fff;
					border: none;
					font-size: 14px;
					cursor: pointer;
					box-shadow: 0 2px 10px #ccc;
				}
			}
		}
	}

	/* 客服弹框样式 */
	.service-modal {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		top: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: flex-end;
		align-items: flex-end;
		z-index: 999;
	}

	.modal-content {
		width: 100%;
		height: 110px;
		background-color: #fff;
		overflow: hidden;
		position: relative;
		box-shadow: none;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 24px;
	}

	.modal-header {
		width: 100%;
		text-align: center;
		padding: 0;
		border-bottom: none;
	}

	.modal-title {
		display: block;
		font-size: 16px;
		font-weight: normal;
		color: #333;
		margin-bottom: 24px;
		margin-top: 0;
		line-height: 1.5;
	}

	.modal-close {
		margin-top: 10px;
		font-size: 14px;
		color: #bbb;
		cursor: pointer;
		text-align: center;
		font-weight: normal;
	}

	.modal-body {
		padding: 15px;
		font-size: 14px;
		line-height: 1.6;
	}
</style>