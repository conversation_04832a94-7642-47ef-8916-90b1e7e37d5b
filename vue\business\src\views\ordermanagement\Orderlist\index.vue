<template>
  <div class="container">
    <!-- 搜索条件区域 -->
    <a-card class="general-card" title="搜索条件">
      <a-form
        ref="queryFormRef"
        :model="queryForm"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 18 }"
        label-align="left"
        auto-label-width
        @submit="handleSearch"
      >
        <!-- 第一行：订单编号、用户姓名、身份证号 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="order_no" label="订单编号">
              <a-input
                v-model="queryForm.order_no"
                placeholder="请输入订单编号"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="user_name" label="下单人姓名">
              <a-input
                v-model="queryForm.user_name"
                placeholder="请输入下单人姓名"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="user_id_card" label="身份证号">
              <a-input
                v-model="queryForm.user_id_card"
                placeholder="请输入身份证号"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第二行：手机号、订单状态、审核人姓名 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="user_mobile" label="手机号">
              <a-input
                v-model="queryForm.user_mobile"
                placeholder="请输入手机号"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="status" label="订单状态">
              <a-select
                v-model="queryForm.status"
                placeholder="请选择订单状态"
                allow-clear
              >
                <a-option :value="0" label="待放款" />
                <a-option :value="1" label="放款中" />
                <a-option :value="2" label="交易关闭" />
                <a-option :value="3" label="交易完成" />
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="sales_assignee_name" label="审核人姓名">
              <a-input
                v-model="queryForm.sales_assignee_name"
                placeholder="请输入审核人姓名"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第三行：渠道来源、初始下单渠道、创建时间 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="channel_id" label="渠道来源">
              <a-select
                v-model="queryForm.channel_id"
                placeholder="请选择渠道来源"
                allow-clear
                :loading="channelLoading"
              >
                <a-option
                  v-for="option in channelOptions"
                  :key="option.id"
                  :value="option.value"
                  :label="option.label"
                />
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="initial_order_channel_id" label="初始下单渠道">
              <a-select
                v-model="queryForm.initial_order_channel_id"
                placeholder="请选择初始下单渠道"
                allow-clear
                :loading="channelLoading"
              >
                <a-option
                  v-for="option in channelOptions"
                  :key="option.id"
                  :value="option.value"
                  :label="option.label"
                />
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="submittedTimeRange" label="创建时间">
              <a-range-picker
                v-model="submittedTimeRange"
                style="width: 100%"
                @change="handleSubmittedTimeChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 账单到期时间-查询逾期的 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="billDueDate" label="账单到期时间">
              <a-range-picker
                v-model="billDueDate"
                style="width: 100%"
                @change="handleBillDueDateChange"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 操作按钮 -->
        <a-row>
          <a-col :span="24">
            <a-form-item>
              <a-space>
                <a-button type="primary" html-type="submit">
                  <template #icon>
                    <icon-search />
                  </template>
                  搜索
                </a-button>
                <a-button @click="handleReset">
                  <template #icon>
                    <icon-refresh />
                  </template>
                  重置
                </a-button>
                <a-button type="primary" status="success" @click="handleShowAssignModal">
                  <template #icon>
                    <icon-user-add />
                  </template>
                  分配订单
                </a-button>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 表格区域 -->
    <a-card class="general-card" title="订单列表">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleRefresh">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>
      <div class="table-box" style="height: 60vh">
        <a-table
          row-key="id"
          :loading="loading"
          :pagination="pagination"
          :data="tableData"
          :bordered="false"
          size="medium"
          :scroll="{ y: '100%' }"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        >
          <template #columns>
            <a-table-column title="No" :width="60" align="center" fixed="left">
              <template #cell="{ rowIndex }">
                {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
              </template>
            </a-table-column>

            <a-table-column title="订单ID" data-index="id" :width="100" />
            <a-table-column title="渠道来源" data-index="channel_name" :width="120" />
            <a-table-column title="姓名" data-index="user_name" :width="100" />
            <a-table-column title="手机号" data-index="user_mobile" :width="130" />
            <a-table-column title="风控分数" data-index="risk_score" :width="100">
              <template #cell="{ record }">
                {{ record.risk_score ? record.risk_score.toFixed(1) : '-' }}
              </template>
            </a-table-column>
            <a-table-column title="总应还金额" data-index="total_repayable_amount" :width="120" align="right">
              <template #cell="{ record }">
                {{ formatCurrency(record.total_repayable_amount) }}
              </template>
            </a-table-column>
            <a-table-column title="已付金额" data-index="amount_paid" :width="120" align="right">
              <template #cell="{ record }">
                {{ formatCurrency(record.amount_paid) }}
              </template>
            </a-table-column>
            <a-table-column title="订单状态" data-index="status" :width="100">
              <template #cell="{ record }">
                {{ getOrderStatusText(record.status) }}
              </template>
            </a-table-column>
            <a-table-column title="借款周期" data-index="loan_period" :width="100">
              <template #cell="{ record }">
                {{ record.loan_period ? `${record.loan_period * record.total_periods}天` : '-' }}
              </template>
            </a-table-column>
            <a-table-column title="还款期数" :width="100">
              <template #cell="{ record }">
                {{ record.paid_periods !== undefined && record.total_periods ? `${record.paid_periods}/${record.total_periods}` : '-' }}
              </template>
            </a-table-column>
            <a-table-column title="关单类型" data-index="reason_for_closure" :width="120">
              <template #cell="{ record }">
                {{ record.reason_for_closure_text || '-' }}
              </template>
            </a-table-column>
            <a-table-column title="总期数" data-index="total_periods" :width="80" />
            <a-table-column title="支付渠道" data-index="payment_channel_name" :width="120" />
            <a-table-column title="订单编号" data-index="order_no" :width="200" />
            <a-table-column title="客户来源" data-index="customer_origin" :width="100" />
            <a-table-column title="初始下单渠道" data-index="initial_order_channel_name" :width="120" />
            <a-table-column title="审核员" data-index="sales_assignee_name" :width="120"></a-table-column>
            <a-table-column title="投诉状态" data-index="complaint_status" :width="100">
              <template #cell="{ record }">
                {{ record.complaint_status === 1 ? '是' : '否' }}
              </template>
            </a-table-column>
            <a-table-column title="下单时间" data-index="created_at" :width="180" />

            <a-table-column title="订单完结时间" data-index="completed_at" :width="180" />


            <a-table-column title="操作" :width="180" fixed="right">
              <template #cell="{ record }">
                <a-space>
                  <a-button size="small" @click="handleView(record)">处理</a-button>
                  <a-button
                    size="small"
                    @click="isOrderAssignee(record) ? handleEditChannel(record) : showPermissionError()"
                    v-if="record.status === 0"
                    :disabled="!isOrderAssignee(record)"
                  >修改渠道</a-button>
                </a-space>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>

    </a-card>

    <!-- 订单详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="订单详情"
      width="800px"
      :footer="false"
    >
      <a-spin :loading="detailLoading">
        <div v-if="orderDetail">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="订单ID">{{ orderDetail.id }}</a-descriptions-item>
            <a-descriptions-item label="订单编号">{{ orderDetail.order_no }}</a-descriptions-item>
            <a-descriptions-item label="用户ID">{{ orderDetail.user_id }}</a-descriptions-item>
            <a-descriptions-item label="用户名称">{{ orderDetail.user_name }}</a-descriptions-item>
            <a-descriptions-item label="手机号">{{ orderDetail.user_mobile }}</a-descriptions-item>
            <a-descriptions-item label="产品名称">{{ orderDetail.product_name }}</a-descriptions-item>
            <a-descriptions-item label="贷款金额">{{ formatCurrency(orderDetail.loan_amount) }}</a-descriptions-item>
            <a-descriptions-item label="实际放款金额">{{ formatCurrency(orderDetail.principal) }}</a-descriptions-item>
            <a-descriptions-item label="总利息">{{ formatCurrency(orderDetail.total_interest) }}</a-descriptions-item>
            <a-descriptions-item label="总担保费">{{ formatCurrency(orderDetail.total_guarantee_fee) }}</a-descriptions-item>
            <a-descriptions-item label="总其他费用">{{ formatCurrency(orderDetail.total_other_fees) }}</a-descriptions-item>
            <a-descriptions-item label="总应还金额">{{ formatCurrency(orderDetail.total_repayable_amount) }}</a-descriptions-item>
            <a-descriptions-item label="已还金额">{{ formatCurrency(orderDetail.amount_paid) }}</a-descriptions-item>
            <a-descriptions-item label="渠道">{{ orderDetail.channel_name }}</a-descriptions-item>
            <a-descriptions-item label="客户来源">{{ orderDetail.customer_origin }}</a-descriptions-item>
            <a-descriptions-item label="初始订单渠道">{{ orderDetail.initial_order_channel_name }}</a-descriptions-item>
            <a-descriptions-item label="订单状态">{{ getOrderStatusText(orderDetail.status) }}</a-descriptions-item>
            <a-descriptions-item label="是否冻结">{{ orderDetail.is_freeze === 1 ? '是' : '否' }}</a-descriptions-item>
            <a-descriptions-item label="投诉状态">{{ orderDetail.complaint_status === 1 ? '是' : '否' }}</a-descriptions-item>
            <a-descriptions-item label="审核状态">{{ getReviewStatusText(orderDetail.review_status) }}</a-descriptions-item>
            <a-descriptions-item label="审核员">{{ orderDetail.sales_assignee_name || '无' }}</a-descriptions-item>
            <!--            <a-descriptions-item label="业务员">{{ orderDetail.sales_assignee_name || '无' }}</a-descriptions-item>-->
            <a-descriptions-item label="催收员">{{ orderDetail.collection_assignee_name || '无' }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ orderDetail.created_at }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ orderDetail.updated_at }}</a-descriptions-item>
            <a-descriptions-item label="放款时间">{{ orderDetail.disbursed_at || '无' }}</a-descriptions-item>
          </a-descriptions>
        </div>
      </a-spin>
    </a-modal>

    <!-- 关闭订单弹窗 -->
    <a-modal
      v-model:visible="closeModalVisible"
      title="关闭订单"
      @ok="handleCloseSubmit"
      @cancel="closeModalVisible = false"
    >
      <a-form ref="closeFormRef" :model="closeForm" layout="vertical">
        <a-form-item field="reason" label="关闭原因" :rules="[{ required: true, message: '请输入关闭原因' }]">
          <a-textarea
            v-model="closeForm.reason"
            placeholder="请输入关闭原因"
            :max-length="500"
            show-word-limit
            :auto-size="{ minRows: 3, maxRows: 6 }"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 修改渠道弹窗 -->
    <a-modal
      v-model:visible="editChannelModalVisible"
      title="修改渠道"
      @ok="handleEditChannelSubmit"
      @cancel="editChannelModalVisible = false"
    >
      <a-form ref="editChannelFormRef" :model="editChannelForm" layout="vertical">
        <a-form-item field="channel_id" label="渠道" :rules="[{ required: true, message: '请选择渠道' }]">
          <a-select
            v-model="editChannelForm.channel_id"
            placeholder="请选择渠道"
            :loading="channelLoading"
          >
            <a-option
              v-for="option in channelOptions"
              :key="option.id"
              :value="option.value"
              :label="option.label"
            />
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 分配订单弹窗 -->
    <a-modal
      v-model:visible="assignModalVisible"
      title="分配订单"
      width="1000px"
      :footer="false"
      :unmount-on-close="false"
    >
      <div class="assign-order-container">
        <!-- 筛选条件 -->
        <div class="filter-section">
          <a-form
            ref="assignQueryFormRef"
            :model="assignQueryForm"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
            auto-label-width
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="user_name" label="下单人姓名">
                  <a-input
                    v-model="assignQueryForm.user_name"
                    placeholder="请输入下单人姓名"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="user_mobile" label="手机号">
                  <a-input
                    v-model="assignQueryForm.user_mobile"
                    placeholder="请输入手机号"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="order_no" label="订单编号">
                  <a-input
                    v-model="assignQueryForm.order_no"
                    placeholder="请输入订单编号"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="submittedTimeRange" label="创建时间">
                  <a-range-picker
                    v-model="assignTimeRange"
                    style="width: 100%"
                    @change="handleAssignTimeChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="16">
                <a-form-item>
                  <a-space>
                    <a-button type="primary" @click="handleSearchPendingOrders">
                      <template #icon>
                        <icon-search />
                      </template>
                      查询
                    </a-button>
                    <a-button
                      type="dashed"
                      :disabled="!isAdmin"
                      @click="handleAssociateUser">
                      <template #icon>
                        <icon-link />
                      </template>
                      关联用户
                    </a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>

        <!-- 订单列表 -->
        <div class="pending-orders-table">
          <a-table
            row-key="id"
            :loading="pendingOrdersLoading"
            :pagination="pendingOrdersPagination"
            :data="pendingOrdersData"
            :bordered="false"
            size="small"
            v-model:selectedKeys="selectedOrderIds"
            :row-selection="{
              type: 'checkbox',
              showCheckedAll: true,
              onlyCurrent: false
            }"
            @page-change="handlePendingOrdersPageChange"
            @page-size-change="handlePendingOrdersPageSizeChange"
          >
            <template #columns>
              <a-table-column title="序号" :width="60" align="center">
                <template #cell="{ rowIndex }">
                  {{ (pendingOrdersPagination.current - 1) * pendingOrdersPagination.pageSize + rowIndex + 1 }}
                </template>
              </a-table-column>
              <a-table-column title="订单编号" data-index="order_no" :width="140" />
              <a-table-column title="渠道" data-index="channel_name" :width="120" />
              <a-table-column title="下单人姓名" data-index="user_name" :width="120" />
              <a-table-column title="下单人手机号" data-index="user_mobile" :width="120" />
              <a-table-column title="已付期数" :width="100">
                <template #cell="{ record }">
                  {{ record.paid_periods !== undefined && record.total_periods ? `${record.paid_periods}/${record.total_periods}` : '0/0' }}
                </template>
              </a-table-column>
              <a-table-column title="总金额" data-index="total_repayable_amount" :width="120" align="right">
                <template #cell="{ record }">
                  {{ formatCurrency(record.total_repayable_amount || 0) }}
                </template>
              </a-table-column>
              <a-table-column title="已付金额" data-index="amount_paid" :width="120" align="right">
                <template #cell="{ record }">
                  {{ formatCurrency(record.amount_paid || 0) }}
                </template>
              </a-table-column>
              <a-table-column title="订单状态" data-index="status" :width="100">
                <template #cell="{ record }">
                  {{ getOrderStatusText(record.status) }}
                </template>
              </a-table-column>
              <a-table-column title="操作" :width="140" fixed="right">
                <template #cell="{ record }">
                  <a-space>
                    <a-button size="mini" type="primary" @click="handleClaimOrder(record)">确认认领</a-button>
                  </a-space>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
      </div>
    </a-modal>

    <!-- 用户列表弹窗 -->
    <a-modal
      v-model:visible="userModalVisible"
      title="选择业务员"
      width="800px"
      @cancel="handleCancelUserSelection"
      @before-ok="handleBeforeUserSelect"
      :mask-closable="false"
      :unmount-on-close="false"
      :ok-button-props="{ loading: assignLoading }"
      @close="handleModalClose"
    >
      <a-spin :loading="userListLoading">
        <a-table
          row-key="id"
          :data="userList"
          :pagination="false"
          size="small"
        >
          <template #columns>
            <a-table-column align="center" :width="80">
              <template #cell="{ record }">
                <a-radio
                  :model-value="selectedUserId"
                  :value="record.id"
                  @change="() => selectedUserId = record.id"
                />
              </template>
            </a-table-column>
            <a-table-column title="序号" :width="60" align="center">
              <template #cell="{ rowIndex }">
                {{ rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column title="账号" data-index="username" :width="120" />
            <a-table-column title="姓名" data-index="name" :width="120" />
            <a-table-column title="邮箱" data-index="email" :width="180" />
            <a-table-column title="是否启用" data-index="status" :width="100">
              <template #cell="{ record }">
                {{ record.status === 0 ? '启用' : '禁用' }}
              </template>
            </a-table-column>
          </template>
        </a-table>
      </a-spin>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import { useRouter } from 'vue-router';
import {
  IconSearch,
  IconRefresh,
  IconUserAdd,
  IconLink,
} from '@arco-design/web-vue/es/icon';
import type {
  OrderQueryParams,
  OrderListItem,
  UpdateOrderChannelRequest,
  OrderAssignResponse
} from '@/api/ordermanagement';
import {
  getOrderList,
  getOrderStatus,
  closeOrder,
  processDisbursement,
  updateOrderChannel,
  claimOrder,
  assignOrder
} from '@/api/ordermanagement';
import { getChannelList, getChannelOptions } from '@/api/channel';
import type { ChannelItem, ChannelQueryParams, ChannelOptionResponse } from '@/api/channel';
import { getMemberList, BusinessAccount } from '@/api/system/member';
import { defHttp } from '@/utils/http';
import { useRoute } from 'vue-router';
import { useUserStore } from '@/store';

// 路由
const router = useRouter();
const route = useRoute();

// 响应式数据
const loading = ref(false);
const tableData = ref<OrderListItem[]>([]);
const channelOptions = ref<{ id: number; name: string; value: number; label: string; }[]>([]);
const channelLoading = ref(false);

// 查询表单
import useOrderManagementStore from '@/store/modules/ordermanagement';

const orderStore = useOrderManagementStore();

const queryFormRef = ref();
const resetFormData = {
  order_no: '',
  user_name: '',
  user_id_card: '',
  user_mobile: '',
  channel_id: undefined,
  customer_origin: '',
  initial_order_channel_id: undefined,
  status: undefined,
  sales_assignee_name: '',
  submitted_at_start: '',
  submitted_at_end: '',
  bill_due_date_start: '',
  bill_due_date_end: '',
}

const queryForm = reactive<OrderQueryParams>({
  ...resetFormData,
  ...orderStore.queryForm,
  page: 1,
  page_size: 20,
});
// 监听表单变化并同步到store
watch(queryForm, (newVal) => {
  orderStore.setQueryForm(newVal);
}, { deep: true });

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
});

// 时间范围
const submittedTimeRange = ref<string[]>([]);
const disbursedTimeRange = ref<string[]>([]);
const completedTimeRange = ref<string[]>([]);
const billDueDate = ref<string[]>([]);

// 订单详情弹窗
const detailModalVisible = ref(false);
const detailLoading = ref(false);
const orderDetail = ref<OrderListItem | null>(null);

// 关闭订单弹窗
const closeModalVisible = ref(false);
const closeFormRef = ref();
const closeForm = reactive({
  reason: '',
});
const currentOrderNo = ref('');

// 修改渠道弹窗
const editChannelModalVisible = ref(false);
const editChannelFormRef = ref();
const editChannelForm = reactive<UpdateOrderChannelRequest>({
  channel_id: 0,
});
const currentOrderId = ref(0);

// 分配订单弹窗
const assignModalVisible = ref(false);
const assignQueryFormRef = ref();
const assignQueryForm = reactive<OrderQueryParams>({
  user_name: '',
  user_mobile: '',
  order_no: '',
  submitted_at_start: '',
  submitted_at_end: '',
});
const assignTimeRange = ref<string[]>([]);
const pendingOrdersLoading = ref(false);
const pendingOrdersPagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
});
const pendingOrdersData = ref<OrderListItem[]>([]);

// 选中的订单ID数组
// 选中的订单ID数组，需要确保初始为空数组
const selectedOrderIds = ref<number[]>([]);

// 用户列表弹窗
const userModalVisible = ref(false);
const userListLoading = ref(false);
const userList = ref<BusinessAccount[]>([]);
const selectedUserId = ref<number | null>(null);

// 分配订单加载状态
const assignLoading = ref(false);

// 判断当前用户是否为管理员
const isAdmin = computed(() => {
  // 从localStorage获取userInfo
  const userInfoStr = localStorage.getItem('userInfo');
  if (!userInfoStr) return false;

  try {
    const userInfo = JSON.parse(userInfoStr);
    // 使用is_admin属性判断是否为管理员
    return userInfo.is_admin === true;
  } catch (e) {
    console.error('解析userInfo失败:', e);
    return false;
  }
});

// 初始化
onMounted(() => {
  // 检查URL中是否有查询参数
  if (route.query) {
    // 处理URL查询参数
    if (route.query.user_name) {
      queryForm.user_name = route.query.user_name as string;
      console.log('从URL获取用户名筛选条件:', queryForm.user_name);
    }

    // 处理其他可能的查询参数
    if (route.query.order_no) {
      queryForm.order_no = route.query.order_no as string;
    }

    if (route.query.user_id_card) {
      queryForm.user_id_card = route.query.user_id_card as string;
    }
  }

  fetchChannelOptions();
  fetchData();
});

// 获取渠道选项
const fetchChannelOptions = async () => {
  try {
    channelLoading.value = true;
    console.log('开始请求渠道选项API');

    // 使用专用的getChannelOptions接口获取渠道选项
    const response = await getChannelOptions();
    console.log('渠道选项API返回:', response);

    // response现在是ApiResponseData<ChannelOptionResponse[]>类型
    if (response && response.code === 0 && Array.isArray(response.data)) {
      // API请求成功，使用返回的data数组
      channelOptions.value = response.data;
      console.log('成功加载渠道选项:', channelOptions.value);
    } else {
      console.error('渠道选项数据格式不正确或返回错误:', response);
      // 即使发生错误，也结束loading状态
      channelOptions.value = [];
    }
  } catch (error) {
    console.error('获取渠道选项失败:', error);
    Message.error('获取渠道选项失败');
    channelOptions.value = [];
  } finally {
    channelLoading.value = false;
  }
};

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true;
    const params = {
      ...queryForm,
      page: pagination.current,
      page_size: pagination.pageSize,
    };

    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key as keyof typeof params] === '' || params[key as keyof typeof params] === undefined || params[key as keyof typeof params] === null) {
        delete params[key as keyof typeof params];
      }
    });

    console.log('[DEBUG] 订单列表请求参数:', JSON.stringify(params));
    console.log('[DEBUG] 是否包含user_name参数:', params.hasOwnProperty('user_name'));

    if (params.user_name) {
      console.log('[DEBUG] user_name参数值:', params.user_name);
    }

    const response = await getOrderList(params);
    console.log('[DEBUG] API返回完整响应:', response); // 添加调试日志

    if (response && response.data) {
      tableData.value = response.data;
      pagination.total = response.total || 0;
      pagination.current = response.page || 1;
      pagination.pageSize = response.pageSize || 20;
    } else {
      console.error('响应格式错误:', response);
      tableData.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
    Message.error('获取订单列表失败');
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};

// 重置
const handleReset = () => {
  // queryFormRef.value?.resetFields();
  orderStore.setQueryForm(resetFormData);
  submittedTimeRange.value = [];
  billDueDate.value = [];

  queryForm.order_no = '';
  queryForm.user_name = '';
  queryForm.user_id_card = '';
  queryForm.user_mobile = '';
  queryForm.channel_id = undefined;
  queryForm.customer_origin = '';
  queryForm.initial_order_channel_id = undefined;
  queryForm.status = undefined;
  queryForm.sales_assignee_name = '';
  queryForm.submitted_at_start = '';
  queryForm.submitted_at_end = '';
  queryForm.bill_due_date_start = '';
  queryForm.bill_due_date_end = '';
  pagination.current = 1;
  fetchData();
};

// 刷新
const handleRefresh = () => {
  fetchData();
};

// 分页变化
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchData();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchData();
};

// 时间范围变化
const handleSubmittedTimeChange = (value: string[]) => {
  if (value && value.length === 2) {
    queryForm.submitted_at_start = value[0];
    queryForm.submitted_at_end = value[1];
  } else {
    queryForm.submitted_at_start = '';
    queryForm.submitted_at_end = '';
  }
};
// 时间范围变化-到期
const handleBillDueDateChange = (value: string[]) => {
  if (value && value.length === 2) {
    queryForm.bill_due_date_start = value[0];
    queryForm.bill_due_date_end = value[1];
  } else {
    queryForm.bill_due_date_start = '';
    queryForm.bill_due_date_end = '';
  }
};

const handleDisbursedTimeChange = (value: string[]) => {
  if (value && value.length === 2) {
    queryForm.disbursed_at_start = value[0];
    queryForm.disbursed_at_end = value[1];
  } else {
    queryForm.disbursed_at_start = '';
    queryForm.disbursed_at_end = '';
  }
};

const handleCompletedTimeChange = (value: string[]) => {
  if (value && value.length === 2) {
    queryForm.completed_at_start = value[0];
    queryForm.completed_at_end = value[1];
  } else {
    queryForm.completed_at_start = '';
    queryForm.completed_at_end = '';
  }
};

// 查看订单详情 - 修改为路由跳转
const handleView = (record: OrderListItem) => {
  router.push({
    path: `/ordermanagement/Detail/${record.id}`,
    query: { orderNo: record.order_no, uid: record.user_id }
  });
};

// 关闭订单
const handleClose = (record: OrderListItem) => {
  currentOrderNo.value = record.order_no;
  closeForm.reason = '';
  closeModalVisible.value = true;
};

// 提交关闭订单
const handleCloseSubmit = async () => {
  try {
    await closeFormRef.value?.validate();
    await closeOrder(currentOrderNo.value, closeForm.reason);
    Message.success('关闭订单成功');
    closeModalVisible.value = false;
    fetchData();
  } catch (error) {
    Message.error('关闭订单失败');
  }
};

// 处理放款
const handleProcessDisbursement = async (record: OrderListItem) => {
  try {
    await processDisbursement(record.order_no);
    Message.success('处理放款请求已提交');
    fetchData();
  } catch (error) {
    Message.error('处理放款失败');
  }
};

// 修改渠道
const handleEditChannel = (record: OrderListItem) => {
  currentOrderId.value = record.id;
  console.log('当前订单渠道名称:', record.channel_name);
  console.log('可用渠道选项:', channelOptions.value);

  // 通过渠道名称查找对应的渠道选项
  // 先检查label字段
  let channelOption = channelOptions.value.find(item => item.label === record.channel_name);

  // 如果没找到，尝试使用name字段
  if (!channelOption) {
    channelOption = channelOptions.value.find(item => item.name === record.channel_name);
  }

  if (channelOption) {
    // 优先使用value字段，如果没有则使用id字段
    editChannelForm.channel_id = channelOption.value || channelOption.id || 0;
    console.log('已找到匹配的渠道选项:', channelOption);
  } else {
    console.warn('未找到匹配的渠道:', record.channel_name);
    editChannelForm.channel_id = 0;
  }

  editChannelModalVisible.value = true;
};

// 提交修改渠道
const handleEditChannelSubmit = async () => {
  try {
    await editChannelFormRef.value?.validate();
    console.log('提交渠道修改，channelId:', editChannelForm.channel_id);

    // 确保使用正确的渠道ID提交
    const response = await defHttp.post<any>({
      url: '/order/manager/updateOrderChannel',
      data: {
        order_id: currentOrderId.value,
        channel_id: editChannelForm.channel_id
      }
    }, {
      isTransformResponse: false // 不转换响应，获取原始数据
    });

    console.log('修改渠道API响应:', response);

    if (response && response.code === 0) {
      Message.success('修改渠道成功');
      editChannelModalVisible.value = false;
      fetchData();
    } else {
      // 显示API返回的具体错误信息
      let errorMsg = '修改渠道失败';

      if (response?.message) {
        errorMsg = response.message;
      }

      // 如果data字段是字符串，优先使用它作为错误消息
      if (response?.data && typeof response.data === 'string') {
        errorMsg = response.data;
      }

      Message.error(errorMsg);
      throw new Error(errorMsg);
    }
  } catch (error) {
    console.error('修改渠道失败:', error);
    if (!(error instanceof Error && error.message !== '修改渠道失败')) {
      Message.error('修改渠道失败，请稍后重试');
    }
  }
};

// 工具函数
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
  }).format(amount);
};

// 获取订单状态文本
const getOrderStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '待放款',
    1: '放款中',
    2: '交易关闭',
    3: '交易完成',
  };
  return statusMap[status] || '未知状态';
};

// 获取审核状态文本
const getReviewStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '待审核',
    1: '审核通过',
    2: '审核拒绝',
  };
  return statusMap[status] || '未知状态';
};

// 检查当前用户是否是订单的分配业务员
const isOrderAssignee = (record: OrderListItem): boolean => {
  // 从localStorage获取userInfo中的uid
  const userInfoStr = localStorage.getItem('userInfo');
  if (!userInfoStr) return false;

  try {
    const userInfo = JSON.parse(userInfoStr);
    const currentUserId = Number(userInfo.uid || 0);

    // 获取订单分配的业务员ID
    const salesAssigneeId = Number(record.sales_assignee_id || 0);

    // 判断用户是否有权限操作 - 必须是订单的分配业务员，管理员也必须满足这个条件
    return (currentUserId > 0 && currentUserId === salesAssigneeId);
  } catch (e) {
    console.error('解析userInfo失败:', e);
    return false;
  }
};

// 显示权限错误提示
const showPermissionError = (): void => {
  Message.warning('没有操作当前订单的权限，只有订单分配的业务员才能修改渠道');
};

// 分配订单弹窗
const handleShowAssignModal = () => {
  // 只在初次显示弹窗时重置选中状态
  selectedOrderIds.value = [];
  assignModalVisible.value = true;
  fetchPendingOrders();
};

// 待分配订单时间范围变化
const handleAssignTimeChange = (value: string[]) => {
  if (value && value.length === 2) {
    assignQueryForm.submitted_at_start = value[0];
    assignQueryForm.submitted_at_end = value[1];
  } else {
    assignQueryForm.submitted_at_start = '';
    assignQueryForm.submitted_at_end = '';
  }
};

// 查询待分配订单
const handleSearchPendingOrders = () => {
  pendingOrdersPagination.current = 1;
  fetchPendingOrders();
};

// 获取待分配订单列表
const fetchPendingOrders = async () => {
  try {
    // 删除对选中状态的清空，避免每次刷新数据都清空用户选择
    // selectedOrderIds.value = [];
    pendingOrdersLoading.value = true;
    const params = {
      ...assignQueryForm,
      // status: 0,                // 待放款状态
      is_sales_assigned: 0,     // 未分配业务员
      page: pendingOrdersPagination.current,
      page_size: pendingOrdersPagination.pageSize,
    };

    // 移除空值但保留status和is_sales_assigned
    Object.keys(params).forEach(key => {
      if (key !== 'status' && key !== 'is_sales_assigned' &&
        (params[key as keyof typeof params] === '' ||
          params[key as keyof typeof params] === undefined ||
          params[key as keyof typeof params] === null)) {
        delete params[key as keyof typeof params];
      }
    });

    console.log('待分配订单请求参数:', JSON.stringify(params));

    // 使用API获取待分配订单列表
    const response = await getOrderList(params);
    console.log('待分配订单API返回:', response);

    if (response && response.data) {
      pendingOrdersData.value = response.data;
      pendingOrdersPagination.total = response.total || 0;
      pendingOrdersPagination.current = response.page || 1;
      pendingOrdersPagination.pageSize = response.pageSize || 20;
    } else {
      console.error('响应格式错误:', response);
      pendingOrdersData.value = [];
      pendingOrdersPagination.total = 0;
    }
  } catch (error) {
    console.error('获取待分配订单列表失败:', error);
    Message.error('获取待分配订单列表失败');
    pendingOrdersData.value = [];
    pendingOrdersPagination.total = 0;
  } finally {
    pendingOrdersLoading.value = false;
  }
};

// 分页变化
const handlePendingOrdersPageChange = (page: number) => {
  pendingOrdersPagination.current = page;
  fetchPendingOrders();
};

const handlePendingOrdersPageSizeChange = (pageSize: number) => {
  pendingOrdersPagination.pageSize = pageSize;
  pendingOrdersPagination.current = 1;
  fetchPendingOrders();
};

// 选中行变化的处理函数
const handleSelectionChange = (rowKeys: (number | string)[]) => {
  console.log('表格选中状态变化，selectedKeys:', rowKeys, '类型:', typeof rowKeys[0], '长度:', rowKeys.length);
  console.log('当前selectedOrderIds值:', selectedOrderIds.value, '长度:', selectedOrderIds.value.length);
  // 不再需要手动设置selectedOrderIds，v-model会自动处理
};

// 获取用户列表
const fetchUserList = async () => {
  try {
    userListLoading.value = true;
    const res = await getMemberList();
    console.log('获取用户列表返回:', res);

    if (res?.code === 0 && Array.isArray(res.data)) {
      userList.value = res.data;
    } else {
      Message.error('获取用户列表失败');
      userList.value = [];
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    Message.error('获取用户列表失败');
    userList.value = [];
  } finally {
    userListLoading.value = false;
  }
};

// 关联用户
const handleAssociateUser = async () => {
  if (!isAdmin.value) {
    Message.warning('没有分配权限');
    return;
  }

  console.log('当前选中的订单ID数组:', selectedOrderIds.value, '长度:', selectedOrderIds.value.length);

  // 检查是否有选中的订单
  if (!selectedOrderIds.value || selectedOrderIds.value.length === 0) {
    Message.warning('请先选择需要分配的订单');
    return;
  }

  // 获取用户列表
  await fetchUserList();
  selectedUserId.value = null;
  userModalVisible.value = true;
};

// 取消用户选择
const handleCancelUserSelection = () => {
  userModalVisible.value = false;
  selectedUserId.value = null;
};

// 监听模态框关闭事件，确保清空状态
const handleModalClose = () => {
  selectedUserId.value = null;
};

// 用户选择弹窗确认前处理
const handleBeforeUserSelect = (done: (closed: boolean) => void) => {
  console.log('handleBeforeUserSelect 被调用, selectedUserId:', selectedUserId.value);

  // 检查是否已选择业务员
  if (selectedUserId.value === null) {
    Message.warning('请选择要分配的业务员');
    done(false); // 阻止弹窗关闭
    return;
  }

  // 再次检查是否有选中的订单
  if (!selectedOrderIds.value || selectedOrderIds.value.length === 0) {
    Message.warning('请先选择需要分配的订单');
    done(false); // 阻止弹窗关闭
    return;
  }

  // 显示加载状态
  assignLoading.value = true;

  // 确保已经有selectedUserId值
  const userId = selectedUserId.value;

  // 从localStorage获取userInfo中的uid
  const userInfoStr = localStorage.getItem('userInfo');
  if (!userInfoStr) {
    Message.error('无法获取当前用户信息，请重新登录');
    assignLoading.value = false;
    done(false);
    return;
  }

  let operatorId;
  try {
    const userInfo = JSON.parse(userInfoStr);
    operatorId = userInfo.uid;
  } catch (e) {
    console.error('解析userInfo失败:', e);
    Message.error('无法获取当前用户信息，请重新登录');
    assignLoading.value = false;
    done(false);
    return;
  }

  if (!operatorId) {
    Message.error('无法获取当前用户信息，请重新登录');
    assignLoading.value = false;
    done(false);
    return;
  }

  // 调用分配订单API
  console.log('准备分配订单:', selectedOrderIds.value, '给用户ID:', userId);

  assignOrder(
    selectedOrderIds.value,
    userId,
    operatorId
  ).then((response: OrderAssignResponse) => {
    console.log('分配订单API完整响应:', JSON.stringify(response));

    // 根据实际响应结构判断操作是否成功
    if (response &&
      response.success_count > 0 &&
      response.success_count === response.total_count) {
      Message.success(`成功将${response.success_count}个订单分配给业务员`);
      // 清空选中的订单列表，避免重复分配
      selectedOrderIds.value = [];
      fetchPendingOrders(); // 刷新待分配订单列表
      fetchData(); // 刷新主订单列表
      done(true); // 允许弹窗关闭
    } else {
      // 如果有失败的订单，显示失败信息
      const failureCount = response?.failure_count || 0;
      const errorMsg = failureCount > 0
        ? `有${failureCount}个订单分配失败`
        : '分配订单失败';
      console.error('分配订单失败:', errorMsg);
      Message.error(errorMsg);
      done(false); // 阻止弹窗关闭
    }
  }).catch(error => {
    console.error('分配订单过程中发生错误:', error);
    Message.error(error instanceof Error ? error.message : '分配订单失败');
    done(false); // 阻止弹窗关闭
  }).finally(() => {
    // 无论结果如何，都关闭加载状态
    assignLoading.value = false;
  });

  // 由于异步操作，这里返回一个Promise，阻止弹窗自动关闭
  // Arco Design Vue会等待Promise解析后才决定是否关闭弹窗
  return new Promise<void>(() => {});
};

// 确认认领订单
const handleClaimOrder = async (record: OrderListItem) => {
  try {
    // 从localStorage获取userInfo中的uid
    const userInfoStr = localStorage.getItem('userInfo');
    if (!userInfoStr) {
      Message.error('无法获取当前用户信息，请重新登录');
      return;
    }

    let uid;
    try {
      const userInfo = JSON.parse(userInfoStr);
      uid = userInfo.uid;
    } catch (e) {
      console.error('解析userInfo失败:', e);
      Message.error('无法获取当前用户信息，请重新登录');
      return;
    }

    if (!uid) {
      Message.error('无法获取当前用户信息，请重新登录');
      return;
    }

    await claimOrder(record.id, uid);
    Message.success(`成功认领订单${record.order_no}`);
    fetchPendingOrders(); // 刷新列表
    fetchData(); // 刷新主订单列表
  } catch (error) {
    console.error('认领订单失败:', error);
    Message.error('认领订单失败');
  }
};
</script>

<style scoped>
.container {
  padding: 0 20px 20px 20px;
}

.general-card {
  margin-bottom: 16px;
}

.assign-order-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 70vh;
}

.filter-section {
  margin-bottom: 16px;
}

.pending-orders-table {
  flex: 1;
  overflow: auto;
  margin-top: 8px;
  max-height: 500px;
}
</style> 