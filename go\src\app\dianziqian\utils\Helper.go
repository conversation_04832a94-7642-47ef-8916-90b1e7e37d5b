package utils

import (
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fincore/utils/log"
	"fmt"
	"io/ioutil"
	"os"
)

// Base64编码
func Base64Encode(dataString string) string {
	encodeString := base64.StdEncoding.EncodeToString([]byte(dataString))
	return encodeString
}

// Base64解码
func Base64Decode(encodeString string) []byte {
	decodeBytes, err := base64.StdEncoding.DecodeString(encodeString)
	if err != nil {
		log.Error(err.Error())
	}
	return decodeBytes
}

// 将文件进行Base64编码
func Base64EncodeByFile(filePath string) string {
	file, err := os.Open(filePath)
	if err != nil {
		log.Info(err.Error())
	}
	fileBytes, err := ioutil.ReadAll(file)
	if err != nil {
		log.Error(err.Error())
	} else {
		file.Close()
	}
	encodeString := base64.StdEncoding.EncodeToString(fileBytes)
	return encodeString
}

// 保存文件
func SaveFileByBase64(base64String, outFilePath string) {
	// 将Base64字符串解码为[]byte
	var fileBytes = Base64Decode(base64String)

	saveFileErr := ioutil.WriteFile(outFilePath, fileBytes, 0666)

	if saveFileErr != nil {
		log.Error("文件保存失败:" + saveFileErr.Error())
		panic(saveFileErr)
	} else {
		log.Error("文件保存成功:" + outFilePath)
	}
}

// byte转json
func BytetoJson(initResult []byte) map[string]interface{} {
	var initResultJson interface{}
	json.Unmarshal(initResult, &initResultJson)
	jsonMap, err := initResultJson.(map[string]interface{})
	if !err {
		log.Error("DO SOMETHING!")
		return nil
	}
	return jsonMap
}

// 生成32位的强随机字符串
func GenerateRandomString() (string, error) {
	length := 32
	// 计算需要的字节数
	// base64编码会将3字节转换为4个字符，因此需要根据目标长度计算所需的字节数
	// 并确保有足够的字节来生成所需长度的字符串
	// bytesNeeded := (length*3 + 3) / 4 // 向上取整
	bytesNeeded := 24

	// 生成随机字节
	randomBytes := make([]byte, bytesNeeded)
	_, err := rand.Read(randomBytes)
	if err != nil {
		return "", fmt.Errorf("生成随机字节失败: %v", err)
	}

	// 编码为base64字符串
	encoded := base64.RawURLEncoding.EncodeToString(randomBytes)

	// 截取到所需长度
	return encoded[:length], nil
}

// StructToJson 结构体转 json
func StructToJson(initResult interface{}) (string, error) {
	jsonB, err := json.Marshal(initResult)
	if err != nil {
		return "", err
	}
	return string(jsonB), nil
}
