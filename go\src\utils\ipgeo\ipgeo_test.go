package ipgeo

import (
	"testing"
	"time"
)

func TestGetIPLocation(t *testing.T) {
	tests := []struct {
		name     string
		ip       string
		wantErr  bool
		expected string
	}{
		{
			name:     "本地IP",
			ip:       "127.0.0.1",
			wantErr:  false,
			expected: "本地",
		},
		{
			name:     "空IP",
			ip:       "",
			wantErr:  false,
			expected: "本地",
		},
		{
			name:     "公网IP - 中国IP",
			ip:       "***************",
			wantErr:  false,
			expected: "", // 实际结果会根据API返回
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			location, err := GetIPLocation(tt.ip)

			if (err != nil) != tt.wantErr {
				t.<PERSON>("GetIPLocation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if location == nil {
				t.<PERSON>rf("GetIPLocation() returned nil location")
				return
			}

			// 对于本地IP，检查特定的返回值
			if tt.ip == "127.0.0.1" || tt.ip == "" {
				if location.Country != "本地" {
					t.<PERSON><PERSON>("GetIPLocation() for local IP, got country = %v, want 本地", location.Country)
				}
			}

			t.Logf("IP: %s, Location: %+v", tt.ip, location)
			t.Logf("Formatted: %s", FormatLocation(location))
		})
	}
}

func TestFormatLocation(t *testing.T) {
	tests := []struct {
		name     string
		location *IPLocation
		expected string
	}{
		{
			name:     "空位置",
			location: nil,
			expected: "",
		},
		{
			name: "完整位置信息",
			location: &IPLocation{
				IP:      "*******",
				Country: "中国",
				Region:  "广东省",
				City:    "深圳市",
			},
			expected: "中国 广东省 深圳市",
		},
		{
			name: "只有国家",
			location: &IPLocation{
				IP:      "*******",
				Country: "美国",
			},
			expected: "美国",
		},
		{
			name: "本地IP",
			location: &IPLocation{
				IP:      "127.0.0.1",
				Country: "本地",
				City:    "本地",
			},
			expected: "本地",
		},
		{
			name: "美国IP示例",
			location: &IPLocation{
				IP:      "*******",
				Country: "美国",
				Region:  "弗吉尼亚州",
				City:    "Ashburn",
			},
			expected: "美国 弗吉尼亚州 Ashburn",
		},
		{
			name: "中国IP示例",
			location: &IPLocation{
				IP:      "***************",
				Country: "中国",
				Region:  "山东",
				City:    "青岛市",
			},
			expected: "中国 山东 青岛市",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := FormatLocation(tt.location)
			if result != tt.expected {
				t.Errorf("FormatLocation() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestGetIPLocationString(t *testing.T) {
	tests := []struct {
		name string
		ip   string
	}{
		{
			name: "本地IP",
			ip:   "127.0.0.1",
		},
		{
			name: "空IP",
			ip:   "",
		},
		{
			name: "公网IP - 谷歌DNS",
			ip:   "*******",
		},
		{
			name: "公网IP - 中国IP",
			ip:   "***************",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetIPLocationString(tt.ip)

			// 结果不应该为空
			if result == "" {
				t.Errorf("GetIPLocationString() returned empty string for IP: %s", tt.ip)
			}

			t.Logf("IP: %s, Location String: %s", tt.ip, result)
		})
	}
}

func TestIsValidIP(t *testing.T) {
	tests := []struct {
		name string
		ip   string
		want bool
	}{
		{
			name: "有效IPv4",
			ip:   "***********",
			want: true,
		},
		{
			name: "有效IPv6",
			ip:   "2001:db8::1",
			want: true,
		},
		{
			name: "空IP",
			ip:   "",
			want: false,
		},
		{
			name: "无效格式",
			ip:   "invalid",
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsValidIP(tt.ip); got != tt.want {
				t.Errorf("IsValidIP() = %v, want %v", got, tt.want)
			}
		})
	}
}

// TestBatchGetIPLocations 测试批量获取IP地理位置
func TestBatchGetIPLocations(t *testing.T) {
	ips := []string{"127.0.0.1", "*******", ""}

	results := BatchGetIPLocations(ips)

	// 检查结果数量（空IP会被跳过）
	expectedCount := 2
	if len(results) != expectedCount {
		t.Errorf("BatchGetIPLocations() returned %d results, want %d", len(results), expectedCount)
	}

	// 检查本地IP的结果
	if location, exists := results["127.0.0.1"]; exists {
		if location.Country != "本地" {
			t.Errorf("BatchGetIPLocations() for 127.0.0.1, got country = %v, want 本地", location.Country)
		}
	} else {
		t.Errorf("BatchGetIPLocations() missing result for 127.0.0.1")
	}

	for ip, location := range results {
		t.Logf("Batch result - IP: %s, Location: %+v", ip, location)
	}
}

// TestGetIPLocationWithCache 测试带缓存的IP地理位置获取功能
func TestGetIPLocationWithCache(t *testing.T) {
	// 测试用的公网IP地址
	testIP := "*******"

	// 清除可能存在的缓存
	_ = ClearIPLocationCache(testIP)

	// 第一次调用 - 应该从API获取并缓存
	t.Logf("第一次调用 - 从API获取数据")
	start1 := time.Now()
	location1, err := GetIPLocation(testIP)
	duration1 := time.Since(start1)

	if err != nil {
		t.Fatalf("第一次获取IP地理位置失败: %v", err)
	}

	if location1 == nil {
		t.Fatal("第一次获取的地理位置信息为空")
	}

	t.Logf("第一次调用结果: %s, 耗时: %v", FormatLocation(location1), duration1)

	// 等待一下确保缓存操作完成（因为是异步的）
	time.Sleep(100 * time.Millisecond)

	// 检查缓存是否存在
	if !CheckIPLocationCacheExists(testIP) {
		t.Error("缓存应该存在但实际不存在")
	}

	// 第二次调用 - 应该从缓存获取
	t.Logf("第二次调用 - 从缓存获取数据")
	start2 := time.Now()
	location2, err := GetIPLocation(testIP)
	duration2 := time.Since(start2)

	if err != nil {
		t.Fatalf("第二次获取IP地理位置失败: %v", err)
	}

	if location2 == nil {
		t.Fatal("第二次获取的地理位置信息为空")
	}

	t.Logf("第二次调用结果: %s, 耗时: %v", FormatLocation(location2), duration2)

	// 验证两次结果一致
	if location1.IP != location2.IP || location1.Country != location2.Country || location1.City != location2.City {
		t.Error("两次获取的结果不一致")
	}

	// 第二次调用应该明显更快（从缓存获取）
	if duration2 >= duration1 {
		t.Logf("警告: 第二次调用耗时 (%v) 不比第一次 (%v) 快，可能缓存未生效", duration2, duration1)
	}

	// 清理测试缓存
	if err := ClearIPLocationCache(testIP); err != nil {
		t.Logf("清理测试缓存失败: %v", err)
	}
}

// TestBatchGetIPLocationsWithCache 测试批量获取IP地理位置的缓存功能
func TestBatchGetIPLocationsWithCache(t *testing.T) {
	testIPs := []string{"*******", "*******", "***************"}

	// 清除可能存在的缓存
	for _, ip := range testIPs {
		_ = ClearIPLocationCache(ip)
	}

	// 第一次批量调用
	t.Logf("第一次批量调用 - 从API获取数据")
	start1 := time.Now()
	results1 := BatchGetIPLocations(testIPs)
	duration1 := time.Since(start1)

	t.Logf("第一次批量调用完成，耗时: %v", duration1)

	if len(results1) != len(testIPs) {
		t.Fatalf("第一次批量调用结果数量不正确，期望: %d, 实际: %d", len(testIPs), len(results1))
	}

	// 等待缓存操作完成
	time.Sleep(500 * time.Millisecond)

	// 第二次批量调用 - 应该从缓存获取
	t.Logf("第二次批量调用 - 从缓存获取数据")
	start2 := time.Now()
	results2 := BatchGetIPLocations(testIPs)
	duration2 := time.Since(start2)

	t.Logf("第二次批量调用完成，耗时: %v", duration2)

	if len(results2) != len(testIPs) {
		t.Fatalf("第二次批量调用结果数量不正确，期望: %d, 实际: %d", len(testIPs), len(results2))
	}

	// 验证结果一致性
	for _, ip := range testIPs {
		loc1, exists1 := results1[ip]
		loc2, exists2 := results2[ip]

		if !exists1 || !exists2 {
			t.Errorf("IP %s 的结果缺失", ip)
			continue
		}

		if loc1.Country != loc2.Country || loc1.City != loc2.City {
			t.Errorf("IP %s 两次获取的结果不一致", ip)
		}

		t.Logf("IP %s: %s", ip, FormatLocation(loc1))
	}

	// 第二次调用应该更快
	if duration2 >= duration1 {
		t.Logf("警告: 第二次批量调用耗时 (%v) 不比第一次 (%v) 快，可能缓存未生效", duration2, duration1)
	}

	// 清理测试缓存
	for _, ip := range testIPs {
		if err := ClearIPLocationCache(ip); err != nil {
			t.Logf("清理IP %s 的测试缓存失败: %v", ip, err)
		}
	}
}

// TestRefreshIPLocationCache 测试刷新缓存功能
func TestRefreshIPLocationCache(t *testing.T) {
	testIP := "*******"

	// 清除可能存在的缓存
	_ = ClearIPLocationCache(testIP)

	// 先获取一次数据建立缓存
	location1, err := GetIPLocation(testIP)
	if err != nil {
		t.Fatalf("初始获取IP地理位置失败: %v", err)
	}

	// 等待缓存建立
	time.Sleep(100 * time.Millisecond)

	// 验证缓存存在
	if !CheckIPLocationCacheExists(testIP) {
		t.Error("缓存应该存在")
	}

	// 刷新缓存
	location2, err := RefreshIPLocationCache(testIP)
	if err != nil {
		t.Fatalf("刷新缓存失败: %v", err)
	}

	// 验证结果
	if location1.IP != location2.IP {
		t.Error("刷新缓存前后IP不一致")
	}

	t.Logf("刷新缓存成功: %s", FormatLocation(location2))

	// 清理测试缓存
	if err := ClearIPLocationCache(testIP); err != nil {
		t.Logf("清理测试缓存失败: %v", err)
	}
}

// TestCacheManagementFunctions 测试缓存管理功能
func TestCacheManagementFunctions(t *testing.T) {
	testIP := "*******"

	// 清除可能存在的缓存
	_ = ClearIPLocationCache(testIP)

	// 验证缓存不存在
	if CheckIPLocationCacheExists(testIP) {
		t.Error("缓存应该不存在")
	}

	// 获取缓存键名
	cacheKey := GetIPLocationCacheKey(testIP)
	expectedKey := "ipgeo:location:*******"
	if cacheKey != expectedKey {
		t.Errorf("缓存键名不正确，期望: %s, 实际: %s", expectedKey, cacheKey)
	}

	// 建立缓存
	_, err := GetIPLocation(testIP)
	if err != nil {
		t.Fatalf("获取IP地理位置失败: %v", err)
	}

	// 等待缓存建立
	time.Sleep(100 * time.Millisecond)

	// 验证缓存存在
	if !CheckIPLocationCacheExists(testIP) {
		t.Error("缓存应该存在")
	}

	// 清除缓存
	if err := ClearIPLocationCache(testIP); err != nil {
		t.Fatalf("清除缓存失败: %v", err)
	}

	// 验证缓存已清除
	if CheckIPLocationCacheExists(testIP) {
		t.Error("缓存应该已被清除")
	}

	t.Logf("缓存管理功能测试通过")
}
