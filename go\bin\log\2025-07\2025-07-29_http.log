{"level":"dev.info","ts":"[2025-07-29 15:01:23.610]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6c82e78abacce582ffa","method":"GET","url":"/business/user/get_logininfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_logininfo","query":"_t=1753772471525"}
{"level":"dev.info","ts":"[2025-07-29 15:01:23.626]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6c82e78abacce582ffa","method":"GET","url":"/business/user/get_logininfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0159645,"response_size":1178}
{"level":"dev.info","ts":"[2025-07-29 15:01:28.475]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6c950717a1c6c109ee8","method":"GET","url":"/business/user/get_logininfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_logininfo","query":"_t=1753772488839"}
{"level":"dev.info","ts":"[2025-07-29 15:01:28.492]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6c950717a1c6c109ee8","method":"GET","url":"/business/user/get_logininfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0165546,"response_size":1178}
{"level":"dev.info","ts":"[2025-07-29 15:02:03.074]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d15ea99f94fd33304b","method":"GET","url":"/business/user/get_logininfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_logininfo","query":"_t=1753772523449"}
{"level":"dev.info","ts":"[2025-07-29 15:02:03.091]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6d15ea99f94fd33304b","method":"GET","url":"/business/user/get_logininfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0175971,"response_size":1178}
{"level":"dev.info","ts":"[2025-07-29 15:02:04.132]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d19dc7fe8c8deb2a76","method":"POST","url":"/business/user/login","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/login","query":""}
{"level":"dev.info","ts":"[2025-07-29 15:02:04.205]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6d19dc7fe8c8deb2a76","method":"POST","url":"/business/user/login","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0723296,"response_size":347}
{"level":"dev.info","ts":"[2025-07-29 15:02:04.231]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d1a3ade1cc98b08749","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_userinfo","query":"_t=*************"}
{"level":"dev.info","ts":"[2025-07-29 15:02:04.231]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d1a3ade1cca135de4f","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/account/get_menu","query":"_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:02:04.235]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d1a3ade1cc98b08749","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0036435,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.error","ts":"[2025-07-29 15:02:04.235]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d1a3ade1cca135de4f","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0036435,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:04.361]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d1ab6c421451ac980b","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_userinfo","query":"_t=*************"}
{"level":"dev.info","ts":"[2025-07-29 15:02:04.361]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d1ab6c42148c2c2543","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/account/get_menu","query":"_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:02:04.365]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d1ab6c421451ac980b","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0032406,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.error","ts":"[2025-07-29 15:02:04.365]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d1ab6c42148c2c2543","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0032406,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:04.493]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d1b343b06c3cded7fb","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_userinfo","query":"_t=*************"}
{"level":"dev.info","ts":"[2025-07-29 15:02:04.493]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d1b343b06c5df8155f","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/account/get_menu","query":"_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:02:04.498]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d1b343b06c5df8155f","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0050393,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.error","ts":"[2025-07-29 15:02:04.498]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d1b343b06c3cded7fb","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0050393,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:04.627]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d1bb4a99d8bd9b1994","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/account/get_menu","query":"_t=*************"}
{"level":"dev.info","ts":"[2025-07-29 15:02:04.627]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d1bb4a99d8b3722676","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_userinfo","query":"_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:02:04.632]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d1bb4a99d8b3722676","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0043839,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.error","ts":"[2025-07-29 15:02:04.632]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d1bb4a99d8bd9b1994","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0043839,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:04.767]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d1c399307c057d40ce","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_userinfo","query":"_t=*************"}
{"level":"dev.info","ts":"[2025-07-29 15:02:04.767]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d1c399307cf4e0d87f","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/account/get_menu","query":"_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:02:04.773]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d1c399307cf4e0d87f","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0056815,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.error","ts":"[2025-07-29 15:02:04.773]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d1c399307c057d40ce","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0056815,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:04.900]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d1cb88a9e8db4f7e93","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/account/get_menu","query":"_t=*************"}
{"level":"dev.info","ts":"[2025-07-29 15:02:04.903]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d1cbb28dbc728d2560","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_userinfo","query":"_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:02:04.905]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d1cb88a9e8db4f7e93","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0049488,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.error","ts":"[2025-07-29 15:02:04.907]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d1cbb28dbc728d2560","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0037955,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:11.557]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d35853a638e9918819","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_userinfo","query":"_t=1753772531908"}
{"level":"dev.error","ts":"[2025-07-29 15:02:11.563]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d35853a638e9918819","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0060273,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:11.592]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d35a682fe84240a9c6","method":"POST","url":"/business/user/logout","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/logout","query":""}
{"level":"dev.info","ts":"[2025-07-29 15:02:11.592]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6d35a682fe84240a9c6","method":"POST","url":"/business/user/logout","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0005055,"response_size":301}
{"level":"dev.info","ts":"[2025-07-29 15:02:11.665]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d35ec86eb8ae562c93","method":"GET","url":"/business/user/get_logininfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_logininfo","query":"_t=1753772532039"}
{"level":"dev.info","ts":"[2025-07-29 15:02:11.680]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6d35ec86eb8ae562c93","method":"GET","url":"/business/user/get_logininfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0144758,"response_size":1178}
{"level":"dev.info","ts":"[2025-07-29 15:02:11.687]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d3600f0430f9f003dd","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_userinfo","query":"_t=1753772531908"}
{"level":"dev.error","ts":"[2025-07-29 15:02:11.688]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d3600f0430f9f003dd","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0016333,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:11.823]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d36832589cb5f61241","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_userinfo","query":"_t=1753772531908"}
{"level":"dev.error","ts":"[2025-07-29 15:02:11.827]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d36832589cb5f61241","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0039716,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:11.952]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d36fe3c92c1ba85ad5","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_userinfo","query":"_t=1753772531908"}
{"level":"dev.error","ts":"[2025-07-29 15:02:11.958]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d36fe3c92c1ba85ad5","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0052422,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:12.083]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d377a99c18c90ee5f7","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_userinfo","query":"_t=1753772531908"}
{"level":"dev.error","ts":"[2025-07-29 15:02:12.086]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d377a99c18c90ee5f7","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0032632,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:12.227]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d38033e91006986d61","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_userinfo","query":"_t=1753772531908"}
{"level":"dev.error","ts":"[2025-07-29 15:02:12.233]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6d38033e91006986d61","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0054482,"response_size":121,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:39.318]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6d9cef19150c676c320","method":"GET","url":"/business/user/get_logininfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_logininfo","query":"_t=1753772559685"}
{"level":"dev.info","ts":"[2025-07-29 15:02:39.374]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6d9cef19150c676c320","method":"GET","url":"/business/user/get_logininfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0571845,"response_size":1178}
{"level":"dev.info","ts":"[2025-07-29 15:02:41.321]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6da465eba3847f43625","method":"POST","url":"/business/user/login","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/login","query":""}
{"level":"dev.info","ts":"[2025-07-29 15:02:41.462]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6da465eba3847f43625","method":"POST","url":"/business/user/login","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1412649,"response_size":347}
{"level":"dev.info","ts":"[2025-07-29 15:02:41.492]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6da508efab819871640","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_userinfo","query":"_t=*************"}
{"level":"dev.info","ts":"[2025-07-29 15:02:41.492]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6da508efab8cb97ef2d","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/account/get_menu","query":"_t=*************"}
{"level":"dev.info","ts":"[2025-07-29 15:02:41.565]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6da508efab819871640","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0735173,"response_size":629}
{"level":"dev.info","ts":"[2025-07-29 15:02:42.096]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6da508efab8cb97ef2d","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.6041484,"response_size":6491}
{"level":"dev.info","ts":"[2025-07-29 15:02:44.095]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6daebb7a4909623c225","method":"GET","url":"/business/dashboard/workplace/get_quick","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/dashboard/workplace/get_quick","query":"_t=*************"}
{"level":"dev.info","ts":"[2025-07-29 15:02:44.145]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6daebb7a4909623c225","method":"GET","url":"/business/dashboard/workplace/get_quick","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0508004,"response_size":542}
{"level":"dev.info","ts":"[2025-07-29 15:02:45.824]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6db52c8d014ef8c01b9","method":"POST","url":"/business/order/manager/listOrders","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/order/manager/listOrders","query":""}
{"level":"dev.info","ts":"[2025-07-29 15:02:45.824]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6db52c8d014bd523927","method":"GET","url":"/business/channel/channelcontroller/getChannelOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/channel/channelcontroller/getChannelOptions","query":"_t=1753772566133"}
{"level":"dev.info","ts":"[2025-07-29 15:02:45.845]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6db52c8d014bd523927","method":"GET","url":"/business/channel/channelcontroller/getChannelOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.021466,"response_size":522}
{"level":"dev.info","ts":"[2025-07-29 15:02:46.739]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6db52c8d014ef8c01b9","method":"POST","url":"/business/order/manager/listOrders","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.9152035,"response_size":19178}
{"level":"dev.info","ts":"[2025-07-29 15:02:48.342]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6dbe8dbf478a9812b0b","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listCustomers","query":""}
{"level":"dev.info","ts":"[2025-07-29 15:02:48.342]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6dbe8dbf4781c02ab08","method":"GET","url":"/business/customer/customercontroller/getCustomerOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/getCustomerOptions","query":"_t=1753772568671"}
{"level":"dev.info","ts":"[2025-07-29 15:02:48.376]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6dbe8dbf4781c02ab08","method":"GET","url":"/business/customer/customercontroller/getCustomerOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0341932,"response_size":603}
{"level":"dev.info","ts":"[2025-07-29 15:02:48.426]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6dbe8dbf478a9812b0b","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0842832,"response_size":18675}
{"level":"dev.info","ts":"[2025-07-29 15:02:51.122]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6dc8e9ba5c0ede23ed9","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/getCustomerDetail","query":"id=86&_t=1753772571482"}
{"level":"dev.info","ts":"[2025-07-29 15:02:51.174]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6dc8e9ba5c0ede23ed9","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0516796,"response_size":1424}
{"level":"dev.info","ts":"[2025-07-29 15:02:51.218]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6dc9452b83c50aebca8","method":"GET","url":"/business/risk/riskcontroller/getReports","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/risk/riskcontroller/getReports","query":"customer_id=86&start_date=&end_date=&_t=1753772571589"}
{"level":"dev.info","ts":"[2025-07-29 15:02:51.231]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6dc9452b83c50aebca8","method":"GET","url":"/business/risk/riskcontroller/getReports","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0122423,"response_size":315}
{"level":"dev.info","ts":"[2025-07-29 15:02:51.248]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6dc96120efc42e99a95","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:02:51.251]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6dc96120efc42e99a95","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0036932,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:51.381]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6dc9e055aec287afe9a","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:02:51.384]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6dc9e055aec287afe9a","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0032937,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:51.526]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6dca6ac33b4c4e8e722","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:02:51.533]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6dca6ac33b4c4e8e722","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.006425,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:51.661]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6dcaeb738b0dc13a8b6","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:02:51.666]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6dcaeb738b0dc13a8b6","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0047184,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:51.792]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6dcb6863870cfb40c5e","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:02:51.799]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6dcb6863870cfb40c5e","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0064611,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:02:51.918]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6dcbe05d6b494e991fc","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:02:51.921]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6dcbe05d6b494e991fc","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0031992,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:03:30.543]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6e5bc4078bc09248130","method":"POST","url":"/business/customer/customercontroller/listRepurchaseCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listRepurchaseCustomers","query":""}
{"level":"dev.info","ts":"[2025-07-29 15:03:30.543]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6e5bc4078bc95c65e2e","method":"GET","url":"/business/customer/customercontroller/getRepurchaseCustomerOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/getRepurchaseCustomerOptions","query":"_t=1753772610858"}
{"level":"dev.info","ts":"[2025-07-29 15:03:30.551]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6e5bc4078bc09248130","method":"POST","url":"/business/customer/customercontroller/listRepurchaseCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0076898,"response_size":237}
{"level":"dev.info","ts":"[2025-07-29 15:03:30.596]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6e5bc4078bc95c65e2e","method":"GET","url":"/business/customer/customercontroller/getRepurchaseCustomerOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0534293,"response_size":609}
{"level":"dev.info","ts":"[2025-07-29 15:03:39.828]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6e7e5b40a1878919dcc","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listCustomers","query":""}
{"level":"dev.info","ts":"[2025-07-29 15:03:39.828]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6e7e5b40a1849171917","method":"GET","url":"/business/customer/customercontroller/getCustomerOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/getCustomerOptions","query":"_t=1753772620151"}
{"level":"dev.info","ts":"[2025-07-29 15:03:39.868]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6e7e5b40a1849171917","method":"GET","url":"/business/customer/customercontroller/getCustomerOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0398517,"response_size":603}
{"level":"dev.info","ts":"[2025-07-29 15:03:39.887]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6e7e5b40a1878919dcc","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0584242,"response_size":18675}
{"level":"dev.info","ts":"[2025-07-29 15:03:44.225]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6e8ebca78b49a518e0a","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/getCustomerDetail","query":"id=86&_t=1753772624589"}
{"level":"dev.info","ts":"[2025-07-29 15:03:44.340]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6e8ebca78b49a518e0a","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1142501,"response_size":1424}
{"level":"dev.info","ts":"[2025-07-29 15:03:44.396]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6e8f5f958dc9a6c776a","method":"GET","url":"/business/risk/riskcontroller/getReports","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/risk/riskcontroller/getReports","query":"customer_id=86&start_date=&end_date=&_t=1753772624733"}
{"level":"dev.info","ts":"[2025-07-29 15:03:44.420]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6e8f5f958dc9a6c776a","method":"GET","url":"/business/risk/riskcontroller/getReports","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0238635,"response_size":315}
{"level":"dev.info","ts":"[2025-07-29 15:03:44.454]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6e8f967e8309d3b1a4e","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:03:44.457]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6e8f967e8309d3b1a4e","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0031928,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:03:44.592]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6e901a21a2056bf6ff8","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:03:44.595]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6e901a21a2056bf6ff8","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0033403,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:03:44.771]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6e90c481de4c92aa97d","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:03:44.775]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6e90c481de4c92aa97d","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0044089,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:03:44.903]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6e91432cdecd0085bac","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:03:44.906]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6e91432cdecd0085bac","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.002663,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:03:45.041]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6e91c5fd528d37e60aa","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:03:45.045]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6e91c5fd528d37e60aa","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0049534,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:03:45.170]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6e9241484309b6c58d3","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:03:45.175]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a6e9241484309b6c58d3","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0049054,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:03:49.045]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6ea0b086708a6f5ec4a","method":"POST","url":"/business/order/manager/listOrders","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/order/manager/listOrders","query":""}
{"level":"dev.info","ts":"[2025-07-29 15:03:49.045]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a6ea0b086708b856c32e","method":"GET","url":"/business/channel/channelcontroller/getChannelOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/channel/channelcontroller/getChannelOptions","query":"_t=1753772629347"}
{"level":"dev.info","ts":"[2025-07-29 15:03:49.086]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6ea0b086708b856c32e","method":"GET","url":"/business/channel/channelcontroller/getChannelOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0418983,"response_size":522}
{"level":"dev.info","ts":"[2025-07-29 15:03:49.669]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a6ea0b086708a6f5ec4a","method":"POST","url":"/business/order/manager/listOrders","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.6242724,"response_size":19178}
{"level":"dev.info","ts":"[2025-07-29 15:07:16.788]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a71a6984dde0faf6fcbb","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listCustomers","query":""}
{"level":"dev.info","ts":"[2025-07-29 15:07:16.789]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a71a69926f50a20342ae","method":"GET","url":"/business/customer/customercontroller/getCustomerOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/getCustomerOptions","query":"_t=1753772837104"}
{"level":"dev.info","ts":"[2025-07-29 15:07:16.891]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a71a69926f50a20342ae","method":"GET","url":"/business/customer/customercontroller/getCustomerOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1016858,"response_size":603}
{"level":"dev.info","ts":"[2025-07-29 15:07:16.932]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a71a6984dde0faf6fcbb","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1436688,"response_size":18675}
{"level":"dev.info","ts":"[2025-07-29 15:07:20.392]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a71b40576c84d8a413cf","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/getCustomerDetail","query":"id=86&_t=1753772840762"}
{"level":"dev.info","ts":"[2025-07-29 15:07:20.456]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a71b40576c84d8a413cf","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0641096,"response_size":1424}
{"level":"dev.info","ts":"[2025-07-29 15:07:20.475]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a71b4549a414be3f357f","method":"GET","url":"/business/risk/riskcontroller/getReports","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/risk/riskcontroller/getReports","query":"customer_id=86&start_date=&end_date=&_t=1753772840849"}
{"level":"dev.info","ts":"[2025-07-29 15:07:20.489]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a71b4549a414be3f357f","method":"GET","url":"/business/risk/riskcontroller/getReports","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.013899,"response_size":315}
{"level":"dev.info","ts":"[2025-07-29 15:07:20.507]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a71b47306a88c7ce2f4c","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:07:20.509]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a71b47306a88c7ce2f4c","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0022545,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:07:20.628]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a71b4e601efcca5777ce","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:07:20.632]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a71b4e601efcca5777ce","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0044522,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:07:20.749]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a71b559fb9c06b5d4720","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:07:20.752]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a71b559fb9c06b5d4720","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0021566,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:07:20.866]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a71b5c93bcf494523856","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:07:20.869]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a71b5c93bcf494523856","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0027562,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:07:20.989]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a71b63e772fcdfcdb079","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:07:20.992]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a71b63e772fcdfcdb079","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0032853,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:07:21.109]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a71b6b14dd1c35bc896b","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=86&_t=*************"}
{"level":"dev.error","ts":"[2025-07-29 15:07:21.113]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a71b6b14dd1c35bc896b","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0038215,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:07:31.706]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a71de2aa4fb8ef6638fb","method":"POST","url":"/business/order/manager/listOrders","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/order/manager/listOrders","query":""}
{"level":"dev.info","ts":"[2025-07-29 15:07:31.706]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a71de2aa4fb8188848da","method":"GET","url":"/business/channel/channelcontroller/getChannelOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/channel/channelcontroller/getChannelOptions","query":"_t=1753772852004"}
{"level":"dev.info","ts":"[2025-07-29 15:07:31.974]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a71de2aa4fb8188848da","method":"GET","url":"/business/channel/channelcontroller/getChannelOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.2688625,"response_size":522}
{"level":"dev.info","ts":"[2025-07-29 15:07:33.708]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a71de2aa4fb8ef6638fb","method":"POST","url":"/business/order/manager/listOrders","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":2.0027754,"response_size":19178}
{"level":"dev.info","ts":"[2025-07-29 15:07:39.390]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a71facb336e8e52c21c0","method":"POST","url":"/business/order/manager/listOrders","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/order/manager/listOrders","query":""}
{"level":"dev.info","ts":"[2025-07-29 15:07:40.252]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a71facb336e8e52c21c0","method":"POST","url":"/business/order/manager/listOrders","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.8617758,"response_size":19178}
{"level":"dev.info","ts":"[2025-07-29 15:08:28.597]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a72b21a5bf8862841e47","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_userinfo","query":"_t=1753772908955"}
{"level":"dev.info","ts":"[2025-07-29 15:08:28.631]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a72b21a5bf8862841e47","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0337849,"response_size":629}
{"level":"dev.info","ts":"[2025-07-29 15:08:28.657]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a72b253b764c08e6cbd0","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/account/get_menu","query":"_t=*************"}
{"level":"dev.info","ts":"[2025-07-29 15:08:29.200]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a72b253b764c08e6cbd0","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.543271,"response_size":6491}
{"level":"dev.info","ts":"[2025-07-29 15:08:29.455]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a72b54d069bc0fcbccde","method":"POST","url":"/business/order/manager/listOrders","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/order/manager/listOrders","query":""}
{"level":"dev.info","ts":"[2025-07-29 15:08:29.460]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a72b55115418b968c0b4","method":"GET","url":"/business/channel/channelcontroller/getChannelOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/channel/channelcontroller/getChannelOptions","query":"_t=1753772909757"}
{"level":"dev.info","ts":"[2025-07-29 15:08:29.504]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a72b55115418b968c0b4","method":"GET","url":"/business/channel/channelcontroller/getChannelOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0447381,"response_size":522}
{"level":"dev.info","ts":"[2025-07-29 15:08:30.201]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a72b54d069bc0fcbccde","method":"POST","url":"/business/order/manager/listOrders","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.7458674,"response_size":19178}
{"level":"dev.info","ts":"[2025-07-29 15:08:45.987]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a72f2e2dedbc4ed3a25a","method":"GET","url":"/uniapp/risk/riskcontroller/getEvaluate","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","path":"/uniapp/risk/riskcontroller/getEvaluate","query":"customer_id=86"}
{"level":"dev.info","ts":"[2025-07-29 15:08:45.987]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a72f2e2dedbc26aa846d","method":"GET","url":"/uniapp/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","path":"/uniapp/bankcard/bankcardcontroller/getBankCardList","query":""}
{"level":"dev.error","ts":"[2025-07-29 15:08:45.990]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a72f2e2dedbc26aa846d","method":"GET","url":"/uniapp/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","status_code":500,"response_time":0.002591,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:08:46.449]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a72f2e2dedbc4ed3a25a","method":"GET","url":"/uniapp/risk/riskcontroller/getEvaluate","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","status_code":200,"response_time":0.4616172,"response_size":489}
{"level":"dev.info","ts":"[2025-07-29 15:08:46.494]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a72f4c6e65b8934316fc","method":"GET","url":"/uniapp/risk/riskcontroller/getProducts","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","path":"/uniapp/risk/riskcontroller/getProducts","query":"customer_id=86"}
{"level":"dev.info","ts":"[2025-07-29 15:08:46.696]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a72f4c6e65b8934316fc","method":"GET","url":"/uniapp/risk/riskcontroller/getProducts","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","status_code":200,"response_time":0.2015579,"response_size":356}
{"level":"dev.info","ts":"[2025-07-29 15:08:46.715]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a72f598dcb94d99580c9","method":"GET","url":"/uniapp/order/get_order_bills","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","path":"/uniapp/order/get_order_bills","query":""}
{"level":"dev.info","ts":"[2025-07-29 15:08:46.728]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a72f598dcb94d99580c9","method":"GET","url":"/uniapp/order/get_order_bills","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","status_code":200,"response_time":0.0136365,"response_size":1065}
{"level":"dev.info","ts":"[2025-07-29 15:08:48.686]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a72fcf120c542555ca02","method":"GET","url":"/uniapp/risk/riskcontroller/getEvaluate","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","path":"/uniapp/risk/riskcontroller/getEvaluate","query":"customer_id=86"}
{"level":"dev.info","ts":"[2025-07-29 15:08:48.686]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a72fcf120c54c22105e3","method":"GET","url":"/uniapp/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","path":"/uniapp/bankcard/bankcardcontroller/getBankCardList","query":""}
{"level":"dev.error","ts":"[2025-07-29 15:08:48.691]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a72fcf120c54c22105e3","method":"GET","url":"/uniapp/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","status_code":500,"response_time":0.0049996,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:08:48.888]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a72fcf120c542555ca02","method":"GET","url":"/uniapp/risk/riskcontroller/getEvaluate","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","status_code":200,"response_time":0.2020075,"response_size":408}
{"level":"dev.info","ts":"[2025-07-29 15:08:48.923]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a72fdd2bc8e8edad8cab","method":"GET","url":"/uniapp/risk/riskcontroller/getProducts","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","path":"/uniapp/risk/riskcontroller/getProducts","query":"customer_id=86"}
{"level":"dev.info","ts":"[2025-07-29 15:08:49.184]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a72fdd2bc8e8edad8cab","method":"GET","url":"/uniapp/risk/riskcontroller/getProducts","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","status_code":200,"response_time":0.2608636,"response_size":356}
{"level":"dev.info","ts":"[2025-07-29 15:08:49.210]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a72fee47e6484255ca3f","method":"GET","url":"/uniapp/order/get_order_bills","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","path":"/uniapp/order/get_order_bills","query":""}
{"level":"dev.info","ts":"[2025-07-29 15:08:49.221]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a72fee47e6484255ca3f","method":"GET","url":"/uniapp/order/get_order_bills","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","status_code":200,"response_time":0.0107861,"response_size":1065}
{"level":"dev.info","ts":"[2025-07-29 15:09:40.779]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a73bf0050d7432854e58","method":"GET","url":"/uniapp/user/getUserinfo","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","path":"/uniapp/user/getUserinfo","query":""}
{"level":"dev.info","ts":"[2025-07-29 15:09:40.873]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a73bf0050d7432854e58","method":"GET","url":"/uniapp/user/getUserinfo","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","status_code":200,"response_time":0.0941954,"response_size":1593}
{"level":"dev.info","ts":"[2025-07-29 15:09:40.892]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a73bf6c97fb4ff47dc2b","method":"GET","url":"/uniapp/risk/riskcontroller/getEvaluate","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","path":"/uniapp/risk/riskcontroller/getEvaluate","query":"customer_id=86"}
{"level":"dev.info","ts":"[2025-07-29 15:09:41.087]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a73bf6c97fb4ff47dc2b","method":"GET","url":"/uniapp/risk/riskcontroller/getEvaluate","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","status_code":200,"response_time":0.1945005,"response_size":408}
{"level":"dev.info","ts":"[2025-07-29 15:09:41.115]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a73c041613f87258a233","method":"GET","url":"/uniapp/risk/riskcontroller/getProducts","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","path":"/uniapp/risk/riskcontroller/getProducts","query":"customer_id=86"}
{"level":"dev.info","ts":"[2025-07-29 15:09:41.395]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a73c041613f87258a233","method":"GET","url":"/uniapp/risk/riskcontroller/getProducts","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","status_code":200,"response_time":0.2798638,"response_size":356}
{"level":"dev.info","ts":"[2025-07-29 15:09:41.413]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a73c15ccc72c57cd2e39","method":"GET","url":"/uniapp/user/complaint/getComplaintRes","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","path":"/uniapp/user/complaint/getComplaintRes","query":"uid=86"}
{"level":"dev.info","ts":"[2025-07-29 15:09:41.427]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a73c15ccc72c57cd2e39","method":"GET","url":"/uniapp/user/complaint/getComplaintRes","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","status_code":200,"response_time":0.01485,"response_size":345}
{"level":"dev.info","ts":"[2025-07-29 15:09:43.114]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a73c7b382ffc187347b4","method":"GET","url":"/uniapp/risk/riskcontroller/getEvaluate","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","path":"/uniapp/risk/riskcontroller/getEvaluate","query":"customer_id=86"}
{"level":"dev.info","ts":"[2025-07-29 15:09:43.114]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a73c7b382ffcf5ea76e7","method":"GET","url":"/uniapp/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","path":"/uniapp/bankcard/bankcardcontroller/getBankCardList","query":""}
{"level":"dev.error","ts":"[2025-07-29 15:09:43.119]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1856a73c7b382ffcf5ea76e7","method":"GET","url":"/uniapp/bankcard/bankcardcontroller/getBankCardList","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","status_code":500,"response_time":0.0047132,"response_size":279,"stacktrace":"fincore/route.InitRouter.RequestIDMiddleware.func3\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 15:09:43.303]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a73c7b382ffc187347b4","method":"GET","url":"/uniapp/risk/riskcontroller/getEvaluate","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","status_code":200,"response_time":0.1886308,"response_size":408}
{"level":"dev.info","ts":"[2025-07-29 15:09:43.325]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a73c87cc136449b183b8","method":"GET","url":"/uniapp/risk/riskcontroller/getProducts","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","path":"/uniapp/risk/riskcontroller/getProducts","query":"customer_id=86"}
{"level":"dev.info","ts":"[2025-07-29 15:09:43.547]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a73c87cc136449b183b8","method":"GET","url":"/uniapp/risk/riskcontroller/getProducts","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","status_code":200,"response_time":0.2221245,"response_size":356}
{"level":"dev.info","ts":"[2025-07-29 15:09:43.570]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1856a73c965f7628d289a839","method":"GET","url":"/uniapp/order/get_order_bills","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","path":"/uniapp/order/get_order_bills","query":""}
{"level":"dev.info","ts":"[2025-07-29 15:09:43.591]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1856a73c965f7628d289a839","method":"GET","url":"/uniapp/order/get_order_bills","ip":"************","user_agent":"Mozilla/5.0 (Linux; Android 7.0; SM-G950U Build/NRD90M) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","status_code":200,"response_time":0.0210796,"response_size":1065}
