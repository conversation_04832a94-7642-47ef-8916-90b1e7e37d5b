# 订单详情页账单信息接口

## 接口说明

获取订单详情页的基础账单信息，包括订单应还总金额、账单列表和打款记录。

**注意：** 支付记录已拆分到独立接口，详见 [订单支付记录接口](#订单支付记录接口)

## 1. 订单账单信息接口

### 接口信息

- **请求方式**: GET
- **接口路径**: `/business/order/manager/getOrderBillInfo`
- **权限要求**: 需要订单操作权限

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| order_no | string | 是 | 订单编号 | "ORD20**********" |

### 响应数据

```json
{
  "code": 200,
  "message": "获取订单账单信息成功",
  "data": {
    "total_repayable_amount": "10000.00",
    "bill_list": [
      {
        "id": 1,
        "period_number": 1,
        "total_due_amount": "5000.00",
        "paid_amount": "5000.00",
        "status": 1,
        "payment_time": "2024-01-15 10:30:00",
        "due_date": "2024-01-15"
      }
    ],
    "disbursement_records": [
      {
        "id": 1,
        "status": 1,
        "payout_account": "统统付小贷",
        "payment_type": "统统付小贷打款",
        "initiated_at": "2024-01-01 09:00:00",
        "completed_at": "2024-01-01 09:05:00",
        "channel_transaction_no": "TXN20**********",
        "amount": "10000.00"
      }
    ]
  }
}
```

### 字段说明

#### 订单应还总金额
- `total_repayable_amount`: 订单总应还金额

#### 账单列表 (bill_list)
- `id`: 账单ID
- `period_number`: 期数
- `total_due_amount`: 当期账单应还总额
- `paid_amount`: 当期已付总额
- `status`: 账单状态 (0-未还, 1-已还, 2-逾期等)
- `payment_time`: 支付时间 (最后一次成功支付时间)
- `due_date`: 还款到期时间

#### 打款记录 (disbursement_records)
- `id`: 记录ID
- `status`: 状态 (0-处理中, 1-成功, 2-失败)
- `payout_account`: 支出账户 (固定为"统统付小贷")
- `payment_type`: 支付类型 (固定为"统统付小贷打款")
- `initiated_at`: 发起时间
- `completed_at`: 完成时间
- `channel_transaction_no`: 渠道流水号
- `amount`: 支付金额

## 2. 订单支付记录接口

### 接口信息

- **请求方式**: GET
- **接口路径**: `/business/order/manager/getOrderPaymentRecords`
- **权限要求**: 需要订单操作权限

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| order_no | string | 是 | 订单编号 | "ORD20**********" |
| page | integer | 否 | 页码，默认1 | 1 |
| pageSize | integer | 否 | 每页数量，默认20，最大100 | 20 |

### 响应数据

```json
{
  "code": 200,
  "message": "获取订单支付记录成功",
  "data": {
    "data": [
      {
        "id": 1,
        "amount": "5000.00",
        "status": 1,
        "image": null,
        "payment_type": "担保",
        "initiated_at": "2024-01-15 10:00:00",
        "completed_at": "2024-01-15 10:30:00",
        "period_number": 1,
        "channel_transaction_no": "PAY202401150001",
        "error_message": null
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 1,
      "totalPages": 1
    }
  }
}
```

### 字段说明

#### 支付记录 (data)
- `id`: 记录ID
- `amount`: 支付金额
- `status`: 状态 (0-处理中, 1-成功, 2-失败)
- `image`: 图片 (预留字段)
- `payment_type`: 支付类型 (担保/资管等)
- `initiated_at`: 支付发起时间
- `completed_at`: 支付完成时间
- `period_number`: 支付期数
- `channel_transaction_no`: 支付渠道流水号
- `error_message`: 错误信息 (失败时显示)

## 3. 性能特性

- 使用 errgroup 实现并发查询，提升响应速度
- 支付记录独立分页查询，避免数据量过大
- 复用现有权限检查机制
- 所有列表记录都包含ID字段，便于前端处理

## 4. 错误码

- `400`: 参数验证失败
- `403`: 权限不足
- `404`: 订单不存在
- `500`: 服务器内部错误

## 5. 实现状态

✅ **已完成实现**

### 实现文件
- `controller.go` - 添加了 `GetOrderBillInfo` 和 `GetOrderPaymentRecords` 控制器方法
- `schema.go` - 添加了参数验证规则
- `service.go` - 添加了响应数据结构定义
- `repository.go` - 实现了数据访问层，遵循分层架构
- `bill_service.go` - 实现了完整的业务逻辑
- `bill_service_test.go` - 单元测试文件

### 技术特性
- ✅ 使用 `errgroup` 实现并发查询，提升性能
- ✅ 复用现有权限检查机制
- ✅ 支付记录独立接口，支持分页查询
- ✅ 完善的错误处理和日志记录
- ✅ 通过静态检查和单元测试
- ✅ 遵循项目编码规范和分层架构
- ✅ 所有列表记录都包含ID字段

### 接口拆分
1. **订单账单信息接口** - 获取基础账单信息（订单应还总金额、账单列表、打款记录）
2. **订单支付记录接口** - 获取支付记录列表（分页）

### 测试验证
```bash
# 编译验证
go build ./app/business/order/

# 静态检查
staticcheck ./app/business/order/

# 单元测试
go test ./app/business/order/bill_service_test.go ./app/business/order/service.go ./app/business/order/repository.go -v
```

所有验证均已通过，接口可以正常使用。
