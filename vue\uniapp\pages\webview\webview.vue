<template>
  <view class="webview-container">
    <web-view :src="url" @message="handleMessage" allow="camera *;autoplay *;"></web-view>
  </view>
</template>

<script>
import user from '@/store/user.js';
import userApi from '@/api/user.js';
	import {
		storeToRefs
	} from 'pinia';
export default {
  data() {
    return {
      url: "",
    };
  },
  onLoad(options) {
    this.url = decodeURIComponent(options.url || "");
    console.log("加载URL:", this.url);
  },
  methods: {
    async handleMessage(e) {
      console.log("收到webview消息:", e);
	  let result = e.detail.data[0];
	  const userStore = user();
	  await userStore.getInfo();
	  const {
	  	userInfo
	  } = storeToRefs(userStore);
	  if (userInfo.value.bizId) {
	  	userApi.getCheckFaceResult({
	  		bizId: userInfo.value.bizId
	  	});
	  }
	  if (result == '1') {
	  	userApi.getFacePhoto({});
	  	uni.redirectTo({
	  		url: '/pages/FaceRecognition/FaceRecognition'
	  	});
	  } else {
	  	uni.redirectTo({
	  		url: '/pages/Authentication/Authentication'
	  	});
	  }
      
    },
  },
};
</script>

<style>
.webview-container {
  width: 100%;
  height: 100vh;
}
</style>
