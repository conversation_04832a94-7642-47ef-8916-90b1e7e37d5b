{"level":"dev.info","ts":"[2025-08-15 09:10:47.366]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcb83f1925b1c9bb29ab9","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id WHERE c.channel_status = ? AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD') GROUP BY c.id ORDER BY c.id, [1]","duration":"10.5199ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-15 09:10:47.383]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcb83f1925b1c9bb29ab9","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id, [1]","duration":"27.0098ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-15 09:10:47.392]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcb83f1925b1c9bb29ab9","sql":"\n\t\tSELECT COUNT(DISTINCT c.id) as count\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ?, [1]","duration":"36.3405ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-15 09:10:47.392]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcb83f1925b1c9bb29ab9","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"36.3405ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-15 09:10:47.398]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcb83f1925b1c9bb29ab9","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"41.7891ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-15 09:10:47.428]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcb83f1925b1c9bb29ab9","sql":"\n\t\tSELECT DISTINCT c.id as channel_id, c.channel_name\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 10 OFFSET 0, [1]","duration":"30.5073ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-15 09:10:52.242]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcb8512b06d74c2e3b202","sql":"\n\t\tSELECT COUNT(DISTINCT c.id) as count\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ?, [1]","duration":"45.5763ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-15 09:10:52.278]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcb8512b06d74c2e3b202","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"81.8424ms","duration_ms":81}
{"level":"dev.info","ts":"[2025-08-15 09:10:52.278]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcb8512b06d74c2e3b202","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"81.8424ms","duration_ms":81}
{"level":"dev.info","ts":"[2025-08-15 09:10:52.278]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcb8512b06d74c2e3b202","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id, [1]","duration":"81.8424ms","duration_ms":81}
{"level":"dev.info","ts":"[2025-08-15 09:10:52.278]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcb8512b06d74c2e3b202","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id WHERE c.channel_status = ? AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD') GROUP BY c.id ORDER BY c.id, [1]","duration":"81.8424ms","duration_ms":81}
{"level":"dev.info","ts":"[2025-08-15 09:10:52.290]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185bcb8512b06d74c2e3b202","sql":"\n\t\tSELECT DISTINCT c.id as channel_id, c.channel_name\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 10 OFFSET 0, [1]","duration":"47.894ms","duration_ms":47}
