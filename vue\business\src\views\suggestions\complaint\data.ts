import { computed } from 'vue';
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
  export const columns = computed<TableColumnData[]>(() => [
    {
      title: '序号', // 使用"序号"代替"ID"
      dataIndex: 'index', // 新的数据索引
      slotName: 'index', // 新的插槽名称
      width: 76,
      align: "center"
    },
    {
      title: '订单状态',
      dataIndex: 'order_status',
      slotName: 'order_status',
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
      slotName: 'mobile',
    },
    {
      title: '投诉内容',
      dataIndex: 'content',
      slotName: 'content',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      slotName: 'remark',
    },
    {
      title: '创建时间',
      dataIndex: 'complaintTime',
      slotName: 'complaintTime',
    },
    {
      title: '操作',
      dataIndex: 'operations',
      slotName: 'operations',
      align:"center"
    },
  ]);
