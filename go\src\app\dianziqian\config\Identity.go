package config

// 身份证OCR识别json信息配置
type OCRRequest struct {
	Image     string `json:"image"`
	Base64Img string `json:"base64Img"`
	Fileld    string `json:"fileld"`
	Side      string `json:"side"`
}

// 身份证OCR识别返回信息
type OCRResponse struct {
	Code    int     `json:"code"`
	Message string  `json:"msg"`
	Data    OCRData `json:"data"`
}

type OCRData struct {
	OrderID    string `json:"orderid"`    // 流水号
	Side       string `json:"side"`       // 身份证面
	RealName   string `json:"realname"`   // 姓名（人像面）
	Sex        string `json:"sex"`        // 性别（人像面）
	Nation     string `json:"nation"`     // 民族（人像面）
	Born       string `json:"born"`       // 生日（人像面）
	Address    string `json:"address"`    // 地址（人像面）
	IDCard     string `json:"idcard"`     // 证件号（人像面）
	Begin      string `json:"begin"`      // 生效日期（国徽面）
	End        string `json:"end"`        // 截止日期（国徽面）
	Department string `json:"department"` // 签发机关（国徽面）
}

type FaceAuthRequest struct {
	RealName     string `json:"realName" binding:"required"` // 真实姓名
	IdCardNo     string `json:"idCardNo" binding:"required"` // 身份证号
	IdCardType   int    `json:"idCardType,omitempty"`        // 证件类型（默认1）
	RedirectUrl  string `json:"redirectUrl,omitempty"`       // 重定向地址
	FaceAuthMode int    `json:"faceAuthMode,omitempty"`      // 认证渠道
	ShowResult   int    `json:"showResult,omitempty"`        // 结果页展示控制
	BizId        string `json:"bizId,omitempty"`             // 业务追踪ID
	MetaInfo     string `json:"metaInfo,omitempty"`          // 支付宝H5专用参数
	IsIframe     int    `json:"isIframe,omitempty"`          // Iframe模式标识
	NotifyUrl    string `json:"notifyUrl,omitempty"`         // 异步通知地址
}

type FaceAuthResponse struct {
	Code    int         `json:"code"`           // 状态码
	Message string      `json:"msg"`            // 状态描述
	Data    *AuthResult `json:"data,omitempty"` // 业务数据
}

type FacePhotoRequest struct {
	SerialNo string `json:"serialNo" binding:"required"` // 流水号
}

type FacePhotoResponse struct {
	Code    int        `json:"code"`           // 状态码
	Message string     `json:"msg"`            // 状态描述
	Data    *FacePhoto `json:"data,omitempty"` // 业务数据
}

type FacePhoto struct {
	FileName string `json:"fileName"` // 人脸活体认证截屏路径
	MD5      string `json:"md5"`      // 人脸活体认证截屏MD5
	Data     string `json:"data"`     // 人脸活体认证截屏BASE 64字符串
}

type AuthResult struct {
	Result   int    `json:"result"`   // 认证结果(0-进行中 1-成功 2-失败)
	SerialNo string `json:"serialNo"` // 流水号
	Type     string `json:"type"`     // 认证类型标识
	FaceUrl  string `json:"faceUrl"`  // 人脸核身链接
}

// 查询人脸核身结果请求
type CheckRequest struct {
	BizId string `json:"bizId,omitempty"` // 业务追踪ID
}

// 查询人脸核身结果响应
type CheckResponse struct {
	Code    int          `json:"code"`           // 状态码
	Message string       `json:"msg"`            // 状态描述
	Data    *CheckResult `json:"data,omitempty"` // 业务数据
}

type CheckResult struct {
	Status int    `json:"status"` // 认证结果("1"-进行中 "2"-成功 "3"-失败)
	Msg    string `json:"msg"`    // 认证结果描述
}
