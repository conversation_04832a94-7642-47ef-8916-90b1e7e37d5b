<template>
	<!-- 页面容器 -->
	<view class="setting-page">
		<!-- 设置选项列表 -->
		<view class="setting-list">
			<view class="setting-item" @click="goToAgreement('register')">
				<text class="item-text">软件注册协议</text>
				<image src="/static/image/more.png" mode="widthFix"></image>
			</view>
			<view class="setting-item" @click="goToAgreement('privacy')">
				<text class="item-text">隐私协议</text>
				<image src="/static/image/more.png" mode="widthFix"></image>
			</view>
			<view class="setting-item" @click="handleInvite">
				<text class="item-text">换绑账户</text>
				<image src="/static/image/more.png" mode="widthFix"></image>
			</view>
			<!-- <view class="setting-item" @click="handleLogout">
				<text class="item-text">退出登录</text>
				<image src="/static/image/more.png" mode="widthFix"></image>
			</view> -->
			<view class="setting-item" @click="handleCancelAccount">
				<text class="item-text">注销账户</text>
				<image src="/static/image/more.png" mode="widthFix"></image>
			</view>
		</view>
		
		<view class="quit" @click="handleLogout">
			<button>退出登录</button>
		</view>

		<!-- 协议说明 -->
		<!-- <view class="agreement">
			<text class="agreement-text" @click="goToAgreement('register')">《软件注册协议》</text>
			<text class="agreement-text" @click="goToAgreement('privacy')">《隐私协议》</text>
		</view> -->
	</view>
</template>
<script setup>
	import {
		onBackPress
	} from '@dcloudio/uni-app';
	import {
		onMounted
	} from 'vue';
	import userApi from '@/api/user.js';
	import user from '@/store/user.js';
	const userStore = user();

	// 返回上一级页面
	const onBack = () => {
		uni.navigateBack();
	};

	// 监听安卓物理返回键（可选，根据需求添加）
	onBackPress((options) => {
		console.log(options)
		// onBack();
		// return true; // 拦截默认返回行为，已自定义处理
	});

	// 换绑账户逻辑（可扩展，比如跳转到换绑页面）
	function handleBindAccount(){
		uni.navigateTo({
			url:"/pages/profile/bindAccount"
		})
	}
	function handleInvite() {
		uni.showToast({
			title: '开发中...',
			icon: 'none'
		})
	}
	// 退出登录逻辑
	const handleLogout = () => {
		uni.showModal({
			title: '提示',
			content: '确定要退出登录吗？',
			cancelText: "取消",
			confirmText: "确定",
			confirmColor: "#1a6eff",
			success: async (res) => {
				if (res.confirm) {
					try {
						console.log('开始退出登录流程');
						
						// 显示加载提示
						uni.showLoading({
							title: '退出中...',
							mask: true
						});
						
						// 先清除本地数据，确保登录状态立即变为false
						console.log('清除本地数据');
						userStore.logout(true);
						uni.removeStorageSync('mobile');
						
						// 验证数据是否清除成功
						const token = uni.getStorageSync('token');
						const mobile = uni.getStorageSync('mobile');
						console.log('验证清除结果 - token:', token, 'mobile:', mobile, 'isLogin:', userStore.isLogin);
						
						// 隐藏加载提示
						uni.hideLoading();
						
						// 显示退出成功提示
						uni.showToast({
							title: '已退出登录',
							icon: 'success',
							duration: 1500
						});
						
						// 延迟跳转，让用户看到提示
						setTimeout(() => {
							console.log('准备跳转到登录页');
							// 使用reLaunch确保清除页面栈，防止用户通过返回键回到已登录页面
							uni.reLaunch({
								url: '/pages/login/login',
								success: () => {
									console.log('成功跳转到登录页');
								},
								fail: (err) => {
									console.error('reLaunch跳转登录页失败:', err);
									// 如果跳转失败，尝试使用redirectTo
									uni.redirectTo({
										url: '/pages/login/login',
										success: () => {
											console.log('redirectTo跳转登录页成功');
										},
										fail: (redirectErr) => {
											console.error('redirectTo跳转也失败:', redirectErr);
											// 最后的备选方案
											uni.navigateTo({
												url: '/pages/login/login'
											});
										}
									});
								}
							});
						}, 1500);
						
					} catch (error) {
						console.error('退出登录过程中发生错误:', error);
						uni.hideLoading();
						
						// 即使发生错误，也要清除本地数据并跳转
						console.log('错误处理：强制清除数据');
						userStore.logout(true);
						uni.removeStorageSync('mobile');
						
						uni.showToast({
							title: '退出成功',
							icon: 'success',
							duration: 1500
						});
						
						setTimeout(() => {
							console.log('错误处理：跳转到登录页');
					uni.reLaunch({
								url: '/pages/login/login'
					});
						}, 1500);
					}
				}
			}
		});
	};

	// 注销账户逻辑（需谨慎，可调用后端接口等）
	const handleCancelAccount = () => {
		uni.showModal({
			title: '提示',
			content: '注销账户将删除所有数据，确定要继续吗？',
			cancelText: "取消",
			confirmText: "确定",
			confirmColor: "#bf3838",
			success: (res) => {
				if (res.confirm) {
					userApi.cancelAccount().then(res => {
						console.log(res)
						if(res.code == 0) {
							userStore.logout(true);
							uni.removeStorageSync('mobile');
							uni.showToast({
								title: '注销成功',
								icon: 'success',
								duration: 1500,
								success: () => {
									setTimeout(() => {
										uni.reLaunch({
											url: '/pages/login/login',
											fail: (err) => {
												console.error('reLaunch跳转登录页失败:', err);
												// 如果跳转失败，尝试使用redirectTo
												uni.redirectTo({
													url: '/pages/login/login'
												});
											}
										});
									}, 1500);
								}
							});
						}
					})
				}
			}
		});
	};

	// 跳转到协议页面
	function goToAgreement(type) {
		let url = '';
		if (type === 'register') {
			url = '/pages/contract/contract';
		} else if (type === 'privacy') {
			url = '/pages/contract/privacy';
		}
		if (url) {
			uni.navigateTo({ url });
		}
	}

	onMounted(() => {
		console.log('设置页面加载完成');
	});
</script>

<style scoped lang="scss">
	page{
		background-color: #eff2f7;
	}
	/* 页面整体样式 */
	.setting-page {
		font-size: 28rpx;
		color: #333;
		overflow: hidden;
	}

	/* 导航栏样式 */
	.nav-bar {
		display: flex;
		align-items: center;
		height: 44px;
		padding: 0 15px;
		border-bottom: 1px solid #f5f5f5;
		background-color: #fff;
	}

	.nav-back {
		width: 20px;
		height: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 10px;
	}

	.nav-title {
		font-size: 16px;
		font-weight: 500;
	}

	/* 设置选项列表样式 */
	.setting-list {
		padding: 20rpx;
		background-color: #fff;
		margin: 20rpx;
		border-radius: 10rpx;
	}

	.setting-item {
		padding: 25rpx 20rpx;
		border-bottom: 1px solid #f4f4f4;
		display: flex;
		align-items: center;
		&:last-child{
			border-bottom: 0;
		}
		text{
			flex: 1;
		}
		image{
			width: 40rpx;
		}
	}

	.item-text {
		font-size: 28rpx;
		color: #444;
	}

	/* 协议说明样式 */
	.agreement {
		display: flex;
		justify-content: center;
		gap: 10px;
		padding: 15px 0;
		color: #999;
		font-size: 12px;
	}

	.agreement-text {
		/* text-decoration: underline; // 移除下划线 */
		cursor: pointer;
	}
	.quit{
		position: fixed;
		left: 0;
		right: 0;
		box-sizing: border-box;
		bottom: 100rpx;
		padding: 30rpx;
		button{
			// background: linear-gradient(to bottom, #1a6eff 20%, #4781e3 45%);
			background: #fff;
			color: #1a6eff;
			padding: 0;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 26rpx;
		}
	}
</style>