---
description: src/*
alwaysApply: false
---
# 订单模块开发规范

## 模块文件
- [src/app/business/order/controller.go](mdc:src/app/business/order/controller.go) - 订单控制器
- [src/app/business/order/schema.go](mdc:src/app/business/order/schema.go) - 订单数据结构
- [src/app/business/order/creation_service.go](mdc:src/app/business/order/creation_service.go) - 订单创建服务
- [src/app/business/order/management_service.go](mdc:src/app/business/order/management_service.go) - 订单管理服务
- [src/model/business_loan_orders.go](mdc:src/model/business_loan_orders.go) - 订单数据模型
- [src/model/business_order_operation_logs.go](mdc:src/model/business_order_operation_logs.go) - 订单操作日志模型
- [src/model/business_order_remarks.go](mdc:src/model/business_order_remarks.go) - 订单备注模型

## 订单状态流转
- 订单状态应严格按照预定义的状态进行流转
- 每次状态变更应记录操作日志
- 状态变更应通过专用的服务方法处理，不应直接在控制器中修改状态

## 订单处理注意事项
- 金额计算必须使用 decimal 包
- 交易信息应该通过事务处理保证一致性
- 关键操作应有日志记录
- 订单编号生成应使用统一的方法
# 订单模块开发规范

## 模块文件
- [src/app/business/order/controller.go](mdc:src/app/business/order/controller.go) - 订单控制器
- [src/app/business/order/schema.go](mdc:src/app/business/order/schema.go) - 订单数据结构
- [src/app/business/order/creation_service.go](mdc:src/app/business/order/creation_service.go) - 订单创建服务
- [src/app/business/order/management_service.go](mdc:src/app/business/order/management_service.go) - 订单管理服务
- [src/model/business_loan_orders.go](mdc:src/model/business_loan_orders.go) - 订单数据模型
- [src/model/business_order_operation_logs.go](mdc:src/model/business_order_operation_logs.go) - 订单操作日志模型
- [src/model/business_order_remarks.go](mdc:src/model/business_order_remarks.go) - 订单备注模型

## 订单状态流转
- 订单状态应严格按照预定义的状态进行流转
- 每次状态变更应记录操作日志
- 状态变更应通过专用的服务方法处理，不应直接在控制器中修改状态

## 订单处理注意事项
- 金额计算必须使用 decimal 包
- 交易信息应该通过事务处理保证一致性
- 关键操作应有日志记录
- 订单编号生成应使用统一的方法
