---
alwaysApply: false
---
# API与状态管理

## API 结构

项目中的 API 请求按业务模块组织：

### 业务系统 API

- [business/src/api](mdc:business/src/api) - 业务系统 API 根目录
  - [business/src/api/base.ts](mdc:business/src/api/base.ts) - 基础 API
  - [business/src/api/common.ts](mdc:business/src/api/common.ts) - 通用 API
  - [business/src/api/channel.ts](mdc:business/src/api/channel.ts) - 渠道相关 API
  - [business/src/api/dashboard](mdc:business/src/api/dashboard) - 仪表盘相关 API
  - [business/src/api/datacenter](mdc:business/src/api/datacenter) - 数据中心相关 API
  - [business/src/api/system](mdc:business/src/api/system) - 系统管理相关 API

### 管理系统 API

- [admin/src/api](mdc:admin/src/api) - 管理系统 API 根目录
  - [admin/src/api/common.ts](mdc:admin/src/api/common.ts) - 通用 API
  - [admin/src/api/dashboard.ts](mdc:admin/src/api/dashboard.ts) - 仪表盘相关 API
  - [admin/src/api/system](mdc:admin/src/api/system) - 系统管理相关 API

### 移动端 API

- [uniapp/api](mdc:uniapp/api) - 移动端 API 根目录
  - [uniapp/api/index.js](mdc:uniapp/api/index.js) - API 入口
  - [uniapp/api/home.js](mdc:uniapp/api/home.js) - 首页相关 API

## HTTP 请求

项目使用封装的 HTTP 请求工具进行 API 调用：

- [business/src/utils/http](mdc:business/src/utils/http) - 业务系统 HTTP 工具
- [admin/src/utils/http](mdc:admin/src/utils/http) - 管理系统 HTTP 工具
- [uniapp/utils/request](mdc:uniapp/utils/request) - 移动端 HTTP 工具

关键文件：

- [business/src/utils/http/axios.ts](mdc:business/src/utils/http/axios.ts) - Axios 封装
- [business/src/utils/http/index.ts](mdc:business/src/utils/http/index.ts) - HTTP 工具入口

## 状态管理

项目使用 Pinia 进行状态管理：

### 业务系统状态

- [business/src/store](mdc:business/src/store) - 业务系统状态管理
  - [business/src/store/index.ts](mdc:business/src/store/index.ts) - Store 入口
  - [business/src/store/modules](mdc:business/src/store/modules) - 状态模块

### 管理系统状态

- [admin/src/store](mdc:admin/src/store) - 管理系统状态管理
  - [admin/src/store/index.ts](mdc:admin/src/store/index.ts) - Store 入口
  - [admin/src/store/modules](mdc:admin/src/store/modules) - 状态模块

### 移动端状态

- [uniapp/store](mdc:uniapp/store) - 移动端状态管理
  - [uniapp/store/index.js](mdc:uniapp/store/index.js) - Store 入口
  - [uniapp/store/user.js](mdc:uniapp/store/user.js) - 用户状态

## 通用工具函数

与 API 和状态相关的工具函数：

- [business/src/utils/auth.ts](mdc:business/src/utils/auth.ts) - 认证工具
- [business/src/utils/storage.ts](mdc:business/src/utils/storage.ts) - 存储工具

## Hooks

项目使用自定义 Hooks 简化状态和 API 操作：

- [business/src/hooks](mdc:business/src/hooks) - 业务系统 Hooks
- [admin/src/hooks](mdc:admin/src/hooks) - 管理系统 Hooks

关键 Hooks：

- [business/src/hooks/request.ts](mdc:business/src/hooks/request.ts) - 请求相关 Hook
- [business/src/hooks/user.ts](mdc:business/src/hooks/user.ts) - 用户相关 Hook
