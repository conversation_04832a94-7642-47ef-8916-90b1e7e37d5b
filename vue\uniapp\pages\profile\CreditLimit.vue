<template>
	<!-- 页面容器 -->
	<view class="credit-page">
		<!-- 额度卡片区域 -->
		<view class="credit-card">
			<view class="main-block">
				<text class="label" style="font-weight: 600;">可用额度（元）</text>
				<text class="main-value">{{ availableCreditLimit }}</text>
			</view>
			<view class="card-item">
				<text class="label">已用额度</text>
				<text class="value">{{ amountLimit-availableCreditLimit }}</text>
			</view>
			<view class="card-item">
				<text class="label">总额度</text>
				<text class="value">{{ amountLimit }}</text>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onLoad
	} from '@dcloudio/uni-app';
	
	import user from '@/store/user.js';
	import { storeToRefs } from 'pinia';
	const userStore = user();
	
	// userInfo -- 用户信息
	// riskResult -- 是否审核通过 0 通过 1 审核 2 拒绝 3 调用风控模型失败
	// amountLimit 额度
	const { userInfo, isLogin, riskResult, amountLimit, availableCreditLimit } = storeToRefs(userStore);
	onLoad(async () => {
		let uid = userInfo.value?.uid;
		if (!uid) {
			await userStore.getInfo();
			uid = userInfo.value?.uid;
		}
		// 更新是否审核通过状态
		await userStore.getEvaluate(uid);
		// 获取授信额度&贷款产品
		await userStore.getProducts(uid);
	});
	
</script>

<style scoped lang="scss">
	/* 页面整体样式 */
	.credit-page {
		display: flex;
		flex-direction: column;
		background-color: #eff2f7;
		min-height: 100vh;
		font-size: 14px;
		color: #333;
	}

	/* 导航栏样式 */
	.nav-bar {
		display: flex;
		align-items: center;
		height: 44px;
		padding: 0 15px;
		border-bottom: 1px solid #fff;
		background-color: #fff;
	}

	.nav-back {
		width: 20px;
		height: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 10px;
	}

	.nav-title {
		font-size: 16px;
		font-weight: 500;
	}

	/* 额度卡片样式 */
	.credit-card {
		padding: 20px;
		background-color: #fafafa;
		margin: 10px;
		border-radius: 8px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
	}

	.main-block {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		margin-bottom: 22px;
	}

	.label {
		font-size: 14px;
		color: #000;
	}

	.main-value {
		font-size: 32px;
		font-weight: 700;
		color: #000;
		margin-top: 6px;
		line-height: 1.1;
	}

	.card-item {
		display: flex;
		justify-content: space-between;
		align-items: baseline;
		margin-bottom: 12px;
	}

	.value {
		font-size: 16px;
		font-weight: 400;
		color: #666;
	}
</style>