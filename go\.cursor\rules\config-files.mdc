---
description: src/*
alwaysApply: false
---
# 配置文件规范

## 主配置文件
- [src/resource/config.yml](mdc:src/resource/config.yml) - 系统主配置文件

## 配置文件结构
配置文件采用YAML格式，主要包含以下部分：
- server: 服务器配置（端口、域名等）
- database: 数据库配置
- redis: Redis缓存配置
- logger: 日志配置
- jwt: JWT认证配置
- upload: 文件上传配置
- third_party: 第三方服务配置（如支付、短信等）

## 配置注意事项
- 敏感信息（如密码、密钥）不应直接写入配置文件，应使用环境变量或加密存储
- 生产环境和开发环境应使用不同的配置文件
- 配置修改后需要重启服务才能生效
- 频繁变动的配置应考虑使用数据库存储，而非配置文件
# 配置文件规范

## 主配置文件
- [src/resource/config.yml](mdc:src/resource/config.yml) - 系统主配置文件

## 配置文件结构
配置文件采用YAML格式，主要包含以下部分：
- server: 服务器配置（端口、域名等）
- database: 数据库配置
- redis: Redis缓存配置
- logger: 日志配置
- jwt: JWT认证配置
- upload: 文件上传配置
- third_party: 第三方服务配置（如支付、短信等）

## 配置注意事项
- 敏感信息（如密码、密钥）不应直接写入配置文件，应使用环境变量或加密存储
- 生产环境和开发环境应使用不同的配置文件
- 配置修改后需要重启服务才能生效
- 频繁变动的配置应考虑使用数据库存储，而非配置文件
