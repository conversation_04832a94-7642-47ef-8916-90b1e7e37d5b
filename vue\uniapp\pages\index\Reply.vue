<template>
  <view class="tousu">
    <!-- 顶部 banner 区域 -->
    <view class="tousu-top">
      <image src="/static/image/toushubg-Bsap913V.png" mode="widthFix"></image>
      <view class="banner-info">
        <text class="title">我的投诉</text>
        <view class="status-tag" v-if="complaintStatus === 1">
          <text>已回复</text>
        </view>
      </view>
    </view>

    <!-- 投诉内容区域 -->
    <view class="form-section">
      <text class="section-title">* 投诉内容</text>
      <view class="complain-box">
        <textarea 
          class="complain-text" 
          v-model="complainContent" 
          placeholder="请输入您的投诉内容"
          :disabled="isViewMode"
          :style="textAreaStyle"
        ></textarea>
      </view>
    </view>

    <!-- 投诉回复区域 -->
    <view class="form-section" v-if="complaintStatus !== -1">
      <text class="section-title">* 投诉回复</text>
      <view class="complain-box">
        <textarea 
          class="complain-text reply" 
          v-model="complainResponse" 
          placeholder="等待客服回复..."
          :disabled="true"
          :style="textAreaStyle"
        ></textarea>
      </view>
    </view>

    <!-- 提交按钮（仅当没有投诉记录时显示） -->
    <view class="submit-btn-container" v-if="complaintStatus === -1">
      <button 
        class="submit-btn" 
        @click="submitComplaint"
        :disabled="!complainContent.trim()"
      >
        提交投诉
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import user from '@/store/user.js';
import complaintApi from '@/api/complaint.js'

const userStore = user();
const { userInfo } = storeToRefs(userStore)

// 数据定义
const complainContent = ref('')
const complainResponse = ref('')
const complaintStatus = ref(-1) // -1: 无投诉, 0: 处理中, 1: 已回复
const complaintId = ref(null)

// 计算属性
const isViewMode = computed(() => complaintStatus.value !== -1)
const textAreaStyle = computed(() => ({
  border: '1px solid #eee'
}))

// 页面加载时获取投诉数据
onMounted(async () => {
  let uid = userInfo.value?.uid
  if (!uid) {
    await userStore.getInfo()
    uid = userInfo.value?.uid
  }
  if (!uid) {
    uni.showToast({ title: '用户信息获取失败', icon: 'none' })
    return
  }
  try {
    const res = await complaintApi.getComplaintRes(Number(uid))
    if (res && res.code === 0 && res.data) {
      complainContent.value = res.data.complainContent || ''
      complainResponse.value = res.data.complainResponse || ''
      complaintStatus.value = res.data.status || 0
      complaintId.value = res.data.id
    } else {
      complainContent.value = ''
      complainResponse.value = ''
    }
  } catch (e) {
    complainContent.value = ''
    complainResponse.value = ''
    uni.showToast({ title: '获取投诉回复失败', icon: 'none' })
  }
})

// 提交投诉
const submitComplaint = async () => {
  if (!complainContent.value.trim()) {
    uni.showToast({ title: '请填写投诉内容', icon: 'none' })
    return
  }

  try {
    uni.showLoading({ title: '提交中...' })
    const res = await complaintApi.submitComplaint({
      content: complainContent.value
    })
    
    if (res?.code === 0) {
      uni.showToast({ title: '投诉提交成功', icon: 'success' })
      complaintStatus.value = 0 // 设置为处理中状态
      complaintId.value = res.data.id
    } else {
      uni.showToast({ title: res?.msg || '提交失败', icon: 'none' })
    }
  } catch (e) {
    uni.showToast({ title: '提交投诉失败', icon: 'none' })
    console.error('提交投诉失败:', e)
  } finally {
    uni.hideLoading()
  }
}
</script>

<style scoped>
/* 页面基础样式 */
.tousu {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  font-size: 14px;
  color: #333;
  padding-bottom: 30px;
}

/* 顶部 banner */
.tousu-top {
  position: relative;
  width: 100%;
  margin-bottom: 15px;
}

.tousu-top image {
  width: 100%;
  height: 150px !important;
  display: block;
}

.banner-info {
  position: absolute;
  left: 20px;
  top: 45px;
  display: flex;
  align-items: center;
}

.banner-info .title {
  font-size: 26px;
  font-weight: bold;
  color: #fff;
  margin-right: 15px;
}

.status-tag {
  background-color: #eff2f7;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #fff;
}

/* 表单板块 */
.form-section {
  background-color: #fff;
  margin: 12px;
  padding: 16px;
  border-radius: 8px;
  /* box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); */
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
  display: block;
}

/* 投诉内容框 */
.complain-box {
  margin-top: 8px;
}

.complain-text {
  width: 100%;
  min-height: 120px;
  padding: 12px;
  border-radius: 6px;
  font-size: 15px;
  line-height: 1.6;
  color: #333;
  box-sizing: border-box;
  background-color: #eff2f7;
}

.complain-text.reply {
  background-color: #eff2f7;
}

/* 提交按钮 */
.submit-btn-container {
  padding: 0 20px;
  margin-top: 30px;
}

.submit-btn {
  background-color: #1890ff;
  color: #fff;
  height: 48px;
  line-height: 48px;
  border-radius: 6px;
  font-size: 16px;
}

.submit-btn[disabled] {
  background-color: #d9d9d9;
  color: rgba(0, 0, 0, 0.25);
}
</style>