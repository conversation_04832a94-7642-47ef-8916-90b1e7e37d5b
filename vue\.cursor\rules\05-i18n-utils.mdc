---
alwaysApply: false
---
# 国际化与工具函数

## 国际化支持

项目支持多语言切换，主要包括中文和英文：

### 业务系统国际化

- [business/src/locale](mdc:business/src/locale) - 业务系统国际化根目录
  - [business/src/locale/index.ts](mdc:business/src/locale/index.ts) - 国际化入口
  - [business/src/locale/zh-CN.ts](mdc:business/src/locale/zh-CN.ts) - 中文语言包
  - [business/src/locale/en-US.ts](mdc:business/src/locale/en-US.ts) - 英文语言包
  - [business/src/locale/zh-CN](mdc:business/src/locale/zh-CN) - 中文模块化语言包
  - [business/src/locale/en-US](mdc:business/src/locale/en-US) - 英文模块化语言包

### 管理系统国际化

- [admin/src/locale](mdc:admin/src/locale) - 管理系统国际化根目录
  - [admin/src/locale/index.ts](mdc:admin/src/locale/index.ts) - 国际化入口
  - [admin/src/locale/zh-CN.ts](mdc:admin/src/locale/zh-CN.ts) - 中文语言包
  - [admin/src/locale/en-US.ts](mdc:admin/src/locale/en-US.ts) - 英文语言包

### 国际化 Hook

项目提供了国际化相关的自定义 Hook：

- [business/src/hooks/locale.ts](mdc:business/src/hooks/locale.ts) - 国际化 Hook

## 工具函数

项目包含丰富的工具函数，帮助开发者处理常见的任务：

### 通用工具

- [business/src/utils](mdc:business/src/utils) - 业务系统工具函数根目录
  - [business/src/utils/index.ts](mdc:business/src/utils/index.ts) - 工具函数入口
  - [business/src/utils/is.ts](mdc:business/src/utils/is.ts) - 类型判断
  - [business/src/utils/auth.ts](mdc:business/src/utils/auth.ts) - 认证相关
  - [business/src/utils/storage.ts](mdc:business/src/utils/storage.ts) - 存储相关
  - [business/src/utils/route-listener.ts](mdc:business/src/utils/route-listener.ts) - 路由监听

### 辅助函数

- [business/src/utils/helper](mdc:business/src/utils/helper) - 辅助函数目录
  - [business/src/utils/helper/permission.ts](mdc:business/src/utils/helper/permission.ts) - 权限辅助函数
  - [business/src/utils/helper/tree.ts](mdc:business/src/utils/helper/tree.ts) - 树结构辅助函数

### 环境配置

- [business/src/utils/env.ts](mdc:business/src/utils/env.ts) - 环境配置工具

## 自定义 Hooks

项目定义了多个自定义 Hooks 提高代码复用性：

### 核心 Hooks

- [business/src/hooks/core](mdc:business/src/hooks/core) - 核心 Hooks 目录
  - [business/src/hooks/core/useContext.ts](mdc:business/src/hooks/core/useContext.ts) - 上下文 Hook
  - [business/src/hooks/core/useBoolean.ts](mdc:business/src/hooks/core/useBoolean.ts) - 布尔值 Hook
  - [business/src/hooks/core/useStorage.ts](mdc:business/src/hooks/core/useStorage.ts) - 存储 Hook

### 业务 Hooks

- [business/src/hooks/user.ts](mdc:business/src/hooks/user.ts) - 用户相关 Hook
- [business/src/hooks/request.ts](mdc:business/src/hooks/request.ts) - 请求相关 Hook
- [business/src/hooks/permission.ts](mdc:business/src/hooks/permission.ts) - 权限相关 Hook
- [business/src/hooks/loading.ts](mdc:business/src/hooks/loading.ts) - 加载状态 Hook

## 事件系统

- [business/src/utils/event.ts](mdc:business/src/utils/event.ts) - 事件工具
