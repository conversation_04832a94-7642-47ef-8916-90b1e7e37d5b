<template>
  <div class="container">
    <Breadcrumb :items="['风控管理', '风控报告']" />
    <a-card class="general-card" title="风控报告查询">
      <!-- 查询表单 -->
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="queryForm"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item field="customer_id" label="客户ID">
                  <a-input-number
                    v-model="queryForm.customer_id"
                    placeholder="请输入客户ID"
                    :min="1"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="start_date" label="开始日期">
                  <a-date-picker
                    v-model="queryForm.start_date"
                    style="width: 100%"
                    placeholder="请选择开始日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item field="end_date" label="结束日期">
                  <a-date-picker
                    v-model="queryForm.end_date"
                    style="width: 100%"
                    placeholder="请选择结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    allow-clear
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 84px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-refresh />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      
      <!-- 数据表格 -->
      <a-table
        row-key="evaluation_id"
        :loading="loading"
        :pagination="false"
        :data="tableData"
        :bordered="false"
        size="medium"
      >
        <template #columns>
          <a-table-column title="评估ID" data-index="evaluation_id" :width="200" />
          <a-table-column title="风控分数" data-index="risk_score" :width="120" align="center">
            <template #cell="{ record }">
              <a-tag
                :color="getRiskScoreColor(record.risk_score)"
              >
                {{ record.risk_score }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="风控结果" data-index="risk_result" :width="120" align="center">
            <template #cell="{ record }">
              <a-tag
                :color="getRiskResultColor(record.risk_result)"
              >
                {{ getRiskResultText(record.risk_result) }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="授信额度" data-index="credit_limit" :width="120" align="center">
            <template #cell="{ record }">
              <span>{{ formatCurrency(record.credit_limit) }}</span>
            </template>
          </a-table-column>
          <a-table-column title="评估时间" data-index="evaluation_time" :width="180" align="center">
            <template #cell="{ record }">
              {{ formatDateTime(record.evaluation_time) }}
            </template>
          </a-table-column>
          <a-table-column title="操作" :width="120" align="center">
            <template #cell="{ record }">
              <a-button
                type="text"
                size="small"
                @click="viewDetail(record)"
              >
                查看详情
              </a-button>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:visible="detailVisible"
      title="风控报告详情"
      :width="1000"
      :footer="false"
    >
      <div v-if="currentRecord">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="评估ID">
            {{ currentRecord.evaluation_id }}
          </a-descriptions-item>
          <a-descriptions-item label="风控分数">
            <a-tag :color="getRiskScoreColor(currentRecord.risk_score)">
              {{ currentRecord.risk_score }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="风控结果">
            <a-tag :color="getRiskResultColor(currentRecord.risk_result)">
              {{ getRiskResultText(currentRecord.risk_result) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="授信额度">
            {{ formatCurrency(currentRecord.credit_limit) }}
          </a-descriptions-item>
          <a-descriptions-item label="评估时间" :span="2">
            {{ formatDateTime(currentRecord.evaluation_time) }}
          </a-descriptions-item>
        </a-descriptions>

        <a-divider>原始数据详情</a-divider>
        
        <a-tabs default-active-key="apply" type="card">
          <a-tab-pane key="apply" title="申请报告详情">
            <a-table
              :data="formatRawData(currentRecord.raw_data?.leida_v4?.apply_report_detail)"
              :pagination="false"
              size="small"
            >
              <template #columns>
                <a-table-column title="字段" data-index="key" :width="200" />
                <a-table-column title="值" data-index="value" />
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="behavior" title="行为报告详情">
            <a-table
              :data="formatRawData(currentRecord.raw_data?.leida_v4?.behavior_report_detail)"
              :pagination="false"
              size="small"
              :scroll="{ y: 400 }"
            >
              <template #columns>
                <a-table-column title="字段" data-index="key" :width="200" />
                <a-table-column title="值" data-index="value" />
              </template>
            </a-table>
          </a-tab-pane>
          
          <a-tab-pane key="current" title="当前报告详情">
            <a-table
              :data="formatRawData(currentRecord.raw_data?.leida_v4?.current_report_detail)"
              :pagination="false"
              size="small"
            >
              <template #columns>
                <a-table-column title="字段" data-index="key" :width="200" />
                <a-table-column title="值" data-index="value" />
              </template>
            </a-table>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconSearch, IconRefresh } from '@arco-design/web-vue/es/icon';
import { getRiskReports, type RiskReportParams, type RiskReportItem } from '@/api/risk/reports';
import useLoading from '@/hooks/loading';


const { loading, setLoading } = useLoading();

// 查询表单
const queryForm = reactive<RiskReportParams>({
  customer_id: undefined,
  start_date: '',
  end_date: '',
});

// 表格数据
const tableData = ref<RiskReportItem[]>([]);

// 详情弹窗
const detailVisible = ref(false);
const currentRecord = ref<RiskReportItem | null>(null);

// 搜索
const search = async () => {
  try {
    setLoading(true);
    const params: RiskReportParams = {};
    
    if (queryForm.customer_id) {
      params.customer_id = queryForm.customer_id;
    }
    if (queryForm.start_date) {
      params.start_date = queryForm.start_date;
    }
    if (queryForm.end_date) {
      params.end_date = queryForm.end_date;
    }
    
    const response = await getRiskReports(params);
    
    if (response.code === 200) {
      tableData.value = response.data || [];
      if (tableData.value.length === 0) {
        Message.info('暂无数据');
      }
    } else {
      Message.error(response.message || '查询失败');
    }
  } catch (error) {
    console.error('查询风控报告失败:', error);
    Message.error('查询失败，请稍后重试');
  } finally {
    setLoading(false);
  }
};

// 重置
const reset = () => {
  queryForm.customer_id = undefined;
  queryForm.start_date = '';
  queryForm.end_date = '';
  tableData.value = [];
};

// 查看详情
const viewDetail = (record: RiskReportItem) => {
  currentRecord.value = record;
  detailVisible.value = true;
};

// 格式化原始数据
const formatRawData = (data: Record<string, string> | undefined) => {
  if (!data) return [];
  return Object.entries(data).map(([key, value]) => ({ key, value }));
};

// 获取风控分数颜色
const getRiskScoreColor = (score: number) => {
  if (score >= 80) return 'green';
  if (score >= 60) return 'orange';
  return 'red';
};

// 获取风控结果颜色
const getRiskResultColor = (result: string) => {
  switch (result) {
    case 'APPROVED':
      return 'green';
    case 'REJECTED':
      return 'red';
    case 'MANUAL_REVIEW':
      return 'orange';
    default:
      return 'gray';
  }
};

// 获取风控结果文本
const getRiskResultText = (result: string) => {
  switch (result) {
    case 'APPROVED':
      return '通过';
    case 'REJECTED':
      return '拒绝';
    case 'MANUAL_REVIEW':
      return '人工审核';
    default:
      return result;
  }
};

// 格式化货币
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
  }).format(amount);
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 组件挂载时自动查询
onMounted(() => {
  search();
});
</script>

<script lang="ts">
export default {
  name: 'RiskReports',
};
</script>

<style scoped lang="less">
.container {
  padding: 0 20px 20px 20px;
  height: 100%;
}

.general-card {
  min-height: calc(100vh - 180px);
}

:deep(.arco-table-th) {
  background-color: var(--color-neutral-3);
}

:deep(.arco-descriptions-item-label) {
  font-weight: 600;
}
</style>