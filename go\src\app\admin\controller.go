package controller

/**
* 引入控制器
* 请把您使用包用 _ "fincore/app/home/<USER>"导入您编写的包 自动生成路由
* 不是使用则注释掉
* 路由规则：包路径“home/article” + 包中结构体“Cate”转小写+方法名(首字母转小写) 即：http://xx.com/home/<USER>/cate/get_list
 */
import (
	_ "fincore/app/admin/business"
	_ "fincore/app/admin/common"

	// _ "fincore/app/admin/dashboard"
	_ "fincore/app/admin/datacenter"
	_ "fincore/app/admin/matter"
	_ "fincore/app/admin/system"
	_ "fincore/app/admin/user"
)
