dbconf:
     # 数据库类型 mysql, sqlite3, post<PERSON><PERSON>, sqlserver
     driver: mysql
     #服务器地址 本地建议 127.0.0.1
     hostname: 127.0.0.1
     #端口 默认3306
     hostport: 3306
     #用户名
     username: root
     #密码
     password: root
     #数据库名
     database: fincore
     #数据表前缀
     prefix: 
jwt:
     secret: 3Bde3BGEbYqtqyEUzW3ry8jKFcaPH17fRmTmqE7MDr05Lwj95uruRKrrkb44TJ4s
     jwt_ttl: 43200
app:
     #版本号
     version: 1.3.0
     #环境状态：dev=开发，pro=生产
     env: dev
     #运行服务端口（根据您的实际情况修改）
     port: 8108
     #运行H5服务的协议（http或https）
     h5protocol: https
     #接口合法性验证
     apisecret: gofly@888
     #接口JWT验证、跨域域名-不添加请求时报403 (开发、部署必改)
     allowurl: http://************:9105,http://************:9106,https://************:444,http://localhost:6020
     #token超时时间单位分钟 
     tokenouttime: 10 
     #调用cpu个数
     cpunum: 3
     # Gin 框架在运行的时候默认是debug模式 有： 开发：debug，生产：release，测试模式：test
     runlogtype: debug
     # 配置代码生成时-前端代码根目录位置(开发必改)
     vueobjroot: D:/Project/develop/vue/gofly_enterprise/business
     #配置企业私有仓网址
     companyPrivateHouse: 
     # 配置根域名访问重定向路径,默认是业务端后台
     rootview: webbusiness
     #不需要token-根模块
     noVerifyTokenRoot: resource,webbusiness,webadmin
     #不需要api接口合法性验证-根模块md5加密
     noVerifyAPIRoot: resource,webbusiness,webadmin,uniapp
     #不需要验证token-具体请求路径
     noVerifyToken: /common/uploadfile/get_image,/common/install/index,/common/install/save,/admin/user/login,/admin/user/logout,/admin/user/refreshtoken,/admin/user/get_code,/admin/user/resetPassword,/business/user/login,/business/user/logout,/business/user/refreshtoken,/business/user/get_code,/business/user/resetPassword,/admin/user/get_logininfo,/business/user/get_logininfo,/uniapp/user/loginBySms,/uniapp/captcha/getCaptcha,/uniapp/user/postSms,/uniapp/user/postBySms,
     #不需要接口合法性-具体请求路径
     noVerifyAPI: /common/install/index,/common/install/save
     #邀请链接页面路径，默认为/login
     invitationPage: /login
     # 文件服务地址，后续要改成 oss
     fileServer: http://************:8108
log:
     # 全局日志等级 (debug/info/warn/error)
     # debug: 调试信息，开发环境使用
     # info: 一般信息，生产环境推荐
     # warn: 警告信息
     # error: 错误信息
     level: info

     # 日志根目录，所有日志文件都存放在此目录下
     # 目录结构: ./log/YYYY-MM/YYYY-MM-DD_模块名.log
     # 例如: ./log/2025-07/2025-07-17_app.log
     root_dir: ./log

     # 日志输出格式
     # json: 结构化JSON格式，便于日志分析和处理
     # text: 纯文本格式，便于人工阅读
     format: json

     # 日志输出方式，both: 同时输出到文件和控制台，file: 只输出到文件，console: 只输出到控制台
     output: file

     # 时区设置，支持标准时区名称如：Asia/Shanghai, UTC, America/New_York, Europe/London 等
     timezone: Asia/Shanghai

     # 是否在日志中显示代码调用行号
     # true: 显示文件名和行号，便于调试
     # false: 不显示，提高性能
     show_line: true

     # 日志文件轮转配置
     max_backups: 10    # 保留的旧日志文件最大数量
     max_size: 100      # 单个日志文件最大大小（MB）
     max_age: 30        # 旧日志文件最大保留天数
     compress: true     # 是否压缩旧日志文件（gzip格式）
sms:
     # 短信平台
     platform: dahantc
     # 短信平台账号
     account: dh48364
     # 短信平台密码
     password: c48b94fef1b0e9bd4f08d0ef5657dc60
     # 短信平台url
     apiurl: http://www.dh3t.com/json/sms/BatchSubmit
     # 短信平台签名
     sign:
     #是否启用第三方接口 T启用 F不启用
     isused: F
vbank:
     # 四要素验证第三方API
     apiurl: "http://v.juhe.cn/verifybankcard4/query"
     # 四要素验证第三方API key (购买后获取)
     apikey: ""
     


