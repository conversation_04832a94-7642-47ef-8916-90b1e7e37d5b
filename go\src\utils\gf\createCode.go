package gf

// "fincore/model" // 注释掉避免循环导入

// 判断某个数据表是否存在指定字段 - 注释掉避免循环导入
// TODO: 需要重构此函数，移动到合适的位置
/*
func IsHaseField(tablename, fields string) bool {
	//获取数据库名
	dielddata, _ := model.DB().Query("select COLUMN_NAME from information_schema.columns where TABLE_SCHEMA='" + global.App.Config.DBconf.Database + "' AND TABLE_NAME='" + tablename + "'")
	var tablefields []interface{}
	for _, val := range dielddata {
		var valjson map[string]interface{}
		mdata, _ := json.Marshal(val)
		json.Unmarshal(mdata, &valjson)
		tablefields = append(tablefields, valjson["COLUMN_NAME"].(string))
	}
	return IsContain(tablefields, fields)
}
*/
