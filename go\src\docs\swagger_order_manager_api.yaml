openapi: 3.0.0
info:
  title: FinCore 订单管理 API
  description: 订单管理控制器的完整API接口文档
  version: 1.0.0
  contact:
    name: FinCore Team
    email: <EMAIL>

servers:
  - url: http://localhost:8080
    description: 订单管理API服务器（开发环境）
  - url: https://api.fincore.com
    description: 订单管理API服务器（生产环境）

components:
  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码，0为成功，1为失败
          example: 0
        message:
          type: string
          description: 响应消息
          example: "操作成功"
        data:
          type: object
          description: 响应数据
        exdata:
          type: object
          description: 扩展数据
        token:
          type: string
          description: 刷新后的token（如果有）
        time:
          type: integer
          description: 响应时间戳
          example: 1701234567
      required:
        - code
        - message
        - time

    BusinessLoanOrder:
      type: object
      description: 订单详细信息（创建订单返回）
      properties:
        id:
          type: integer
          description: 订单ID
          example: 12345
        order_no:
          type: string
          description: 订单编号
          example: "ORD20241201001"
        user_id:
          type: integer
          description: 用户ID
          example: 1001
        product_rule_id:
          type: integer
          description: 产品规则ID
          example: 2
        loan_amount:
          type: string
          description: 申请贷款金额（Decimal类型，JSON中为字符串）
          example: "5000.00"
        principal:
          type: string
          description: 实际放款本金
          example: "5000.00"
        total_interest:
          type: string
          description: 总利息
          example: "300.00"
        total_guarantee_fee:
          type: string
          description: 总担保费
          example: "150.00"
        total_other_fees:
          type: string
          description: 总其他费用
          example: "50.00"
        total_repayable_amount:
          type: string
          description: 总应还金额
          example: "5500.00"
        amount_paid:
          type: string
          description: 已还金额
          example: "0.00"
        channel_id:
          type: integer
          description: 渠道ID
          example: 1
        customer_origin:
          type: string
          description: 客户来源
          example: "APP"
        initial_order_channel_id:
          type: integer
          description: 初始下单渠道ID
          example: 1
        payment_channel_id:
          type: integer
          nullable: true
          description: 支付渠道ID
          example: null
        status:
          type: integer
          description: "订单状态: 0-待放款, 1-放款中, 2-交易关闭, 3-交易完成"
          example: 0
        is_freeze:
          type: integer
          description: "是否冻结: 0-否, 1-是"
          example: 0
        is_refund_needed:
          type: integer
          description: "是否需要退款: 0-否, 1-是"
          example: 0
        complaint_status:
          type: integer
          description: "投诉状态: 0-否, 1-是"
          example: 0
        review_status:
          type: integer
          description: "审核状态: 0-待审核, 1-审核通过, 2-审核拒绝"
          example: 0
        created_at:
          type: string
          format: date-time
          nullable: true
          description: 提交时间
          example: "2024-12-01T10:00:00Z"
        disbursed_at:
          type: string
          format: date-time
          nullable: true
          description: 放款时间
          example: null
        completed_at:
          type: string
          format: date-time
          nullable: true
          description: 完成时间
          example: null
        updated_at:
          type: string
          format: date-time
          nullable: true
          description: 更新时间
          example: "2024-12-01T10:00:00Z"

    OrderCreateRequest:
      type: object
      properties:
        user_id:
          type: number
          description: 用户ID
          minimum: 1
          example: 12345
        channel_id:
          type: number
          description: 渠道ID
          minimum: 1
          example: 1
        product_rule_id:
          type: number
          description: 产品规则ID（可选，系统将根据申请金额自动匹配）
          minimum: 1
          example: 2
        loan_amount:
          type: number
          description: 申请贷款金额
          minimum: 0.01
          maximum: 999999999.99
          example: 5000.00
        customer_origin:
          type: string
          description: 客户来源
          maxLength: 50
          example: "APP"
        payment_channel_id:
          type: number
          description: 支付渠道ID（可选，默认为1）
          minimum: 1
          example: 1
      required:
        - user_id
        - channel_id
        - loan_amount

    OrderListItem:
      type: object
      description: 订单列表项（基于OrderListItem结构）
      properties:
        id:
          type: integer
          description: 订单ID
          example: 12345
        order_no:
          type: string
          description: 订单编号
          example: "ORD20241201001"
        user_id:
          type: integer
          description: 用户ID
          example: 1001
        product_rule_id:
          type: integer
          description: 产品规则ID
          example: 2
        loan_amount:
          type: number
          description: 申请贷款金额（float64类型）
          example: 5000.00
        principal:
          type: number
          description: 实际放款本金
          example: 5000.00
        total_interest:
          type: number
          description: 总利息
          example: 300.00
        total_guarantee_fee:
          type: number
          description: 总担保费
          example: 150.00
        total_other_fees:
          type: number
          description: 总其他费用
          example: 50.00
        total_repayable_amount:
          type: number
          description: 总应还金额
          example: 5500.00
        amount_paid:
          type: number
          description: 已还金额
          example: 0.00
        paid_periods:
          type: integer
          description: 已还款期数（status=1的还款账单数量）
          example: 0
        customer_origin:
          type: string
          description: 客户来源
          example: "APP"
        status:
          type: integer
          description: "订单状态: 0-待放款, 1-放款中, 2-交易关闭, 3-交易完成"
          example: 0
        is_freeze:
          type: integer
          description: "是否冻结: 0-否, 1-是"
          example: 0
        complaint_status:
          type: integer
          description: "投诉状态: 0-否, 1-是"
          example: 0
        audit_assignee_id:
          type: integer
          description: 审核员ID
          example: 0
        review_status:
          type: integer
          description: "审核状态: 0-待审核, 1-审核通过, 2-审核拒绝"
          example: 0
        sales_assignee_id:
          type: integer
          description: 业务员ID
          example: 0
        collection_assignee_id:
          type: integer
          description: 催收员ID
          example: 0
        created_at:
          type: string
          description: 创建时间
          example: "2024-12-01 10:00:00"
        updated_at:
          type: string
          description: 更新时间
          example: "2024-12-01 10:00:00"
        disbursed_at:
          type: string
          description: 放款时间
          example: ""
        user_name:
          type: string
          description: 用户名
          example: "张三"
        user_mobile:
          type: string
          description: 用户手机号（非管理员用户脱敏显示）
          example: "138****5678"
        user_id_card:
          type: string
          description: 用户身份证号（非管理员用户脱敏显示）
          example: "320***********1234"
        product_name:
          type: string
          description: 产品名称
          example: "小额贷款产品"
        loan_period:
          type: integer
          description: 借款天数
          example: 30
        total_periods:
          type: integer
          description: 借款总期数
          example: 1
        audit_assignee_name:
          type: string
          description: 审核员名称
          example: "李四"
        sales_assignee_name:
          type: string
          description: 业务员名称
          example: ""
        collection_assignee_name:
          type: string
          description: 催收员名称
          example: ""
        channel_name:
          type: string
          description: 渠道名称
          example: "APP推广渠道"
        initial_order_channel_name:
          type: string
          description: 初始下单渠道名称（来自用户注册时的渠道）
          example: "微信小程序"
        payment_channel_name:
          type: string
          description: 支付渠道名称
          example: "统统付"
        is_repeat_purchase:
          type: boolean
          description: 是否为复购用户
          example: false
        reason_for_closure:
          type: integer
          nullable: true
          description: 关单原因代码
          example: null
        reason_for_closure_text:
          type: string
          description: 关单原因文本
          example: ""
        has_complaint:
          type: integer
          description: "是否有投诉：0-无投诉，1-有投诉"
          example: 0
        risk_score:
          type: number
          nullable: true
          description: 风控分数（0-100分，可能为空）
          example: 75.5

    OrderListRequest:
      type: object
      properties:
        order_no:
          type: string
          description: 订单编号
          maxLength: 32
          example: "ORD20241201001"
        user_id:
          type: number
          description: 用户ID
          minimum: 1
          example: 12345
        product_rule_id:
          type: number
          description: 产品规则ID
          minimum: 1
          example: 2
        loan_amount_min:
          type: number
          description: 申请贷款金额最小值
          minimum: 0.01
          example: 1000.00
        loan_amount_max:
          type: number
          description: 申请贷款金额最大值
          minimum: 0.01
          example: 10000.00
        channel_id:
          type: number
          description: 渠道ID
          minimum: 1
          example: 1
        customer_origin:
          type: string
          description: 客户来源
          maxLength: 50
          example: "APP"
        initial_order_channel_id:
          type: number
          description: 初始下单渠道ID（从用户表business_app_account.channelId自动获取）
          minimum: 1
          example: 1
        payment_channel_id:
          type: number
          description: 支付渠道ID（默认为1）
          minimum: 1
          example: 1
        status:
          type: number
          description: "订单状态: 0-待放款, 1-放款中, 2-交易关闭, 3-交易完成"
          minimum: 0
          maximum: 3
          example: 0
        is_freeze:
          type: number
          description: "是否冻结: 0-否, 1-是"
          minimum: 0
          maximum: 1
          example: 0
        is_refund_needed:
          type: number
          description: "是否需要退款: 0-否, 1-是"
          minimum: 0
          maximum: 1
          example: 0
        complaint_status:
          type: number
          description: "投诉状态: 0-否, 1-是"
          minimum: 0
          maximum: 1
          example: 0
        user_name:
          type: string
          description: 用户姓名
          maxLength: 50
          example: "张三"
        user_id_card:
          type: string
          description: 用户身份证号
          maxLength: 18
          example: "320123199001011234"
        user_mobile:
          type: string
          description: 用户手机号
          pattern: '^1[3-9]\d{9}$'
          example: "13800138000"
        audit_assignee_name:
          type: string
          description: 审核人姓名
          maxLength: 50
          example: "李四"
        reason_for_closure:
          type: number
          description: "关单原因: 0-终审拒绝, 1-法院涉案, 2-纯白户, 3-客户失联, 4-不提供资料, 5-多余订单, 6-重新下单, 7-客户不同意方案"
          minimum: 0
          maximum: 7
          example: 0
        audit_assignee_id:
          type: number
          description: 审核员ID
          minimum: 1
          example: 100
        review_status:
          type: number
          description: "审核状态: 0-待审核, 1-审核通过, 2-审核拒绝"
          minimum: 0
          maximum: 2
          example: 0
        sales_assignee_id:
          type: number
          description: 跟进的业务员ID
          minimum: 1
          example: 200
        is_sales_assigned:
          type: number
          description: "是否已分配业务员: 0-未分配, 1-已分配"
          minimum: 0
          maximum: 1
          example: 1
        collection_assignee_id:
          type: number
          description: 当前催收员ID
          minimum: 1
          example: 300
        created_at_start:
          type: string
          description: "下单时间开始范围 (格式: YYYY-MM-DD HH:mm:ss)"
          example: "2024-01-01 00:00:00"
        created_at_end:
          type: string
          description: "下单时间结束范围 (格式: YYYY-MM-DD HH:mm:ss)"
          example: "2024-12-31 23:59:59"
        disbursed_at_start:
          type: string
          description: "放款时间开始范围 (格式: YYYY-MM-DD HH:mm:ss)"
          example: "2024-01-01 00:00:00"
        disbursed_at_end:
          type: string
          description: "放款时间结束范围 (格式: YYYY-MM-DD HH:mm:ss)"
          example: "2024-12-31 23:59:59"
        completed_at_start:
          type: string
          description: "结清/关闭时间开始范围 (格式: YYYY-MM-DD HH:mm:ss)"
          example: "2024-01-01 00:00:00"
        completed_at_end:
          type: string
          description: "结清/关闭时间结束范围 (格式: YYYY-MM-DD HH:mm:ss)"
          example: "2024-12-31 23:59:59"
        page:
          type: number
          description: 页码
          minimum: 1
          default: 1
          example: 1
        page_size:
          type: number
          description: 每页数量
          minimum: 1
          maximum: 100
          default: 20
          example: 20

    CloseOrderRequest:
      type: object
      properties:
        reason:
          type: string
          description: 关闭原因
          maxLength: 200
          example: "用户主动取消"
        operatorId:
          type: integer
          description: 操作员ID
          minimum: 1
          example: 1001
        operatorName:
          type: string
          description: 操作员姓名
          maxLength: 50
          example: "管理员"
      required:
        - reason

    DisbursementProcessRequest:
      type: object
      properties:
        orderNo:
          type: string
          description: 订单编号
          minLength: 1
          maxLength: 50
          example: "ORD20241201001"
        operatorId:
          type: number
          description: 操作员ID
          minimum: 1
          default: 0
          example: 1001
      required:
        - orderNo

    OrderAssignRequest:
      type: object
      properties:
        orderId:
          type: number
          description: 订单ID
          minimum: 1
          example: 12345
        salesId:
          type: number
          description: 业务员ID
          minimum: 1
          example: 2001
        operatorId:
          type: number
          description: 操作员ID
          minimum: 1
          default: 0
          example: 1001
      required:
        - orderId
        - salesId

    OrderClaimRequest:
      type: object
      properties:
        orderId:
          type: number
          description: 订单ID
          minimum: 1
          example: 12345
        salesId:
          type: number
          description: 业务员ID
          minimum: 1
          example: 2001
      required:
        - orderId
        - salesId

    ManualReviewRequest:
      type: object
      properties:
        orderId:
          type: number
          description: 订单ID
          minimum: 1
          example: 12345
        salesId:
          type: number
          description: 业务员ID
          minimum: 1
          example: 2001
        approved:
          type: boolean
          description: "审核结果：true-通过，false-拒绝"
          example: true
        remarks:
          type: string
          description: 审核备注
          maxLength: 500
          default: ""
          example: "审核通过，客户资质良好"
      required:
        - orderId
        - salesId
        - approved

    OrderStatusResponse:
      type: object
      description: 订单状态查询返回（基于GetOrderStatus方法）
      properties:
        orderId:
          type: integer
          description: 订单ID
          example: 12345
        orderNo:
          type: string
          description: 订单编号
          example: "ORD20241201001"
        status:
          type: integer
          description: "订单状态: 0-待放款, 1-放款中, 2-交易关闭, 3-交易完成"
          example: 0
        statusDescription:
          type: string
          description: 状态描述
          example: "待放款"
        canStartDisbursement:
          type: boolean
          description: 是否可以开始放款
          example: true
        canMarkAsCompleted:
          type: boolean
          description: 是否可以标记为完成
          example: false
        canClose:
          type: boolean
          description: 是否可以关闭
          example: true
        submittedAt:
          type: string
          format: date-time
          nullable: true
          description: 提交时间
          example: "2024-12-01T10:00:00Z"
        disbursedAt:
          type: string
          format: date-time
          nullable: true
          description: 放款时间
          example: null
        completedAt:
          type: string
          format: date-time
          nullable: true
          description: 完成时间
          example: null

    PaginationResponse:
      type: object
      description: 分页响应（基于pagination.PaginationResponse）
      properties:
        total:
          type: integer
          description: 总记录数
          example: 100
        page:
          type: integer
          description: 当前页码
          example: 1
        pageSize:
          type: integer
          description: 每页数量
          example: 20
        totalPages:
          type: integer
          description: 总页数
          example: 5
        hasNext:
          type: boolean
          description: 是否有下一页
          example: true
        hasPrev:
          type: boolean
          description: 是否有上一页
          example: false
        data:
          type: array
          description: 数据列表
          items:
            $ref: '#/components/schemas/OrderListItem'

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误状态码，1表示失败
          example: 1
        message:
          type: string
          description: 错误消息
          example: "参数验证失败"
        data:
          type: object
          description: 错误详情
        time:
          type: integer
          description: 响应时间戳
          example: 1701234567
      required:
        - code
        - message
        - time

    OrderCustomerInfo:
      type: object
      description: 下单人详细信息
      properties:
        name:
          type: string
          description: 姓名
          example: "张三"
        repeat_buy_count:
          type: integer
          description: 复购次数
          example: 2
        age:
          type: integer
          description: 年龄（从身份证计算）
          example: 30
        in_progress_orders:
          type: integer
          description: 在途订单数
          example: 1
        completed_orders:
          type: integer
          description: 完结订单数
          example: 3
        location:
          type: string
          description: 所在位置
          example: "湖南长沙岳麓区"
        mobile:
          type: string
          description: 手机号
          example: "13800138001"
        risk_score:
          type: integer
          description: 风控评分
          example: 85
        gender:
          type: string
          description: 性别（从身份证计算）
          enum: ["男", "女", "未知"]
          example: "男"
        total_orders:
          type: integer
          description: 全部订单数
          example: 4
        first_login_ip:
          type: string
          description: 首次登录IP地址（当前为空）
          example: ""
        first_login_location:
          type: string
          description: 首次登录地理位置（中文简体）
          example: ""
        id_card_front_url:
          type: string
          description: 身份证正面照片URL
          example: "https://example.com/front.jpg"
        id_card_back_url:
          type: string
          description: 身份证反面照片URL
          example: "https://example.com/back.jpg"
        id_card_no:
          type: string
          description: 身份证号
          example: "******************"
        order_time:
          type: string
          description: 下单时间
          example: "2024-01-01 12:00:00"
        order_status:
          type: integer
          description: 订单状态（1-放款中，2-待放款，3-交易关闭，4-交易完成）
          enum: [1, 2, 3, 4]
          example: 1
        last_login_ip:
          type: string
          description: 最后登录IP地址
          example: "*************"
        last_login_location:
          type: string
          description: 最后登录地理位置（中文简体）
          example: "中国 广东 深圳市"
        payment_channel:
          type: string
          description: 订单支付渠道
          example: "宝付"
        order_no:
          type: string
          description: 订单编号
          example: "ORD20240101001"
        loan_amount:
          type: number
          format: float
          description: 申请金额
          example: 5000.00
        user_status:
          type: integer
          description: 用户状态（0-正常，1-白名单，2-黑名单，4-风控）
          enum: [0, 1, 2, 4]
          example: 0
        register_time:
          type: string
          description: 注册时间
          example: "2024-01-01 10:00:00"
        past_quota:
          type: number
          format: float
          description: 历史额度
          example: 5000.00
        all_quota:
          type: number
          format: float
          description: 总额度
          example: 10000.00
        reminder_quota:
          type: number
          format: float
          description: 剩余额度
          example: 3000.00
        emergency_contact_0:
          type: object
          description: 紧急联系人0
          properties:
            name:
              type: string
              description: 姓名
              example: "张三"
            phone:
              type: string
              description: 电话
              example: "13800138001"
            relation:
              type: string
              description: 关系
              example: "父亲"
        emergency_contact_1:
          type: object
          description: 紧急联系人1
          properties:
            name:
              type: string
              description: 姓名
              example: "李四"
            phone:
              type: string
              description: 电话
              example: "13800138002"
            relation:
              type: string
              description: 关系
              example: "配偶"
        remarks:
          type: array
          description: 订单备注列表
          items:
            type: object
            properties:
              id:
                type: integer
                description: 备注ID
                example: 1
              content:
                type: string
                description: 备注内容
                example: "客户资料审核通过"
              user_id:
                type: integer
                description: 备注人用户ID
                example: 10
              user_name:
                type: string
                description: 备注人姓名
                example: "张审核员"
              create_time:
                type: string
                description: 备注创建时间
                example: "2024-01-01 12:00:00"
        review_status:
          type: integer
          description: 复审状态：0-未复审，1-复审通过，2-复审拒绝
          enum: [0, 1, 2]
          example: 1
        review_remark:
          type: string
          description: 复审备注
          example: "复审通过，客户资质良好"

    OrderChannelUpdateRequest:
      type: object
      description: 修改订单渠道请求参数
      properties:
        order_id:
          type: integer
          description: 订单ID
          minimum: 1
          example: 14
        channel_id:
          type: integer
          description: 新的渠道ID
          minimum: 1
          example: 2
      required:
        - order_id
        - channel_id

    OrderProgressResponse:
      type: object
      description: 订单进度响应数据
      properties:
        order_created_at:
          type: string
          description: 订单创建时间
          example: "2024-01-15 10:30:00"
        customer_name:
          type: string
          description: 订单所属客户名
          example: "张三"
        risk_passed_at:
          type: string
          nullable: true
          description: 风控通过时间，如果没有风控通过记录则为null
          example: "2024-01-15 11:00:00"
        final_status:
          type: string
          description: 最终订单状态
          enum: ["closed", "disbursed"]
          example: "closed"
        order_closed_info:
          $ref: '#/components/schemas/OrderClosedInfo'
        order_disbursed_info:
          $ref: '#/components/schemas/OrderDisbursedInfo'
      required:
        - order_created_at
        - customer_name
        - final_status

    OrderClosedInfo:
      type: object
      description: 订单关闭信息
      properties:
        closed_at:
          type: string
          description: 订单关闭时间
          example: "2024-01-15 15:30:00"
        closure_remarks:
          type: string
          description: 订单关闭备注
          example: "客户不同意方案"
        audit_assignee_name:
          type: string
          description: 审核员名称
          example: "李审核员"
      required:
        - closed_at
        - closure_remarks
        - audit_assignee_name

    OrderDisbursedInfo:
      type: object
      description: 订单打款信息
      properties:
        disbursement_id:
          type: string
          description: 打款编号
          example: "DIS20240115001"
        operator_name:
          type: string
          description: 打款操作人名称，audit_assignee_id为0时显示"风控自动放款"
          example: "王操作员"
        disbursed_at:
          type: string
          description: 打款时间
          example: "2024-01-15 16:00:00"
      required:
        - disbursement_id
        - operator_name
        - disbursed_at

paths:
  /business/order/manager/createOrder:
    post:
      summary: 创建订单
      description: |
        创建新的贷款订单，支持自动产品规则匹配。

        **重要变更说明：**
        - 初始渠道ID现在自动从用户表(business_app_account.channelId)获取，无需传入
        - 支付渠道ID如果不传入，默认使用渠道ID为1
        - 产品规则ID可选，系统将根据申请金额自动匹配合适的产品规则
      tags:
        - 订单管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderCreateRequest'
            example:
              user_id: 12345
              channel_id: 1
              product_rule_id: 2
              loan_amount: 5000.00
              customer_origin: "APP"
              payment_channel_id: 1
      responses:
        '200':
          description: 创建订单成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/BusinessLoanOrder'
              example:
                code: 0
                message: "创建订单成功"
                data:
                  id: 12345
                  order_no: "ORD20241201001"
                  user_id: 1001
                  product_rule_id: 2
                  loan_amount: "5000.00"
                  principal: "5000.00"
                  total_interest: "300.00"
                  total_guarantee_fee: "150.00"
                  total_other_fees: "50.00"
                  total_repayable_amount: "5500.00"
                  amount_paid: "0.00"
                  channel_id: 1
                  customer_origin: "APP"
                  initial_order_channel_id: 1
                  payment_channel_id: 1
                  status: 0
                  is_freeze: 0
                  is_refund_needed: 0
                  complaint_status: 0
                  review_status: 0
                  created_at: "2024-12-01T10:00:00Z"
                  disbursed_at: null
                  completed_at: null
                  updated_at: "2024-12-01T10:00:00Z"
                exdata: null
                token: ""
                time: 1701234567
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 1
                message: "参数验证失败"
                data:
                  errors:
                    - field: "user_id"
                      message: "用户ID不能为空"
                time: 1701234567
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 1
                message: "创建订单失败"
                data: "数据库连接异常"
                time: 1701234567

  /business/order/manager/listOrders:
    post:
      summary: 获取订单列表
      description: 支持分页和多条件筛选的订单列表查询，支持权限控制。包含客户投诉状态、风控分数、已还款期数字段，支持按业务员分配状态筛选。
      tags:
        - 订单管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderListRequest'
            example:
              status: 2
              channel_id: 1
              loan_amount_min: 1000.00
              loan_amount_max: 10000.00
              is_sales_assigned: 1
              created_at_start: "2024-01-01 00:00:00"
              created_at_end: "2024-12-31 23:59:59"
              page: 1
              page_size: 20
      responses:
        '200':
          description: 获取订单列表成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/PaginationResponse'
              example:
                code: 0
                message: "获取订单列表成功"
                data:
                  total: 100
                  page: 1
                  pageSize: 20
                  totalPages: 5
                  hasNext: true
                  hasPrev: false
                  data:
                    - id: 12345
                      order_no: "ORD20241201001"
                      user_id: 1001
                      product_rule_id: 2
                      loan_amount: 5000.00
                      principal: 5000.00
                      total_interest: 300.00
                      total_guarantee_fee: 150.00
                      total_other_fees: 50.00
                      total_repayable_amount: 5500.00
                      amount_paid: 0.00
                      paid_periods: 0
                      customer_origin: "APP"
                      status: 0
                      is_freeze: 0
                      complaint_status: 0
                      audit_assignee_id: 100
                      review_status: 0
                      sales_assignee_id: 0
                      collection_assignee_id: 0
                      created_at: "2024-12-01 10:00:00"
                      updated_at: "2024-12-01 10:00:00"
                      disbursed_at: ""
                      user_name: "张三"
                      user_mobile: "138****5678"
                      user_id_card: "320***********1234"
                      product_name: "小额贷款产品"
                      loan_period: 30
                      total_periods: 1
                      audit_assignee_name: "李四"
                      sales_assignee_name: ""
                      collection_assignee_name: ""
                      channel_name: "APP推广渠道"
                      initial_order_channel_name: "微信小程序"
                      payment_channel_name: "统统付"
                      is_repeat_purchase: false
                      reason_for_closure: null
                      reason_for_closure_text: ""
                      has_complaint: 0
                      risk_score: 75.5
                exdata: null
                token: ""
                time: 1701234567
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/order/manager/getOrderStatus:
    get:
      summary: 获取订单状态
      description: 根据订单编号查询订单的当前状态信息
      tags:
        - 订单管理
      parameters:
        - name: orderNo
          in: query
          required: true
          description: 订单编号
          schema:
            type: string
          example: "ORD20241201001"
      responses:
        '200':
          description: 获取订单状态成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/OrderStatusResponse'
              example:
                code: 0
                message: "获取订单状态成功"
                data:
                  orderId: 12345
                  orderNo: "ORD20241201001"
                  status: 0
                  statusDescription: "待放款"
                  canStartDisbursement: true
                  canMarkAsCompleted: false
                  canClose: true
                  submittedAt: "2024-12-01T10:00:00Z"
                  disbursedAt: null
                  completedAt: null
                exdata: null
                token: ""
                time: 1701234567
        '400':
          description: 订单编号不能为空
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 1
                message: "订单编号不能为空"
                data: null
                time: 1701234567
        '404':
          description: 订单不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 1
                message: "订单不存在"
                data: null
                time: 1701234567
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 1
                message: "查询订单状态失败"
                data: "数据库连接异常"
                time: 1701234567

  /business/order/manager/closeOrder/{orderNo}:
    post:
      summary: 关闭订单
      description: 根据订单编号关闭指定订单，需要提供关闭原因
      tags:
        - 订单管理
      parameters:
        - name: orderNo
          in: path
          required: true
          description: 订单编号
          schema:
            type: string
          example: "ORD20241201001"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CloseOrderRequest'
            example:
              reason: "用户主动取消"
              operatorId: 1001
              operatorName: "管理员"
      responses:
        '200':
          description: 关闭订单成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                status: 200
                message: "关闭订单成功"
                data: null
        '400':
          description: 参数验证失败或订单编号为空
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                status: 400
                message: "参数验证失败"
                data:
                  errors:
                    - field: "reason"
                      message: "关闭原因不能为空"
        '404':
          description: 订单不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                status: 404
                message: "订单不存在"
                data: null
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/order/manager/processDisbursement:
    post:
      summary: 处理订单放款
      description: 处理订单的放款流程，包括自动放款和人工审核放款
      tags:
        - 订单管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DisbursementProcessRequest'
            example:
              orderNo: "ORD20241201001"
              operatorId: 1001
      responses:
        '200':
          description: 处理放款成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                status: 200
                message: "处理放款成功"
                data:
                  order_no: "ORD20241201001"
                  status: 3
                  status_name: "放款中"
                  disbursement_amount: 5000.00
                  processed_at: "2024-12-01 12:00:00"
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                status: 400
                message: "参数验证失败"
                data:
                  errors:
                    - field: "orderNo"
                      message: "订单编号不能为空"
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/order/manager/assignOrder:
    post:
      summary: 分配订单给业务员
      description: 管理员将订单分配给指定的业务员进行跟进处理
      tags:
        - 订单管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderAssignRequest'
            example:
              orderId: 12345
              salesId: 2001
              operatorId: 1001
      responses:
        '200':
          description: 分配订单成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                status: 200
                message: "分配订单成功"
                data: null
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                status: 400
                message: "参数验证失败"
                data:
                  errors:
                    - field: "orderId"
                      message: "订单ID不能为空"
                    - field: "salesId"
                      message: "业务员ID不能为空"
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/order/manager/claimOrder:
    post:
      summary: 业务员认领订单
      description: 业务员主动认领待分配的订单进行跟进处理
      tags:
        - 订单管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderClaimRequest'
            example:
              orderId: 12345
              salesId: 2001
      responses:
        '200':
          description: 认领订单成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                status: 200
                message: "认领订单成功"
                data: null
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                status: 400
                message: "参数验证失败"
                data:
                  errors:
                    - field: "orderId"
                      message: "订单ID不能为空"
                    - field: "salesId"
                      message: "业务员ID不能为空"
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/order/manager/manualReview:
    post:
      summary: 人工审核订单
      description: 业务员对订单进行人工审核，决定通过或拒绝
      tags:
        - 订单管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManualReviewRequest'
            example:
              orderId: 12345
              salesId: 2001
              approved: true
              remarks: "审核通过，客户资质良好"
      responses:
        '200':
          description: 人工审核完成
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                status: 200
                message: "审核通过，已进入放款流程"
                data:
                  order_no: "ORD20241201001"
                  status: 2
                  status_name: "待放款"
                  reviewed_at: "2024-12-01 14:00:00"
                  reviewer_id: 2001
                  remarks: "审核通过，客户资质良好"
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                status: 400
                message: "参数验证失败"
                data:
                  errors:
                    - field: "orderId"
                      message: "订单ID不能为空"
                    - field: "approved"
                      message: "审核结果不能为空"
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/order/manager/getPendingOrders:
    get:
      summary: 获取待分配订单列表
      description: 获取所有待分配给业务员的订单列表，支持分页查询
      tags:
        - 订单管理
      parameters:
        - name: page
          in: query
          required: false
          description: 页码（从1开始）
          schema:
            type: string
            pattern: '^[1-9]\d*$'
            default: "1"
          example: "1"
        - name: pageSize
          in: query
          required: false
          description: 每页数量
          schema:
            type: string
            pattern: '^[1-9]\d*$'
            default: "20"
          example: "20"
      responses:
        '200':
          description: 获取待分配订单列表成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/PaginationResponse'
              example:
                status: 200
                message: "获取待分配订单列表成功"
                data:
                  list:
                    - id: 12345
                      order_no: "ORD20241201001"
                      user_id: 12345
                      channel_id: 1
                      loan_amount: 5000.00
                      status: 0
                      created_at: "2024-12-01 10:00:00"
                      sales_assignee_id: null
                    - id: 12346
                      order_no: "ORD20241201002"
                      user_id: 12346
                      channel_id: 1
                      loan_amount: 3000.00
                      status: 0
                      created_at: "2024-12-01 11:00:00"
                      sales_assignee_id: null
                  total: 50
                  page: 1
                  pageSize: 20
                  totalPages: 3
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                status: 400
                message: "参数验证失败"
                data:
                  errors:
                    - field: "page"
                      message: "页码格式不正确"
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/order/manager/updateOrderChannel:
    post:
      summary: 修改订单渠道
      description: 根据订单ID修改订单的渠道，只有待放款状态的订单才能修改渠道
      tags:
        - 订单管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderChannelUpdateRequest'
            example:
              order_id: 14
              channel_id: 2
      responses:
        '200':
          description: 修改订单渠道成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        nullable: true
              example:
                code: 0
                message: "修改订单渠道成功"
                data: null
                exdata: null
                token: ""
                time: 1701234567
        '400':
          description: 参数验证失败或业务规则错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                invalid_order_id:
                  summary: 订单ID无效
                  value:
                    code: 1
                    message: "参数错误"
                    data: "订单ID格式无效"
                    time: 1701234567
                invalid_channel_id:
                  summary: 渠道ID无效
                  value:
                    code: 1
                    message: "参数验证失败"
                    data:
                      errors:
                        - field: "channel_id"
                          message: "渠道ID必须大于0"
                    time: 1701234567
                order_status_invalid:
                  summary: 订单状态不允许修改
                  value:
                    code: 1
                    message: "修改订单渠道失败"
                    data: "订单状态不允许修改渠道，当前状态: 放款中"
                    time: 1701234567
                channel_disabled:
                  summary: 渠道未启用
                  value:
                    code: 1
                    message: "修改订单渠道失败"
                    data: "渠道未启用，无法使用"
                    time: 1701234567
                no_change:
                  summary: 渠道未发生变化
                  value:
                    code: 1
                    message: "修改订单渠道失败"
                    data: "订单渠道未发生变化"
                    time: 1701234567
        '404':
          description: 订单或渠道不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                order_not_found:
                  summary: 订单不存在
                  value:
                    code: 1
                    message: "修改订单渠道失败"
                    data: "订单不存在"
                    time: 1701234567
                channel_not_found:
                  summary: 渠道不存在
                  value:
                    code: 1
                    message: "修改订单渠道失败"
                    data: "渠道不存在"
                    time: 1701234567
        '401':
          description: 用户未登录或权限不足
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                code: 1
                message: "用户信息获取失败"
                data: "请重新登录"
                time: 1701234567
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/order/manager/getOrderCustomerInfo:
    get:
      summary: 获取下单人信息
      description: 根据订单ID获取下单人的详细信息，包括用户基本信息、订单统计、风控评分、额度信息等
      tags:
        - 订单管理
      parameters:
        - name: orderId
          in: query
          required: true
          schema:
            type: string
            pattern: '^[1-9]\d*$'
          description: 订单ID
          example: "123"
      responses:
        '200':
          description: 获取下单人信息成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/OrderCustomerInfo'
              example:
                code: 0
                message: "获取下单人信息成功"
                data:
                  name: "张三"
                  repeat_buy_count: 2
                  age: 30
                  in_progress_orders: 1
                  completed_orders: 3
                  location: "湖南长沙岳麓区"
                  mobile: "13800138001"
                  risk_score: 85
                  gender: "男"
                  total_orders: 4
                  first_login_ip: ""
                  first_login_location: ""
                  id_card_front_url: "https://example.com/front.jpg"
                  id_card_back_url: "https://example.com/back.jpg"
                  id_card_no: "******************"
                  order_time: "2024-01-01 12:00:00"
                  order_status: 1
                  last_login_ip: "*************"
                  last_login_location: "中国 广东 深圳市"
                  payment_channel: "宝付"
                  order_no: "ORD20240101001"
                  loan_amount: 5000.00
                  user_status: 0
                  register_time: "2024-01-01 10:00:00"
                  past_quota: 5000.00
                  all_quota: 10000.00
                  reminder_quota: 3000.00
                  emergency_contact_0:
                    name: "张父亲"
                    phone: "13800138001"
                    relation: "父亲"
                  emergency_contact_1:
                    name: "李配偶"
                    phone: "13800138002"
                    relation: "配偶"
                  remarks:
                    - id: 1
                      content: "客户资料审核通过"
                      user_id: 10
                      user_name: "张审核员"
                      create_time: "2024-01-01 12:00:00"
                  review_status: 1
                  review_remark: "复审通过，客户资质良好"
                exdata: null
                token: ""
                time: 1701234567
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                missing_order_id:
                  summary: 缺少订单ID
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "订单ID不能为空"
                    time: 1701234567
                invalid_order_id:
                  summary: 订单ID格式错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: "订单ID格式错误"
                    time: 1701234567
        '404':
          description: 订单或用户不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                order_not_found:
                  summary: 订单不存在
                  value:
                    code: 1
                    message: "获取下单人信息失败"
                    data: "订单不存在"
                    time: 1701234567
                user_not_found:
                  summary: 用户不存在
                  value:
                    code: 1
                    message: "获取下单人信息失败"
                    data: "用户不存在"
                    time: 1701234567
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /business/order/manager/getOrderProgress:
    get:
      summary: 获取订单进度
      description: |
        根据订单编号获取订单的详细进度信息，包括：
        - 订单创建时间
        - 订单所属客户名
        - 风控通过时间（如果有）
        - 最终订单状态（关闭或打款）
        - 订单关闭信息（如果是关闭状态）
        - 订单打款信息（如果是打款状态）

        **业务逻辑说明：**
        - 风控通过时间：通过 user_id 关联 risk_evaluations 表，查询 risk_result=0 的最新记录的 updated_at
        - 订单关闭：取 business_loan_orders.completed_at、closure_remarks、通过 audit_assignee_id 关联查询审核员名称
        - 订单打款：取 business_loan_orders.disbursed_at、通过 audit_assignee_id 关联查询操作人名称
        - 特殊处理：audit_assignee_id=0 时显示"风控自动放款"
      tags:
        - 订单管理
      parameters:
        - name: order_no
          in: query
          required: true
          description: 订单编号
          schema:
            type: string
            minLength: 1
            maxLength: 32
            pattern: '^[A-Za-z0-9]+$'
          example: "ORD20240115001"
      responses:
        '200':
          description: 获取订单进度成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/OrderProgressResponse'
              examples:
                order_closed:
                  summary: 订单关闭状态
                  value:
                    code: 0
                    message: "获取订单进度成功"
                    data:
                      order_created_at: "2024-01-15 10:30:00"
                      customer_name: "张三"
                      risk_passed_at: "2024-01-15 11:00:00"
                      final_status: "closed"
                      order_closed_info:
                        closed_at: "2024-01-15 15:30:00"
                        closure_remarks: "客户不同意方案"
                        audit_assignee_name: "李审核员"
                      order_disbursed_info: null
                    exdata: null
                    token: ""
                    time: 1701234567
                order_disbursed:
                  summary: 订单打款状态
                  value:
                    code: 0
                    message: "获取订单进度成功"
                    data:
                      order_created_at: "2024-01-15 10:30:00"
                      customer_name: "李四"
                      risk_passed_at: "2024-01-15 11:00:00"
                      final_status: "disbursed"
                      order_closed_info: null
                      order_disbursed_info:
                        disbursement_id: "DIS20240115001"
                        operator_name: "王操作员"
                        disbursed_at: "2024-01-15 16:00:00"
                    exdata: null
                    token: ""
                    time: 1701234567
                auto_disbursement:
                  summary: 风控自动放款
                  value:
                    code: 0
                    message: "获取订单进度成功"
                    data:
                      order_created_at: "2024-01-15 10:30:00"
                      customer_name: "王五"
                      risk_passed_at: "2024-01-15 11:00:00"
                      final_status: "disbursed"
                      order_closed_info: null
                      order_disbursed_info:
                        disbursement_id: "DIS20240115001"
                        operator_name: "风控自动放款"
                        disbursed_at: "2024-01-15 16:00:00"
                    exdata: null
                    token: ""
                    time: 1701234567
                no_risk_record:
                  summary: 无风控通过记录
                  value:
                    code: 0
                    message: "获取订单进度成功"
                    data:
                      order_created_at: "2024-01-15 10:30:00"
                      customer_name: "赵六"
                      risk_passed_at: null
                      final_status: "closed"
                      order_closed_info:
                        closed_at: "2024-01-15 15:30:00"
                        closure_remarks: "风控未通过"
                        audit_assignee_name: "系统自动"
                      order_disbursed_info: null
                    exdata: null
                    token: ""
                    time: 1701234567
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                missing_order_no:
                  summary: 订单编号为空
                  value:
                    code: 1
                    message: "订单编号不能为空"
                    data: null
                    time: 1701234567
                invalid_order_no:
                  summary: 订单编号格式错误
                  value:
                    code: 1
                    message: "参数验证失败"
                    data:
                      errors:
                        - field: "order_no"
                          message: "订单编号格式错误"
                    time: 1701234567
        '404':
          description: 订单不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                order_not_found:
                  summary: 订单不存在
                  value:
                    code: 1
                    message: "获取订单进度失败"
                    data: "订单不存在"
                    time: 1701234567
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                database_error:
                  summary: 数据库查询失败
                  value:
                    code: 1
                    message: "获取订单进度失败"
                    data: "查询订单失败: 数据库连接异常"
                    time: 1701234567
                customer_info_error:
                  summary: 获取客户信息失败
                  value:
                    code: 1
                    message: "获取订单进度失败"
                    data: "获取客户姓名失败: 客户不存在"
                    time: 1701234567

tags:
  - name: 订单管理
    description: 订单管理相关的所有API接口，包括创建、查询、审核、分配、放款等功能
