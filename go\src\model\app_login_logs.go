package model

import (
	"context"
	"time"

	"github.com/gogf/gf/v2/util/gconv"
)

type AppLoginLogs struct {
	ID        int    `json:"id"`
	UserID    int    `json:"user_id"`
	LoginTime string `json:"login_time"`
}

type AppLoginLogsService struct {
	ctx context.Context
}

func NewAppLoginLogsService(ctx context.Context) *AppLoginLogsService {
	return &AppLoginLogsService{ctx: ctx}
}

func (s *AppLoginLogsService) TableName() string {
	return "app_login_logs"
}

// CreateAppLoginLogs 创建登陆记录
func (s *AppLoginLogsService) CreateAppLoginLogs(userID int, loginTime string) (insertID int64, err error) {
	appLoginLogs := AppLoginLogs{
		UserID:    userID,
		LoginTime: loginTime,
	}
	return DB(WithContext(s.ctx)).Table(s.TableName()).Data(appLoginLogs).InsertGetId()
}

type QueryLoginLogCondition struct {
	UserID    int       `json:"user_id"`
	LoginTime time.Time `json:"login_time"`
}

// QueryAppLoginLogs 查询登陆记录
func (s *AppLoginLogsService) QueryAppLoginLogs(condition QueryLoginLogCondition) (appLoginLogs AppLoginLogs, err error) {
	query := DB(WithContext(s.ctx)).Table(s.TableName())

	if condition.UserID > 0 {
		query = query.Where("user_id", condition.UserID)
	}

	data, err := query.First()
	if err != nil {
		return
	}
	err = gconv.Struct(data, &appLoginLogs)
	return

}
