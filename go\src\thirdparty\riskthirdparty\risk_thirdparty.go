package riskthirdparty

import (
	"bytes"
	"crypto/aes"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fincore/global"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.uber.org/zap"
)

// RiskThirdPartyConfig 第三方风控配置
type RiskThirdPartyConfig struct {
	MerchantID string        `json:"merchant_id"`
	AESKey     string        `json:"aes_key"`
	URL        string        `json:"url"`
	ProductID  string        `json:"product_id"`
	Timeout    time.Duration `json:"timeout"`
}

// RiskRequest 风控请求参数
type RiskRequest struct {
	Name   string `json:"name"`
	IDNo   string `json:"idNo"`
	Mobile string `json:"mobile"`
}

// RiskResponse 风控响应结果
type RiskResponse struct {
	Code    int                    `json:"code"`
	Message string                 `json:"message"`
	Data    map[string]interface{} `json:"data"`
}

// RiskThirdPartyService 第三方风控服务
type RiskThirdPartyService struct {
	config *RiskThirdPartyConfig
	client *http.Client
}

// NewRiskThirdPartyService 创建第三方风控服务实例
func NewRiskThirdPartyService() *RiskThirdPartyService {
	return &RiskThirdPartyService{
		config: GetConfigFromGlobal(),
		client: createHTTPClient(),
	}
}

// GetConfig 获取配置
func (s *RiskThirdPartyService) GetConfig() *RiskThirdPartyConfig {
	return s.config
}

// SetConfig 设置配置
func (s *RiskThirdPartyService) SetConfig(config *RiskThirdPartyConfig) {
	s.config = config
}

// GetConfigFromGlobal 从全局配置中获取第三方风控配置
func GetConfigFromGlobal() *RiskThirdPartyConfig {
	if global.App == nil {
		panic("全局配置未初始化，请先初始化配置")
	}

	riskConfig := global.App.Config.RiskThirdParty
	return &RiskThirdPartyConfig{
		MerchantID: riskConfig.MerchantID,
		AESKey:     riskConfig.AESKey,
		URL:        riskConfig.URL,
		ProductID:  riskConfig.ProductID,
		Timeout:    time.Duration(riskConfig.Timeout) * time.Second,
	}
}

// QueryRiskData 查询风控数据
func (s *RiskThirdPartyService) QueryRiskData(request *RiskRequest) (*RiskResponse, error) {
	// 1. 序列化业务参数
	riskDataJSON, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("JSON序列化失败: %v", err)
	}
	// 2. AES加密
	aesStr, err := aesEncrypt(string(riskDataJSON), s.config.AESKey)
	if err != nil {
		return nil, fmt.Errorf("AES加密失败: %v", err)
	}

	fmt.Printf("merchantId:%s", s.config.MerchantID)
	fmt.Printf("productId:%s", s.config.ProductID)

	// 3. 构建请求参数
	params := map[string]interface{}{
		"merchantId": s.config.MerchantID,
		"productId":  s.config.ProductID,
		"riskData":   aesStr,
	}

	global.App.Log.Info("QueryRiskData", zap.String("merchantId", s.config.MerchantID), zap.String("productId", s.config.ProductID), zap.String("riskData", aesStr))

	// 4. 发送HTTP请求
	result, err := s.invokePostWithMap(s.config.URL, params)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	// 5. 解析响应
	response := &RiskResponse{}

	// 检查响应状态
	if code, ok := result["code"]; ok {
		if codeStr, ok := code.(string); ok {
			if codeStr != "success" {
				return nil, fmt.Errorf("风控查询失败: %v", codeStr)
			}
		}
	}
	if data, ok := result["data"]; ok {
		if dataMap, ok := data.(map[string]any); ok {
			response.Data = dataMap
		}
	}
	return response, nil
}

// aesEncrypt AES加密工具
func aesEncrypt(input, key string) (string, error) {
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}

	// PKCS5Padding
	blockSize := block.BlockSize()
	padding := blockSize - len(input)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	plainText := append([]byte(input), padText...)

	// ECB模式加密
	cipherText := make([]byte, len(plainText))
	for i := 0; i < len(plainText); i += blockSize {
		block.Encrypt(cipherText[i:i+blockSize], plainText[i:i+blockSize])
	}

	return base64.StdEncoding.EncodeToString(cipherText), nil
}

// createHTTPClient HTTP客户端配置
func createHTTPClient() *http.Client {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			MinVersion: tls.VersionTLS12,
			MaxVersion: tls.VersionTLS13,
		},
		MaxIdleConns:        1000,
		MaxIdleConnsPerHost: 1000,
		IdleConnTimeout:     60 * time.Second,
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   45 * time.Second,
	}

	return client
}

// invokePostWithMap HTTP POST请求
func (s *RiskThirdPartyService) invokePostWithMap(url string, paramsMap map[string]interface{}) (map[string]interface{}, error) {
	// 将参数转换为JSON
	jsonData, err := json.Marshal(paramsMap)
	if err != nil {
		return nil, err
	}

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Content-Encoding", "UTF-8")

	// 发送请求
	resp, err := s.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 解析JSON响应
	var result map[string]interface{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
