---
description: go/*
alwaysApply: false
---
# 应用启动流程

## 入口文件

项目启动入口在 [main.go](mdc:src/main.go) 文件中，主要执行以下流程：

1. 初始化配置 `global.App.Config.InitializeConfig()`
2. 初始化服务 `Service.InitService()`
3. 初始化日志系统 `global.App.Log = bootstrap.InitializeLog()`
4. 设置 CPU 使用数量，通过配置控制
5. 启动服务器 `bootstrap.RunServer()`

## 关键启动文件

- [bootstrap/log.go](mdc:src/bootstrap/log.go) - 日志系统初始化
- [bootstrap/router.go](mdc:src/bootstrap/router.go) - HTTP 服务和路由初始化

## 全局变量

全局配置和变量通过 [global/](mdc:src/global) 包进行管理，包含：

1. 配置信息
2. 日志实例
3. 数据库连接
4. Redis 连接
5. 应用状态信息

应用启动时会先加载配置，然后初始化各个服务组件，最后启动 HTTP 服务器开始接收请求。
