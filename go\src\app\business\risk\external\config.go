package external

import (
	"fmt"
	"fincore/global"
)

// ThirdRiskConfig 第三方风控配置
type ThirdRiskConfig struct {
	Enabled     bool   `yaml:"enabled"`      // 是否启用第三方风控服务
	URL         string `yaml:"url"`          // 第三方风控接口地址
	AppID       string `yaml:"app_id"`       // 应用ID（对应MerchantID）
	AESKey      string `yaml:"aes_key"`      // AES加密密钥
	Timeout     int    `yaml:"timeout"`      // 请求超时时间（秒）
	ServiceCode string `yaml:"service_code"` // 服务码
	DataSource  string `yaml:"data_source"`  // 数据源标识
}

// GetThirdRiskConfig 获取第三方风控配置
func GetThirdRiskConfig() (*ThirdRiskConfig, error) {
	if global.App == nil {
		return nil, fmt.Errorf("全局配置未初始化")
	}

	thirdRisk := global.App.Config.ThirdRisk
	return &ThirdRiskConfig{
		Enabled:     thirdRisk.Enabled,
		URL:         thirdRisk.URL,
		AppID:       thirdRisk.AppID, // 现在从 third_risk.app_id 读取
		AESKey:      thirdRisk.AESKey,
		Timeout:     thirdRisk.Timeout,
		ServiceCode: thirdRisk.ServiceCode,
		DataSource:  thirdRisk.DataSource,
	}, nil
}
