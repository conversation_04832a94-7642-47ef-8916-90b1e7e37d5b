package auth

import (
	"context"
	"net/http"
	"time"
)

// AuthType 认证类型
type AuthType string

const (
	AuthTypeSignature AuthType = "signature" // 签名认证（电子签）
	AuthTypePassword  AuthType = "password"  // 账号密码认证（短信）
	AuthTypeEncrypt   AuthType = "encrypt"   // 加密认证（风控）
)

// Authenticator 认证器接口
type Authenticator interface {
	// GetAuthType 获取认证类型
	GetAuthType() AuthType
	// Authenticate 执行认证，修改请求
	Authenticate(ctx context.Context, req *http.Request, bodyData []byte) error
	// IsTokenExpired 检查token是否过期（用于需要刷新token的认证方式）
	IsTokenExpired(err error) bool
	// RefreshToken 刷新token（如果支持）
	RefreshToken(ctx context.Context) error
}

// AuthConfig 认证配置接口
type AuthConfig interface {
	GetString(key string) string
	GetDuration(key string) time.Duration
}
