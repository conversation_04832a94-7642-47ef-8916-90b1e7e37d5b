<template>
  <div class="container">
    <Breadcrumb :items="['渠道管理', '渠道列表']" />
    <a-card class="general-card onelineCard" style="height: calc(100% - 50px);">
      <a-row style="margin-bottom: 10px">
        <a-col :span="16">
          <a-space>
            <a-input 
              :style="{width:'220px'}" 
              v-model="formModel.channel_name" 
              placeholder="渠道名称" 
              allow-clear 
            />
            <a-input 
              :style="{width:'180px'}" 
              v-model="formModel.channel_code" 
              placeholder="渠道编码" 
              allow-clear 
            />
            <a-select 
              v-model="formModel.channel_status" 
              :options="statusOptions" 
              placeholder="渠道状态" 
              :style="{width:'120px'}" 
            />
            <a-range-picker 
              v-model="createTimeRange" 
              :style="{width:'200px'}" 
              format="YYYY-MM-DD"
            />
            <a-button type="primary" @click="search">
              <template #icon>
                <icon-search />
              </template>
              查询
            </a-button>
            <a-button @click="reset">
              重置
            </a-button>
          </a-space>
        </a-col>
        <a-col :span="8" style="text-align: right;">
          <a-space>
            <a-button type="primary" @click="handleCreate">
              <template #icon>
                <icon-plus />
              </template>
              新增渠道
            </a-button>
            <a-tooltip content="刷新">
              <div class="action-icon" @click="search">
                <icon-refresh size="18" />
              </div>
            </a-tooltip>
            <a-dropdown @select="handleSelectDensity">
              <a-tooltip content="表格密度">
                <div class="action-icon">
                  <icon-line-height size="18" />
                </div>
              </a-tooltip>
              <template #content>
                <a-doption
                  v-for="item in densityList"
                  :key="item.value"
                  :value="item.value"
                  :class="{ active: item.value === size }"
                >
                  <span>{{ item.name }}</span>
                </a-doption>
              </template>
            </a-dropdown>
          </a-space>
        </a-col>
      </a-row>
      
      <a-table
        row-key="id"
        :loading="loading"
        :pagination="paginationConfig"
        :columns="columns"
        :data="renderData"
        :bordered="{wrapper:true,cell:true}"
        :size="size"
        ref="artable"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <template #risk_level="{ record }">
          <a-tag :color="record.risk_level === 0 ? 'red' : 'green'">
            {{ record.risk_level === 0 ? '紧' : '松' }}
          </a-tag>
        </template>
        
        <template #auto_label="{ record }">
          <a-tag :color="record.auto_label === 0 ? 'blue' : 'orange'">
            {{ record.auto_label === 0 ? '自动放款' : '手动放款' }}
          </a-tag>
        </template>
        
        <template #channel_status="{ record }">
          <a-tag :color="record.channel_status === 1 ? 'green' : 'red'">
            {{ record.channel_status === 1 ? '启用' : '禁用' }}
          </a-tag>
        </template>
        

        
        <template #create_time="{ record }">
          {{ record.create_time || '-' }}
        </template>
        
        <template #operations="{ record }">
          <div class="action-buttons">
            <div class="action-row">
              <a-button size="small" @click="handleEdit(record)">编辑</a-button>
              <a-button size="small" @click="handleGenerateInvitation(record)">
                邀请链接
              </a-button>
            </div>
            <div class="action-row">
              <a-popconfirm content="您确定要删除该渠道吗?" @ok="handleDelete(record)">
                <a-button size="small" danger>删除</a-button>
              </a-popconfirm>
            </div>
          </div>
        </template>
      </a-table>
    </a-card>
    
    <!-- 新增/编辑渠道模态框 -->
    <a-modal
      v-model:visible="channelModalVisible"
      :title="isEdit ? '修改渠道' : '新增渠道'"
      width="800px"
      :footer="false"
    >
      <a-form
        ref="channelFormRef"
        :model="channelForm"
        :rules="channelFormRules"
        layout="vertical"
        @submit="handleChannelSubmit($event)"
      >
        <a-form-item field="channel_name" label="渠道名称">
          <a-input
            v-model="channelForm.channel_name"
            placeholder="请输入渠道名称"
          />
        </a-form-item>
        
        <a-form-item field="mobile" label="手机号">
          <a-input
            v-model="channelForm.mobile"
            placeholder="请输入手机号"
          />
        </a-form-item>
        
        <a-form-item field="risk_level" label="风控级别">
          <a-select
            v-model="channelForm.risk_level"
            placeholder="请选择风控级别"
          >
            <a-option :value="0">紧</a-option>
            <a-option :value="1">松</a-option>
          </a-select>
        </a-form-item>
        
        <a-form-item field="auto_label" label="自动标签">
          <a-select
            v-model="channelForm.auto_label"
            placeholder="请选择自动标签"
          >
            <a-option :value="0">自动放款</a-option>
            <a-option :value="1">手动放款</a-option>
          </a-select>
        </a-form-item>
        
        <a-form-item field="channel_status" label="渠道状态">
          <a-select
            v-model="channelForm.channel_status"
            placeholder="请选择渠道状态"
          >
            <a-option :value="1">启用</a-option>
            <a-option :value="0">禁用</a-option>
          </a-select>
        </a-form-item>
        
        <a-form-item field="loan_rules" label="放款规则">
          <div class="loan-rules-container" style="z-index:9999; position:relative;">
            <a-select
              v-model="selectedRule"
              placeholder="请选择产品规则"
              style="margin-bottom: 12px; width: 100%;"
              @change="handleAddRule"
              :loading="productRulesLoading"
              allow-clear
              :options="productRules.map(r => ({ label: `${r.rule_name} (${r.loan_amount}元/${r.loan_period}天)`, value: String(r.id) }))"
            />
            <div v-for="(rule, index) in channelForm.loan_rules" :key="index" class="rule-item">
              <div class="rule-header">
                <span class="rule-name">{{ getProductRuleName(rule.rule_id) }}</span>
                <a-button 
                  type="text" 
                  size="small" 
                  danger 
                  @click="handleRemoveRule(index)"
                >
                  删除
                </a-button>
              </div>
              <div class="rule-inputs">
                <a-input-number
                  v-model="rule.min_risk_score"
                  placeholder="风控阈值下限"
                  :min="0"
                  :max="100"
                  :precision="2"
                  style="width: 150px; margin-right: 8px;"
                />
                <span style="margin: 0 8px;">~</span>
                <a-input-number
                  v-model="rule.max_risk_score"
                  placeholder="风控阈值上限"
                  :min="0"
                  :max="1000"
                  :precision="2"
                  style="width: 150px;"
                />
              </div>
            </div>
            <div v-if="channelForm.loan_rules.length === 0" class="empty-rules">
              暂无放款规则，请选择添加
            </div>
          </div>
        </a-form-item>
        
        <a-form-item field="risk_control_1_limit" label="续贷提额（策略1）">
          <a-input-number
            v-model="channelForm.risk_control_1_limit"
            placeholder="请输入续贷提额"
            :min="0"
            style="width: 100%"
          />
        </a-form-item>
        
<!--        <a-form-item field="risk_control_2_limit" label="续贷提额（策略2）">
          <a-input-number
            v-model="channelForm.risk_control_2_limit"
            placeholder="请输入续贷提额"
            :min="0"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item field="point_amount_1" label="节点额度">
          <a-input-number
            v-model="channelForm.point_amount_1"
            placeholder="请输入节点额度"
            :min="0"
            style="width: 100%"
          />
        </a-form-item>-->
        
        <a-form-item field="total_amount" label="封顶额度">
          <a-input-number
            v-model="channelForm.total_amount"
            placeholder="请输入封顶额度"
            :min="0"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item field="remark" label="备注">
          <a-textarea
            v-model="channelForm.remark"
            placeholder="请输入备注"
            :max-length="500"
            show-word-limit
            :auto-size="{ minRows: 3, maxRows: 6 }"
          />
        </a-form-item>
        
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :loading="submitting">
              确定
            </a-button>
            <a-button @click="handleChannelCancel">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>



  </div>
</template>

<script lang="ts" setup>
import { computed, ref, onMounted } from 'vue';
import type { TableColumnData } from '@arco-design/web-vue/es/table/interface';
import type { SelectOptionData } from '@arco-design/web-vue/es/select/interface';
import { getChannelList, generateChannelInvitation, createChannel, updateChannel, deleteChannel, getProductRules, type ProductRule } from '@/api/channel';
import { Message } from '@arco-design/web-vue';
import { IconPlus, IconRefresh, IconLineHeight, IconDownload } from '@arco-design/web-vue/es/icon';
import useLoading from '@/hooks/loading';

const { loading, setLoading } = useLoading(false);



// 表格密度选项
const densityList = computed(() => [
  { name: '迷你', value: 'mini' },
  { name: '小', value: 'small' },
  { name: '中等', value: 'medium' },
  { name: '大', value: 'large' },
]);

type SizeProps = 'mini' | 'small' | 'medium' | 'large';
const size = ref<SizeProps>('large');

// 状态选项
const statusOptions = computed<SelectOptionData[]>(() => [
  { label: "全部", value: "" },
  { label: "启用", value: 1 },
  { label: "禁用", value: 0 },
]);

// 表格列定义
const columns = computed<TableColumnData[]>(() => [
  {
    title: '渠道名称',
    dataIndex: 'channel_name',
    width: 120,
  },
  {
    title: '渠道编码',
    dataIndex: 'channel_code',
    width: 120,
  },
  {
    title: '联系手机',
    dataIndex: 'mobile',
    width: 120,
  },
  {
    title: '风控级别',
    dataIndex: 'risk_level',
    slotName: 'risk_level',
    width: 100,
    align: 'center',
  },
  {
    title: '自动标签',
    dataIndex: 'auto_label',
    slotName: 'auto_label',
    width: 100,
    align: 'center',
  },
  {
    title: '渠道状态',
    dataIndex: 'channel_status',
    slotName: 'channel_status',
    width: 100,
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    slotName: 'create_time',
    width: 150,
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operations',
    slotName: 'operations',
    width: 240,
    align: 'center',
    fixed: 'right',
  },
]);

// 表格数据
const renderData = ref([]);

// 渠道表单相关状态
const channelModalVisible = ref(false);
const isEdit = ref(false);
const submitting = ref(false);
const channelFormRef = ref();
const currentEditId = ref(null);
const selectedRule = ref<string>('');

// 产品规则相关状态
const productRules = ref<ProductRule[]>([]);
const productRulesLoading = ref(false);

// 渠道表单数据
const generateChannelFormModel = () => {
  return {
    channel_name: '',
    mobile: '',
    risk_level: 0,
    auto_label: 0,
    channel_status: 1,
    loan_rules: [], // 改为放款规则数组：[{rule_id: number, min_risk_score: number, max_risk_score: number}]
    risk_control_1_limit: 500,
    risk_control_2_limit: 1000,
    point_amount_1: 1000, // 默认节点额度
    point_amount_2: 2000, // 默认节点额度(策略2)
    total_amount: 8000,
    remark: ''
  };
};

const channelForm = ref(generateChannelFormModel());

// 表单验证规则
const channelFormRules = {
  channel_name: [
    { required: true, message: '请输入渠道名称' }
  ],
  mobile: [
    { required: true, message: '请输入手机号' },
    {
      validator: (value: string, callback:any) => {
        if(!/^1[3456789]\d{9}$/.test(value)) {
          callback('手机号格式不正确');
        }else{
          callback();
        }
      }
    },
    // { pattern: /^1[3456789]\d{9}$/, message: '手机号格式不正确' }
  ],
  channel_status: [
    { required: true, message: '请选择渠道状态' }
  ]
};

// 查询表单
const generateFormModel = () => {
  return {
    channel_name: '',
    channel_code: '',
    channel_status: '',
  };
};
const formModel = ref(generateFormModel());
const createTimeRange = ref([]);

// 分页配置
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const paginationConfig = computed(() => ({
  current: currentPage.value,
  pageSize: pageSize.value,
  total: total.value,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
  pageSizeOptions: ['10', '20', '50', '100'],
}));

// 获取数据
const fetchData = async () => {
  setLoading(true);
  try {
    const params = {
      ...formModel.value,
      page: currentPage.value,
      pageSize: pageSize.value,
      ...(createTimeRange.value && createTimeRange.value.length === 2 && {
        start_time: createTimeRange.value[0],
        end_time: createTimeRange.value[1],
      }),
    };

    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === undefined || params[key] === null) {
        delete params[key];
      }
    });

    const response = await getChannelList(params);

    if (response && response.data) {
      const { data: listData, total: totalCount } = response;
      renderData.value = Array.isArray(listData) ? listData : [];
      total.value = totalCount || 0;
    } else {
      renderData.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取渠道列表失败:', error);
    Message.error('获取渠道列表失败');
    renderData.value = [];
    total.value = 0;
  } finally {
    setLoading(false);
  }
};

// 搜索
const search = () => {
  currentPage.value = 1;
  fetchData();
};

// 重置
const reset = () => {
  formModel.value = generateFormModel();
  createTimeRange.value = [];
  currentPage.value = 1;
  fetchData();
};

// 分页处理
const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchData();
};

const handlePageSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchData();
};

// 表格密度处理
const handleSelectDensity = (val: string | number | Record<string, any> | undefined) => {
  size.value = val as SizeProps;
};

// 操作按钮处理
const handleCreate = () => {
  isEdit.value = false;
  channelForm.value = generateChannelFormModel();
  channelModalVisible.value = true;
};

const handleEdit = (record: any) => {
  isEdit.value = true;
  currentEditId.value = record.id;
  
  // 解析放款规则
  let loanRules = [];
  if (record.loan_rules) {
    try {
      if (typeof record.loan_rules === 'string') {
        // 如果是JSON字符串，解析为数组
        loanRules = JSON.parse(record.loan_rules);
      } else if (Array.isArray(record.loan_rules)) {
        // 如果已经是数组，直接使用
        loanRules = record.loan_rules;
      }
    } catch (error) {
      console.error('解析放款规则失败:', error);
      loanRules = [];
    }
  }
  
  // 填充表单数据
  channelForm.value = {
    channel_name: record.channel_name || '',
    mobile: record.mobile || '',
    risk_level: record.risk_level || 0,
    auto_label: record.auto_label || 0,
    channel_status: record.channel_status || 1,
    loan_rules: loanRules,
    risk_control_1_limit: record.risk_control_1_limit || 500,
    risk_control_2_limit: record.risk_control_2_limit || 1000,
    point_amount_1: record.point_amount_1 || null,
    total_amount: record.total_amount || 8000,
    remark: record.remark || ''
  };
  
  channelModalVisible.value = true;
};



const handleGenerateInvitation = async (record: any) => {
  const loadingMessage = Message.loading('正在生成邀请链接...');
  
  try {
    const response = await generateChannelInvitation({
      channel_id: record.id
    });
    
    console.log('生成邀请链接响应:', response);
    
    // HTTP拦截器已处理响应，直接检查数据是否存在
    if (response && (response as any).invitation_url) {
      const invitationUrl = (response as any).invitation_url;
      
      // 复制到剪贴板 - 兼容性处理
      try {
        if (navigator.clipboard && navigator.clipboard.writeText) {
          // 现代浏览器使用 Clipboard API
          await navigator.clipboard.writeText(invitationUrl);
          Message.success('邀请链接已复制到剪贴板');
        } else {
          // 降级方案：使用传统的 document.execCommand
          const textArea = document.createElement('textarea');
          textArea.value = invitationUrl;
          textArea.style.position = 'fixed';
          textArea.style.left = '-999999px';
          textArea.style.top = '-999999px';
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          
          const successful = document.execCommand('copy');
          document.body.removeChild(textArea);
          
          if (successful) {
            Message.success('邀请链接已复制到剪贴板');
          } else {
            // 如果复制失败，显示链接让用户手动复制
            Message.info({
              content: `请手动复制链接：${invitationUrl}`,
              duration: 10000
            });
          }
        }
      } catch (clipboardError) {
        console.error('复制到剪贴板失败:', clipboardError);
        // 显示链接让用户手动复制
        Message.info({
          content: `请手动复制链接：${invitationUrl}`,
          duration: 10000
        });
      }
    } else {
      console.error('响应数据中没有找到邀请链接:', response);
      Message.error('生成邀请链接失败：返回数据格式错误');
    }
  } catch (error) {
    console.error('生成邀请链接异常:', error);
    Message.error('生成邀请链接失败');
  } finally {
    // 清除loading提示
    loadingMessage.close();
  }
};



const handleDelete = async (record: any) => {
  try {
    await deleteChannel(record.id);
    Message.success('删除渠道成功');
    fetchData(); // 刷新列表
  } catch (error) {
    console.error('删除渠道失败:', error);
    Message.error('删除渠道失败');
  }
};

// 渠道表单处理
const handleChannelSubmit = async (event: any) => {
  if(!event.errors) {
    try {
      submitting.value = true;
      // event.values 才是真正的表单数据
      const data = event.values || event;
      const submitData = {
        ...data,
        loan_rules: data.loan_rules && data.loan_rules.length > 0 ? JSON.stringify(data.loan_rules) : ''
      };
      if (isEdit.value) {
        // 编辑渠道时不传 channel_code
        const { channel_code, ...updateData } = submitData;
        await updateChannel(currentEditId.value, updateData);
        Message.success('修改渠道成功');
      } else {
        // 新增渠道时不传 channel_code，让后端自动生成
        const { channel_code, ...createData } = submitData;
        if (!createData.point_amount_1) createData.point_amount_1 = 1000;
        if (!createData.point_amount_2) createData.point_amount_2 = 2000;
        await createChannel(createData);
        Message.success('新增渠道成功');
      }
      channelModalVisible.value = false;
      fetchData();
    } catch (error) {
      console.error('提交渠道失败:', error);
      Message.error(isEdit.value ? '修改渠道失败' : '新增渠道失败');
    } finally {
      submitting.value = false;
    }
  }
};

const handleChannelCancel = () => {
  channelModalVisible.value = false;
  channelForm.value = generateChannelFormModel();
  currentEditId.value = null;
  selectedRule.value = '';
};

// 放款规则处理
const handleAddRule = (ruleId: string) => {
  const idNum = Number(ruleId);
  if (!idNum) return;
  const existingRule = channelForm.value.loan_rules.find((rule: any) => rule.rule_id === idNum);
  if (existingRule) {
    Message.warning('该产品规则已存在');
    selectedRule.value = '';
    return;
  }
  channelForm.value.loan_rules.push({
    rule_id: idNum,
    min_risk_score: 0,
    max_risk_score: 1000
  });
  selectedRule.value = '';
};

const handleRemoveRule = (index: number) => {
  channelForm.value.loan_rules.splice(index, 1);
};

// 获取产品规则名称
const getProductRuleName = (ruleId: number) => {
  const rule = productRules.value.find((rule: ProductRule) => rule.id === ruleId);
  console.log('查找产品规则:', ruleId, '找到:', rule);
  return rule ? rule.rule_name : '未知规则';
};

// 加载产品规则列表
const loadProductRules = async () => {
  try {
    productRulesLoading.value = true;
    const response = await getProductRules();
    // HTTP拦截器已经处理了响应，直接返回data数组
    productRules.value = response || [];
    console.log('加载的产品规则:', productRules.value);
  } catch (error) {
    console.error('加载产品规则失败:', error);
    Message.error('加载产品规则失败');
  } finally {
    productRulesLoading.value = false;
  }
};

// 初始化
onMounted(() => {
  fetchData();
  loadProductRules();
});
</script>

<script lang="ts">
export default {
  name: 'ChannelList',
};
</script>

<style scoped lang="less">
.container {
  padding: 0 20px 20px 20px;
  height: 100%;
}

:deep(.arco-table-th) {
  &:last-child {
    .arco-table-th-item-title {
      margin-left: 16px;
    }
  }
}

.action-icon {
  margin-left: 12px;
  cursor: pointer;
}

.active {
  color: #0960bd;
  background-color: #e3f4fc;
}

:deep(.general-card > .arco-card-header) {
  padding: 10px 16px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  width: 100%;
}

.action-row {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-row .arco-btn {
  margin: 1px;
  font-size: 12px;
  padding: 2px 8px;
  height: 24px;
  line-height: 20px;
}

.upload-area {
  width: 200px;
  height: 100px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #1890ff;
}

.upload-icon {
  font-size: 24px;
  color: #999;
  margin-bottom: 8px;
}

.upload-text {
  color: #666;
  font-size: 14px;
}

.loan-rules-container {
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
}

.rule-item {
  background-color: white;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
}

.rule-item:last-child {
  margin-bottom: 0;
}

.rule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.rule-name {
  font-weight: 500;
  color: #1d2129;
}

.rule-inputs {
  display: flex;
  align-items: center;
}

.empty-rules {
  text-align: center;
  color: #86909c;
  padding: 20px;
  background-color: white;
  border: 1px dashed #e5e6eb;
  border-radius: 4px;
}
</style> 