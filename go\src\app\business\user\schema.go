package user

import "fincore/utils/jsonschema"

func GetLoginSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "管理员登陆",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"username": {
				Type:        "string",
				Required:    true,
				Description: "用户名",
			},
			"password": {
				Type:        "string",
				Required:    true,
				Description: "密码",
			},
			"mobile": {
				Type:        "string",
				Required:    true,
				Pattern:     "^1[3-9]\\d{9}$",
				Description: "手机号",
			},
			"code": {
				Type:     "string",
				Required: true,
				// 6位数字
				Pattern:     "^\\d{6}$",
				Description: "短信验证码",
			},
		},
		Required: []string{"username", "password", "mobile", "code"},
	}
}
