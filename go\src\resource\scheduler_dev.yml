# 定时任务调度器配置文件
# 开发环境配置

scheduler:
  # 调度器基础配置
  name: "fincore-scheduler-dev"       # 调度器名称
  timezone: "Asia/Shanghai"           # 时区设置
  max_concurrent_jobs: 5              # 最大并发任务数（开发环境减少）
  default_timeout: 1800               # 默认任务超时时间（秒）
  force_kill_timeout: 120              # 强制结束超时时间（秒，开发环境更短）

  # 优雅退出配置
  cancel_running_tasks: true          # 是否批量取消正在运行的任务（开发环境建议true以便快速退出）
  shutdown_timeout: 30                # 退出等待超时时间（秒，开发环境更短）

  # 并发控制
  concurrency:
    enable_parallel: true             # 是否启用并行模式
    max_parallel_per_task: 5          # 每个任务最大并行数（开发环境减少）


