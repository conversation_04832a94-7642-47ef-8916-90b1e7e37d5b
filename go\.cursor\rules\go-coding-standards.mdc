---
description: src/*
alwaysApply: false
---
# Go语言编码规范

## 命名约定
- 使用驼峰命名法
- 接口名称应以"er"结尾（例如：Writer, Reader）
- 文件名使用小写和下划线
- 包名使用小写单词
- 控制器方法命名：
  - GET请求方法通常使用 `Index`, `Show`, `List` 等前缀
  - POST请求方法通常使用 `Create`, `Store` 等前缀
  - PUT/PATCH请求方法通常使用 `Update` 前缀
  - DELETE请求方法通常使用 `Delete`, `Remove` 前缀

## 错误处理
- 返回错误时，优先使用项目定义的错误类型
- 使用 `utils/results/rebjson.go` 提供的标准响应结构

## 代码组织
- 在每个业务模块中，通常包含：
  - `controller.go`: 处理HTTP请求
  - `schema.go`: 定义请求和响应结构
  - `service.go`: 业务逻辑处理

## 注释规范
- 所有导出的函数、类型、变量应有注释
- 使用 `//` 进行单行注释
- 使用 `/* */` 进行多行注释

## 项目特有约定
- 金额处理使用 `utils/decimal` 包，避免浮点运算
- 日期时间处理使用 `utils/datetime` 包
- 数据库操作使用 `utils/gform` 包封装的方法
# Go语言编码规范

## 命名约定
- 使用驼峰命名法
- 接口名称应以"er"结尾（例如：Writer, Reader）
- 文件名使用小写和下划线
- 包名使用小写单词
- 控制器方法命名：
  - GET请求方法通常使用 `Index`, `Show`, `List` 等前缀
  - POST请求方法通常使用 `Create`, `Store` 等前缀
  - PUT/PATCH请求方法通常使用 `Update` 前缀
  - DELETE请求方法通常使用 `Delete`, `Remove` 前缀

## 错误处理
- 返回错误时，优先使用项目定义的错误类型
- 使用 `utils/results/rebjson.go` 提供的标准响应结构

## 代码组织
- 在每个业务模块中，通常包含：
  - `controller.go`: 处理HTTP请求
  - `schema.go`: 定义请求和响应结构
  - `service.go`: 业务逻辑处理

## 注释规范
- 所有导出的函数、类型、变量应有注释
- 使用 `//` 进行单行注释
- 使用 `/* */` 进行多行注释

## 项目特有约定
- 金额处理使用 `utils/decimal` 包，避免浮点运算
- 日期时间处理使用 `utils/datetime` 包
- 数据库操作使用 `utils/gform` 包封装的方法
