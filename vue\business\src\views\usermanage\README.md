# 用户管理模块

## 功能概述

用户管理模块提供了完整的客户信息管理功能，包括客户查询、详情查看、备注管理等核心功能。

## 主要功能

### 1. 客户列表查询
- 支持21个搜索条件的灵活查询
- 包含基础信息、风控信息、状态信息、时间范围等筛选条件
- 每行3个搜索条件的响应式布局

### 2. 客户信息展示
- 19列的详细信息表格
- 包含渠道、审核人、认证状态、额度信息等
- 身份证信息脱敏显示，悬浮显示完整信息

### 3. 客户操作功能
- **查看详情**：显示客户的完整信息
- **编辑备注**：添加或修改客户备注信息
- **查看备注**：查看当前客户备注
- **查看催收**：查看催收相关信息（功能待开发）

## 搜索条件说明

### 基础信息（6个）
1. 姓名 - 客户真实姓名
2. 手机号 - 客户注册手机号
3. 身份证 - 身份证号码
4. 审核人 - 负责审核的工作人员
5. 渠道来源 - 客户来源渠道
6. 用户备注 - 客户相关备注信息

### 风控信息（3个）
7. 是否风控 - 是否进入风控流程
8. 风控流水号 - 风控系统流水号
9. 风控分数范围 - 风控评分区间

### 状态信息（8个）
10. 认证状态 - 两级级联选择（阶段+子状态）
11. 订单状态 - 订单处理状态
12. 注册未实名 - 已注册但未完成实名认证
13. 实名未下单 - 已实名但未下单
14. 有额度未下单 - 有可用额度但未下单
15. 是否新用户 - 是否为新注册用户
16. 是否投诉 - 是否有投诉记录
17. 放款次数 - 历史放款次数

### 其他信息（4个）
18. 剩余额度范围 - 可用额度区间
19. 设备来源 - 注册设备类型
20. 进件时间范围 - 进件时间区间
21. 注册时间范围 - 注册时间区间

## 表格列说明

1. **No** - 序号
2. **渠道名称** - 关联渠道表显示
3. **审核人** - 关联审核人表显示
4. **客户来源** - 设备来源
5. **姓名** - 客户姓名
6. **手机号** - 联系电话
7. **用户备注** - 备注信息
8. **进件时间** - 首次进件时间
9. **注册时间** - 账户注册时间
10. **放款次数** - 历史放款次数
11. **认证状态** - 当前认证状态
12. **订单状态** - 订单处理状态
13. **投诉状态** - 投诉处理状态
14. **历史额度** - 历史总额度
15. **总额度** - 当前总额度
16. **剩余额度** - 可用额度
17. **模型分** - 风控模型评分
18. **身份证** - 脱敏显示，悬浮显示完整信息
19. **操作** - 操作按钮组

## 技术实现

### 前端技术栈
- Vue 3 + TypeScript
- Arco Design Vue组件库
- Composition API

### 后端技术栈
- Go + Gin框架
- MySQL数据库
- JSON Schema参数验证

### 核心特性
- **位运算处理**：identityStatus字段使用64位位运算
- **关联查询**：渠道、审核人等关联表查询
- **参数验证**：严格的前后端参数验证
- **响应式布局**：灵活的搜索条件布局

## 文件结构

```
src/views/usermanage/
├── index.vue                 # 主入口文件
├── list/
│   └── index.vue            # 客户列表页面
├── locale/
│   ├── zh-CN.ts            # 中文语言包
│   └── en-US.ts            # 英文语言包
└── README.md               # 说明文档

src/api/
└── usermanage.ts           # API接口定义
```

## 使用说明

1. **访问路径**：`/usermanage/list`
2. **权限要求**：需要登录认证
3. **操作流程**：
   - 设置搜索条件
   - 点击搜索按钮查询
   - 在结果列表中进行相关操作

## 注意事项

1. **身份证显示**：默认脱敏显示，鼠标悬浮显示完整信息
2. **时间范围**：支持日期范围选择
3. **分页处理**：支持分页大小调整和快速跳转
4. **数据导出**：支持搜索结果导出（功能待完善）

## 后续开发计划

1. 完善客户详情查看功能
2. 实现催收信息查看
3. 添加数据导出功能
4. 优化搜索性能
5. 增加更多筛选条件 