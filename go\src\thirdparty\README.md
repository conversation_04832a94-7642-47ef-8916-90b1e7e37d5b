# 统一第三方服务框架

这是一个统一的第三方服务对接框架，用于标准化和简化第三方API的集成过程。

## 📁 目录结构

```
thirdparty/
├── auth/                    # 认证模块
│   ├── authenticator.go     # 认证器接口
│   ├── signature_auth.go    # 签名认证（电子签）
│   ├── password_auth.go     # 账号密码认证（短信）
│   └── encrypt_auth.go      # 加密认证（风控）
├── client/                  # HTTP客户端
│   └── http_client.go       # 统一HTTP客户端
├── error_handler/           # 错误处理
│   └── error_mapper.go      # 错误映射器
├── service/                 # 服务基础
│   └── service.go           # 服务接口和基础实现
├── types/                   # 类型定义
│   └── types.go             # 公共类型定义
├── dianziqian.go            # 电子签服务
├── sms.go                   # 短信服务
├── fengkong.go              # 风控服务
├── examples.go              # 使用示例
└── README.md               # 说明文档
```

## 🚀 核心特性

- **统一接口**: 所有第三方服务都遵循相同的接口规范
- **多种认证方式**: 支持签名认证、账号密码认证、加密认证等
- **错误统一处理**: 将不同第三方的错误码映射为统一的内部错误码
- **重试机制**: 内置重试逻辑，支持指数退避
- **配置管理**: 灵活的配置接口，支持不同类型的配置项
- **易于扩展**: 新增第三方服务只需实现标准接口

## 📋 已支持的服务

### 1. 电子签服务 (dianziqian.go)
- **认证方式**: RSA签名认证
- **支持功能**: 
  - 创建个人账户 (CreatePersonalAccount)
  - 查询个人账户 (QueryPersonalAccount) 
  - 实名认证 (CheckPersonalName)
  - OCR识别 (OCR)
  - 人脸认证 (FaceAuth)
  - 查询人脸认证结果 (CheckFaceResult)
  - 创建文件 (CreateFile)
  - 添加签署方 (AddSigner)
  - 下载文件 (DownloadFile)

### 2. 短信服务 (sms.go)
- **认证方式**: 账号密码认证
- **支持功能**:
  - 发送短信 (SendSMS)
  - 查询短信状态 (QuerySMSStatus)
  - 查询余额 (GetBalance)

### 3. 风控服务 (fengkong.go)
- **认证方式**: 数据加密认证
- **支持功能**:
  - 风险检查 (RiskCheck)
  - 欺诈检测 (FraudDetection)
  - 黑名单检查 (BlacklistCheck)

## 🔧 使用方法

### 1. 配置服务

```go
// 电子签服务配置
dianziqianConfig := SimpleConfig{
    "host":        "https://prev.asign.cn/",
    "app_id":      "your_app_id",
    "private_key": "your_private_key",
    "timeout":     30 * time.Second,
}

// 短信服务配置
smsConfig := SimpleConfig{
    "base_url": "https://api.sms.com",
    "username": "your_username",
    "password": "your_password",
    "timeout":  30 * time.Second,
}

// 风控服务配置
fengkongConfig := SimpleConfig{
    "base_url":   "https://api.fengkong.com",
    "machine_id": "your_machine_id",
    "secret_key": "your_secret_key",
    "algorithm":  "aes",
    "timeout":    30 * time.Second,
}
```

### 2. 创建服务实例

```go
// 创建电子签服务
dianziqianService, err := thirdparty.NewDianziqianService(dianziqianConfig)
if err != nil {
    log.Fatalf("创建电子签服务失败: %v", err)
}

// 创建短信服务
smsService, err := thirdparty.NewSMSService(smsConfig)
if err != nil {
    log.Fatalf("创建短信服务失败: %v", err)
}

// 创建风控服务
fengkongService, err := thirdparty.NewFengkongService(fengkongConfig)
if err != nil {
    log.Fatalf("创建风控服务失败: %v", err)
}
```

### 3. 调用服务

```go
ctx := context.Background()

// 调用电子签OCR服务
ocrParams := map[string]interface{}{
    "base64Img": "base64_data",
    "side":      "front",
}
resp, err := dianziqianService.Call(ctx, "OCR", ocrParams)
if err != nil {
    log.Printf("调用失败: %v", err)
} else {
    fmt.Printf("调用成功: %+v\n", resp)
}

// 调用短信服务
smsParams := map[string]interface{}{
    "phone":   "13800138000",
    "message": "您的验证码是：123456",
}
resp, err = smsService.Call(ctx, "SendSMS", smsParams)

// 调用风控服务
riskParams := map[string]interface{}{
    "userId":    "user123",
    "operation": "transfer",
    "amount":    10000,
}
resp, err = fengkongService.Call(ctx, "RiskCheck", riskParams)
```

## 🆕 快速接入新的第三方服务

### 步骤1: 实现认证器（如果需要新的认证方式）

```go
// 如果需要新的认证方式，实现 auth.Authenticator 接口
type APIKeyAuth struct {
    apiKey string
}

func (a *APIKeyAuth) Authenticate(ctx context.Context, req *http.Request, bodyData []byte) error {
    req.Header.Set("Authorization", "Bearer " + a.apiKey)
    return nil
}
```

### 步骤2: 在thirdparty目录下创建新服务文件

直接在thirdparty根目录下创建新的第三方服务文件：

```go
// payment.go (支付服务示例)
package thirdparty

type PaymentService struct {
    *service.BaseService
}

func NewPaymentService(config types.ServiceConfig) (*PaymentService, error) {
    // 1. 创建认证器
    authenticator := auth.NewAPIKeyAuth(config.GetString("api_key"))
    
    // 2. 创建错误映射器
    errorMapper := error_handler.NewStandardErrorMapper()
    
    // 3. 创建HTTP客户端
    clientConfig := &client.ClientConfig{
        BaseURL:       config.GetString("base_url"),
        Timeout:       config.GetDuration("timeout"),
        Authenticator: authenticator,
        ErrorMapper:   errorMapper,
    }
    httpClient := client.NewHTTPClient(clientConfig)
    
    // 4. 创建基础服务
    baseService := service.NewBaseService("payment", "1.0.0", httpClient, config)
    
    paymentService := &PaymentService{BaseService: baseService}
    
    // 5. 添加支持的方法
    paymentService.AddMethod("CreateOrder")
    paymentService.AddMethod("QueryOrder")
    
    return paymentService, nil
}

func (s *PaymentService) Call(ctx context.Context, method string, params map[string]interface{}) (*types.Response, error) {
    switch method {
    case "CreateOrder":
        return s.createOrder(ctx, params)
    case "QueryOrder":
        return s.queryOrder(ctx, params)
    default:
        return nil, fmt.Errorf("不支持的方法: %s", method)
    }
}

func (s *PaymentService) createOrder(ctx context.Context, params map[string]interface{}) (*types.Response, error) {
    req := &types.Request{
        Method: "POST",
        Path:   "/api/orders",
        Data:   params,
    }
    return s.GetClient().Do(ctx, req)
}
```

### 步骤3: 使用新服务

```go
// 配置
config := SimpleConfig{
    "base_url": "https://api.payment.com",
    "api_key":  "your_api_key",
    "timeout":  30 * time.Second,
}

// 创建服务
paymentService, err := thirdparty.NewPaymentService(config)
if err != nil {
    log.Fatalf("创建服务失败: %v", err)
}

// 调用服务
resp, err := paymentService.Call(ctx, "CreateOrder", params)
```

## 🎯 设计原则

1. **完全兼容现有实现**: 电子签服务完全参考现有的 `go/src/app/dianziqian` 实现
2. **统一接口**: 所有服务都实现 `ThirdPartyService` 接口
3. **松耦合**: 各模块之间通过接口交互，便于测试和替换
4. **易扩展**: 新增服务只需实现标准接口，无需修改框架代码
5. **错误统一**: 统一的错误处理和响应格式
6. **简洁管理**: 第三方服务文件直接放在thirdparty根目录

## 🔍 认证方式详解

### 签名认证 (电子签)
- 使用RSA私钥对请求进行签名
- 完全参考现有电子签实现的签名算法
- 支持multipart/form-data格式请求

### 账号密码认证 (短信)
- 在请求参数中添加用户名和密码
- 使用JSON格式请求

### 加密认证 (风控)
- 对请求内容进行AES/DES加密
- 包含机器ID标识

## 📖 运行示例

```bash
cd go/src/thirdparty
go run examples.go
```

## 📝 文件组织约定

- 第三方服务文件直接放在thirdparty根目录：`dianziqian.go`、`sms.go`、`fengkong.go`
- 所有第三方对接代码统一在thirdparty包下
- 新增第三方服务直接在thirdparty目录下添加对应文件
- 示例代码命名为`examples.go`

这个框架使得接入新的第三方服务变得简单而标准化，同时保持了与现有实现的完全兼容性。 