package order

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"fincore/app/business/contract"
	"fincore/global"
	"fincore/model"
	"fincore/route/middleware"
	"fincore/thirdparty/payment/sumpay"
	"fincore/utils/convert"
	"fincore/utils/gform"
	"fincore/utils/idcard"
	"fincore/utils/ipgeo"
	"fincore/utils/log"
	"fincore/utils/oss"
	"fincore/utils/pagination"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
	"golang.org/x/sync/errgroup"
)

// GetOrderList 获取订单列表
func (s *Service) GetOrderList(ctx *gin.Context, params map[string]interface{}) (*pagination.PaginationResponse, error) {
	// 使用convert包工具函数获取分页参数
	page := convert.GetIntFromMap(params, "page", 1)
	pageSize := convert.GetIntFromMap(params, "pageSize", 20)

	// 构建分页请求
	paginationReq := pagination.PaginationRequest{
		Page:     page,
		PageSize: pageSize,
	}
	paginationReq.ValidateAndNormalize()

	// 调用Repository层获取订单列表
	repository := NewRepository(ctx)
	// todo 性能优化版方法 GetOrderListOptimized 可放入下个版本
	result, err := repository.GetOrderListWithFilters(params, paginationReq)
	if err != nil {
		s.logger.Errorf("获取订单列表失败: %v", err)
		return nil, fmt.Errorf("获取订单列表失败: %v", err)
	}
	// 处理查询结果数据
	processedList := s.processOrderListData(result.Data.([]gform.Data), ctx)
	result.Data = processedList

	return result, nil
}

// GetDueBillList 获取到期账单列表
func (s *Service) GetDueBillList(ctx *gin.Context, params map[string]interface{}) (*pagination.PaginationResponse, error) {
	// 使用convert包工具函数获取分页参数
	page := convert.GetIntFromMap(params, "page", 1)
	pageSize := convert.GetIntFromMap(params, "pageSize", 20)

	// 构建分页请求
	paginationReq := pagination.PaginationRequest{
		Page:     page,
		PageSize: pageSize,
	}
	paginationReq.ValidateAndNormalize()

	// 调用Repository层获取订单列表
	repository := NewRepository(ctx)
	result, err := repository.GetDueBillListWithFilters(params, paginationReq)
	if err != nil {
		s.logger.Errorf("获取到期账单列表失败: %v", err)
		return nil, fmt.Errorf("获取到期账单列表失败: %v", err)
	}
	return result, nil
}

// GetDueBillStatistics 获取到期账单统计
func (s *Service) GetDueBillStatistics(ctx *gin.Context, params map[string]interface{}) (*DueBillStatistics, error) {
	// 调用Repository层获取订单列表
	repository := NewRepository(ctx)
	result, err := repository.GetDueBillStatisticsWithFilters(params)
	if err != nil {
		s.logger.Errorf("获取到期账单统计失败: %v", err)
		return nil, fmt.Errorf("获取到期账单统计失败: %v", err)
	}
	return result, nil
}

// GetOverdueBillList 获取逾期账单列表
func (s *Service) GetOverdueBillList(ctx *gin.Context, params map[string]interface{}) (*pagination.PaginationResponse, error) {
	// 使用convert包工具函数获取分页参数
	page := convert.GetIntFromMap(params, "page", 1)
	pageSize := convert.GetIntFromMap(params, "pageSize", 20)

	// 构建分页请求
	paginationReq := pagination.PaginationRequest{
		Page:     page,
		PageSize: pageSize,
	}
	paginationReq.ValidateAndNormalize()

	// 调用Repository层获取订单列表
	repository := NewRepository(ctx)
	result, err := repository.GetOverdueBillListWithFilters(params, paginationReq)
	if err != nil {
		s.logger.Errorf("获取逾期账单列表失败: %v", err)
		return nil, fmt.Errorf("获取逾期账单列表失败: %v", err)
	}

	return result, nil
}

// GetCollectionList 获取催收详情列表
func (s *Service) GetCollectionList(ctx *gin.Context, params map[string]interface{}) ([]gform.Data, error) {
	return model.DB().Table("collection_record cr").LeftJoin("business_account ba", "cr.admin_id = ba.id").Fields(`
	ba.username as recorder,
	cr.result as result,
	cr.note as note,
	DATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time
	`).Where("cr.bill_id", params["bill_id"]).OrderBy("cr.id DESC").Get()
}

func (s *Service) DistributeCollection(ctx *gin.Context, params map[string]interface{}) error {
	billIds, _ := params["bill_ids"].([]interface{})
	for _, id := range billIds {
		_, err := model.DB().Table("business_repayment_bills").Data(
			map[string]interface{}{"collection_assignee_id": params["admin_id"]}).Where(
			"id", id).Update()
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *Service) RecordCollection(ctx *gin.Context, params map[string]interface{}) error {
	getuser, _ := ctx.Get("user")
	user := getuser.(*middleware.UserClaims)
	bills, _ := model.DB().Table("business_repayment_bills").Fields("id").Where("id", params["bill_id"]).Get()
	if len(bills) == 0 {
		return errors.New("订单可能不存在")
	}
	params["admin_id"] = user.ID
	params["created_at"] = time.Now()

	err := model.DB().Transaction(
		// 第一个业务
		func(db gform.IOrm) error {
			_, err := db.Table("collection_record").Data(params).Insert()
			if err != nil {
				return err
			}
			return nil
		},
		// 第二个业务
		func(db gform.IOrm) error {
			_, err := db.Table("business_repayment_bills").
				Data(map[string]interface{}{"last_collection_time": time.Now()}).
				Where("id", params["bill_id"]).Update()
			if err != nil {
				return err
			}
			return nil
		},
	)
	return err
}

// createOperationLog 创建操作日志
func (s *Service) createOperationLog(orderID, operatorID int, operatorName, action, details string) {
	// 参数验证
	if orderID <= 0 {
		return
	}
	if operatorName == "" {
		operatorName = "系统"
	}
	if action == "" {
		action = "未知操作"
	}

	l := &model.BusinessOrderOperationLogs{
		OrderID:      orderID,
		OperatorID:   operatorID,
		OperatorName: operatorName,
		Action:       action,
		Details:      details,
	}

	// 忽略日志记录错误，不影响主业务流程
	err := s.operationModel.CreateLog(l)
	if err != nil {
		// 记录到系统日志，但不抛出错误
		s.logger.WithFields(
			log.OrderID(orderID),
			log.String("operator_name", operatorName),
			log.String("action", action),
			log.ErrorField(err),
		).Error("创建操作日志失败")
	}
}

// createOperationLogInTransaction 在事务中创建操作日志
func (s *Service) createOperationLogInTransaction(tx gform.IOrm, orderID, operatorID int, operatorName, action, details string) error {
	// 参数验证
	if orderID <= 0 {
		return fmt.Errorf("订单ID无效")
	}
	if operatorName == "" {
		operatorName = "系统"
	}
	if action == "" {
		action = "未知操作"
	}

	// 构建日志数据
	now := time.Now()
	data := map[string]interface{}{
		"order_id":      orderID,
		"operator_id":   operatorID,
		"operator_name": operatorName,
		"action":        action,
		"details":       details,
		"created_at":    now,
	}

	// 在事务中插入操作日志
	_, err := tx.Table("business_order_operation_logs").InsertGetId(data)
	if err != nil {
		s.logger.Errorf("插入操作日志失败: %v", err)
		return fmt.Errorf("插入操作日志失败: %v", err)
	}

	return nil
}

// processOrderListData 处理订单列表数据
func (s *Service) processOrderListData(list []gform.Data, ctx *gin.Context) []OrderListItem {
	result := make([]OrderListItem, len(list))
	// 批量获取风控分数
	riskScoreMap := s.getRiskScoresBatch(ctx, list)
	// 权限控制：非管理员用户进行脱敏处理
	isAdmin := IsAdminUser(ctx)

	for i, item := range list {
		// 获取原始数据
		userIDCard := convert.GetStringFromMap(item, "user_id_card")
		userMobile := convert.GetStringFromMap(item, "user_mobile")
		repeatBuyNum := convert.GetIntFromMap(item, "repeat_buy_num", 0)
		reasonForClosureVal := convert.GetIntFromMap(item, "reason_for_closure", -2)
		userID := convert.GetIntFromMap(item, "user_id", 0)
		riskScore := riskScoreMap[userID] // 从批量查询结果中获取风控分数

		if !isAdmin {
			userIDCard = MaskIDCard(userIDCard)
			userMobile = MaskMobile(userMobile)
		}

		// 处理关单原因
		var reasonForClosure *int
		if reasonForClosureVal >= -1 {
			reasonForClosure = &reasonForClosureVal
		}

		orderItem := OrderListItem{
			ID:                   convert.GetIntFromMap(item, "id", 0),
			OrderNo:              convert.GetStringFromMap(item, "order_no"),
			UserID:               convert.GetIntFromMap(item, "user_id", 0),
			ProductRuleID:        convert.GetIntFromMap(item, "product_rule_id", 0),
			LoanAmount:           convert.GetFloatFromMap(item, "loan_amount", 0.0),
			Principal:            convert.GetFloatFromMap(item, "principal", 0.0),
			TotalInterest:        convert.GetFloatFromMap(item, "total_interest", 0.0),
			TotalGuaranteeFee:    convert.GetFloatFromMap(item, "total_guarantee_fee", 0.0),
			TotalOtherFees:       convert.GetFloatFromMap(item, "total_other_fees", 0.0),
			TotalRepayableAmount: convert.GetFloatFromMap(item, "total_repayable_amount", 0.0),
			AmountPaid:           convert.GetFloatFromMap(item, "amount_paid", 0.0),
			PaidPeriods:          convert.GetIntFromMap(item, "paid_periods", 0),
			Channel:              convert.GetStringFromMap(item, "channel"),
			RiskScore:            riskScore,
			CustomerOrigin:       convert.GetStringFromMap(item, "customer_origin"),
			Status:               convert.GetIntFromMap(item, "status", 0),
			IsFreeze:             convert.GetIntFromMap(item, "is_freeze", 0),
			ComplaintStatus:      convert.GetIntFromMap(item, "complaint_status", 0),
			ReviewStatus:         convert.GetIntFromMap(item, "review_status", 0),
			SalesAssigneeID:      convert.GetIntFromMap(item, "sales_assignee_id", 0),
			CollectionAssigneeID: convert.GetIntFromMap(item, "collection_assignee_id", 0),
			CreatedAt: convert.Ternary(convert.GetTimeFromMap(item, "created_at") != time.Time{},
				convert.GetTimeFromMap(item, "created_at").Format("2006-01-02 15:04:05"), "").(string),
			UpdatedAt: convert.Ternary(convert.GetTimeFromMap(item, "updated_at") != time.Time{},
				convert.GetTimeFromMap(item, "updated_at").Format("2006-01-02 15:04:05"), "").(string),
			DisbursedAt: convert.Ternary(convert.GetTimeFromMap(item, "disbursed_at") != time.Time{},
				convert.GetTimeFromMap(item, "disbursed_at").Format("2006-01-02 15:04:05"), "").(string),
			CompletedAt: convert.Ternary(convert.GetTimeFromMap(item, "completed_at") != time.Time{},
				convert.GetTimeFromMap(item, "completed_at").Format("2006-01-02 15:04:05"), "").(string),
			UserName:                convert.GetStringFromMap(item, "user_name"),
			UserMobile:              userMobile,
			UserIDCard:              userIDCard,
			ProductName:             convert.GetStringFromMap(item, "product_name"),
			LoanPeriod:              convert.GetIntFromMap(item, "loan_period", 0),
			TotalPeriods:            convert.GetIntFromMap(item, "total_periods", 0),
			AuditAssigneeName:       convert.GetStringFromMap(item, "audit_assignee_name"),
			SalesAssigneeName:       convert.GetStringFromMap(item, "sales_assignee_name"),
			CollectionAssigneeName:  convert.GetStringFromMap(item, "collection_assignee_name"),
			ChannelName:             convert.GetStringFromMap(item, "channel_name"),
			InitialOrderChannelName: convert.GetStringFromMap(item, "initial_order_channel_name"),
			PaymentChannelName:      convert.GetStringFromMap(item, "payment_channel_name"),
			IsRepeatPurchase:        repeatBuyNum > 0,
			ReasonForClosure:        reasonForClosure,
			ReasonForClosureText:    FormatReasonForClosure(reasonForClosure),
		}

		result[i] = orderItem
	}

	return result
}

// getRiskScoresBatch 批量获取风控分数
func (s *Service) getRiskScoresBatch(ctx *gin.Context, orderList []gform.Data) map[int]int {
	// 如果列表为空，直接返回空结果
	if len(orderList) == 0 {
		return make(map[int]int)
	}

	// 提取所有用户ID
	userIDs := make([]int, 0, len(orderList))
	userIDMap := make(map[int]bool)

	for _, item := range orderList {
		userID := convert.GetIntFromMap(item, "user_id", 0)
		if userID > 0 && !userIDMap[userID] {
			userIDs = append(userIDs, userID)
			userIDMap[userID] = true
		}
	}

	// 如果没有有效的用户ID，直接返回空结果
	if len(userIDs) == 0 {
		return make(map[int]int)
	}

	// 调用Repository层获取风控分数
	repository := NewRepository(ctx)
	result, err := repository.GetRiskScoresBatch(userIDs)
	if err != nil {
		s.logger.WithFields(
			log.String("operation", "batch_query_risk_scores"),
			log.ErrorField(err),
		).Error("批量查询风控分数失败")
		return make(map[int]int)
	}

	return result
}

// GetOrderStatus 获取订单状态信息
func (s *Service) GetOrderStatus(orderID int) (map[string]interface{}, error) {
	if orderID <= 0 {
		return nil, fmt.Errorf("订单ID无效")
	}

	order, err := s.orderModel.GetOrderByID(nil, orderID)
	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}

	result := map[string]interface{}{
		"orderId":              order.ID,
		"orderNo":              order.OrderNo,
		"status":               order.Status,
		"statusDescription":    order.GetStatusDescription(),
		"canStartDisbursement": order.CanStartDisbursement(),
		"canMarkAsCompleted":   order.CanMarkAsCompleted(),
		"canClose":             order.CanClose(),
		"submittedAt":          order.CreatedAt,
		"disbursedAt":          order.DisbursedAt,
		"completedAt":          order.CompletedAt,
	}

	return result, nil
}

// GetPendingOrdersForAssignment 获取待分配的订单列表
// 显示订单条件(1234 需都满足)：
// 1. 订单状态为待放款 (废弃)
// 2. 渠道配置自动放款 风控结果为 1 或 3 或无风控结果；(废弃)
// 3. 渠道配置人工放款 风控结果为 0 或 1 或 3 或无风控结果；(废弃)
// 4. 订单未分配业务员
func (s *Service) GetPendingOrdersForAssignment(ctx context.Context, params map[string]interface{}) (*pagination.PaginationResponse, error) {
	// 构建基础查询条件
	baseConditions := func(query gform.IOrm) gform.IOrm {
		return query.
			LeftJoin("business_app_account baa", "blo.user_id = baa.id").
			LeftJoin("channel c", "blo.channel_id = c.id").
			// Where("blo.status", model.OrderStatusPendingDisbursement).
			Where("blo.sales_assignee_id IS NULL OR blo.sales_assignee_id = 0")
		// Where(`(
		// 	(c.auto_disbursement = 1 AND (blo.risk_control_result IN (1,3) OR blo.risk_control_result IS NULL))
		// 	OR
		// 	(c.auto_disbursement = 0 AND (blo.risk_control_result IN (0,1,3) OR blo.risk_control_result IS NULL))
		// )`)
	}

	// 创建数据查询
	baseQuery := baseConditions(model.DB().Table("business_loan_orders blo"))

	// 创建计数查询（使用相同的条件）
	countQuery := baseConditions(model.DB().Table("business_loan_orders blo"))

	// 设置查询字段
	baseQuery = baseQuery.Fields(`
		blo.id,
		blo.order_no,
		blo.user_id,
		blo.principal,
		blo.total_repayable_amount,
		blo.created_at,
		blo.risk_control_results,
		baa.name as user_name,
		baa.mobile as user_mobile,
		c.auto_label,
		c.channel_name
	`)

	// 设置排序
	baseQuery = baseQuery.OrderBy("blo.id DESC")

	// 执行分页查询
	paginationRequest := pagination.PaginationRequest{
		Page:     convert.GetIntFromMap(params, "page", 1),
		PageSize: convert.GetIntFromMap(params, "pageSize", 20),
	}
	paginationRequest.ValidateAndNormalize()

	return pagination.PaginateWithCustomQuery(countQuery, baseQuery, paginationRequest)
}

// GetOrderCustomerInfo 获取下单人信息
func (s *Service) GetOrderCustomerInfo(orderID int64) (map[string]interface{}, error) {
	// 1. 查询订单基本信息
	orderInfo, err := s.getOrderBasicInfo(orderID)
	if err != nil {
		return nil, err
	}

	userID := convert.GetIntFromMap(orderInfo, "user_id", 0)
	if userID == 0 {
		return nil, fmt.Errorf("订单用户ID无效")
	}

	// 2. 使用errgroup并发查询各种信息以提高性能
	var (
		userInfo       map[string]interface{}
		orderStats     map[string]interface{}
		paymentChannel map[string]interface{}
		riskScore      int
		orderRemarks   []model.OrderRemarkWithUser // 订单备注列表
	)

	g, _ := errgroup.WithContext(context.Background())

	// 并发查询用户基本信息
	g.Go(func() error {
		userInfo, err = s.getUserBasicInfo(int64(userID))
		return err
	})

	// 并发查询用户订单统计
	g.Go(func() error {
		orderStats, err = s.GetUserOrderStats(int64(userID))
		return err
	})

	// 并发查询支付渠道信息
	g.Go(func() error {
		paymentChannelID := convert.GetIntFromMap(orderInfo, "payment_channel_id", 0)
		if paymentChannelID > 0 {
			paymentChannel, err = s.getPaymentChannelInfo(int64(paymentChannelID))
		}
		return err
	})

	// 并发查询风控评分
	g.Go(func() error {
		riskScore, err = s.getUserRiskScore(int64(userID))
		return err
	})

	// 并发查询订单备注列表
	g.Go(func() error {
		orderRemarks, err = s.remarksModel.GetRemarksByOrderID(orderID)
		return err
	})

	// 等待所有查询完成
	if err := g.Wait(); err != nil {
		return nil, fmt.Errorf("查询用户信息失败: %v", err)
	}

	// 3. 组装响应数据
	result := s.assembleCustomerInfo(orderInfo, userInfo, orderStats, paymentChannel, riskScore, orderRemarks)

	return result, nil
}

// getOrderBasicInfo 获取订单基本信息
func (s *Service) getOrderBasicInfo(orderID int64) (map[string]interface{}, error) {
	query := `
		SELECT
			id, order_no, user_id, product_rule_id, loan_amount, principal,
			total_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,
			amount_paid, channel_id, payment_channel_id, customer_origin, status,
			is_freeze, review_status, review_remark,
			created_at, disbursed_at, completed_at
		FROM business_loan_orders
		WHERE id = ?
	`

	result, err := model.DB().Query(query, orderID)
	if err != nil {
		return nil, fmt.Errorf("查询订单信息失败: %v", err)
	}

	if len(result) == 0 {
		return nil, fmt.Errorf("订单不存在")
	}

	return result[0], nil
}

// getUserBasicInfo 获取用户基本信息
func (s *Service) getUserBasicInfo(userID int64) (map[string]interface{}, error) {
	query := `
		SELECT
			id, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,
			repeatBuyNum, riskScore, address, province, city,
			lastLoginIp, createtime, status,
			allQuota, reminderQuota,
			emergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,
			emergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation
		FROM business_app_account
		WHERE id = ?
	`

	result, err := model.DB().Query(query, userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户信息失败: %v", err)
	}

	if len(result) == 0 {
		return nil, fmt.Errorf("用户不存在")
	}

	return result[0], nil
}

// getUserOrderStats 获取用户订单统计信息
func (s *Service) GetUserOrderStats(userID int64) (map[string]interface{}, error) {
	query := `
		SELECT
			COUNT(*) as total_orders,
			SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,
			SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,
			MIN(created_at) as first_order_time
		FROM business_loan_orders
		WHERE user_id = ?
	`

	result, err := model.DB(model.WithContext(s.ctx)).Query(query, userID)
	if err != nil {
		return nil, fmt.Errorf("查询用户订单统计失败: %v", err)
	}

	if len(result) == 0 {
		return map[string]interface{}{
			"total_orders":       0,
			"in_progress_orders": 0,
			"completed_orders":   0,
			"first_order_time":   nil,
		}, nil
	}

	return result[0], nil
}

// getPaymentChannelInfo 获取支付渠道信息
func (s *Service) getPaymentChannelInfo(channelID int64) (map[string]interface{}, error) {
	query := `
		SELECT id, channel_name, channel_code
		FROM business_payment_channels
		WHERE id = ?
	`

	result, err := model.DB().Query(query, channelID)
	if err != nil {
		return nil, fmt.Errorf("查询支付渠道信息失败: %v", err)
	}

	if len(result) == 0 {
		return map[string]interface{}{
			"channel_name": "未知渠道",
			"channel_code": "",
		}, nil
	}

	return result[0], nil
}

// getUserRiskScore 获取用户风控评分
func (s *Service) getUserRiskScore(userID int64) (int, error) {
	// 优先从business_app_account表获取
	query1 := `
		SELECT riskScore
		FROM business_app_account
		WHERE id = ? AND riskScore > 0
	`

	result, err := model.DB().Query(query1, userID)
	if err == nil && len(result) > 0 {
		riskScore := convert.GetIntFromMap(result[0], "riskScore", 0)
		if riskScore > 0 {
			return riskScore, nil
		}
	}

	// 如果没有，从risk_evaluations表获取最新的评分
	query2 := `
		SELECT risk_score
		FROM risk_evaluations
		WHERE customer_id = ?
		ORDER BY evaluation_time DESC
		LIMIT 1
	`

	result, err = model.DB().Query(query2, userID)
	if err != nil {
		return 0, fmt.Errorf("查询风控评分失败: %v", err)
	}

	if len(result) == 0 {
		return 0, nil // 没有风控评分记录
	}

	return convert.GetIntFromMap(result[0], "risk_score", 0), nil
}

// assembleCustomerInfo 组装下单人信息响应数据
// 参数说明：
// - orderInfo: 订单基本信息
// - userInfo: 用户基本信息
// - orderStats: 用户订单统计信息
// - paymentChannel: 支付渠道信息
// - riskScore: 风控评分
// - orderRemarks: 订单备注列表
func (s *Service) assembleCustomerInfo(orderInfo, userInfo, orderStats, paymentChannel map[string]interface{}, riskScore int, orderRemarks []model.OrderRemarkWithUser) map[string]interface{} {
	// 获取基本信息
	name := convert.GetStringFromMap(userInfo, "name")
	mobile := convert.GetStringFromMap(userInfo, "mobile")
	idCardNo := convert.GetStringFromMap(userInfo, "idCard")

	// 计算年龄和性别
	age := idcard.CalculateAge(idCardNo)
	gender := idcard.GetGenderFromIDCard(idCardNo)

	// 获取位置信息
	province := convert.GetStringFromMap(userInfo, "province")
	city := convert.GetStringFromMap(userInfo, "city")
	address := convert.GetStringFromMap(userInfo, "address")
	location := ""
	if province != "" && city != "" {
		location = province + city
	}
	if address != "" {
		if location != "" {
			location += " " + address
		} else {
			location = address
		}
	}

	// 获取订单统计
	totalOrders := convert.GetIntFromMap(orderStats, "total_orders", 0)
	inProgressOrders := convert.GetIntFromMap(orderStats, "in_progress_orders", 0)
	completedOrders := convert.GetIntFromMap(orderStats, "completed_orders", 0)

	// 获取支付渠道名称
	paymentChannelName := convert.GetStringFromMap(paymentChannel, "channel_name")

	// 获取IP地址并转换为地理位置
	firstLoginIP := "" // 当前表结构中没有此字段
	lastLoginIP := convert.GetStringFromMap(userInfo, "lastLoginIp")

	// 转换IP地址为地理位置
	firstLoginLocation := ""
	lastLoginLocation := ""

	if firstLoginIP != "" {
		firstLoginLocation = ipgeo.GetIPLocationString(firstLoginIP)
	}

	if lastLoginIP != "" {
		lastLoginLocation = ipgeo.GetIPLocationString(lastLoginIP)
	}

	// 获取紧急联系人信息
	emergencyContact0 := map[string]interface{}{
		"name":     convert.GetStringFromMap(userInfo, "emergencyContact0Name"),     // 紧急联系人0姓名
		"phone":    convert.GetStringFromMap(userInfo, "emergencyContact0Phone"),    // 紧急联系人0电话
		"relation": convert.GetStringFromMap(userInfo, "emergencyContact0Relation"), // 紧急联系人0关系
	}

	emergencyContact1 := map[string]interface{}{
		"name":     convert.GetStringFromMap(userInfo, "emergencyContact1Name"),     // 紧急联系人1姓名
		"phone":    convert.GetStringFromMap(userInfo, "emergencyContact1Phone"),    // 紧急联系人1电话
		"relation": convert.GetStringFromMap(userInfo, "emergencyContact1Relation"), // 紧急联系人1关系
	}

	// 处理订单备注列表
	// 将备注信息转换为前端需要的格式，包含时间格式化
	var remarksList []map[string]interface{}
	for _, remark := range orderRemarks {
		remarkItem := map[string]interface{}{
			"id":          remark.ID,                                                     // 备注ID
			"content":     remark.Content,                                                // 备注内容
			"user_id":     remark.UserID,                                                 // 备注人用户ID
			"user_name":   remark.UserName,                                               // 备注人姓名
			"create_time": time.Unix(remark.CreateTime, 0).Format("2006-01-02 15:04:05"), // 格式化创建时间
		}
		remarksList = append(remarksList, remarkItem)
	}

	// 处理图片URL，兼容OSS和本地存储
	var idCardFrontUrl, idCardBackUrl, facePhotoUrl string
	originalFrontUrl := convert.GetStringFromMap(userInfo, "idCardFrontUrl")
	originalBackUrl := convert.GetStringFromMap(userInfo, "idCardBackUrl")
	originalFacePhotoUrl := convert.GetStringFromMap(userInfo, "facePhotoUrl")

	if oss.IsOSSEnabled() {
		// OSS模式：生成签名URL
		ossClient, err := oss.GetOSSClient()
		if err != nil {
			log.Error("OSS客户端初始化失败: %v", err)
			// 降级到原始URL
			idCardFrontUrl = originalFrontUrl
			idCardBackUrl = originalBackUrl
			facePhotoUrl = originalFacePhotoUrl
		} else {
			if originalFrontUrl != "" {
				objectKey := oss.GetObjectKeyFromURL(originalFrontUrl)
				idCardFrontUrl, err = ossClient.GetSignedURL(objectKey, 15*time.Minute)
				if err != nil {
					log.Error("获取ID卡正面URL失败: %v", err)
					idCardFrontUrl = originalFrontUrl
				}
			}
			if originalBackUrl != "" {
				objectKey := oss.GetObjectKeyFromURL(originalBackUrl)
				idCardBackUrl, err = ossClient.GetSignedURL(objectKey, 15*time.Minute)
				if err != nil {
					log.Error("获取ID卡反面URL失败: %v", err)
					idCardBackUrl = originalBackUrl
				}
			}

			if originalFacePhotoUrl != "" {
				objectKey := oss.GetObjectKeyFromURL(originalFacePhotoUrl)
				facePhotoUrl, err = ossClient.GetSignedURL(objectKey, 15*time.Minute)
				if err != nil {
					log.Error("获取人脸照片URL失败: %v", err)
					facePhotoUrl = originalFacePhotoUrl
				}
			}
		}
	} else {
		// 本地模式：拼接服务器地址
		imageServer := global.App.Config.App.FileServer
		if originalFrontUrl != "" {
			idCardFrontUrl = fmt.Sprintf("%s/%s", imageServer, originalFrontUrl)
		}
		if originalBackUrl != "" {
			idCardBackUrl = fmt.Sprintf("%s/%s", imageServer, originalBackUrl)
		}
	}

	registerTime := convert.GetIntFromMap(userInfo, "createtime", 0)
	registerTimeDate := carbon.CreateFromTimestamp(int64(registerTime)).Format("Y-m-d H:i:s")

	// 组装响应数据
	result := map[string]interface{}{
		"name":                 name,                                                    // 姓名
		"repeat_buy_count":     convert.GetIntFromMap(userInfo, "repeatBuyNum", 0),      // 复购次数
		"age":                  age,                                                     // 年龄
		"in_progress_orders":   inProgressOrders,                                        // 在途订单数
		"completed_orders":     completedOrders,                                         // 完结订单数
		"location":             location,                                                // 所在位置
		"mobile":               mobile,                                                  // 手机号
		"risk_score":           riskScore,                                               // 风控评分
		"gender":               gender,                                                  // 性别
		"total_orders":         totalOrders,                                             // 全部订单数
		"first_login_ip":       firstLoginIP,                                            // 首次登录IP（当前表结构中没有此字段）
		"first_login_location": firstLoginLocation,                                      // 首次登录地理位置
		"id_card_front_url":    idCardFrontUrl,                                          // 身份证正面照片
		"id_card_back_url":     idCardBackUrl,                                           // 身份证反面照片
		"face_photo_url":       facePhotoUrl,                                            // 人脸照片
		"id_card_no":           idCardNo,                                                // 身份证号
		"order_time":           convert.GetStringFromMap(orderInfo, "created_at"),       // 下单时间
		"order_status":         convert.GetIntFromMap(orderInfo, "status", 0),           // 订单状态
		"last_login_ip":        lastLoginIP,                                             // 最后登录IP
		"last_login_location":  lastLoginLocation,                                       // 最后登录地理位置
		"payment_channel":      paymentChannelName,                                      // 订单支付渠道
		"order_no":             convert.GetStringFromMap(orderInfo, "order_no"),         // 订单编号
		"loan_amount":          convert.GetFloatFromMap(orderInfo, "loan_amount", 0.0),  // 申请金额
		"user_status":          convert.GetIntFromMap(userInfo, "status", 0),            // 用户状态
		"register_time":        registerTimeDate,                                        // 注册时间
		"all_quota":            convert.GetFloatFromMap(userInfo, "allQuota", 0.0),      // 总额度
		"reminder_quota":       convert.GetFloatFromMap(userInfo, "reminderQuota", 0.0), // 剩余额度
		"emergency_contact_0":  emergencyContact0,                                       // 紧急联系人0
		"emergency_contact_1":  emergencyContact1,                                       // 紧急联系人1
		"remarks":              remarksList,                                             // 订单备注列表
		"review_status":        convert.GetIntFromMap(orderInfo, "review_status", 0),    // 复审状态：0-未复审，1-复审通过，2-复审拒绝
		"review_remark":        convert.GetStringFromMap(orderInfo, "review_remark"),    // 复审备注
	}

	return result
}

// RefreshDisbursementStatus 刷新放款状态
func (s *Service) RefreshDisbursementStatus(orderNo string) (err error) {
	// 1. 查询订单信息
	var (
		order          *model.BusinessLoanOrders
		paymentService sumpay.SumpayServiceInterface
		response       map[string]interface{}
		g              *errgroup.Group
		transaction    []model.BusinessPaymentTransactions
		ctx1           context.Context
	)

	ctx1, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	g, _ = errgroup.WithContext(ctx1)

	// 查询订单
	g.Go(func() error {
		order, err = s.orderModel.GetOrderByOrderNo(orderNo)
		if err != nil {
			return fmt.Errorf("查询订单失败: %v", err)
		}
		return nil
	})

	// 查询提交的支付流水
	g.Go(func() error {
		transaction, err = s.transactionModel.GetTransactionsByConditions(model.TransactionCondition{
			OrderNo: orderNo,
		})
		if err != nil {
			return fmt.Errorf("查询支付流水失败: %v", err)
		}
		return nil
	})

	if err := g.Wait(); err != nil {
		return err
	}

	if order == nil {
		return fmt.Errorf("订单不存在")
	}

	if order.Status != model.OrderStatusPendingDisbursement {
		return fmt.Errorf("订单状态无需刷新")
	}

	// 是否存在已提交的支付流水
	hasSubmittedTransaction := false
	for _, t := range transaction {
		if t.Status == model.TransactionStatusSubmitted && t.ChannelTransactionNo != "" {
			hasSubmittedTransaction = true
			break
		}
	}

	if !hasSubmittedTransaction {
		return fmt.Errorf("订单未提交放款或放款流水号为空")
	}

	paymentService, err = s.getPaymentService(s.ctx)
	if err != nil {
		return fmt.Errorf("获取支付服务失败: %v", err)
	}

	response, err = paymentService.QueryDisbursement(sumpay.DisbursementQueryParams{
		OrderNo: order.OrderNo,
		Rows:    100,
		Page:    1,
	})

	if err != nil {
		return fmt.Errorf("查询放款结果失败: %v", err)
	}

	var (
		successResponse                      sumpay.DisbursementQuerySuccessResponse
		failedResponse                       sumpay.DisbursementQueryFailedResponse
		sumpayOrderSearchAgentPayResponse    sumpay.SumpayOrderSearchAgentPayResponse
		sumpayOrderSearchAgentPayResponseStr string
		callbackResultJson                   []byte
		callbackResult                       string
	)

	if response["resp_code"] == sumpay.RespCodeSuccess {

		callbackResultJson, err = json.Marshal(response)
		if err != nil {
			return fmt.Errorf("转换放款结果失败: %v", err)
		}
		callbackResult = string(callbackResultJson)

		sumpayOrderSearchAgentPayResponseStr = response["sumpay_order_search_agent_pay_response"].(string)
		err = convert.JsonToStruct(sumpayOrderSearchAgentPayResponseStr, &sumpayOrderSearchAgentPayResponse)
		if err != nil {
			return fmt.Errorf("解析放款结果失败: %v", err)
		}

		delete(response, "sumpay_order_search_agent_pay_response")
		err = convert.MapToStruct(response, &successResponse)
		if err != nil {
			return fmt.Errorf("解析放款结果失败: %v", err)

		}
		successResponse.SumpayOrderSearchAgentPayResponse = sumpayOrderSearchAgentPayResponse

		// 事务处理
		err = model.DB(model.WithContext(s.ctx)).Transaction(func(tx gform.IOrm) error {
			// 更新订单状态 放款中
			order.Status = model.OrderStatusDisbursed
			err = s.orderModel.UpdateOrderStatus(tx, int(order.ID), int(order.Status))
			if err != nil {
				return fmt.Errorf("更新订单状态失败: %v", err)
			}

			// 更新流水记录状态
			err = s.transactionModel.UpdateTransactionStatus(
				tx,
				model.UpdateTransactionStatusResultWhere{
					ChannelTransactionNo: sumpayOrderSearchAgentPayResponse.AgentPays[0].TradeNo,
				},
				map[string]interface{}{
					"status":          model.TransactionStatusSuccess,
					"callback_result": callbackResult,
				},
			)

			if err != nil {
				return fmt.Errorf("更新流水记录状态失败: %v", err)
			}

			return nil
		})

	} else {
		err = convert.MapToStruct(response, &failedResponse)
		if err != nil {
			return fmt.Errorf("解析放款结果失败: %v", err)
		}

		err = fmt.Errorf("刷新放款状态失败: %v", failedResponse.RespMsg)

	}

	return
}

// DownloadContract 下载合同
func (s *Service) DownloadContract(orderNo string) (resp contract.DownloadContractResp, err error) {
	// 查询订单
	order, err := s.orderModel.GetOrderByOrderNo(orderNo)
	if err != nil {
		return resp, fmt.Errorf("查询订单失败: %v", err)
	}

	if order == nil {
		return resp, fmt.Errorf("订单不存在")
	}

	// 查询合同
	contractInfo, err := s.contractModel.GetContractByID(int(order.ContractID))
	if err != nil {
		return resp, fmt.Errorf("查询合同失败: %v", err)
	}

	if contractInfo == nil {
		return resp, fmt.Errorf("合同不存在")
	}

	// 下载合同
	contractService := contract.NewContractStatusService(s.ctx)
	return contractService.DownloadContract(&contract.DownloadContractRequest{
		ContractNo: contractInfo.ContractID,
	})

}
