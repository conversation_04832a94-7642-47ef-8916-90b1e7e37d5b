<template>
  <div class="container">
    <div class="info-head">
      <el-space>
        <el-button type="primary" link @click="goBack">
          <template #icon>
            <icon-arrow-left />
          </template>
          {{ t('ordermanagement.detail.backToList') }}
        </el-button>
        <el-divider direction="vertical" />
        {{ t('ordermanagement.detail.title') }}
      </el-space>
    </div>
    <div class="info-box">

      <el-descriptions title="个人资料" class="info-box-desc">
        <template #title>
          <div class="title">个人资料</div>
        </template>
        <el-descriptions-item label="姓名：">{{ safeGetNestedValue(customerInfo, 'name') || orderDetail?.user_name }}</el-descriptions-item>
        <el-descriptions-item label="性别：">{{ safeGetNestedValue(customerInfo, 'gender') || getGenderFromIdCard(orderDetail?.user_id_card) }}</el-descriptions-item>
        <el-descriptions-item label="年龄">{{ safeGetNestedValue(customerInfo, 'age') || '-' }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ safeGetNestedValue(customerInfo, 'mobile') || orderDetail?.user_mobile }}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ safeGetNestedValue(customerInfo, 'id_card_no') || orderDetail?.user_id_card }}</el-descriptions-item>
        <el-descriptions-item label="所在位置">{{ safeGetNestedValue(customerInfo, 'location') || '-' }}</el-descriptions-item>
        <el-descriptions-item label="人脸识别照片" v-if="safeGetNestedValue(customerInfo, 'face_photo_url')">
          <el-image style="width: 150px; height: 100px" :src="safeGetNestedValue(customerInfo, 'face_photo_url')" :preview-src-list="[safeGetNestedValue(customerInfo, 'face_photo_url')]" fit="cover">
            <template #error>
              <div class="image-slot">
                <el-icon><icon-picture /></el-icon>
              </div>
            </template>
          </el-image>
        </el-descriptions-item>
        <el-descriptions-item label="身份证照片" v-if="safeGetNestedValue(customerInfo, 'id_card_front_url') || safeGetNestedValue(customerInfo, 'id_card_back_url')">
          <el-image style="width: 150px; height: 100px" :src="getFullImageUrl(safeGetNestedValue(customerInfo, 'id_card_front_url'))" :preview-src-list="[getFullImageUrl(safeGetNestedValue(customerInfo, 'id_card_front_url'))]" fit="cover">
            <template #error>
              <div class="image-slot">
                <el-icon><icon-picture /></el-icon>
              </div>
            </template>
          </el-image>
          <el-image style="width: 150px; height: 100px;margin-left: 10px" :src="getFullImageUrl(safeGetNestedValue(customerInfo, 'id_card_back_url'))" :preview-src-list="[getFullImageUrl(safeGetNestedValue(customerInfo, 'id_card_back_url'))]" fit="cover">
            <template #error>
              <div class="image-slot">
                <el-icon><icon-picture /></el-icon>
              </div>
            </template>
          </el-image>
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="订单信息" class="info-box-desc">
        <template #title>
          <div class="title">订单信息</div>
        </template>
        <el-descriptions-item label="订单状态">
          {{ safeGetNestedValue(customerInfo, 'order_status') !== undefined ? getCustomerOrderStatusText(safeGetNestedValue(customerInfo, 'order_status')) : getOrderStatusText(orderDetail?.status || 0) }}
        </el-descriptions-item>
        <el-descriptions-item label="下单时间">{{ safeGetNestedValue(customerInfo, 'order_time') || orderDetail?.created_at }}</el-descriptions-item>
        <el-descriptions-item label="申请金额">{{ formatCurrency(safeGetNestedValue(customerInfo, 'loan_amount') || orderDetail?.loan_amount) }}</el-descriptions-item>
        <el-descriptions-item label="订单归属">{{ safeGetNestedValue(customerInfo, 'payment_channel') || orderDetail?.payment_channel_name }}</el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="用户统计" class="info-box-desc">
        <template #title>
          <div class="title">用户统计</div>
        </template>
        <el-descriptions-item label="用户状态">{{ safeGetNestedValue(customerInfo, 'user_status') !== undefined ? getUserStatusText(safeGetNestedValue(customerInfo, 'user_status')) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="注册时间">{{ safeGetNestedValue(customerInfo, 'register_time') || '-' }}</el-descriptions-item>
        <el-descriptions-item label="复购次数">{{ safeGetNestedValue(customerInfo, 'repeat_buy_count') || '-' }}</el-descriptions-item>
        <el-descriptions-item label="在途订单数">{{ safeGetNestedValue(customerInfo, 'in_progress_orders') || '-' }}</el-descriptions-item>
        <el-descriptions-item label="完结订单数">{{ safeGetNestedValue(customerInfo, 'completed_orders') || '-' }}</el-descriptions-item>
        <el-descriptions-item label="全部订单数">
          <el-space>
            <el-tag type="primary">{{ safeGetNestedValue(customerInfo, 'total_orders') || '-' }}</el-tag>
            <el-button type="primary" size="small" @click="goToOrderList">查看</el-button>
          </el-space>
        </el-descriptions-item>
      </el-descriptions>

      <el-descriptions title="额度信息" class="info-box-desc">
        <template #title>
          <div class="title">额度信息</div>
        </template>
        <el-descriptions-item label="风控评分">{{ safeGetNestedValue(customerInfo, 'risk_score') || orderDetail?.risk_score || '-' }}</el-descriptions-item>
        <el-descriptions-item label="历史额度">{{ safeGetNestedValue(customerInfo, 'past_quota') ? formatCurrency(safeGetNestedValue(customerInfo, 'past_quota')) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="总额度">{{ safeGetNestedValue(customerInfo, 'all_quota') ? formatCurrency(safeGetNestedValue(customerInfo, 'all_quota')) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="剩余额度">{{ safeGetNestedValue(customerInfo, 'reminder_quota') ? formatCurrency(safeGetNestedValue(customerInfo, 'reminder_quota')) : '-' }}</el-descriptions-item>
        <el-descriptions-item label="最后登录IP地址">{{ safeGetNestedValue(customerInfo, 'last_login_location') || '-' }}</el-descriptions-item>
      </el-descriptions>

      <div class="info-box-btn">
        <el-space>
          <el-button @click="orderDetail.status === 0 ? handleProcessDisbursement() : undefined" type="primary">放款</el-button>
          <el-button type="primary" @click="handleRefreshDisbursement">放款刷新</el-button>
          <!-- 移除:disabled属性，保留点击条件判断 -->
          <el-button type="primary" @click="handleEarlySettlementButtonClick">提前结清</el-button>
          <!-- 移除:disabled属性，保留点击条件判断 -->
          <el-button type="primary" @click="handleCloseOrderButtonClick">关闭订单</el-button>

          <el-button type="primary" @click="openRemarkDialog">备注</el-button>

          <el-button type="primary" @click="handleCancelClaimButtonClick">取消认领</el-button>
        </el-space>
      </div>
    </div>


    <div class="info-tabs">
      <el-tabs v-model="activeKey" class="demo-tabs">
        <el-tab-pane label="订单信息" name="1">

          <!-- 订单信息内容 -->
          <el-descriptions border>
            <template #title>
              <div class="title">订单信息</div>
            </template>
            <el-descriptions-item label="订单ID">{{ orderDetail?.id || '-' }}</el-descriptions-item>
            <el-descriptions-item label="订单编号">{{ orderDetail?.order_no || '-' }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ orderDetail?.created_at || '-' }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ orderDetail?.updated_at || '-' }}</el-descriptions-item>
            <el-descriptions-item label="借款金额">{{ formatCurrency(orderDetail?.loan_amount) }}</el-descriptions-item>
            <el-descriptions-item label="借款期限">{{ orderDetail?.loan_period ? `${orderDetail.loan_period * orderDetail.total_periods}天` : '-' }}</el-descriptions-item>
          </el-descriptions>

          <el-descriptions title="客户信息" border>
            <template #title>
              <div class="title" style="margin-top: 20px;">客户信息</div>
            </template>
            <el-descriptions-item label="紧急联系人姓名：" :span="1">{{
                safeGetNestedValue(customerInfo, 'emergency_contact_0.name') || '-'
              }}</el-descriptions-item>
            <el-descriptions-item label="紧急联系人电话：" :span="1">{{
                safeGetNestedValue(customerInfo, 'emergency_contact_0.phone') || '-'
              }}</el-descriptions-item>
            <el-descriptions-item label="紧急联系人关系：" :span="1">{{
                safeGetNestedValue(customerInfo, 'emergency_contact_0.relation') || '-'
              }}</el-descriptions-item>
            <el-descriptions-item label="紧急联系人姓名：" :span="1">{{
                safeGetNestedValue(customerInfo, 'emergency_contact_1.name') || '-'
              }}</el-descriptions-item>
            <el-descriptions-item label="紧急联系人电话：" :span="1">{{
                safeGetNestedValue(customerInfo, 'emergency_contact_1.phone') || '-'
              }}</el-descriptions-item>
            <el-descriptions-item label="紧急联系人关系：" :span="1">{{
                safeGetNestedValue(customerInfo, 'emergency_contact_1.relation') || '-'
              }}</el-descriptions-item>
            <el-descriptions-item label="学历：">{{
                detailData.degree
              }}</el-descriptions-item>
            <el-descriptions-item label="婚姻：">{{
                detailData.marry
              }}</el-descriptions-item>
            <el-descriptions-item label="职业：">{{
                detailData.occupation
              }}</el-descriptions-item>
            <el-descriptions-item label="年收入：">{{
                detailData.yearRevenue
              }}</el-descriptions-item>
          </el-descriptions>


          <div class="card-list">
            <div class="title">客户银行卡</div>

            <div class="card-table">
              <el-table :data="cardList" style="width: 100%">
                <el-table-column type="index" label="No" width="50" />
                <el-table-column prop="name" label="持卡人姓名" width="150" />
                <el-table-column prop="bank_name" label="所属银行"></el-table-column>
                <el-table-column prop="bank_card_no" label="银行卡号"></el-table-column>
                <el-table-column prop="card_type_name" label="银行卡类型"></el-table-column>
                <el-table-column prop="bank_phone" label="绑定手机号"></el-table-column>
                <el-table-column prop="created_at" label="绑定时间"></el-table-column>
              </el-table>
            </div>
          </div>


          <!-- 紧急联系人信息 -->
<!--          <div class="info-section contact-section">
            <div class="title">紧急联系人</div>
            <el-row :gutter="16">
              <el-col :span="24">
                <div class="content-wrapper">
                  <el-row :gutter="[16, 16]" v-if="safeGetNestedValue(customerInfo, 'emergency_contact_0') || safeGetNestedValue(customerInfo, 'emergency_contact_1')">
                    &lt;!&ndash; 联系人1 &ndash;&gt;
                    <el-col :span="24" v-if="safeGetNestedValue(customerInfo, 'emergency_contact_0')">
                      <div class="contact-row">
                        <div class="contact-header">联系人1</div>
                        <div class="contact-info">
                          <div class="contact-item">
                            <span class="contact-label">姓名：</span>
                            <span class="contact-value">{{ safeGetNestedValue(customerInfo, 'emergency_contact_0.name') || '-' }}</span>
                          </div>
                          <div class="contact-item">
                            <span class="contact-label">关系：</span>
                            <span class="contact-value">{{ safeGetNestedValue(customerInfo, 'emergency_contact_0.relation') || '-' }}</span>
                          </div>
                          <div class="contact-item">
                            <span class="contact-label">电话：</span>
                            <span class="contact-value">{{ safeGetNestedValue(customerInfo, 'emergency_contact_0.phone') || '-' }}</span>
                          </div>
                        </div>
                      </div>
                    </el-col>
                    &lt;!&ndash; 联系人2 &ndash;&gt;
                    <el-col :span="24" v-if="safeGetNestedValue(customerInfo, 'emergency_contact_1')">
                      <div class="contact-row">
                        <div class="contact-header">联系人2</div>
                        <div class="contact-info">
                          <div class="contact-item">
                            <span class="contact-label">姓名：</span>
                            <span class="contact-value">{{ safeGetNestedValue(customerInfo, 'emergency_contact_1.name') || '-' }}</span>
                          </div>
                          <div class="contact-item">
                            <span class="contact-label">关系：</span>
                            <span class="contact-value">{{ safeGetNestedValue(customerInfo, 'emergency_contact_1.relation') || '-' }}</span>
                          </div>
                          <div class="contact-item">
                            <span class="contact-label">电话：</span>
                            <span class="contact-value">{{ safeGetNestedValue(customerInfo, 'emergency_contact_1.phone') || '-' }}</span>
                          </div>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                  <el-empty v-else description="暂无紧急联系人信息" />
                </div>
              </el-col>
            </el-row>
          </div>-->


          <!-- 复审信息 -->
          <div class="info-section">
            <div class="title">复审信息</div>
            <div class="content-wrapper">
              <div v-if="safeGetNestedValue(customerInfo, 'review_status') !== undefined">
                <el-descriptions border>
                  <el-descriptions-item label="复审状态">
                    <el-tag :color="getReviewStatusColor(safeGetNestedValue(customerInfo, 'review_status'))">
                      {{ getReviewStatusText(safeGetNestedValue(customerInfo, 'review_status')) }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="复审备注">
                    {{ safeGetNestedValue(customerInfo, 'review_remark') || '无' }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
              <el-empty v-else description="暂无复审信息" />
            </div>
          </div>

          <!-- 备注列表 -->
          <div class="info-section">
            <div class="title">备注列表</div>
            <div class="content-wrapper table-box">
              <el-table :data="getRemarksData()" empty-text="暂无备注信息" style="width: 100%">
                <el-table-column type="index" label="No" width="50" />
                <el-table-column prop="user_name" label="备注人" width="150" />
                <el-table-column prop="create_time" label="备注时间"></el-table-column>
                <el-table-column prop="content" label="备注内容"></el-table-column>
              </el-table>
            </div>
          </div>


        </el-tab-pane>
        <el-tab-pane label="风控报告" name="2">
          <template v-if="fkTabList && fkTabList.length > 0">
            <RiskControlReport :fkTabList="fkTabList" />
          </template>
          <el-empty v-else description="暂无风控报告" />
        </el-tab-pane>
        <el-tab-pane label="账单信息" name="3">
          <bill-info ref="billInfoRef" :orderDetail="orderDetail" @update="loadDetailPage"></bill-info>
        </el-tab-pane>
        <el-tab-pane label="催收记录" name="4">
          <div class="title">商家催收</div>
          <div class="cuishou-table">
            <el-table :data="[]" style="width: 100%" empty-text="暂无账单信息">
              <el-table-column type="index" label="No" width="50" />
              <el-table-column prop="total_due_amount" label="记录人" />
              <el-table-column prop="paid_amount" label="记录时间"  />
              <el-table-column prop="status" label="结果"  />
              <el-table-column prop="user_name" label="小记"  />
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="流程进度" name="5">
          <div class="title">订单进度</div>
          <div class="steps-box">
            <a-steps type="dot" direction="vertical">
              <a-step status="process" v-if="progress.order_disbursed_info">
                借款打款-编号：{{ progress.order_disbursed_info.disbursement_id }}
                <template #description>
                  <div class="step-desc">
                    <div class="step-desc-name">{{ progress.order_disbursed_info.operator_name }}</div>
                    <div class="step-desc-time">{{ progress.order_disbursed_info.disbursed_at }}</div>
                  </div>
                </template>
              </a-step>
              <a-step status="process" v-if="progress.order_closed_info">
                商家关单（{{ progress.order_closed_info.closure_remarks }}）
                <template #description>
                  <div class="step-desc">
                    <div class="step-desc-name">{{ progress.order_closed_info.audit_assignee_name }}</div>
                    <div class="step-desc-time">{{ progress.order_closed_info.closed_at }}</div>
                  </div>
                </template>
              </a-step>
              <a-step status="process">
                风控通过等待放款
                <template #description>
                  <div class="step-desc">
                    <div class="step-desc-name">风控</div>
                    <div class="step-desc-time">{{ progress.risk_passed_at?progress.risk_passed_at:'-' }}</div>
                  </div>
                </template>
              </a-step>
              <a-step status="process">
                买家下单-等待风控
                <template #description>
                  <div class="step-desc">
                    <div class="step-desc-name">{{ progress.customer_name }}</div>
                    <div class="step-desc-time">{{ progress.order_created_at }}</div>
                  </div>
                </template>
              </a-step>
            </a-steps>
          </div>

        </el-tab-pane>
      </el-tabs>
    </div>





    <!--  关闭订单对话框  -->
    <el-dialog
      v-model="closeOrderDialogVisible"
      :title="t('ordermanagement.detail.closeOrder')"
      width="500"
    >
      <div class="close-order-form">
        <el-form :model="closeOrderForm" layout="vertical">
          <el-form-item field="reasonType" :label="t('ordermanagement.detail.closeReasonType')" required>
            <el-select v-model="closeOrderForm.reasonType" :placeholder="t('ordermanagement.detail.selectCloseReasonType')">
              <el-option label="终审拒绝" :value="0"></el-option>
              <el-option label="法院涉案" :value="1"></el-option>
              <el-option label="纯白户" :value="2"></el-option>
              <el-option label="客户失联" :value="3"></el-option>
              <el-option label="不提供资料" :value="4"></el-option>
              <el-option label="多余订单" :value="5"></el-option>
              <el-option label="重新下单" :value="6"></el-option>
              <el-option label="客户不同意方案" :value="7"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item field="reason" :label="t('ordermanagement.detail.closeReason')">
            <el-input
              type="textarea"
              v-model="closeOrderForm.reason"
              :placeholder="t('ordermanagement.detail.closeReasonPlaceholder')"
              allow-clear
              :auto-size="{minRows: 4, maxRows: 6}"
              show-word-limit
              :max-length="1000"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeOrderDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleCloseOrder">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>


    <el-dialog v-model="remarkDialogVisible" :title="t('ordermanagement.detail.addRemark')" width="500">
      <div class="remark-textarea-container">
        <el-input
          type="textarea"
          v-model="remarkContent"
          :placeholder="t('ordermanagement.detail.remarkPlaceholder')"
          allow-clear
          :auto-size="{minRows: 5, maxRows: 8}"
          show-word-limit
          :max-length="1000"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="remarkDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitRemark">
            提交
          </el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script lang="ts" setup name="Orderlist">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import {
  IconArrowLeft,
} from '@arco-design/web-vue/es/icon';
import {OrderListItem, OrderCustomerInfo, getOrderProgress} from '@/api/ordermanagement';
import { getOrderList, getOrderCustomerInfo, createOrderRemark, closeOrder, earlySettlement } from '@/api/ordermanagement';
import { defHttp } from '@/utils/http';
import { isUrl } from '@/utils/is';
import { useI18n } from 'vue-i18n';
import { useUserStore } from '@/store';
import { Picture as IconPicture } from '@element-plus/icons-vue';
import billInfo from './components/billInfo.vue';

const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const userStore = useUserStore();
const loading = ref(true);
const orderDetail = ref<OrderListItem | null>(null);
const customerInfo = ref<OrderCustomerInfo | null>(null);
import {ElMessageBox} from "element-plus";
import RiskControlReport from '@/components/riskControlReport/index.vue';


const activeKey = ref('1');
// 用户详情信息
const detailData = ref({});
// 银行卡列表
const cardList = ref<any>([]);

// 获取当前用户ID
const getCurrentUserId = (): number => {
  // 从localStorage获取userInfo中的uid
  const userInfoStr = localStorage.getItem('userInfo');
  if (!userInfoStr) return 0;
  
  try {
    const userInfo = JSON.parse(userInfoStr);
    const userId = Number(userInfo.uid) || 0;
    return userId;
  } catch (e) {
    console.error('解析userInfo失败:', e);
    return 0;
  }
};

// 检查当前用户是否有权限操作订单
const hasOrderPermission = (): boolean => {
  console.log('------权限检查开始------');
  
  if (!orderDetail.value) {
    console.log('订单详情不存在');
    return false;
  }
  
  // 获取当前用户ID
  const userInfoStr = localStorage.getItem('userInfo');
  if (!userInfoStr) {
    console.log('userInfo不存在');
    return false;
  }
  
  try {
    const userInfo = JSON.parse(userInfoStr);
    console.log('userInfo:', userInfo);
    
    // 获取当前用户ID
    const currentUserId = Number(userInfo.uid || 0);
    console.log('当前用户ID:', currentUserId, '类型:', typeof currentUserId);
    
    // 获取订单分配的业务员ID
    const salesAssigneeId = Number(orderDetail.value.sales_assignee_id || 0);
    console.log('订单业务员ID:', salesAssigneeId, '类型:', typeof salesAssigneeId);
    
    // 判断用户是否有权限操作 - 必须是订单的分配业务员
    const hasPermission = currentUserId > 0 && currentUserId === salesAssigneeId;
    console.log('权限判断结果:', hasPermission);
    
    console.log('------权限检查结束------');
    return hasPermission;
  } catch (e) {
    console.error('解析userInfo失败:', e);
    return false;
  }
};

// 检查权限并提示
const checkPermissionAndTip = (): boolean => {
  if (!hasOrderPermission()) {
    Message.warning('没有操作当前订单的权限，请在订单分配后操作');
    return false;
  }
  return true;
};

// 备注相关
const remarkDialogVisible = ref(false);
const cancelConfirmVisible = ref(false);
const remarkContent = ref('');

// 关闭订单相关
const closeOrderDialogVisible = ref(false);
const closeOrderForm = ref({
  reasonType: undefined,
  reason: '',
});


// 放款相关
const disbursementDialogVisible = ref(false);
import type {
  FkDataList
} from '@/api/usermanage';
import {
  getReports, getBankCardList, getCustomerDetail
} from '@/api/usermanage';
// 风控相关
const fkTabList = ref<FkDataList>([]);

const progress = ref({});
onMounted(async () => {
  await fetchOrderDetail();
  fkTabList.value = await getReports({
    customer_id: String(route.query.uid),
    start_date: '',
    end_date: '',
  });
  getOrderProgress(String(route.query.orderNo)).then(res => {
    progress.value = res
  })
});


// 处理取消认领
const handleCancelClaim = async () => {
  try {
    // 先检查业务员权限
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      return;
    }
    
    if (!orderDetail.value?.id) {
      throw new Error('订单ID不存在');
    }
    
    // 获取当前用户ID作为操作人ID
    const operatorId = getCurrentUserId();
    
    // 构造请求参数
    const requestData = {
      order_id: orderDetail.value.id,
      operatorId: operatorId || 0 // 如果获取不到ID则使用0
    };
    
    // 调用取消认领API
    const response = await defHttp.post<any>({
      url: '/order/manager/cancelClaimOrder',
      data: requestData
    }, {
      isTransformResponse: false  // 不转换响应，获取原始数据
    });
    
    if (response && response.code === 0) {
      Message.success('取消认领成功');
      
      // 刷新订单详情
      await fetchOrderDetail();
    } else {
      throw new Error(response?.message || '取消认领失败');
    }
  } catch (error) {
    console.error('取消认领失败:', error);
    Message.error(error instanceof Error ? error.message : '取消认领失败');
  }
};
const billInfoRef = ref();
// 处理放款刷新
const handleRefreshDisbursement = async () => {
  try {
    if (!orderDetail.value?.order_no) {
      Message.warning('订单编号不存在');
      return;
    }

    loading.value = true;
    // 调用getDisbursementStatus API
    const response = await defHttp.get<any>(
      {
        url: '/order/manager/getDisbursementStatus',
        params: { order_no: orderDetail.value.order_no }
      },
      {
        isTransformResponse: false // 不转换响应，获取原始数据
      }
    );

    console.log('放款刷新响应:', response);

    if (response && response.code === 0) {
      Message.success('放款状态刷新成功');
      // 刷新订单详情，更新页面状态
      await fetchOrderDetail();
      // 刷新账单
      billInfoRef.value.getReastData();
    } else {
      // 显示接口返回的错误信息
      Message.error(response?.data || '放款状态刷新失败');
    }
  } catch (error) {
    console.error('放款状态刷新失败:', error);
    Message.error(error instanceof Error ? error.message : '放款状态刷新失败');
  } finally {
    loading.value = false;
  }
};

const fetchOrderDetail = async () => {
  try {
    loading.value = true;
    const orderId = Number(route.params.id);
    const orderNo = route.query.orderNo as string;

    if (!orderId || isNaN(orderId)) {
      throw new Error('无效的订单ID');
    }

    // 获取订单基础信息
    const response = await getOrderList({
      order_no: orderNo,
      page: 1,
      page_size: 1
    });

    if (response && response.data && response.data.length > 0) {
      orderDetail.value = response.data[0];

      // 获取下单人详细信息
      try {
        const customerResponse = await getOrderCustomerInfo(orderId);

        detailData.value = await getCustomerDetail(route.query.uid as string);

        const cardRes = await getBankCardList({ customer_id: route.query.uid as string });
        if(cardRes.list && cardRes.list.length > 0) {
          cardRes.list.forEach((item: any) => {
            item.name = detailData.value.name
          })
        }
        cardList.value = cardRes.list;


        // 处理API响应，适应不同的数据结构
        if (customerResponse) {
          // 使用类型断言处理类型问题
          customerInfo.value = customerResponse as unknown as OrderCustomerInfo;
        }
      } catch (customerError) {
        console.error('获取下单人信息失败:', customerError);
        Message.warning('获取下单人详细信息失败，显示基础信息');
      }
    } else {
      throw new Error('找不到订单详情');
    }
  } catch (error) {
    console.error('获取订单详情失败:', error);
    Message.error('获取订单详情失败');
    orderDetail.value = null;
    customerInfo.value = null;
  } finally {
    loading.value = false;
  }
};

const goBack = () => {
  // router.push('/ordermanagement/Orderlist');
  router.go(-1)
};

// 获取订单状态文本
const getOrderStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '待放款',
    1: '放款中',
    2: '交易关闭',
    3: '交易完成',
  };
  return statusMap[status] || '未知状态';
};

// 获取CustomerInfo中订单状态文本
const getCustomerOrderStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '待放款',
    1: '放款中',
    2: '交易关闭',
    3: '交易完成',
  };
  return statusMap[status] || '未知状态';
};

// 获取用户状态文本
const getUserStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '正常',
    1: '白名单',
    2: '黑名单',
    4: '风控拦截',
  };
  return statusMap[status] || '未知状态';
};

// 格式化货币
const formatCurrency = (amount?: number): string => {
  if (amount === undefined || amount === null) return '¥0.00';
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
  }).format(amount);
};

// 从身份证号获取性别
const getGenderFromIdCard = (idCard?: string): string => {
  if (!idCard || idCard.length < 18) return '未知';
  // 身份证第17位，奇数为男，偶数为女
  const genderCode = parseInt(idCard.charAt(16));
  return genderCode % 2 === 1 ? '男' : '女';
};

// 获取图片完整URL
const getFullImageUrl = (url?: string): string => {
  if (!url) return '';

  // 如果已经是完整URL（以http或https开头），则直接返回
  if (isUrl(url)) {
    return url;
  }

  // 判断环境并获取对应的域名前缀
  const domainPrefix = import.meta.env.VITE_APP_ENV === "production"
    ? window?.globalConfig?.Root_url
    : window?.globalConfig?.Root_url_dev;

  // 如果URL已经包含域名前缀，则直接返回
  if (url.startsWith(domainPrefix)) {
    return url;
  }

  // 如果URL以/开头，则直接拼接域名
  if (url.startsWith('/')) {
    return `${domainPrefix}${url}`;
  }

  // 否则添加/再拼接
  return `${domainPrefix}/${url}`;
};

// 获取复审状态文本
const getReviewStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '未复审',
    1: '已通过',
    2: '未通过',
  };
  return statusMap[status] || '未知状态';
};

// 获取复审状态颜色
const getReviewStatusColor = (status: number): string => {
  const colorMap: Record<number, string> = {
    0: 'default',
    1: 'success',
    2: 'error',
  };
  return colorMap[status] || 'default';
};

// 安全获取嵌套值
const safeGetNestedValue = (obj: any, path: string, defaultValue?: any): any => {
  const keys = path.split('.');
  let result = obj;
  for (const key of keys) {
    if (result && typeof result === 'object' && key in result) {
      result = result[key];
    } else {
      return defaultValue;
    }
  }
  return result;
};

// 获取备注数据
const getRemarksData = () => {
  // 尝试多种可能的数据路径
  let remarks: any[] = [];

  // 尝试直接访问customerInfo.remarks
  if (Array.isArray(safeGetNestedValue(customerInfo.value, 'remarks'))) {
    remarks = safeGetNestedValue(customerInfo.value, 'remarks');
  }
  // 尝试访问customerInfo.data.remarks
  else if (Array.isArray(safeGetNestedValue(customerInfo.value, 'data.remarks'))) {
    remarks = safeGetNestedValue(customerInfo.value, 'data.remarks');
  }
  // 从响应对象中寻找 remarks 字段
  else if (customerInfo.value && typeof customerInfo.value === 'object') {
    // 在对象的任何层级查找名为remarks的数组
    const findRemarks = (obj: any, depth = 0): any[] | null => {
      if (!obj || depth > 3 || typeof obj !== 'object') return null;

      for (const key in obj) {
        if (key === 'remarks' && Array.isArray(obj[key])) {
          return obj[key];
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          const found: any[] | null = findRemarks(obj[key], depth + 1);
          if (found) return found;
        }
      }
      return null;
    };

    const foundRemarks = findRemarks(customerInfo.value);
    if (foundRemarks) {
      remarks = foundRemarks;
    }
  }

  return remarks || [];
};

// 打开备注对话框
const openRemarkDialog = () => {
  // 先检查业务员权限
  if (!hasOrderPermission()) {
    Message.warning('没有操作当前订单的权限，请在订单分配后操作');
    return;
  }

  remarkDialogVisible.value = true;
  remarkContent.value = '';
};

// 打开关闭订单对话框
const openCloseOrderDialog = () => {
  // 先检查业务员权限
  if (!hasOrderPermission()) {
    Message.warning('没有操作当前订单的权限，请在订单分配后操作');
    return;
  }

  // 检查订单状态
  if (!orderDetail.value || orderDetail.value.status !== 0) {
    Message.warning('只有待放款状态的订单才能进行关闭操作');
    return;
  }

  closeOrderDialogVisible.value = true;
  closeOrderForm.value = {
    reasonType: undefined,
    reason: '',
  };
};



// 提前结清按钮点击处理 - 入口函数
const handleEarlySettlementButtonClick = () => {
  // 先检查权限，没有权限则直接提示，不进行后续操作
  if (!hasOrderPermission()) {
    Message.warning('没有操作当前订单的权限，请在订单分配后操作');
    return;
  }

  // 检查订单状态
  if (!orderDetail.value) {
    Message.warning('订单信息不存在');
    return;
  }

  // 检查订单状态是否为放款中(1)
  if (orderDetail.value.status !== 1) {
    Message.warning(`只有放款中状态的订单才能进行提前结清操作，当前状态: ${getOrderStatusText(orderDetail.value.status)}`);
    return;
  }

  // 权限和状态验证通过，打开确认对话框
  ElMessageBox.confirm(
    '确定要对订单进行提前结清操作吗？此操作不可撤销。',
    '提前结清确认?',
    {
      confirmButtonText: '确认结清',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    handleEarlySettlementConfirm();
  })
};

// 提前结清确认处理
const handleEarlySettlementConfirm = async () => {
  // 显示加载提示
  const loadingMessage = Message.loading('正在提交提前结清请求...');

  try {
    if (!orderDetail.value?.order_no) {
      Message.error('订单编号不存在');
      loadingMessage.close();
      return;
    }

    // 再次检查权限
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      loadingMessage.close();
      return;
    }

    // 再次检查订单状态
    if (!orderDetail.value || orderDetail.value.status !== 1) {
      Message.warning(`只有放款中状态的订单才能进行提前结清操作，当前状态: ${getOrderStatusText(orderDetail.value?.status || 0)}`);
      loadingMessage.close();
      return;
    }

    // 获取当前用户ID作为操作人ID
    const operatorId = getCurrentUserId();

    // 构造请求参数
    const requestData = {
      orderNo: orderDetail.value.order_no,
      operatorId: operatorId || 0 // 如果获取不到ID则使用0
    };

    // 发送请求
    const response = await defHttp.post<any>({
      url: '/order/manager/earlySettlement',
      data: requestData
    }, {
      isTransformResponse: false  // 不转换响应，获取原始数据
    });

    // 关闭加载提示
    loadingMessage.close();

    // 根据API文档，code=0表示成功
    if (response && response.code === 0) {

      Message.success('提前结清成功');

      // 刷新订单详情
      fetchOrderDetail();
    } else {
      throw new Error(response?.message || '提前结清失败');
    }
  } catch (error) {
    // 关闭加载提示
    loadingMessage.close();

    console.error('提前结清失败:', error);
    Message.error(error instanceof Error ? error.message : '提前结清失败');
  }
};

// 打开放款确认对话框
const handleProcessDisbursement = () => {
  // 先检查业务员权限
  if (!hasOrderPermission()) {
    Message.warning('没有操作当前订单的权限，请在订单分配后操作');
    return;
  }

  // 再检查订单状态
  if (!orderDetail.value || orderDetail.value.status !== 0) {
    Message.warning('只有待放款状态的订单才能进行放款操作');
    return;
  }

  ElMessageBox.confirm(
    '确定要对订单进行放款操作吗？此操作不可撤销。',
    '确认放款?',
    {
      confirmButtonText: '确认放款',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    confirmProcessDisbursement()
  })

  // disbursementDialogVisible.value = true;
};

// 确认处理放款
const confirmProcessDisbursement = async () => {
  try {
    if (!orderDetail.value?.order_no) {
      throw new Error('订单编号不存在');
    }

    // 再次检查权限
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      // done(false);
      return;
    }

    // 获取当前用户ID作为操作人ID
    const operatorId = getCurrentUserId();

    // 构造请求参数，operatorId为可选参数，不传
    const requestData = {
      orderNo: orderDetail.value.order_no
    };

    console.log('放款处理请求参数:', requestData);

    // 调用放款处理API
    const response = await defHttp.post<any>({
      url: '/order/manager/processDisbursement',
      data: requestData
    }, {
      isTransformResponse: false  // 不转换响应，获取原始数据
    });

    console.log('放款处理API响应:', response);

    if (response && response.code === 0) {
      Message.success('放款请求已提交请刷新放款状态');
      // 自动关闭确认放款弹窗
      disbursementDialogVisible.value = false;
      // done(true);

      // 刷新订单详情
      await fetchOrderDetail();
    } else {
      // 直接显示接口返回的错误信
      Message.error(response?.data || '放款处理失败');
      // 关闭放款确认对话框
      disbursementDialogVisible.value = false;
      // done(false);
      return;
    }
  } catch (error) {
    console.error('放款处理失败:', error);
    Message.error(error instanceof Error ? error.message : '放款处理失败');
    // done(false);
  }
};

// 跳转到订单列表并按用户姓名筛选
const goToOrderList = () => {
  const userName = safeGetNestedValue(customerInfo.value, 'name') || orderDetail.value?.user_name;
  
  // 确保参数名与API文档匹配，使用user_name作为筛选参数
  const query: Record<string, string> = {};
  if (userName) {
    query.user_name = userName;
  }
  
  // 使用replace而不是push，以确保用户可以返回到详情页
  router.push({
    path: '/ordermanagement/Orderlist',
    query
  });
};

// 处理关闭订单
const handleCloseOrder = async (done: Function) => {
  console.log('handleCloseOrder被调用');
  try {
    // 表单验证
    if (closeOrderForm.value.reasonType === undefined) {
      Message.warning(t('ordermanagement.detail.selectCloseReasonType'));
      // done(false);
      return;
    }
    
    // 备注是可选的，不需要验证非空
    
    if (!orderDetail.value?.order_no) {
      throw new Error('订单编号不存在');
    }
    
    // 再次检查权限
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      // done(false);
      return;
    }
    
    // 获取当前用户ID作为操作人ID
    const operatorId = getCurrentUserId();
    
    // 按照swagger规范构造请求参数
    const requestData = {
      orderNo: orderDetail.value.order_no,
      reason_for_closure: closeOrderForm.value.reasonType,
      closure_remarks: closeOrderForm.value.reason?.trim() || '', // 可选参数
      operatorId: operatorId || 0 // 如果获取不到ID则使用0
    };
    
    console.log('关闭订单请求参数:', requestData);
    
    // 使用defHttp直接调用API，不需要包含/business前缀，因为defHttp已经配置了基础URL
    // 关键是设置isTransformResponse为false，直接获取原始响应
    const response = await defHttp.post<any>({
      url: '/order/manager/closeOrder',
      data: requestData
    }, {
      isTransformResponse: false
    });
    
    console.log('关闭订单API响应:', response);
    
    if (response && response.code === 0) {
      Message.success(t('ordermanagement.detail.closeOrderSuccess'));
      // done(true);
      // 关闭成功后返回列表页
      setTimeout(() => {
        goBack();
      }, 1000);
    } else {
      throw new Error(response?.message || '关闭订单失败');
    }
  } catch (error) {
    console.error('关闭订单失败:', error);
    Message.error(error instanceof Error ? error.message : t('ordermanagement.detail.closeOrderFailed'));
    // done(false);
  }
};

// 获取关闭原因类型文本
const getCloseReasonTypeText = (type?: number): string => {
  if (type === undefined) return '未知原因';
  
  const reasonMap: Record<number, string> = {
    0: '终审拒绝',
    1: '法院涉案',
    2: '纯白户',
    3: '客户失联',
    4: '不提供资料',
    5: '多余订单',
    6: '重新下单',
    7: '客户不同意方案',
  };
  
  return reasonMap[type] || '未知原因';
};

// 处理取消前确认
const handleBeforeCancelRemark = () => {
  // 如果有内容，则显示确认对话框
  if (remarkContent.value && remarkContent.value.trim()) {
    cancelConfirmVisible.value = true;
    // 阻止主对话框关闭
    return false;
  }
  // 如果没有内容，允许关闭
  return true;
};

// 确认取消备注 (二次确认"确认"按钮)
const confirmCancelEditing = (done: Function) => {
  // 先关闭确认对话框
  cancelConfirmVisible.value = false;
  // 再关闭主对话框
  remarkDialogVisible.value = false;
  // 清空内容
  remarkContent.value = '';
  done(true);
};

// 处理提交备注
const handleSubmitRemark = async (done: Function) => {
  try {
    if (!remarkContent.value || !remarkContent.value.trim()) {
      Message.warning(t('ordermanagement.detail.remarkEmpty'));
      // done(false);
      return;
    }

    const orderId = Number(route.params.id);
    if (!orderId || isNaN(orderId)) {
      throw new Error('无效的订单ID');
    }
    
    // 再次检查权限
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      // done(false);
      return;
    }
    
    // 获取当前用户ID作为操作人ID
    const operatorId = getCurrentUserId();
    
    // 构造请求参数
    const requestData = {
      order_id: orderId,
      content: remarkContent.value,
    };
    
    console.log('添加备注请求参数:', requestData);

    // 调用添加备注API
    const response = await defHttp.post<any>({
      url: '/order/manager/createOrderRemark',
      data: requestData
    }, {
      isTransformResponse: false  // 不转换响应，获取原始数据
    });
    
    console.log('添加备注API响应:', response);
    
    if (response && response.code === 0) {
      Message.success(t('ordermanagement.detail.remarkSuccess'));
      
      // 重新获取订单详情，刷新备注列表
      await fetchOrderDetail();
      
      // done(true);
      remarkDialogVisible.value = false;
    } else {
      throw new Error(response?.message || t('ordermanagement.detail.remarkFailed'));
    }
  } catch (error) {
    console.error('添加备注失败:', error);
    Message.error(error instanceof Error ? error.message : t('ordermanagement.detail.remarkFailed'));
    // done(false);
  }
};

// 处理关闭订单按钮点击
const handleCloseOrderButtonClick = () => {
  // 检查订单状态
  if (!orderDetail.value || orderDetail.value.status !== 0) {
    Message.warning('只有待放款状态的订单才能进行关闭操作');
    return;
  }
  
  // 检查权限
  if (!hasOrderPermission()) {
    Message.warning('没有操作当前订单的权限，请在订单分配后操作');
    return;
  }
  
  // 权限验证通过，打开关闭订单对话框
  openCloseOrderDialog();
};

// 处理取消认领按钮点击
const handleCancelClaimButtonClick = () => {
  // 检查是否有销售分配ID
  if (!orderDetail.value?.sales_assignee_id || orderDetail.value.sales_assignee_id === 0) {
    Message.warning('订单未分配业务员，无法取消认领');
    return;
  }
  
  // 检查权限
  if (!hasOrderPermission()) {
    Message.warning('没有操作当前订单的权限，请在订单分配后操作');
    return;
  }
  
  // 权限验证通过，执行取消认领
  handleCancelClaim();
};

// 刷新页面数据
async function loadDetailPage() {
  console.log('刷新订单&账单');
// 刷新订单详情，更新页面状态
  await fetchOrderDetail();
}
</script>

<style scoped lang="less">
.container {
  margin: 10px;
}

.info-head {
  background: #fff;
  margin-bottom: 10px;
  padding: 10px 0;
}
.info-box {
  padding: 10px;
  background: #fff;
  .info-box-desc{
    margin-top: 10px;
  }
  .info-box-btn {
    padding: 10px 0;
  }
}
.title {
  padding-left: 10px;
  position: relative;
  color: #333;
  font-size: 16px;
  font-weight: bold;
  &:after {
    content: '';
    position: absolute;
    background: #409eff;
    width: 3px;
    height: 100%;
    left: 0;
    top: 0;
    border-radius: 10px;
  }
}

.info-tabs {
  background: #fff;
  margin-top: 15px;
  padding: 0 10px 10px;
}

.info-section {
  margin-bottom: 32px;
  .title{
    margin-bottom: 15px;
  }
}
.contact-section {
  margin-top: 36px;
}
.content-wrapper {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
}


.contact-row {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.contact-header {
  font-weight: bold;
  margin-bottom: 16px;
  font-size: 15px;
  color: #1D2129;
  border-bottom: 1px solid #e9e9e9;
  padding-bottom: 8px;
}

.contact-info {
  display: flex;
  gap: 32px;
}

.contact-item {
  display: flex;
  align-items: center;
}

.contact-label {
  font-weight: 500;
  color: #4E5969;
  margin-right: 4px;
}

.contact-value {
  color: #1D2129;
}
.table-box,.cuishou-table{
  :deep(.el-table th.el-table__cell){
    background: #efefef;
  }
  :deep(.el-table thead) {
    color: #333;
  }
}
.cuishou-table{
  margin-top: 15px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 30px;
}
.steps-box{
  margin-top: 20px;
  padding: 0 10px;
}
.step-desc{
  margin-top: 5px;
  .step-desc-name{
    color: #666;
  }
  .step-desc-time{
    color: #999;
    margin-top: 5px;
  }
}
.card-list {
  padding: 20px 0;


}
.card-table, .amountTable, .cuishou-table {
  margin-top: 20px;
  :deep(.el-table__header-wrapper){
  }
  :deep(.el-table th.el-table__cell){
    background: #efefef;
  }
  :deep(.el-table thead) {
    color: #333;
  }
}
</style> 