<template>
	<view class="bankcard">
		<!-- <view class="bankcard-top">
			<view class="">

			</view>
		</view> -->
		<view class="bankcard-list" v-if="dataObj.list.length > 0">
			<template v-for="(item,index) in dataObj.list" :key="item.id">
				<view class="bankcard-item" v-if="index<1">
					<!-- 芯片图标 -->
					<image src="/static/image/zk.png" mode="widthFix" class="item-xinp"></image>
					<view class="bankcard-item-box">
						<!-- logo -->
						<view class="item-icon">
							<image src="/static/image/yhk-logo.png" mode="widthFix"></image>
						</view>
						<view class="item-box">
							<view class="item-name">
								<text>{{ item.bank_name }}</text>
								<!-- <image class="item-zk" mode="widthFix" src="/static/image/zk.png"></image> -->
							</view>
							<view class="item-no">
								{{ item.bank_card_no_end_txt }}
							</view>
						</view>
					</view>
					<view class="bankcard-item-footer">
						<view class="bankcard-item-footer-left">
							{{ item.card_type_name }}
						</view>
						<view class="bankcard-item-footer-phone">
							{{ item.bank_phone_end }}
						</view>
					</view>
				</view>
			</template>
			<view class="bankcard-hint">
				主卡说明：主卡将作为您还款和优先放款银行卡。在主卡余额不足时，将按照扣款顺序进行扣款，请您及时存入足够的还款金额。
			</view>
		</view>
		<template v-if="dataObj.list.length > 1">
			<view class="bankcard-middle">
				其他银行卡
			</view>
			<view class="other-card-list">
				<template v-for="(item,index) in dataObj.list" :key="item.id">
					<view class="other-card-item" v-if="index>0">
						<view class="other-card-title">
							{{ item.bank_name }} ({{ item.bank_card_no_end }})
						</view>
						<view class="item-hint">
							{{ item.bank_phone_end }}
						</view>
					</view>
				</template>
			</view>
		</template>

		<noData v-if="dataObj.list.length <= 0" name="暂无银行卡,请添加"></noData>

		<view @click="bankcard" class="bankcard-add-btn">
			+添加银行卡
		</view>

		<!-- <image src="/static/image/img_v3_02o0_ff296122-fe3a-4cc3-9e07-6faca0b6589g.jpg" mode="widthFix" style="width: 100%;"></image> -->
	</view>
</template>

<script setup>
	import {
		reactive
	} from 'vue';
	import cardApi from '@/api/card.js';
	import noData from '../components/no-data';
	let dataObj = reactive({
		mainCard: {},
		list: []
	});

	function bankcard() {
		uni.navigateTo({
			url: "/pages/CollectionCard/CollectionCard"
		})
	}
	import {
		onLoad,
		onShow
	} from "@dcloudio/uni-app";

	function getList() {
		cardApi.getCardList().then(res => {
			if(res.data && res.data.list) {
				let list = res.data.list.filter(item => item.card_status == 1);
				list.forEach(item => {
					let bank_card_no = item.bank_card_no;
					let bank_phone = item.bank_phone;
					item.bank_card_no_end = bank_card_no.slice(bank_card_no.length - 4, bank_card_no.length);
					item.bank_card_no_end_txt =
						`(尾号${bank_card_no.slice(bank_card_no.length-4,bank_card_no.length)})`;
					item.bank_phone_end = `手机尾号${bank_phone.slice(bank_phone.length-4,bank_phone.length)}`
				})
				dataObj.list = list;
			}
		})
	}
	onShow(() => {
		getList()
	})
</script>

<style lang="scss" scoped>
	.bankcard{
		background-color: #eff2f7;
		min-height: 100%;
	}
	.bankcard-top {
		width: 100%;
		height: 200px;
	}

	.bankcard-list {
		padding: 30rpx;

		.bankcard-item {
			background: rgba(36, 83, 255, .5);
			background: linear-gradient(135deg, #007aff, #385c81);
			// background: linear-gradient(135deg, #007aff, #385c81);
			border-radius: 30rpx;
			color: #fff;
			box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
			position: relative;
			display: flex;
			flex-direction: column;

			.item-xinp {
				position: absolute;
				top: 32rpx;
				right: 44rpx;
				width: 72rpx;
				height: auto;
			}

			&::before {
				content: '';
				position: absolute;
				top: 40rpx;
				right: 40rpx;
				width: 80rpx;
				height: 60rpx;
				background: rgba(255, 255, 255, 0.2);
				border-radius: 5px;
			}

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 0;
				width: 100%;
				height: 80rpx;
				background: rgba(0, 0, 0, 0.1);
				border-radius: 0 0 30rpx 30rpx;
			}

			.bankcard-item-box {
				display: flex;
				align-items: center;
				border-bottom: 1px solid linear-gradient(135deg, #2c3e50, #3498db);
				flex: 1;
				padding: 20rpx 30rpx;

				.item-icon {
					background: rgba(204, 204, 204, .5);
					width: 60rpx;
					height: 50rpx;
					border-radius: 5rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					image {
						width: 50rpx;
						height: auto;
					}
				}

				.item-box {
					flex: 1;
					padding: 20rpx 0;
					margin-left: 30rpx;
					height: 120rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-around;

					.item-name {
						display: flex;
						align-items: center;

						.item-zk {
							width: 40rpx;
							margin-left: 20rpx;
						}
					}

				}
			}

			.bankcard-item-footer {
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 30rpx;

				.bankcard-item-footer-left {
					font-size: 26rpx;
				}

				.bankcard-item-footer-phone {
					font-size: 24rpx;
				}
			}

		}

		.bankcard-hint {
			font-size: 26rpx;
			padding: 30rpx 20rpx 0;
			color: #777;
			line-height: 45rpx;
		}
	}

	.bankcard-middle {
		border-bottom: 1px solid #dbdbdb;
		padding: 30rpx;
		background-color: #fff;
		margin: 0 20rpx;
		border-radius: 15rpx 15rpx 0 0;
	}
	.other-card-list{
		background-color: #fff;
		overflow: hidden;
		padding: 0 40rpx;
		margin: 0 20rpx;
		border-radius: 0 0 15rpx 15rpx;
		.other-card-item{
			padding: 20rpx 0;
			border-bottom: 1px dashed #dbdbdb;
			&:last-child{
				border-bottom: 0;
			}
			.other-card-title{
				color: #333;
				font-size: 28rpx;
				margin-bottom: 10rpx;
			}
			.item-hint{
				color: #888;
				font-size: 24rpx;
			}
		}
	}

	.bankcard-add-btn {
		position: fixed;
		bottom: 60rpx;
		left: 30rpx;
		right: 30rpx;
		background-color: #007aff;
		border-radius: 50rpx;
		padding: 20rpx;
		text-align: center;
		color: #fff;
	}
</style>