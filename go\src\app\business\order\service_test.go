package order

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"fincore/global"
	"fincore/model"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

// RefreshRiskData 单元测试
//
// 本测试文件为 RefreshRiskData 函数提供了全面的单元测试，包括：
// 1. 正常用户的风控数据刷新测试

// 运行测试命令：
// go test -v ./app/business/order -run TestRefreshRiskData
//
// 注意：测试可能会因为第三方接口配置问题而出现警告，这是正常的测试环境问题，
// 不影响 RefreshRiskData 函数本身的逻辑正确性。

// setupTestEnvironment 设置测试环境，切换到正确的工作目录
func setupTestEnvironment() (string, error) {
	path, err := os.Getwd()
	if err != nil {
		return "", err
	}

	// 切换到src目录，确保能找到resource/config.yml
	srcPath := filepath.Join(path, "../../../")
	err = os.Chdir(srcPath)
	if err != nil {
		return "", err
	}

	return path, nil // 返回原始路径，用于defer恢复
}

func init() {
	// 设置环境变量为dev
	os.Setenv("ENV", "dev")

	// 设置测试环境
	_, err := setupTestEnvironment()
	if err != nil {
		panic(err)
	}
	// 初始化日志系统
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger

	// 初始化数据库连接
	model.MyInit("test")
}

// setupOrderService 设置订单服务
func setupOrderService() *Service {
	return NewOrderService(context.Background())
}

// TestRefreshRiskData 测试风控数据刷新功能
func TestRefreshRiskData(t *testing.T) {
	orderService := setupOrderService()

	// 测试正常用户刷新风控数据
	userID := 30 // 使用存在的测试用户ID
	t.Logf("开始测试: 正常用户刷新风控数据 - 测试正常用户的风控数据刷新流程")

	// 执行风控数据刷新
	err := orderService.RefreshRiskData(userID)

	assert.NoError(t, err)
	if err != nil {
		t.Logf("刷新失败(可能是测试环境问题): %v", err)
	} else {
		t.Logf("刷新成功: 用户ID=%d", userID)
	}
}
