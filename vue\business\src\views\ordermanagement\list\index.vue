<template>
  <div class="container">
    <!-- 搜索条件区域 -->
    <a-card class="general-card" title="搜索条件">
      <a-form
        ref="queryFormRef"
        :model="queryForm"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 18 }"
        label-align="left"
        auto-label-width
        @submit="handleSearch"
      >
        <!-- 第一行：订单编号、用户ID、产品规则ID -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="order_no" label="订单编号">
              <a-input
                v-model="queryForm.order_no"
                placeholder="请输入订单编号"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="user_id" label="用户ID">
              <a-input
                v-model="queryForm.user_id"
                placeholder="请输入用户ID"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="product_rule_id" label="产品规则ID">
              <a-input
                v-model="queryForm.product_rule_id"
                placeholder="请输入产品规则ID"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第二行：贷款金额范围、渠道ID、客户来源 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="loan_amount" label="贷款金额">
              <a-input-group>
                <a-input
                  v-model="queryForm.loan_amount_min"
                  placeholder="最小值"
                  style="width: 45%"
                />
                <a-input
                  style="
                    width: 10%;
                    text-align: center;
                    pointer-events: none;
                    background-color: #f7f8fa;
                  "
                  placeholder="~"
                  readonly
                />
                <a-input
                  v-model="queryForm.loan_amount_max"
                  placeholder="最大值"
                  style="width: 45%"
                />
              </a-input-group>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="channel_id" label="渠道ID">
              <a-input
                v-model="queryForm.channel_id"
                placeholder="请输入渠道ID"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="customer_origin" label="客户来源">
              <a-select
                v-model="queryForm.customer_origin"
                placeholder="请选择客户来源"
                allow-clear
              >
                <a-option value="APP" label="APP" />
                <a-option value="WEB" label="WEB" />
                <a-option value="H5" label="H5" />
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第三行：订单状态、是否冻结、是否投诉 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="status" label="订单状态">
              <a-select
                v-model="queryForm.status"
                placeholder="请选择订单状态"
                allow-clear
              >
                <a-option value="0" label="待放款" />
                <a-option value="1" label="放款中" />
                <a-option value="2" label="交易关闭" />
                <a-option value="3" label="交易完成" />
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="is_freeze" label="是否冻结">
              <a-select
                v-model="queryForm.is_freeze"
                placeholder="请选择是否冻结"
                allow-clear
              >
                <a-option value="1" label="是" />
                <a-option value="0" label="否" />
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="complaint_status" label="是否投诉">
              <a-select
                v-model="queryForm.complaint_status"
                placeholder="请选择是否投诉"
                allow-clear
              >
                <a-option value="1" label="是" />
                <a-option value="0" label="否" />
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第四行：审核状态、审核员ID、业务员ID -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="review_status" label="审核状态">
              <a-select
                v-model="queryForm.review_status"
                placeholder="请选择审核状态"
                allow-clear
              >
                <a-option value="0" label="待审核" />
                <a-option value="1" label="审核通过" />
                <a-option value="2" label="审核拒绝" />
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="audit_assignee_id" label="审核员ID">
              <a-input
                v-model="queryForm.audit_assignee_id"
                placeholder="请输入审核员ID"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="sales_assignee_id" label="业务员ID">
              <a-input
                v-model="queryForm.sales_assignee_id"
                placeholder="请输入业务员ID"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第五行：下单时间范围 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="submittedTimeRange" label="下单时间">
              <a-range-picker
                v-model="submittedTimeRange"
                style="width: 100%"
                @change="handleSubmittedTimeChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="disbursedTimeRange" label="放款时间">
              <a-range-picker
                v-model="disbursedTimeRange"
                style="width: 100%"
                @change="handleDisbursedTimeChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="completedTimeRange" label="结清/关闭时间">
              <a-range-picker
                v-model="completedTimeRange"
                style="width: 100%"
                @change="handleCompletedTimeChange"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 操作按钮 -->
        <a-row>
          <a-col :span="24">
            <a-form-item>
              <a-space>
                <a-button type="primary" html-type="submit">
                  <template #icon>
                    <icon-search />
                  </template>
                  搜索
                </a-button>
                <a-button @click="handleReset">
                  <template #icon>
                    <icon-refresh />
                  </template>
                  重置
                </a-button>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 表格区域 -->
    <a-card class="general-card" title="订单列表">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleRefresh">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <a-table
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :data="tableData"
        :bordered="false"
        size="medium"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <template #columns>
          <a-table-column title="No" :width="60" align="center">
            <template #cell="{ rowIndex }">
              {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
            </template>
          </a-table-column>
          
          <a-table-column title="订单编号" data-index="order_no" :width="140" />
          <a-table-column title="用户名" data-index="user_name" :width="100" />
          <a-table-column title="手机号" data-index="user_mobile" :width="120" />
          <a-table-column title="贷款金额" data-index="loan_amount" :width="120" align="right">
            <template #cell="{ record }">
              {{ formatCurrency(record.loan_amount) }}
            </template>
          </a-table-column>
          <a-table-column title="实际放款金额" data-index="principal" :width="120" align="right">
            <template #cell="{ record }">
              {{ formatCurrency(record.principal) }}
            </template>
          </a-table-column>
          <a-table-column title="总应还金额" data-index="total_repayable_amount" :width="120" align="right">
            <template #cell="{ record }">
              {{ formatCurrency(record.total_repayable_amount) }}
            </template>
          </a-table-column>
          <a-table-column title="已还金额" data-index="amount_paid" :width="120" align="right">
            <template #cell="{ record }">
              {{ formatCurrency(record.amount_paid) }}
            </template>
          </a-table-column>
          <a-table-column title="渠道" data-index="channel" :width="120" />
          <a-table-column title="客户来源" data-index="customer_origin" :width="100" />
          <a-table-column title="订单状态" data-index="status" :width="100">
            <template #cell="{ record }">
              {{ getOrderStatusText(record.status) }}
            </template>
          </a-table-column>
          <a-table-column title="审核状态" data-index="review_status" :width="100">
            <template #cell="{ record }">
              {{ getReviewStatusText(record.review_status) }}
            </template>
          </a-table-column>
          <a-table-column title="审核员" data-index="audit_assignee_name" :width="100" />
          <a-table-column title="业务员" data-index="sales_assignee_name" :width="100" />
          <a-table-column title="创建时间" data-index="created_at" :width="150" />
          <a-table-column title="放款时间" data-index="disbursed_at" :width="150" />
          <a-table-column title="操作" :width="200" fixed="right">
            <template #cell="{ record }">
              <a-space>
                <a-button size="small" @click="handleView(record)">查看</a-button>
                <a-button size="small" @click="handleClose(record)" v-if="record.status === 0">关闭</a-button>
                <a-button size="small" @click="handleProcessDisbursement(record)" v-if="record.status === 0">处理放款</a-button>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>

    <!-- 订单详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="订单详情"
      width="800px"
      :footer="false"
    >
      <a-spin :loading="detailLoading">
        <div v-if="orderDetail">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="订单ID">{{ orderDetail.id }}</a-descriptions-item>
            <a-descriptions-item label="订单编号">{{ orderDetail.order_no }}</a-descriptions-item>
            <a-descriptions-item label="用户ID">{{ orderDetail.user_id }}</a-descriptions-item>
            <a-descriptions-item label="用户名称">{{ orderDetail.user_name }}</a-descriptions-item>
            <a-descriptions-item label="手机号">{{ orderDetail.user_mobile }}</a-descriptions-item>
            <a-descriptions-item label="产品名称">{{ orderDetail.product_name }}</a-descriptions-item>
            <a-descriptions-item label="贷款金额">{{ formatCurrency(orderDetail.loan_amount) }}</a-descriptions-item>
            <a-descriptions-item label="实际放款金额">{{ formatCurrency(orderDetail.principal) }}</a-descriptions-item>
            <a-descriptions-item label="总利息">{{ formatCurrency(orderDetail.total_interest) }}</a-descriptions-item>
            <a-descriptions-item label="总担保费">{{ formatCurrency(orderDetail.total_guarantee_fee) }}</a-descriptions-item>
            <a-descriptions-item label="总其他费用">{{ formatCurrency(orderDetail.total_other_fees) }}</a-descriptions-item>
            <a-descriptions-item label="总应还金额">{{ formatCurrency(orderDetail.total_repayable_amount) }}</a-descriptions-item>
            <a-descriptions-item label="已还金额">{{ formatCurrency(orderDetail.amount_paid) }}</a-descriptions-item>
            <a-descriptions-item label="渠道">{{ orderDetail.channel }}</a-descriptions-item>
            <a-descriptions-item label="客户来源">{{ orderDetail.customer_origin }}</a-descriptions-item>
            <a-descriptions-item label="初始订单渠道">{{ orderDetail.initial_order_channel }}</a-descriptions-item>
            <a-descriptions-item label="订单状态">{{ getOrderStatusText(orderDetail.status) }}</a-descriptions-item>
            <a-descriptions-item label="是否冻结">{{ orderDetail.is_freeze === 1 ? '是' : '否' }}</a-descriptions-item>
            <a-descriptions-item label="投诉状态">{{ orderDetail.complaint_status === 1 ? '是' : '否' }}</a-descriptions-item>
            <a-descriptions-item label="审核状态">{{ getReviewStatusText(orderDetail.review_status) }}</a-descriptions-item>
            <a-descriptions-item label="审核员">{{ orderDetail.audit_assignee_name || '无' }}</a-descriptions-item>
            <a-descriptions-item label="业务员">{{ orderDetail.sales_assignee_name || '无' }}</a-descriptions-item>
            <a-descriptions-item label="催收员">{{ orderDetail.collection_assignee_name || '无' }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ orderDetail.created_at }}</a-descriptions-item>
            <a-descriptions-item label="更新时间">{{ orderDetail.updated_at }}</a-descriptions-item>
            <a-descriptions-item label="放款时间">{{ orderDetail.disbursed_at || '无' }}</a-descriptions-item>
          </a-descriptions>
        </div>
      </a-spin>
    </a-modal>

    <!-- 关闭订单弹窗 -->
    <a-modal
      v-model:visible="closeModalVisible"
      title="关闭订单"
      @ok="handleCloseSubmit"
      @cancel="closeModalVisible = false"
    >
      <a-form ref="closeFormRef" :model="closeForm" layout="vertical">
        <a-form-item field="reason" label="关闭原因" :rules="[{ required: true, message: '请输入关闭原因' }]">
          <a-textarea
            v-model="closeForm.reason"
            placeholder="请输入关闭原因"
            :max-length="500"
            show-word-limit
            :auto-size="{ minRows: 3, maxRows: 6 }"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import {
  IconSearch,
  IconRefresh,
} from '@arco-design/web-vue/es/icon';
import type {
  OrderQueryParams,
  OrderListItem,
} from '@/api/ordermanagement';
import {
  getOrderList,
  getOrderStatus,
  closeOrder,
  processDisbursement,
} from '@/api/ordermanagement';

// 响应式数据
const loading = ref(false);
const tableData = ref<OrderListItem[]>([]);

// 查询表单
const queryFormRef = ref();
const queryForm = reactive<OrderQueryParams>({
  order_no: '',
  user_id: undefined,
  product_rule_id: undefined,
  loan_amount_min: undefined,
  loan_amount_max: undefined,
  channel_id: undefined,
  customer_origin: '',
  status: undefined,
  is_freeze: undefined,
  complaint_status: undefined,
  audit_assignee_id: undefined,
  review_status: undefined,
  sales_assignee_id: undefined,
  submitted_at_start: '',
  submitted_at_end: '',
  disbursed_at_start: '',
  disbursed_at_end: '',
  completed_at_start: '',
  completed_at_end: '',
  page: 1,
  page_size: 20,
});

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
});

// 时间范围
const submittedTimeRange = ref<string[]>([]);
const disbursedTimeRange = ref<string[]>([]);
const completedTimeRange = ref<string[]>([]);

// 订单详情弹窗
const detailModalVisible = ref(false);
const detailLoading = ref(false);
const orderDetail = ref<OrderListItem | null>(null);

// 关闭订单弹窗
const closeModalVisible = ref(false);
const closeFormRef = ref();
const closeForm = reactive({
  reason: '',
});
const currentOrderNo = ref('');

// 初始化
onMounted(() => {
  fetchData();
});

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true;
    const params = {
      ...queryForm,
      page: pagination.current,
      page_size: pagination.pageSize,
    };

    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key as keyof typeof params] === '' || params[key as keyof typeof params] === undefined || params[key as keyof typeof params] === null) {
        delete params[key as keyof typeof params];
      }
    });

    const response = await getOrderList(params);
    console.log('API返回完整响应:', response); // 添加调试日志

    if (response && response.data) {
      tableData.value = Array.isArray(response.data) ? response.data : [];
      pagination.total = response.total || 0;
      pagination.current = response.page || 1;
      pagination.pageSize = response.pageSize || 20;
    } else {
      console.error('响应格式错误:', response);
      tableData.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
    Message.error('获取订单列表失败');
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};

// 重置
const handleReset = () => {
  queryFormRef.value?.resetFields();
  submittedTimeRange.value = [];
  disbursedTimeRange.value = [];
  completedTimeRange.value = [];
  pagination.current = 1;
  fetchData();
};

// 刷新
const handleRefresh = () => {
  fetchData();
};

// 分页变化
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchData();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchData();
};

// 时间范围变化
const handleSubmittedTimeChange = (value: string[]) => {
  if (value && value.length === 2) {
    queryForm.submitted_at_start = value[0];
    queryForm.submitted_at_end = value[1];
  } else {
    queryForm.submitted_at_start = '';
    queryForm.submitted_at_end = '';
  }
};

const handleDisbursedTimeChange = (value: string[]) => {
  if (value && value.length === 2) {
    queryForm.disbursed_at_start = value[0];
    queryForm.disbursed_at_end = value[1];
  } else {
    queryForm.disbursed_at_start = '';
    queryForm.disbursed_at_end = '';
  }
};

const handleCompletedTimeChange = (value: string[]) => {
  if (value && value.length === 2) {
    queryForm.completed_at_start = value[0];
    queryForm.completed_at_end = value[1];
  } else {
    queryForm.completed_at_start = '';
    queryForm.completed_at_end = '';
  }
};

// 查看订单详情
const handleView = async (record: OrderListItem) => {
  try {
    detailLoading.value = true;
    detailModalVisible.value = true;
    // 这里应该请求详细信息API，目前直接使用列表数据展示
    orderDetail.value = record;
  } catch (error) {
    Message.error('获取订单详情失败');
  } finally {
    detailLoading.value = false;
  }
};

// 关闭订单
const handleClose = (record: OrderListItem) => {
  currentOrderNo.value = record.order_no;
  closeForm.reason = '';
  closeModalVisible.value = true;
};

// 提交关闭订单
const handleCloseSubmit = async () => {
  try {
    await closeFormRef.value?.validate();
    await closeOrder(currentOrderNo.value, closeForm.reason);
    Message.success('关闭订单成功');
    closeModalVisible.value = false;
    fetchData();
  } catch (error) {
    Message.error('关闭订单失败');
  }
};

// 处理放款
const handleProcessDisbursement = async (record: OrderListItem) => {
  try {
    await processDisbursement(record.order_no);
    Message.success('处理放款请求已提交');
    fetchData();
  } catch (error) {
    Message.error('处理放款失败');
  }
};

// 工具函数
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
  }).format(amount);
};

// 获取订单状态文本
const getOrderStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '待放款',
    1: '放款中',
    2: '交易关闭',
    3: '交易完成',
  };
  return statusMap[status] || '未知状态';
};

// 获取审核状态文本
const getReviewStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    0: '待审核',
    1: '审核通过',
    2: '审核拒绝',
  };
  return statusMap[status] || '未知状态';
};
</script>

<style scoped>
.container {
  padding: 0 20px 20px 20px;
}

.general-card {
  margin-bottom: 16px;
}
</style> 