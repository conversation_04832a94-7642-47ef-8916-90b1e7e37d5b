package scheduler

import (
	"fincore/app/scheduler/config"
	"fincore/app/scheduler/engine"
	"fincore/app/scheduler/registry"
	"fincore/app/scheduler/tasks"
	"fincore/utils/log"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// formatDuration 格式化时间间隔为易读格式
func formatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%.1f秒", d.Seconds())
	}

	days := int(d.Hours()) / 24
	hours := int(d.Hours()) % 24
	minutes := int(d.Minutes()) % 60
	seconds := int(d.Seconds()) % 60

	var parts []string
	if days > 0 {
		parts = append(parts, fmt.Sprintf("%d天", days))
	}
	if hours > 0 {
		parts = append(parts, fmt.Sprintf("%d小时", hours))
	}
	if minutes > 0 {
		parts = append(parts, fmt.Sprintf("%d分钟", minutes))
	}
	if seconds > 0 || len(parts) == 0 {
		parts = append(parts, fmt.Sprintf("%d秒", seconds))
	}

	result := ""
	for i, part := range parts {
		if i > 0 {
			result += " "
		}
		result += part
	}
	return result
}

// SchedulerManager 调度器管理器
type SchedulerManager struct {
	config    *config.SchedulerConfig // 配置
	registry  *registry.TaskRegistry  // 任务注册中心
	discovery *registry.TaskDiscovery // 任务发现器
	engine    *engine.ScheduleEngine  // 调度引擎
	executor  *engine.TaskExecutor    // 任务执行器
	logger    *log.Logger             // 日志器
	isStarted bool                    // 是否已启动
	mutex     sync.RWMutex            // 读写锁
	startTime time.Time               // 启动时间
}

// ManagerStatus 管理器状态
type ManagerStatus struct {
	IsStarted       bool                   `json:"is_started"`
	StartTime       *time.Time             `json:"start_time,omitempty"`
	Uptime          time.Duration          `json:"uptime"`
	SchedulerName   string                 `json:"scheduler_name"`
	TaskCount       int                    `json:"task_count"`
	ActiveJobsCount int                    `json:"active_jobs_count"`
	EngineRunning   bool                   `json:"engine_running"`
	Config          map[string]interface{} `json:"config"`
}

// NewSchedulerManager 创建调度器管理器
func NewSchedulerManager() (*SchedulerManager, error) {
	// 加载配置
	cfg := config.GetConfig()

	// 创建日志器
	logger := log.GetModule("scheduler")
	config.SetLogger(logger)

	// 创建任务注册中心
	taskRegistry := registry.NewTaskRegistry(logger)

	// 创建任务发现器
	taskDiscovery := registry.NewTaskDiscovery(taskRegistry, logger)

	// 创建任务执行器
	taskExecutor := engine.NewTaskExecutor(cfg, logger)

	// 创建调度引擎
	scheduleEngine := engine.NewScheduleEngine(taskRegistry, taskExecutor, cfg, logger)

	manager := &SchedulerManager{
		config:    cfg,
		registry:  taskRegistry,
		discovery: taskDiscovery,
		engine:    scheduleEngine,
		executor:  taskExecutor,
		logger:    logger,
	}

	logger.Info("调度器管理器创建成功",
		zap.String("scheduler_name", cfg.Scheduler.Name),
		zap.String("timezone", cfg.Scheduler.Timezone),
		zap.Int("max_concurrent_jobs", cfg.Scheduler.MaxConcurrentJobs),
	)

	return manager, nil
}

// Start 启动调度器
func (m *SchedulerManager) Start() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.isStarted {
		return fmt.Errorf("调度器已经启动")
	}

	m.logger.Info("正在启动调度器管理器")

	// 验证配置
	if err := m.validateConfig(); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	// 启动调度引擎
	if err := m.engine.Start(); err != nil {
		return fmt.Errorf("启动调度引擎失败: %w", err)
	}

	m.isStarted = true
	m.startTime = time.Now()

	m.logger.Info("调度器管理器启动成功",
		zap.String("scheduler_name", m.config.Scheduler.Name),
		zap.Int("registered_tasks", m.registry.GetTaskCount()),
	)

	return nil
}

// Stop 停止调度器
func (m *SchedulerManager) Stop() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.isStarted {
		return fmt.Errorf("调度器未启动")
	}

	m.logger.Info("正在停止调度器管理器")

	// 停止调度引擎
	if err := m.engine.Stop(); err != nil {
		m.logger.Error("停止调度引擎失败", zap.Error(err))
		return err
	}

	m.isStarted = false

	uptime := time.Since(m.startTime)
	m.logger.Info("调度器管理器已停止",
		zap.String("uptime", formatDuration(uptime)),
	)

	return nil
}

// GracefulStop 优雅停止调度器
func (m *SchedulerManager) GracefulStop() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.isStarted {
		return fmt.Errorf("调度器未启动")
	}

	// 1. 首先停止调度引擎，不再调度新任务
	if err := m.engine.Stop(); err != nil {
		m.logger.Error("停止调度引擎失败", zap.Error(err))
		return err
	}

	// 2. 根据配置决定是否取消正在运行的任务
	if m.config.Scheduler.CancelRunningTasks {
		m.logger.Info("配置为强制取消正在运行的任务")
		if err := m.cancelAllRunningTasks(); err != nil {
			m.logger.Error("取消正在运行的任务失败", zap.Error(err))
		}
	} else {
		m.logger.Info("配置为等待正在运行的任务完成")
	}

	// 3. 等待所有任务完成或超时
	if err := m.waitForTasksCompletion(); err != nil {
		m.logger.Error("等待任务完成失败", zap.Error(err))
		return err
	}

	m.isStarted = false

	uptime := time.Since(m.startTime)
	m.logger.Info("调度器管理器已停止",
		zap.String("uptime", formatDuration(uptime)),
	)

	return nil
}

// cancelAllRunningTasks 取消所有正在运行的任务
func (m *SchedulerManager) cancelAllRunningTasks() error {
	activeJobs := m.executor.GetActiveJobs()
	if len(activeJobs) == 0 {
		m.logger.Info("没有正在运行的任务需要取消")
		return nil
	}

	m.logger.Info("开始取消正在运行的任务", zap.Int("task_count", len(activeJobs)))

	var errors []error
	for taskName := range activeJobs {
		if err := m.executor.CancelTask(taskName); err != nil {
			m.logger.Error("取消任务失败",
				zap.String("task_name", taskName),
				zap.Error(err),
			)
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("取消部分任务失败，错误数量: %d", len(errors))
	}

	m.logger.Info("所有正在运行的任务已取消")
	return nil
}

// waitForTasksCompletion 等待任务完成
func (m *SchedulerManager) waitForTasksCompletion() error {
	timeout := time.Duration(m.config.Scheduler.ShutdownTimeout) * time.Second
	checkInterval := 1 * time.Second

	m.logger.Info("等待任务完成",
		zap.Duration("配置超时时间：", timeout),
	)

	start := time.Now()
	for {
		activeJobs := m.executor.GetActiveJobs()
		if len(activeJobs) == 0 {
			m.logger.Info("所有任务已完成")
			break
		}

		if time.Since(start) > timeout {
			m.logger.Warn("等待任务完成超时，强制停止",
				zap.Int("remaining_tasks", len(activeJobs)),
				zap.Duration("timeout", timeout),
			)

			// 超时后强制取消剩余任务
			for taskName := range activeJobs {
				if err := m.executor.ForceKillTask(taskName); err != nil {
					m.logger.Error("强制结束任务失败",
						zap.String("task_name", taskName),
						zap.Error(err),
					)
				}
			}
			break
		}

		m.logger.Info("等待正在运行的任务完成",
			zap.Int("running_tasks", len(activeJobs)),
			zap.Duration("elapsed", time.Since(start)),
		)

		time.Sleep(checkInterval)
	}

	return nil
}

// RegisterTask 注册任务
func (m *SchedulerManager) RegisterTask(task tasks.TaskInterface) error {
	if err := m.registry.RegisterTask(task); err != nil {
		return err
	}

	// 如果调度器已启动，需要动态添加任务
	if m.isStarted {
		if err := m.engine.AddJob(task.GetName(), task); err != nil {
			// 如果添加失败，从注册中心移除
			m.registry.UnregisterTask(task.GetName())
			return fmt.Errorf("动态添加任务失败: %w", err)
		}
	}

	return nil
}

// UnregisterTask 注销任务
func (m *SchedulerManager) UnregisterTask(taskName string) error {
	// 如果调度器已启动，先从引擎中移除
	if m.isStarted {
		if err := m.engine.RemoveJob(taskName); err != nil {
			m.logger.Warn("从调度引擎移除任务失败",
				zap.String("task_name", taskName),
				zap.Error(err),
			)
		}
	}

	return m.registry.UnregisterTask(taskName)
}

// RegisterTasks 批量注册任务
func (m *SchedulerManager) RegisterTasks(tasks ...tasks.TaskInterface) *registry.DiscoveryResult {
	return m.discovery.AutoDiscoverTasks(tasks...)
}

// GetStatus 获取管理器状态
func (m *SchedulerManager) GetStatus() *ManagerStatus {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	status := &ManagerStatus{
		IsStarted:       m.isStarted,
		SchedulerName:   m.config.Scheduler.Name,
		TaskCount:       m.registry.GetTaskCount(),
		ActiveJobsCount: len(m.executor.GetActiveJobs()),
		EngineRunning:   m.engine.IsRunning(),
		Config:          m.config.GetConfigSummary(),
	}

	if m.isStarted {
		status.StartTime = &m.startTime
		status.Uptime = time.Since(m.startTime)
	}

	return status
}

// GetTaskStatus 获取任务状态
func (m *SchedulerManager) GetTaskStatus(taskName string) (map[string]interface{}, error) {
	// 检查任务是否存在
	if !m.registry.HasTask(taskName) {
		return nil, fmt.Errorf("任务 %s 不存在", taskName)
	}

	// 获取作业状态
	jobStatus, err := m.engine.GetJobStatus(taskName)
	if err != nil {
		return nil, err
	}

	// 获取任务元数据
	metadata, err := m.registry.GetTaskMetadata(taskName)
	if err != nil {
		return nil, err
	}

	// 合并信息
	status := make(map[string]interface{})
	for k, v := range jobStatus {
		status[k] = v
	}

	status["metadata"] = metadata
	status["is_active"] = m.executor.IsTaskRunning(taskName)

	return status, nil
}

// GetAllTasksStatus 获取所有任务状态
func (m *SchedulerManager) GetAllTasksStatus() map[string]interface{} {
	allJobs := m.engine.GetAllJobsStatus()
	allTasks := m.registry.GetAllTasksMetadata()
	activeJobs := m.executor.GetActiveJobs()

	tasksStatus := make(map[string]interface{})
	for _, metadata := range allTasks {
		taskName := metadata.Name

		status := map[string]interface{}{
			"metadata":  metadata,
			"is_active": false,
		}

		// 添加作业状态
		if jobsMap, ok := allJobs["jobs"].(map[string]interface{}); ok {
			if jobStatus, exists := jobsMap[taskName]; exists {
				for k, v := range jobStatus.(map[string]interface{}) {
					status[k] = v
				}
			}
		}

		// 检查是否活跃
		if _, isActive := activeJobs[taskName]; isActive {
			status["is_active"] = true
		}

		tasksStatus[taskName] = status
	}

	return map[string]interface{}{
		"manager_status": m.GetStatus(),
		"engine_status":  allJobs,
		"executor_stats": m.executor.GetExecutorStats(),
		"registry_stats": m.registry.GetRegistryStats(),
		"tasks":          tasksStatus,
	}
}

// CancelTask 取消任务执行
func (m *SchedulerManager) CancelTask(taskName string) error {
	return m.executor.CancelTask(taskName)
}

// ForceKillTask 强制结束任务
func (m *SchedulerManager) ForceKillTask(taskName string) error {
	return m.executor.ForceKillTask(taskName)
}

// IsRunning 检查调度器是否运行中
func (m *SchedulerManager) IsRunning() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.isStarted
}

// GetConfig 获取配置
func (m *SchedulerManager) GetConfig() *config.SchedulerConfig {
	return m.config
}

// GetRegistry 获取任务注册中心
func (m *SchedulerManager) GetRegistry() *registry.TaskRegistry {
	return m.registry
}

// GetDiscovery 获取任务发现器
func (m *SchedulerManager) GetDiscovery() *registry.TaskDiscovery {
	return m.discovery
}

// validateConfig 验证配置
func (m *SchedulerManager) validateConfig() error {
	if m.config.Scheduler.Name == "" {
		return fmt.Errorf("调度器名称不能为空")
	}

	if m.config.Scheduler.MaxConcurrentJobs <= 0 {
		return fmt.Errorf("最大并发任务数必须大于0")
	}

	if m.config.Scheduler.DefaultTimeout <= 0 {
		return fmt.Errorf("默认超时时间必须大于0")
	}

	return nil
}

// Restart 重启调度器
func (m *SchedulerManager) Restart() error {
	m.logger.Info("正在重启调度器管理器")

	if m.IsRunning() {
		if err := m.Stop(); err != nil {
			return fmt.Errorf("停止调度器失败: %w", err)
		}
	}

	// 等待一小段时间确保完全停止
	time.Sleep(1 * time.Second)

	if err := m.Start(); err != nil {
		return fmt.Errorf("启动调度器失败: %w", err)
	}

	m.logger.Info("调度器管理器重启成功")
	return nil
}

// GetHealthCheck 获取健康检查信息
func (m *SchedulerManager) GetHealthCheck() map[string]interface{} {
	status := m.GetStatus()

	health := map[string]interface{}{
		"status":      "healthy",
		"timestamp":   time.Now(),
		"uptime":      status.Uptime,
		"is_running":  status.IsStarted,
		"task_count":  status.TaskCount,
		"active_jobs": status.ActiveJobsCount,
	}

	// 检查各组件状态
	if !status.IsStarted {
		health["status"] = "stopped"
	} else if !status.EngineRunning {
		health["status"] = "degraded"
		health["reason"] = "调度引擎未运行"
	}

	return health
}
