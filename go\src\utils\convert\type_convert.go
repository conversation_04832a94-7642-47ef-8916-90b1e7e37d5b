package convert

import (
	"encoding/json"
	"strconv"
	"time"
)

// ConvertToInt 转换为整型指针
func ConvertToInt(v interface{}) *int {
	switch val := v.(type) {
	case int:
		return &val
	case int64:
		intVal := int(val)
		return &intVal
	case float64:
		intVal := int(val)
		return &intVal
	case string:
		if intVal, err := strconv.Atoi(val); err == nil {
			return &intVal
		}
	}
	return nil
}

// ConvertToFloat 转换为浮点型指针
func ConvertToFloat(v interface{}) *float64 {
	switch val := v.(type) {
	case float64:
		return &val
	case int:
		floatVal := float64(val)
		return &floatVal
	case int64:
		floatVal := float64(val)
		return &floatVal
	case string:
		if floatVal, err := strconv.ParseFloat(val, 64); err == nil {
			return &floatVal
		}
	}
	return nil
}

// ConvertToInt64 转换为int64
func ConvertToInt64(v interface{}) *int64 {
	switch val := v.(type) {
	case int64:
		return &val
	case int:
		int64Val := int64(val)
		return &int64Val
	case float64:
		int64Val := int64(val)
		return &int64Val
	case string:
		if int64Val, err := strconv.ParseInt(val, 10, 64); err == nil {
			return &int64Val
		}
	}
	return nil
}

// ConvertToString 转换为字符串
func ConvertToString(v interface{}) string {
	switch val := v.(type) {
	case string:
		return val
	case int:
		return strconv.Itoa(val)
	case int64:
		return strconv.FormatInt(val, 10)
	case float64:
		return strconv.FormatFloat(val, 'f', -1, 64)
	}
	return ""
}

// ConvertToBool 转换为布尔值指针
func ConvertToBool(v interface{}) *bool {
	switch val := v.(type) {
	case bool:
		return &val
	case int:
		boolVal := val != 0
		return &boolVal
	case string:
		if boolVal, err := strconv.ParseBool(val); err == nil {
			return &boolVal
		}
		// 支持 "1"/"0" 格式
		if val == "1" {
			boolVal := true
			return &boolVal
		} else if val == "0" {
			boolVal := false
			return &boolVal
		}
	}
	return nil
}

// MustConvertToInt 强制转换为int，转换失败返回默认值
func MustConvertToInt(v interface{}, defaultValue int) int {
	if result := ConvertToInt(v); result != nil {
		return *result
	}
	return defaultValue
}

// MustConvertToFloat 强制转换为float64，转换失败返回默认值
func MustConvertToFloat(v interface{}, defaultValue float64) float64 {
	if result := ConvertToFloat(v); result != nil {
		return *result
	}
	return defaultValue
}

// MustConvertToInt64 强制转换为int64，转换失败返回默认值
func MustConvertToInt64(v interface{}, defaultValue int64) int64 {
	if result := ConvertToInt64(v); result != nil {
		return *result
	}
	return defaultValue
}

// MustConvertToBool 强制转换为bool，转换失败返回默认值
func MustConvertToBool(v interface{}, defaultValue bool) bool {
	if result := ConvertToBool(v); result != nil {
		return *result
	}
	return defaultValue
}

// GetStringFromMap 从map中获取字符串值
func GetStringFromMap(data map[string]interface{}, key string) string {
	if value, exists := data[key]; exists {
		return ConvertToString(value)
	}
	return ""
}

// GetFloatFromMap 从map中获取float64值，失败返回默认值
func GetFloatFromMap(data map[string]interface{}, key string, defaultValue float64) float64 {
	if value, exists := data[key]; exists {
		return MustConvertToFloat(value, defaultValue)
	}
	return defaultValue
}

// GetIntFromMap 从map中获取int值，失败返回默认值
func GetIntFromMap(data map[string]interface{}, key string, defaultValue int) int {
	if value, exists := data[key]; exists {
		return MustConvertToInt(value, defaultValue)
	}
	return defaultValue
}

// GetBoolFromMap 从map中获取bool值，失败返回默认值
func GetBoolFromMap(data map[string]interface{}, key string, defaultValue bool) bool {
	if value, exists := data[key]; exists {
		return MustConvertToBool(value, defaultValue)
	}
	return defaultValue
}

// GetTimeFromMap 从map中获取time.Time值，失败返回默认值
func GetTimeFromMap(data map[string]interface{}, key string) time.Time {
	if value, exists := data[key]; exists {
		if value != nil {
			return ConvertToTime(value)
		}
	}
	return time.Time{}
}

// ConvertToTime 转换为time.Time
func ConvertToTime(v interface{}) time.Time {
	switch val := v.(type) {
	case time.Time:
		return val
	case string:
		if timeVal, err := time.Parse("2006-01-02 15:04:05", val); err == nil {
			return timeVal
		}
	case int64:
		return time.Unix(val, 0)
	case int:
		return time.Unix(int64(val), 0)
	case float64:
		return time.Unix(int64(val), 0)
	}
	return time.Time{}
}

// Ternary 三元运算符
func Ternary(condition bool, trueValue, falseValue interface{}) interface{} {
	if condition {
		return trueValue
	}
	return falseValue
}

// DirectConvertToInt64 直接转换为int64值（不返回指针）
// 用于数据库查询结果的类型转换，转换失败返回0
func DirectConvertToInt64(value interface{}) int64 {
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int64:
		return v
	case int:
		return int64(v)
	case int32:
		return int64(v)
	case uint:
		return int64(v)
	case uint32:
		return int64(v)
	case uint64:
		return int64(v)
	case float64:
		return int64(v)
	case float32:
		return int64(v)
	case string:
		if v == "" {
			return 0
		}
		if result, err := strconv.ParseInt(v, 10, 64); err == nil {
			return result
		}
		return 0
	case []byte:
		if len(v) == 0 {
			return 0
		}
		if result, err := strconv.ParseInt(string(v), 10, 64); err == nil {
			return result
		}
		return 0
	default:
		return 0
	}
}

// DirectConvertToString 直接转换为string值（不返回指针）
// 用于数据库查询结果的类型转换，转换失败返回空字符串
func DirectConvertToString(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case []byte:
		return string(v)
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case int32:
		return strconv.FormatInt(int64(v), 10)
	case uint:
		return strconv.FormatUint(uint64(v), 10)
	case uint32:
		return strconv.FormatUint(uint64(v), 10)
	case uint64:
		return strconv.FormatUint(v, 10)
	case float32:
		return strconv.FormatFloat(float64(v), 'f', -1, 32)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	case bool:
		return strconv.FormatBool(v)
	default:
		return ""
	}
}

// MapToStruct map 转 struct
func MapToStruct(data map[string]interface{}, v interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	return json.Unmarshal(jsonData, v)
}

// JsonToStruct json 转 struct
func JsonToStruct(jsonStr string, v interface{}) error {
	return json.Unmarshal([]byte(jsonStr), v)
}
