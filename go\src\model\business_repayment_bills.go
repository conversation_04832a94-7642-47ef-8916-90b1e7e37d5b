package model

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"fincore/utils/gform"
)

// 还款账单状态常量
const (
	// 待支付
	RepaymentBillStatusUnpaid = 0
	// 已支付
	RepaymentBillStatusPaid = 1
	// 逾期已支付
	RepaymentBillStatusOverduePaid = 2
	// 逾期待支付
	RepaymentBillStatusOverdueUnpaid = 3
	// 已取消
	RepaymentBillStatusCancelled = 4
	// 已结算
	RepaymentBillStatusSettled = 5
	// 已退款
	RepaymentBillStatusRefunded = 6
	// 部分还款
	RepaymentBillStatusPartialPaid = 7
	// 提前结清
	RepaymentBillStatusEarlySettlement = 8
	// 逾期部分支付
	RepaymentBillStatusOverduePartialPaid = 9
)

// 还款计划状态描述映射
var RepaymentBillStatusDescriptions = map[int]string{
	RepaymentBillStatusUnpaid:             "待支付",
	RepaymentBillStatusPaid:               "已支付",
	RepaymentBillStatusOverduePaid:        "逾期已支付",
	RepaymentBillStatusOverdueUnpaid:      "逾期待支付",
	RepaymentBillStatusCancelled:          "已取消",
	RepaymentBillStatusSettled:            "已结算",
	RepaymentBillStatusRefunded:           "已退款",
	RepaymentBillStatusPartialPaid:        "部分还款",
	RepaymentBillStatusEarlySettlement:    "提前结清",
	RepaymentBillStatusOverduePartialPaid: "逾期部分支付",
}

// BusinessRepaymentBills 还款账单数据模型
type BusinessRepaymentBills struct {
	ID                   int        `json:"id" gorm:"primaryKey;autoIncrement" db:"id"`                // 主键ID，自增
	OrderID              int        `json:"order_id" db:"order_id"`                                    // 订单ID，关联贷款订单表
	UserID               int        `json:"user_id" db:"user_id"`                                      // 用户ID，关联用户表
	PeriodNumber         int        `json:"period_number" db:"period_number"`                          // 期数，第几期还款
	DuePrincipal         Decimal    `json:"due_principal,string" db:"due_principal"`                   // 应还本金，单位：元
	DueInterest          Decimal    `json:"due_interest,string" db:"due_interest"`                     // 应还利息，单位：元
	DueGuaranteeFee      Decimal    `json:"due_guarantee_fee,string" db:"due_guarantee_fee"`           // 应还担保费，单位：元
	DueOtherFees         Decimal    `json:"due_other_fees,string" db:"due_other_fees"`                 // 应还其他费用，单位：元
	AssetManagementEntry Decimal    `json:"asset_management_entry,string" db:"asset_management_entry"` // 当期资管费用(本金+利息+其他费用)，单位：元
	LateFee              Decimal    `json:"late_fee,string" db:"late_fee"`                             // 逾期费，单位：元
	TotalDueAmount       Decimal    `json:"total_due_amount,string" db:"total_due_amount"`             // 总应还金额，计算字段（本金+利息+担保费+其他费用+逾期费）
	PaidAmount           Decimal    `json:"paid_amount,string" db:"paid_amount"`                       // 已还金额，单位：元
	TotalRefundAmount    Decimal    `json:"total_refund_amount,string" db:"total_refund_amount"`       // 累计退款金额，单位：元
	Status               int        `json:"status" db:"status"`                                        // 账单状态：0-待支付；1-已支付；2-逾期已支付；3-逾期待支付；4-已取消；5-已结算；6-已退款；7-部分还款；8-提前结清
	DueDate              time.Time  `json:"due_date" db:"due_date"`                                    // 到期日期，格式：YYYY-MM-DD
	DeductRetryCount     int        `json:"deduct_retry_count" db:"deduct_retry_count"`                // 代扣重试次数
	LastDeductAttemptAt  *int64     `json:"last_deduct_attempt_at" db:"last_deduct_attempt_at"`        // 最后代扣尝试时间，Unix时间戳，可为空
	TotalWaiveAmount     Decimal    `json:"total_waive_amount,string" db:"total_waive_amount"`         // 累计减免金额，单位：元
	PaidAt               *time.Time `json:"paid_at" db:"paid_at"`                                      // 还款时间，Unix时间戳，可为空
	CreatedAt            *time.Time `json:"created_at" db:"created_at"`                                // 创建时间，Unix时间戳
	UpdatedAt            *time.Time `json:"updated_at" db:"updated_at"`                                // 更新时间，Unix时间戳
	DeletedAt            *time.Time `json:"deleted_at" db:"deleted_at"`                                // 删除时间，Unix时间戳
}

func GetBillStatusText(status int) string {
	return RepaymentBillStatusDescriptions[status]
}

// TableName 指定表名
func (BusinessRepaymentBills) TableName() string {
	return "business_repayment_bills"
}

// BusinessRepaymentBillsService 还款账单服务
type BusinessRepaymentBillsService struct {
	ctx context.Context
}

// NewBusinessRepaymentBillsService 创建还款账单服务实例
func NewBusinessRepaymentBillsService(ctx context.Context) *BusinessRepaymentBillsService {
	return &BusinessRepaymentBillsService{ctx: ctx}
}

// CreateBill 创建还款账单
func (s *BusinessRepaymentBillsService) CreateBill(bill *BusinessRepaymentBills) error {
	now := time.Now()
	// 计算总应还金额
	bill.TotalDueAmount = bill.DuePrincipal + bill.DueInterest + bill.DueGuaranteeFee + bill.DueOtherFees + bill.LateFee

	data := map[string]interface{}{
		"order_id":           bill.OrderID,
		"user_id":            bill.UserID,
		"period_number":      bill.PeriodNumber,
		"due_principal":      bill.DuePrincipal,
		"due_interest":       bill.DueInterest,
		"due_guarantee_fee":  bill.DueGuaranteeFee,
		"due_other_fees":     bill.DueOtherFees,
		"late_fee":           bill.LateFee,
		"total_due_amount":   bill.TotalDueAmount,
		"paid_amount":        bill.PaidAmount,
		"status":             bill.Status,
		"due_date":           bill.DueDate,
		"deduct_retry_count": bill.DeductRetryCount,
		"created_at":         &now,
		"updated_at":         &now,
	}

	insertID, err := DB().Table("business_repayment_bills").InsertGetId(data)
	if err != nil {
		return fmt.Errorf("创建还款账单失败: %v", err)
	}

	// 设置插入的ID
	bill.ID = int(insertID)

	return nil
}

// GetBillsByOrderID 根据订单ID获取还款账单列表
func (s *BusinessRepaymentBillsService) GetBillsByOrderID(tx gform.IOrm, orderID int) ([]BusinessRepaymentBills, error) {

	var handler gform.IOrm
	if tx != nil {
		handler = tx
	} else {
		handler = DB()
	}

	data, err := handler.Table("business_repayment_bills").Where("order_id", orderID).OrderBy("period_number ASC").Get()
	if err != nil {
		return nil, fmt.Errorf("查询还款账单失败: %v", err)
	}

	var bills []BusinessRepaymentBills
	for _, item := range data {
		var bill BusinessRepaymentBills
		if err := mapToStruct(item, &bill); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		bills = append(bills, bill)
	}

	return bills, nil
}

// GetBillsByUserID 根据用户ID获取还款账单列表
func (s *BusinessRepaymentBillsService) GetBillsByUserID(userID int) ([]BusinessRepaymentBills, error) {
	data, err := DB().Table("business_repayment_bills").Where("user_id", userID).OrderBy("period_number ASC").Get()
	if err != nil {
		return nil, fmt.Errorf("查询还款账单失败: %v", err)
	}

	var bills []BusinessRepaymentBills
	for _, item := range data {
		var bill BusinessRepaymentBills
		if err := mapToStruct(item, &bill); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		bills = append(bills, bill)
	}

	return bills, nil
}

type UpdateBillParams struct {
	DuePrincipal         Decimal    `json:"due_principal"`          // 应还本金，单位：元
	DueInterest          Decimal    `json:"due_interest"`           // 应还利息，单位：元
	DueGuaranteeFee      Decimal    `json:"due_guarantee_fee"`      // 应还担保费，单位：元
	DueOtherFees         Decimal    `json:"due_other_fees"`         // 应还其他费用，单位：元
	AssetManagementEntry Decimal    `json:"asset_management_entry"` // 资管费，单位：元
	LateFee              Decimal    `json:"late_fee"`               // 逾期费，单位：元
	TotalDueAmount       Decimal    `json:"total_due_amount"`       // 总应还金额，计算字段（本金+利息+担保费+其他费用+逾期费）
	PaidAmount           Decimal    `json:"paid_amount"`            // 已还金额，单位：元
	Status               int        `json:"status"`                 // 账单状态：0-未还，1-已还，2-逾期，3-部分还款
	DueDate              string     `json:"due_date"`               // 到期日期，格式：YYYY-MM-DD
	DeductRetryCount     int        `json:"deduct_retry_count"`     // 代扣重试次数
	LastDeductAttemptAt  *time.Time `json:"last_deduct_attempt_at"` // 最后代扣尝试时间，Unix时间戳，可为空
	PaidAt               *time.Time `json:"paid_at"`                // 还款时间，Unix时间戳，可为空
}

type UpdateBillResultWhere struct {
	ID           int    `json:"id"`
	OrderID      int    `json:"order_id"`
	PeriodNumber int    `json:"period_number"`
	Status       int    `json:"status"`
	DueDate      string `json:"due_date"`
}

// UpdateBill 更新账单信息
func (s *BusinessRepaymentBillsService) UpdateBill(tx gform.IOrm, where UpdateBillResultWhere, updateMap map[string]interface{}) (upID int64, err error) {
	query := tx.Table("business_repayment_bills").Where("id", where.ID)
	if where.OrderID != 0 {
		query = query.Where("order_id", where.OrderID)
	}
	if where.PeriodNumber != 0 {
		query = query.Where("period_number", where.PeriodNumber)
	}
	if where.Status != 0 {
		query = query.Where("status", where.Status)
	}
	if where.DueDate != "" {
		query = query.Where("due_date", where.DueDate)
	}

	upID, err = query.Update(updateMap)
	if err != nil {
		return
	}

	if upID == 0 {
		err = fmt.Errorf("更新还款账单失败: 未找到账单")
		return
	}

	return
}

// GetBillByID 根据账单ID获取还款账单
func (s *BusinessRepaymentBillsService) GetBillByID(billID int) (*BusinessRepaymentBills, error) {
	data, err := DB(WithContext(s.ctx)).Table("business_repayment_bills").Where("id", billID).First()
	if err != nil {
		return nil, fmt.Errorf("查询还款账单失败: %v", err)
	}
	if len(data) == 0 {
		return nil, fmt.Errorf("还款账单不存在")
	}

	var bill BusinessRepaymentBills
	if err := mapToStruct(data, &bill); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &bill, nil
}

// GetBillsByStatus 根据状态获取还款账单列表（只查询到期的账单）
func (s *BusinessRepaymentBillsService) GetBillsByStatus(statuses []int) ([]BusinessRepaymentBills, error) {
	if len(statuses) == 0 {
		return []BusinessRepaymentBills{}, nil
	}

	// 转换为interface{}切片
	statusesInterface := make([]interface{}, len(statuses))
	for i, status := range statuses {
		statusesInterface[i] = status
	}

	// 获取当前日期
	currentDate := time.Now().Format("2006-01-02")

	data, err := DB(WithContext(s.ctx)).Table("business_repayment_bills").
		WhereIn("status", statusesInterface).
		Where("due_date", "<=", currentDate). // 只查询到期的账单
		OrderBy("due_date ASC, period_number ASC").
		Get()
	if err != nil {
		return nil, fmt.Errorf("查询还款账单失败: %v", err)
	}

	var bills []BusinessRepaymentBills
	for _, item := range data {
		var bill BusinessRepaymentBills
		if err := mapToStruct(item, &bill); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		bills = append(bills, bill)
	}

	return bills, nil
}

// GetTotalWaiveAmountByOrderID 获取订单的总减免金额
func (s *BusinessRepaymentBillsService) GetTotalWaiveAmountByOrderID(orderID int) (float64, error) {
	query := `
		SELECT COALESCE(SUM(total_waive_amount), 0) as total_waive_amount
		FROM business_repayment_bills
		WHERE order_id = ?
	`

	result, err := DB().Query(query, orderID)
	if err != nil {
		return 0, fmt.Errorf("查询订单总减免金额失败: %v", err)
	}

	if len(result) == 0 {
		return 0, nil
	}

	// 获取查询结果
	totalWaiveAmountInterface := result[0]["total_waive_amount"]
	if totalWaiveAmountInterface == nil {
		return 0, nil
	}

	// 将结果转换为float64
	var totalWaiveAmount float64
	switch v := totalWaiveAmountInterface.(type) {
	case float64:
		totalWaiveAmount = v
	case string:
		totalWaiveAmount, err = strconv.ParseFloat(v, 64)
		if err != nil {
			return 0, fmt.Errorf("转换减免金额失败: %w", err)
		}
	case []byte:
		totalWaiveAmount, err = strconv.ParseFloat(string(v), 64)
		if err != nil {
			return 0, fmt.Errorf("转换减免金额失败: %w", err)
		}
	default:
		return 0, nil
	}

	return totalWaiveAmount, nil
}
