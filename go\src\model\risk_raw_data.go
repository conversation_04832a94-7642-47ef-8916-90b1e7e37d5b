package model

import (
	"fmt"
	"strconv"
	"time"
)

// RiskRawDataService 风控原始数据服务
type RiskRawDataService struct{}

// NewRiskRawDataService 创建风控原始数据服务实例
func NewRiskRawDataService() *RiskRawDataService {
	return &RiskRawDataService{}
}

// GetByEvaluationID 根据评估ID获取风控原始数据
func (s *RiskRawDataService) GetByEvaluationID(evaluationID string) (*RiskRawData, error) {
	data, err := DB().Table("risk_raw_data").
		Where("evaluation_id", evaluationID).
		First()
	if err != nil {
		return nil, fmt.Errorf("查询风控原始数据失败: %v", err)
	}

	if data == nil {
		return nil, nil // 没有找到数据
	}

	// 将查询结果映射到RiskRawData结构体
	rawData := &RiskRawData{}
	s.mapToRiskRawData(data, rawData)

	return rawData, nil
}

// CreateRawData 创建风控原始数据记录
func (s *RiskRawDataService) CreateRawData(rawData *RiskRawData) error {
	data := map[string]interface{}{
		"evaluation_id": rawData.EvaluationID,
	}

	// 添加第三方响应数据（如果有值）
	if rawData.LeidaV4Response != "" {
		data["leida_v4_response"] = rawData.LeidaV4Response
	}
	if rawData.TanZhenCResponse != "" {
		data["tan_zhen_c_response"] = rawData.TanZhenCResponse
	}
	if rawData.ZwscResponse != "" {
		data["zwsc_response"] = rawData.ZwscResponse
	}
	if rawData.DataSource != "" {
		data["data_source"] = rawData.DataSource
	}

	_, err := DB().Table("risk_raw_data").Data(data).Insert()
	if err != nil {
		return fmt.Errorf("创建风控原始数据记录失败: %v", err)
	}

	return nil
}

// mapToRiskRawData 将数据库查询结果映射到RiskRawData结构体
func (s *RiskRawDataService) mapToRiskRawData(data map[string]interface{}, rawData *RiskRawData) {
	// 辅助函数：安全地转换为int64
	parseInt64 := func(v interface{}) int64 {
		if v == nil {
			return 0
		}
		switch val := v.(type) {
		case int64:
			return val
		case int:
			return int64(val)
		case float64:
			return int64(val)
		case string:
			if i, err := strconv.ParseInt(val, 10, 64); err == nil {
				return i
			}
		}
		return 0
	}

	// 辅助函数：安全地转换为string
	parseString := func(v interface{}) string {
		if v == nil {
			return ""
		}
		switch val := v.(type) {
		case string:
			return val
		case []byte:
			return string(val)
		default:
			return fmt.Sprintf("%v", val)
		}
	}

	// 辅助函数：安全地转换为time.Time
	parseTime := func(v interface{}) time.Time {
		if v == nil {
			return time.Time{}
		}
		timeStr := parseString(v)

		// 尝试多种时间格式解析
		formats := []string{
			"2006-01-02 15:04:05",
			time.RFC3339,
			"2006-01-02T15:04:05Z",
			"2006-01-02",
		}

		for _, format := range formats {
			if t, err := time.Parse(format, timeStr); err == nil {
				return t
			}
		}

		// 尝试解析Unix时间戳
		if i, err := strconv.ParseInt(timeStr, 10, 64); err == nil {
			return time.Unix(i, 0)
		}

		return time.Time{}
	}

	// 映射所有字段
	if v, ok := data["id"]; ok {
		rawData.ID = parseInt64(v)
	}
	if v, ok := data["evaluation_id"]; ok {
		rawData.EvaluationID = parseString(v)
	}
	if v, ok := data["leida_v4_response"]; ok {
		rawData.LeidaV4Response = parseString(v)
	}
	if v, ok := data["tan_zhen_c_response"]; ok {
		rawData.TanZhenCResponse = parseString(v)
	}
	if v, ok := data["zwsc_response"]; ok {
		rawData.ZwscResponse = parseString(v)
	}
	if v, ok := data["data_source"]; ok {
		rawData.DataSource = parseString(v)
	}
	if v, ok := data["created_at"]; ok {
		rawData.CreatedAt = parseTime(v)
	}
	if v, ok := data["updated_at"]; ok {
		rawData.UpdatedAt = parseTime(v)
	}
}
