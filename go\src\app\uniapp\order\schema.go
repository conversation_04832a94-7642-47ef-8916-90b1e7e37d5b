package order

import "fincore/utils/jsonschema"

// floatPtr 返回float64指针，用于jsonschema的Min/Max字段
func floatPtr(f float64) *float64 {
	return &f
}

// GetRepaymentPreviewSchema 还款计划预览参数验证规则
func GetRepaymentPreviewSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "还款计划预览参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"product_rule_id": {
				Type:        "string",
				Required:    true,
				Pattern:     "^[1-9]\\d*$",
				Description: "产品规则ID（必填，正整数）",
			},
		},
		Required: []string{"product_rule_id"},
	}
}

// GetOrderBillsSchema 订单账单查询参数验证规则
func GetOrderBillsSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "订单账单查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"order_id": {
				Type:        "string",
				Required:    true,
				Pattern:     "^[1-9]\\d*$",
				Description: "订单ID（必填，正整数）",
			},
		},
		Required: []string{"order_id"},
	}
}

// GetOrderCreateSchema 创建订单的参数验证规则
func GetOrderCreateSchema() jsonschema.Schema {
	minVal := 0.01
	maxVal := 999999999.99

	return jsonschema.Schema{
		Title: "创建订单参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"channel_code": {
				Type:        "string",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "渠道编码",
			},
			"product_rule_id": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "产品规则ID（可选，系统将根据风控评分自动匹配）",
			},
			"loan_amount": {
				Type:        "number",
				Required:    true,
				Min:         &minVal,
				Max:         &maxVal,
				Description: "申请贷款金额",
			},
			"customer_origin": {
				Type:        "string",
				Required:    true,
				MaxLength:   50,
				Description: "客户来源",
				Default:     "",
			},
			"initial_order_channel_id": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "初始下单渠道ID",
			},
		},
		Required: []string{"channel_code", "loan_amount", "product_rule_id", "customer_origin"},
	}
}

// GetCheckCanCreateSchema 检查是否可创建订单的参数验证规则
func GetCheckCanCreateSchema() jsonschema.Schema {
	minVal := 0.01
	maxVal := 999999999.99

	return jsonschema.Schema{
		Title: "检查是否可创建订单参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"loan_amount": {
				Type:        "number",
				Required:    true,
				Min:         &minVal,
				Max:         &maxVal,
				Description: "申请贷款金额",
			},
		},
		Required: []string{"loan_amount"},
	}
}
