package scheduler

import (
	"fincore/app/scheduler/registry"
	"fincore/app/scheduler/tasks"
	"fincore/utils/log"
	"fmt"
	"sync"

	"go.uber.org/zap"
)

// 全局调度器管理器实例
var (
	globalManager *SchedulerManager
	initMutex     sync.Mutex
	logger        *log.Logger
)

// Initialize 初始化调度器模块
func Initialize() error {
	initMutex.Lock()
	defer initMutex.Unlock()

	if globalManager != nil {
		return fmt.Errorf("调度器已经初始化")
	}

	// 初始化日志器
	logger = log.GetModule("scheduler")
	logger.Info("正在初始化调度器模块")

	// 创建调度器管理器
	manager, err := NewSchedulerManager()
	if err != nil {
		logger.Error("创建调度器管理器失败", zap.Error(err))
		return fmt.Errorf("创建调度器管理器失败: %w", err)
	}

	globalManager = manager

	// 注册内置任务
	if err := registerBuiltinTasks(); err != nil {
		logger.Error("注册内置任务失败", zap.Error(err))
		return fmt.Errorf("注册内置任务失败: %w", err)
	}

	// 初始化任务
	InitializeTasks()

	logger.Info("调度器模块初始化成功")
	return nil
}

// Start 启动调度器
func Start() error {
	if globalManager == nil {
		return fmt.Errorf("调度器未初始化，请先调用 Initialize()")
	}

	logger.Info("正在启动调度器")

	if err := globalManager.Start(); err != nil {
		logger.Error("启动调度器失败", zap.Error(err))
		return err
	}

	logger.Info("调度器启动成功")
	return nil
}

// Stop 停止调度器
func Stop() error {
	if globalManager == nil {
		return fmt.Errorf("调度器未初始化")
	}

	logger.Info("正在停止调度器")

	if err := globalManager.Stop(); err != nil {
		logger.Error("停止调度器失败", zap.Error(err))
		return err
	}

	logger.Info("调度器已停止")
	return nil
}

// GracefulStop 优雅停止调度器
func GracefulStop() error {
	if globalManager == nil {
		return fmt.Errorf("调度器未初始化")
	}

	if err := globalManager.GracefulStop(); err != nil {
		logger.Error("停止调度器失败", zap.Error(err))
		return err
	}

	logger.Info("任务调度器已停止")
	return nil
}

// Restart 重启调度器
func Restart() error {
	if globalManager == nil {
		return fmt.Errorf("调度器未初始化")
	}

	return globalManager.Restart()
}

// RegisterTask 注册任务
func RegisterTask(task tasks.TaskInterface) error {
	if globalManager == nil {
		return fmt.Errorf("调度器未初始化，请先调用 Initialize()")
	}

	return globalManager.RegisterTask(task)
}

// RegisterTasks 批量注册任务
func RegisterTasks(tasks ...tasks.TaskInterface) *registry.DiscoveryResult {
	if globalManager == nil {
		logger.Error("调度器未初始化，无法注册任务")
		return &registry.DiscoveryResult{
			TotalFound:     len(tasks),
			FailureCount:   len(tasks),
			FailureReasons: []string{"调度器未初始化"},
		}
	}

	return globalManager.RegisterTasks(tasks...)
}

// UnregisterTask 注销任务
func UnregisterTask(taskName string) error {
	if globalManager == nil {
		return fmt.Errorf("调度器未初始化")
	}

	return globalManager.UnregisterTask(taskName)
}

// GetStatus 获取调度器状态
func GetStatus() *ManagerStatus {
	if globalManager == nil {
		return &ManagerStatus{
			IsStarted:     false,
			SchedulerName: "未初始化",
		}
	}

	return globalManager.GetStatus()
}

// GetTaskStatus 获取任务状态
func GetTaskStatus(taskName string) (map[string]interface{}, error) {
	if globalManager == nil {
		return nil, fmt.Errorf("调度器未初始化")
	}

	return globalManager.GetTaskStatus(taskName)
}

// GetAllTasksStatus 获取所有任务状态
func GetAllTasksStatus() map[string]interface{} {
	if globalManager == nil {
		return map[string]interface{}{
			"error": "调度器未初始化",
		}
	}

	return globalManager.GetAllTasksStatus()
}

// CancelTask 取消任务执行
func CancelTask(taskName string) error {
	if globalManager == nil {
		return fmt.Errorf("调度器未初始化")
	}

	return globalManager.CancelTask(taskName)
}

// ForceKillTask 强制结束任务
func ForceKillTask(taskName string) error {
	if globalManager == nil {
		return fmt.Errorf("调度器未初始化")
	}

	return globalManager.ForceKillTask(taskName)
}

// IsRunning 检查调度器是否运行中
func IsRunning() bool {
	if globalManager == nil {
		return false
	}

	return globalManager.IsRunning()
}

// GetHealthCheck 获取健康检查信息
func GetHealthCheck() map[string]interface{} {
	if globalManager == nil {
		return map[string]interface{}{
			"status":     "not_initialized",
			"message":    "调度器未初始化",
			"is_running": false,
		}
	}

	return globalManager.GetHealthCheck()
}

// GetManager 获取调度器管理器实例（用于高级操作）
func GetManager() *SchedulerManager {
	return globalManager
}

// registerBuiltinTasks 注册内置任务
func registerBuiltinTasks() error {
	logger.Info("正在注册内置任务")

	// 注册示例任务
	builtinTasks := []tasks.TaskInterface{
		// 可以在这里添加更多内置任务
	}

	// 如果没有内置任务，直接返回
	if len(builtinTasks) == 0 {
		logger.Info("没有内置任务需要注册")
		return nil
	}

	result := globalManager.RegisterTasks(builtinTasks...)

	// 打印注册结果
	globalManager.GetDiscovery().PrintDiscoveryReport(result)

	if result.FailureCount > 0 {
		return fmt.Errorf("注册内置任务失败，成功: %d, 失败: %d",
			result.SuccessCount, result.FailureCount)
	}

	logger.Info("内置任务注册完成",
		zap.Int("success_count", result.SuccessCount),
		zap.Strings("success_tasks", result.SuccessTasks),
	)

	return nil
}

// InitializeAndStart 初始化并启动调度器（便捷方法）
func InitializeAndStart() error {
	if err := Initialize(); err != nil {
		return err
	}

	return Start()
}

// StopAndCleanup 停止并清理调度器（便捷方法）
func StopAndCleanup() error {
	if err := Stop(); err != nil {
		return err
	}

	// 清理全局实例
	initMutex.Lock()
	globalManager = nil
	initMutex.Unlock()

	logger.Info("调度器已清理")
	return nil
}

// GetTaskNames 获取所有任务名称
func GetTaskNames() []string {
	if globalManager == nil {
		return []string{}
	}

	return globalManager.GetRegistry().GetTaskNames()
}

// GetTaskCount 获取任务数量
func GetTaskCount() int {
	if globalManager == nil {
		return 0
	}

	return globalManager.GetRegistry().GetTaskCount()
}

// HasTask 检查任务是否存在
func HasTask(taskName string) bool {
	if globalManager == nil {
		return false
	}

	return globalManager.GetRegistry().HasTask(taskName)
}

// GetTaskInfo 获取任务详细信息
func GetTaskInfo(taskName string) (map[string]interface{}, error) {
	if globalManager == nil {
		return nil, fmt.Errorf("调度器未初始化")
	}

	return globalManager.GetDiscovery().GetTaskInfo(taskName)
}

// GetAllTasksInfo 获取所有任务的详细信息
func GetAllTasksInfo() map[string]interface{} {
	if globalManager == nil {
		return map[string]interface{}{
			"error": "调度器未初始化",
		}
	}

	return globalManager.GetDiscovery().GetAllTasksInfo()
}

// ValidateTasks 验证所有任务
func ValidateTasks() error {
	if globalManager == nil {
		return fmt.Errorf("调度器未初始化")
	}

	return globalManager.GetRegistry().ValidateAllTasks()
}

// CheckTaskConflicts 检查任务冲突
func CheckTaskConflicts() []string {
	if globalManager == nil {
		return []string{"调度器未初始化"}
	}

	return globalManager.GetDiscovery().CheckTaskConflicts()
}

// GetRegistryStats 获取注册中心统计信息
func GetRegistryStats() map[string]interface{} {
	if globalManager == nil {
		return map[string]interface{}{
			"error": "调度器未初始化",
		}
	}

	return globalManager.GetRegistry().GetRegistryStats()
}

// GetExecutorStats 获取执行器统计信息
func GetExecutorStats() map[string]interface{} {
	if globalManager == nil {
		return map[string]interface{}{
			"error": "调度器未初始化",
		}
	}

	return globalManager.executor.GetExecutorStats()
}
