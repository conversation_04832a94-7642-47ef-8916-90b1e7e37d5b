package channel

import "fincore/utils/jsonschema"

// GetChannelListSchema 获取渠道列表的参数验证规则
func GetChannelListSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "渠道列表查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"channel_name": {
				Type:        "string",
				Required:    false,
				MaxLength:   100,
				Description: "渠道名称（模糊查询）",
			},
			"channel_code": {
				Type:        "string",
				Required:    false,
				MaxLength:   50,
				Description: "渠道编码",
			},
			"channel_status": {
				Type:        "number",
				Required:    false,
				Enum:        []string{"0", "1"},
				Description: "渠道状态：0-禁用，1-启用",
			},

			"mobile": {
				Type:        "string",
				Required:    false,
				MaxLength:   20,
				Description: "手机号（模糊查询）",
			},
			"page": {
				Type:        "number",
				Required:    true,
				Description: "页码",
				Default:     1,
			},
			"pageSize": {
				Type:        "number",
				Required:    true,
				Description: "每页数量",
				Default:     10,
			},
			"start_time": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "开始时间（YYYY-MM-DD）",
			},
			"end_time": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "结束时间（YYYY-MM-DD）",
			},
		},
		Required: []string{},
	}
}

// GetChannelCreateSchema 创建渠道的参数验证规则
func GetChannelCreateSchema() jsonschema.Schema {
	minVal := 0.0
	maxVal := 999999999.99

	return jsonschema.Schema{
		Title: "创建渠道参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"channel_name": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				MaxLength:   100,
				Description: "渠道名称",
			},
			"channel_code": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				MaxLength:   50,
				Description: "渠道编码",
			},

			"mobile": {
				Type:        "string",
				Required:    true,
				MaxLength:   20,
				Pattern:     "^1[3-9]\\d{9}$",
				Description: "手机号",
			},
			"risk_level": {
				Type:        "number",
				Required:    false,
				Enum:        []string{"0", "1"},
				Description: "风控级别：0-紧，1-松",
				Default:     0,
			},
			"auto_label": {
				Type:        "number",
				Required:    false,
				Enum:        []string{"0", "1"},
				Description: "自动标签：0-自动放款，1-手动放款",
				Default:     0,
			},
			"channel_status": {
				Type:        "number",
				Required:    true,
				Enum:        []string{"0", "1"},
				Description: "渠道状态：0-禁用，1-启用",
				Default:     0,
			},

			"loan_rules": {
				Type:        "string",
				Required:    false,
				Description: "放款规则JSON字符串",
			},
			"risk_control_1_limit": {
				Type:        "number",
				Required:    false,
				Min:         &minVal,
				Max:         &maxVal,
				Description: "续贷提额（策略1）",
				Default:     500.00,
			},
			"risk_control_1_upper": {
				Type:        "number",
				Required:    false,
				Min:         &minVal,
				Max:         &maxVal,
				Description: "续贷规则(策略1)额度上限",
				Default:     0.00,
			},
			"risk_control_2_limit": {
				Type:        "number",
				Required:    false,
				Min:         &minVal,
				Max:         &maxVal,
				Description: "续贷提额（策略2）",
				Default:     1000.00,
			},
			"risk_control_2_upper": {
				Type:        "number",
				Required:    false,
				Min:         &minVal,
				Max:         &maxVal,
				Description: "续贷规则(策略2)额度上限",
				Default:     0.00,
			},
			"point_amount_1": {
				Type:        "number",
				Required:    false,
				Min:         &minVal,
				Max:         &maxVal,
				Description: "节点额度",
			},
			"point_amount_2": {
				Type:        "number",
				Required:    false,
				Min:         &minVal,
				Max:         &maxVal,
				Description: "节点额度(策略2)",
			},
			"total_amount": {
				Type:        "number",
				Required:    false,
				Min:         &minVal,
				Max:         &maxVal,
				Description: "封顶额度",
				Default:     8000.00,
			},
			"remark": {
				Type:        "string",
				Required:    false,
				MaxLength:   1000,
				Description: "备注",
			},
		},
		Required: []string{"channel_name", "mobile", "channel_status"},
	}
}

// GetChannelUpdateSchema 更新渠道的参数验证规则
func GetChannelUpdateSchema() jsonschema.Schema {
	minVal := 0.0
	maxVal := 999999999.99

	return jsonschema.Schema{
		Title: "更新渠道参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"id": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "渠道ID",
			},
			"channel_name": {
				Type:        "string",
				Required:    false,
				MinLength:   1,
				MaxLength:   100,
				Description: "渠道名称",
			},
			"mobile": {
				Type:        "string",
				Required:    false,
				MaxLength:   20,
				Pattern:     "^1[3-9]\\d{9}$",
				Description: "手机号",
			},
			"risk_level": {
				Type:        "number",
				Required:    false,
				Enum:        []string{"0", "1"},
				Description: "风控级别：0-紧，1-松",
			},
			"auto_label": {
				Type:        "number",
				Required:    false,
				Enum:        []string{"0", "1"},
				Description: "自动标签：0-自动放款，1-手动放款",
			},
			"channel_status": {
				Type:        "number",
				Required:    false,
				Enum:        []string{"0", "1"},
				Description: "渠道状态：0-禁用，1-启用",
			},
			"loan_rules": {
				Type:        "string",
				Required:    false,
				Description: "放款规则JSON字符串",
			},
			"auto_disbursement": {
				Type:        "number",
				Required:    false,
				Enum:        []string{"0", "1"},
				Description: "是否自动放款：0-否(人工审核)，1-是(自动放款)",
			},
			"risk_control_1_limit": {
				Type:        "number",
				Required:    false,
				Min:         &minVal,
				Max:         &maxVal,
				Description: "续贷提额（策略1）",
			},
			"risk_control_2_limit": {
				Type:        "number",
				Required:    false,
				Min:         &minVal,
				Max:         &maxVal,
				Description: "续贷提额（策略2）",
			},
			"point_amount_1": {
				Type:        "number",
				Required:    false,
				Min:         &minVal,
				Max:         &maxVal,
				Description: "节点额度",
			},
			"total_amount": {
				Type:        "number",
				Required:    false,
				Min:         &minVal,
				Max:         &maxVal,
				Description: "封顶额度",
			},
			"remark": {
				Type:        "string",
				Required:    false,
				MaxLength:   1000,
				Description: "备注",
			},
		},
		Required: []string{"id"},
	}
}

// GetLoanRuleSchema 放款规则验证规则
func GetLoanRuleSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "放款规则参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"rule_id": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "产品规则ID",
			},
			"min_risk_score": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{0}[0],
				Max:         &[]float64{100}[0],
				Description: "风控阈值下限（0-100）",
			},
			"max_risk_score": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{0}[0],
				Max:         &[]float64{100}[0],
				Description: "风控阈值上限（0-100）",
			},
		},
		Required: []string{"rule_id", "min_risk_score", "max_risk_score"},
	}
}

// GetChannelInvitationSchema 生成渠道邀请链接的参数验证规则
func GetChannelInvitationSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "生成渠道邀请链接参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"channel_id": {
				Type:        "number",
				Required:    true,
				Min:         &[]float64{1}[0],
				Description: "渠道ID",
			},
		},
		Required: []string{"channel_id"},
	}
}

// GetChannelStatusSchema 检查渠道状态的参数验证规则
func GetChannelStatusSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "检查渠道状态参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"channel_code": {
				Type:        "string",
				Required:    true,
				MinLength:   1,
				MaxLength:   50,
				Description: "渠道编码",
			},
		},
		Required: []string{"channel_code"},
	}
}
