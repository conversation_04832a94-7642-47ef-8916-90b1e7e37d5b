export default {
  // Menu related
  'menu.channel': 'Channel Management',
  'menu.channel.list': 'Channel List',
  
  // Common operations
  'channel.operation.create': 'Add Channel',
  'channel.operation.edit': 'Edit Channel',
  'channel.operation.view': 'View Channel',
  'channel.operation.delete': 'Delete Channel',
  'channel.operation.loanRules': 'Loan Rules',
  
  // Table columns
  'channel.columns.id': 'ID',
  'channel.columns.channelName': 'Channel Name',
  'channel.columns.channelCode': 'Channel Code',
  'channel.columns.domain': 'Domain',
  'channel.columns.mobile': 'Mobile',
  'channel.columns.channelStatus': 'Channel Status',
  'channel.columns.channelUsage': 'Usage Status',
  'channel.columns.createTime': 'Create Time',
  'channel.columns.operations': 'Operations',
  
  // Status
  'channel.status.enabled': 'Enabled',
  'channel.status.disabled': 'Disabled',
  'channel.usage.normal': 'Normal',
  'channel.usage.maintenance': 'Maintenance',
  
  // Form
  'channel.form.channelName': 'Channel Name',
  'channel.form.channelCode': 'Channel Code',
  'channel.form.domain': 'Domain',
  'channel.form.mobile': 'Mobile',
  'channel.form.channelStatus': 'Channel Status',
  'channel.form.channelUsage': 'Usage Status',
  'channel.form.channelName.placeholder': 'Please enter channel name',
  'channel.form.channelCode.placeholder': 'Please enter channel code',
  'channel.form.domain.placeholder': 'Please enter domain',
  'channel.form.mobile.placeholder': 'Please enter mobile number',
  
  // Validation messages
  'channel.form.channelName.required': 'Please enter channel name',
  'channel.form.channelCode.required': 'Please enter channel code',
  'channel.form.domain.required': 'Please enter domain',
  'channel.form.mobile.required': 'Please enter mobile number',
  'channel.form.mobile.pattern': 'Please enter a valid mobile number format',
  
  // Message prompts
  'channel.message.createSuccess': 'Channel created successfully',
  'channel.message.updateSuccess': 'Channel updated successfully',
  'channel.message.deleteSuccess': 'Channel deleted successfully',
  'channel.message.deleteConfirm': 'Are you sure you want to delete this channel?',
  'channel.message.generateCodeSuccess': 'Channel code generated successfully',
  
  // Search form
  'channel.search.channelName': 'Channel Name',
  'channel.search.channelCode': 'Channel Code',
  'channel.search.channelStatus': 'Channel Status',
  'channel.search.placeholder.channelName': 'Please enter channel name',
  'channel.search.placeholder.channelCode': 'Please enter channel code',
  'channel.search.placeholder.all': 'All',
  
  // Loan rules
  'channel.loanRules.title': 'Loan Rules Management',
  'channel.loanRules.ruleId': 'Rule ID',
  'channel.loanRules.minRiskScore': 'Min Risk Score',
  'channel.loanRules.maxRiskScore': 'Max Risk Score',
  'channel.loanRules.addRule': 'Add Rule',
  'channel.loanRules.saveSuccess': 'Rules saved successfully',
}; 