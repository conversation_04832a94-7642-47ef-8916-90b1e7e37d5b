package thirdparty

import (
	"context"
	"fmt"

	"fincore/thirdparty/auth"
	"fincore/thirdparty/client"
	"fincore/thirdparty/error_handler"
	"fincore/thirdparty/service"
	"fincore/thirdparty/types"
)

// SMSService 短信服务
type SMSService struct {
	*service.BaseService
}

// NewSMSService 创建短信服务
func NewSMSService(config types.ServiceConfig) (*SMSService, error) {
	// 创建账号密码认证器
	authenticator := auth.NewPasswordAuth(
		config.GetString("username"),
		config.GetString("password"),
	)

	// 创建错误映射器
	errorMapper := error_handler.NewStandardErrorMapper()
	// 添加短信服务特有的错误映射
	errorMapper.AddErrorMapping("SMS001", error_handler.ErrorInfo{
		Code: "3001", Message: "手机号格式错误", Retryable: false,
	})
	errorMapper.AddErrorMapping("SMS002", error_handler.ErrorInfo{
		Code: "3002", Message: "短信余额不足", Retryable: false,
	})

	// 创建HTTP客户端
	clientConfig := &client.ClientConfig{
		BaseURL:       config.GetString("base_url"),
		Timeout:       config.GetDuration("timeout"),
		Authenticator: authenticator,
		ErrorMapper:   errorMapper,
	}

	httpClient := client.NewHTTPClient(clientConfig)

	// 创建基础服务
	baseService := service.NewBaseService("sms", "1.0.0", httpClient, config)

	smsService := &SMSService{
		BaseService: baseService,
	}

	// 添加支持的方法
	smsService.AddMethod("SendSMS")
	smsService.AddMethod("QuerySMSStatus")
	smsService.AddMethod("GetBalance")

	return smsService, nil
}

// Initialize 初始化服务
func (s *SMSService) Initialize(config types.ServiceConfig) error {
	// 验证必要的配置项
	requiredFields := []string{"base_url", "username", "password"}
	for _, field := range requiredFields {
		if config.GetString(field) == "" {
			return fmt.Errorf("缺少必要配置项: %s", field)
		}
	}
	return nil
}

// Call 调用服务
func (s *SMSService) Call(ctx context.Context, method string, params map[string]interface{}) (*types.Response, error) {
	switch method {
	case "SendSMS":
		return s.sendSMS(ctx, params)
	case "QuerySMSStatus":
		return s.querySMSStatus(ctx, params)
	case "GetBalance":
		return s.getBalance(ctx, params)
	default:
		return nil, fmt.Errorf("不支持的方法: %s", method)
	}
}

// sendSMS 发送短信
func (s *SMSService) sendSMS(ctx context.Context, params map[string]interface{}) (*types.Response, error) {
	req := &types.Request{
		Method: "POST",
		Path:   "/sms/send",
		Data:   params,
	}

	return s.GetClient().Do(ctx, req)
}

// querySMSStatus 查询短信状态
func (s *SMSService) querySMSStatus(ctx context.Context, params map[string]interface{}) (*types.Response, error) {
	req := &types.Request{
		Method: "GET",
		Path:   "/sms/status",
		QueryParams: map[string]string{
			"messageId": fmt.Sprintf("%v", params["messageId"]),
		},
	}

	return s.GetClient().Do(ctx, req)
}

// getBalance 查询余额
func (s *SMSService) getBalance(ctx context.Context, params map[string]interface{}) (*types.Response, error) {
	req := &types.Request{
		Method: "GET",
		Path:   "/account/balance",
	}

	return s.GetClient().Do(ctx, req)
}
