package bootstrap

import (
	"context"
	"fincore/app/scheduler"
	"fincore/global"
	"fincore/model"
	"fincore/route"
	"fincore/utils/gf"
	"fincore/utils/utilstool/goredis"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"runtime"
	"strings"
	"syscall"
	"time"
)

// 优雅重启/停止服务器
func RunServer() {
	//加载路由
	r := route.InitRouter()
	routes := ""
	for _, route := range r.Routes() {
		if !strings.Contains(route.Path, "/admin/") && route.Path != "/" && !strings.Contains(route.Path, "/*filepath") {
			routes = routes + fmt.Sprintf("%v\n", route.Path)
		}
	}
	filePath := "runtime/app/routers.txt"
	gf.WriteToFile(filePath, routes)
	model.MyInit(1) //初始化数据

	// 初始化 redis
	goredis.InitRedisClient()

	// 创建HTTP服务器（开发和生产环境都使用相同的优雅关闭机制）
	srv := &http.Server{
		Addr:    ":" + global.App.Config.App.Port,
		Handler: r,
	}

	if global.App.Config.App.Env == "dev" {
		fmt.Printf("\n %c[1;40;32m%s%c[0m\n", 0x1B, "在浏览器访问：​​http://127.0.0.1:"+global.App.Config.App.Port+"/common/install/index ​进行​安装​", 0x1B)
	}

	global.App.Log.Info("启动端口：" + global.App.Config.App.Port)

	// 在后台启动HTTP服务器
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			str := fmt.Sprintf("HTTP服务器启动失败: %s\n", err)
			global.App.Log.Error(str)
		}
	}()

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	// 基础信号
	signals := []os.Signal{os.Interrupt, syscall.SIGTERM}
	// 在非 Windows 系统上添加更多信号
	if runtime.GOOS != "windows" {
		signals = append(signals, syscall.SIGQUIT, syscall.SIGHUP)
	}

	signal.Notify(quit, signals...)
	<-quit
	global.App.Log.Info("程序正在退出运行...")

	// 1. 首先优雅停止定时任务调度器
	if err := scheduler.GracefulStop(); err != nil {
		global.App.Log.Error("停止定时任务调度器失败: " + err.Error())
	} else {
		global.App.Log.Info("定时任务调度器已停止")
	}

	// 2. 然后关闭HTTP服务器
	global.App.Log.Info("正在关闭HTTP服务器...")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		str := fmt.Sprintf("HTTP服务器关闭失败: %s\n", err)
		global.App.Log.Error(str)
	} else {
		global.App.Log.Info("HTTP服务器已关闭")
	}
}
