{"level":"dev.info","ts":"[2025-08-14 09:43:07.330]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":"1","page_size":"10"}}
{"level":"dev.info","ts":"[2025-08-14 09:43:07.377]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":111,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 09:43:16.638]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":"1","page_size":"10"}}
{"level":"dev.info","ts":"[2025-08-14 09:43:16.692]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":111,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 09:43:25.141]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":"1","page_size":"10"}}
{"level":"dev.info","ts":"[2025-08-14 09:43:25.191]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":111,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 09:43:36.114]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"fund_type":"收款","page":"1","page_size":"5","payment_method":"担保代扣"}}
{"level":"dev.info","ts":"[2025-08-14 09:43:36.179]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":1,"page":1,"pageSize":5}
{"level":"dev.info","ts":"[2025-08-14 09:44:28.301]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":750,"disbursement_customer_count":1,"disbursement_order_count":12,"due_amount":238.52,"due_repayment_amount":201.68,"due_repayment_rate":84.56,"due_funds_recovery_rate":80.63,"overdue_customer_count":1,"overdue_amount":0,"duration":"52.33ms"}
{"level":"dev.info","ts":"[2025-08-14 09:44:39.270]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":750,"disbursement_customer_count":1,"disbursement_order_count":12,"due_amount":238.52,"due_repayment_amount":201.68,"due_repayment_rate":84.56,"due_funds_recovery_rate":80.63,"overdue_customer_count":1,"overdue_amount":0,"duration":"41.6889ms"}
{"level":"dev.info","ts":"[2025-08-14 09:45:28.417]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":750,"disbursement_customer_count":1,"disbursement_order_count":12,"due_amount":238.52,"due_repayment_amount":201.68,"due_repayment_rate":84.56,"due_funds_recovery_rate":80.63,"overdue_customer_count":1,"overdue_amount":0,"duration":"7.6009101s"}
{"level":"dev.info","ts":"[2025-08-14 09:46:32.443]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":0,"disbursement_customer_count":0,"disbursement_order_count":0,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"4.5241537s"}
{"level":"dev.info","ts":"[2025-08-14 09:46:45.277]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":0,"disbursement_customer_count":0,"disbursement_order_count":0,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"35.6498ms"}
{"level":"dev.info","ts":"[2025-08-14 09:46:51.226]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":0,"disbursement_customer_count":0,"disbursement_order_count":0,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"65.8202ms"}
{"level":"dev.info","ts":"[2025-08-14 09:47:00.996]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":0,"disbursement_customer_count":0,"disbursement_order_count":0,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"62.6907ms"}
{"level":"dev.info","ts":"[2025-08-14 09:47:04.697]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":0,"disbursement_customer_count":0,"disbursement_order_count":0,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"46.23ms"}
{"level":"dev.info","ts":"[2025-08-14 09:47:13.335]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":0,"disbursement_customer_count":0,"disbursement_order_count":0,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"29.6574ms"}
{"level":"dev.info","ts":"[2025-08-14 09:47:14.477]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":0,"disbursement_customer_count":0,"disbursement_order_count":0,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"25.8199ms"}
{"level":"dev.info","ts":"[2025-08-14 09:47:38.485]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":0,"disbursement_customer_count":0,"disbursement_order_count":0,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"43.304ms"}
{"level":"dev.info","ts":"[2025-08-14 09:48:05.117]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":0,"disbursement_customer_count":0,"disbursement_order_count":0,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"20.440155s"}
{"level":"dev.info","ts":"[2025-08-14 09:49:59.331]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":0,"disbursement_customer_count":0,"disbursement_order_count":0,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"98.3964ms"}
{"level":"dev.info","ts":"[2025-08-14 09:50:00.703]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":0,"disbursement_customer_count":0,"disbursement_order_count":0,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"39.6386ms"}
{"level":"dev.info","ts":"[2025-08-14 09:50:44.653]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":0,"disbursement_customer_count":0,"disbursement_order_count":0,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"37.1040026s"}
{"level":"dev.info","ts":"[2025-08-14 09:59:18.125]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":650,"disbursement_customer_count":1,"disbursement_order_count":10,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"11.6643699s"}
{"level":"dev.info","ts":"[2025-08-14 09:59:57.978]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":650,"disbursement_customer_count":1,"disbursement_order_count":10,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"94.2258ms"}
{"level":"dev.info","ts":"[2025-08-14 09:59:59.310]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":650,"disbursement_customer_count":1,"disbursement_order_count":10,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"58.8433ms"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.001]","caller":"statistics/service.go:39","msg":"开始执行渠道统计任务"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.002]","caller":"statistics/service.go:43","msg":"统计时间范围","start_time":"2025-08-14 00:00:00","end_time":"2025-08-14 23:59:59"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.047]","caller":"statistics/service.go:60","msg":"找到启用渠道","channel_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.047]","caller":"statistics/service.go:109","msg":"开始统计渠道数据","channel_id":1,"channel_name":"qd0805"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.047]","caller":"statistics/service.go:109","msg":"开始统计渠道数据","channel_id":3,"channel_name":"qd0805"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.047]","caller":"statistics/service.go:109","msg":"开始统计渠道数据","channel_id":2,"channel_name":"qd0806_2"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.093]","caller":"statistics/service.go:132","msg":"渠道统计数据","channel_id":2,"channel_name":"qd0806_2","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.093]","caller":"statistics/service.go:132","msg":"渠道统计数据","channel_id":1,"channel_name":"qd0805","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.093]","caller":"statistics/service.go:132","msg":"渠道统计数据","channel_id":3,"channel_name":"qd0805","new_user_count":0,"real_name_count":0,"transaction_count":0}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.124]","caller":"statistics/service.go:147","msg":"渠道统计完成","channel_id":2,"channel_name":"qd0806_2"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.131]","caller":"statistics/service.go:147","msg":"渠道统计完成","channel_id":3,"channel_name":"qd0805"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.132]","caller":"statistics/service.go:147","msg":"渠道统计完成","channel_id":1,"channel_name":"qd0805"}
{"level":"dev.info","ts":"[2025-08-14 10:00:00.132]","caller":"statistics/service.go:100","msg":"渠道统计任务执行完成","success_channel_count":3}
{"level":"dev.info","ts":"[2025-08-14 10:00:03.970]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":700,"disbursement_customer_count":1,"disbursement_order_count":11,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"34.7312ms"}
{"level":"dev.info","ts":"[2025-08-14 10:00:05.184]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":700,"disbursement_customer_count":1,"disbursement_order_count":11,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"56.1224ms"}
{"level":"dev.info","ts":"[2025-08-14 10:00:10.572]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":750,"disbursement_customer_count":1,"disbursement_order_count":12,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"54.2311ms"}
{"level":"dev.info","ts":"[2025-08-14 10:10:56.300]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":650,"disbursement_customer_count":1,"disbursement_order_count":10,"due_amount":0,"due_repayment_amount":0,"due_repayment_rate":0,"due_funds_recovery_rate":0,"overdue_customer_count":1,"overdue_amount":0,"duration":"66.1276ms"}
{"level":"dev.info","ts":"[2025-08-14 10:11:09.172]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":0,"disbursement_customer_count":0,"disbursement_order_count":0,"due_amount":80.6,"due_repayment_amount":80.6,"due_repayment_rate":100,"due_funds_recovery_rate":69.05,"overdue_customer_count":1,"overdue_amount":0,"duration":"25.8861ms"}
{"level":"dev.info","ts":"[2025-08-14 10:11:13.451]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":700,"disbursement_customer_count":1,"disbursement_order_count":11,"due_amount":172.7,"due_repayment_amount":135.86,"due_repayment_rate":78.67,"due_funds_recovery_rate":67.89,"overdue_customer_count":1,"overdue_amount":0,"duration":"36.4221ms"}
{"level":"dev.info","ts":"[2025-08-14 10:11:18.279]","caller":"statistics/service.go:384","msg":"首页统计数据获取完成","disbursement_amount":50,"disbursement_customer_count":1,"disbursement_order_count":1,"due_amount":172.7,"due_repayment_amount":135.86,"due_repayment_rate":78.67,"due_funds_recovery_rate":67.89,"overdue_customer_count":1,"overdue_amount":0,"duration":"46.061ms"}
{"level":"dev.info","ts":"[2025-08-14 10:56:24.300]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 10:56:24.361]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":111,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 10:59:01.919]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 10:59:01.958]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":111,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:01:43.491]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 11:02:18.875]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 11:02:18.953]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":111,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:03:25.536]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 11:06:08.143]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 11:06:08.235]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":111,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:06:24.934]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 11:06:59.917]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 11:07:03.078]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":92,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:08:10.740]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 11:08:10.809]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":92,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:08:45.479]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 11:08:45.529]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":92,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:08:54.658]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 11:08:54.708]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":92,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:09:18.453]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 11:10:27.383]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":"3","page_size":"10"}}
{"level":"dev.info","ts":"[2025-08-14 11:11:00.523]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":92,"page":3,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:11:11.820]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":"2","page_size":"10"}}
{"level":"dev.info","ts":"[2025-08-14 11:11:17.439]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":92,"page":2,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:11:23.708]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":"5","page_size":"10"}}
{"level":"dev.info","ts":"[2025-08-14 11:11:23.765]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":92,"page":5,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:17:48.625]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":"5","page_size":"10","payment_method":"0"}}
{"level":"dev.info","ts":"[2025-08-14 11:17:58.254]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":40,"page":4,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:18:48.345]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10,"payment_method":"0"}}
{"level":"dev.info","ts":"[2025-08-14 11:18:48.388]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":40,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:19:21.435]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10,"payment_method":"1"}}
{"level":"dev.error","ts":"[2025-08-14 11:19:21.436]","caller":"statistics/service.go:597","msg":"获取收入明细列表失败","error":"查询收入明细列表失败: 获取总记录数失败: where data format is wrong","stacktrace":"fincore/app/business/statistics.(*Service).GetIncomeDetailsList.func2\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:597\ngolang.org/x/sync/errgroup.(*Group).add.func1\n\tC:/Users/<USER>/go/pkg/mod/golang.org/x/sync@v0.15.0/errgroup/errgroup.go:128"}
{"level":"dev.error","ts":"[2025-08-14 11:19:21.436]","caller":"statistics/service.go:586","msg":"获取收入统计数据失败","error":"查询收入统计数据失败: where data format is wrong","stacktrace":"fincore/app/business/statistics.(*Service).GetIncomeDetailsList.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:586\ngolang.org/x/sync/errgroup.(*Group).add.func1\n\tC:/Users/<USER>/go/pkg/mod/golang.org/x/sync@v0.15.0/errgroup/errgroup.go:128"}
{"level":"dev.info","ts":"[2025-08-14 11:19:26.478]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10,"payment_method":"1"}}
{"level":"dev.error","ts":"[2025-08-14 11:19:26.478]","caller":"statistics/service.go:586","msg":"获取收入统计数据失败","error":"查询收入统计数据失败: where data format is wrong","stacktrace":"fincore/app/business/statistics.(*Service).GetIncomeDetailsList.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:586\ngolang.org/x/sync/errgroup.(*Group).add.func1\n\tC:/Users/<USER>/go/pkg/mod/golang.org/x/sync@v0.15.0/errgroup/errgroup.go:128"}
{"level":"dev.error","ts":"[2025-08-14 11:19:26.478]","caller":"statistics/service.go:597","msg":"获取收入明细列表失败","error":"查询收入明细列表失败: 获取总记录数失败: where data format is wrong","stacktrace":"fincore/app/business/statistics.(*Service).GetIncomeDetailsList.func2\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:597\ngolang.org/x/sync/errgroup.(*Group).add.func1\n\tC:/Users/<USER>/go/pkg/mod/golang.org/x/sync@v0.15.0/errgroup/errgroup.go:128"}
{"level":"dev.info","ts":"[2025-08-14 11:19:34.008]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10,"payment_method":"1"}}
{"level":"dev.error","ts":"[2025-08-14 11:19:54.735]","caller":"statistics/service.go:586","msg":"获取收入统计数据失败","error":"查询收入统计数据失败: where data format is wrong","stacktrace":"fincore/app/business/statistics.(*Service).GetIncomeDetailsList.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:586\ngolang.org/x/sync/errgroup.(*Group).add.func1\n\tC:/Users/<USER>/go/pkg/mod/golang.org/x/sync@v0.15.0/errgroup/errgroup.go:128"}
{"level":"dev.error","ts":"[2025-08-14 11:19:54.735]","caller":"statistics/service.go:597","msg":"获取收入明细列表失败","error":"查询收入明细列表失败: 获取总记录数失败: where data format is wrong","stacktrace":"fincore/app/business/statistics.(*Service).GetIncomeDetailsList.func2\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:597\ngolang.org/x/sync/errgroup.(*Group).add.func1\n\tC:/Users/<USER>/go/pkg/mod/golang.org/x/sync@v0.15.0/errgroup/errgroup.go:128"}
{"level":"dev.info","ts":"[2025-08-14 11:20:05.835]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10,"payment_method":"1"}}
{"level":"dev.error","ts":"[2025-08-14 11:20:05.836]","caller":"statistics/service.go:597","msg":"获取收入明细列表失败","error":"查询收入明细列表失败: 获取总记录数失败: where data format is wrong","stacktrace":"fincore/app/business/statistics.(*Service).GetIncomeDetailsList.func2\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:597\ngolang.org/x/sync/errgroup.(*Group).add.func1\n\tC:/Users/<USER>/go/pkg/mod/golang.org/x/sync@v0.15.0/errgroup/errgroup.go:128"}
{"level":"dev.error","ts":"[2025-08-14 11:20:05.836]","caller":"statistics/service.go:586","msg":"获取收入统计数据失败","error":"查询收入统计数据失败: where data format is wrong","stacktrace":"fincore/app/business/statistics.(*Service).GetIncomeDetailsList.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:586\ngolang.org/x/sync/errgroup.(*Group).add.func1\n\tC:/Users/<USER>/go/pkg/mod/golang.org/x/sync@v0.15.0/errgroup/errgroup.go:128"}
{"level":"dev.info","ts":"[2025-08-14 11:20:43.834]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10,"payment_method":"1"}}
{"level":"dev.info","ts":"[2025-08-14 11:23:28.484]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10,"payment_method":"1"}}
{"level":"dev.error","ts":"[2025-08-14 11:23:32.690]","caller":"statistics/service.go:586","msg":"获取收入统计数据失败","error":"查询收入统计数据失败: where data format is wrong","stacktrace":"fincore/app/business/statistics.(*Service).GetIncomeDetailsList.func1\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:586\ngolang.org/x/sync/errgroup.(*Group).add.func1\n\tC:/Users/<USER>/go/pkg/mod/golang.org/x/sync@v0.15.0/errgroup/errgroup.go:128"}
{"level":"dev.error","ts":"[2025-08-14 11:23:32.690]","caller":"statistics/service.go:597","msg":"获取收入明细列表失败","error":"查询收入明细列表失败: 获取总记录数失败: where data format is wrong","stacktrace":"fincore/app/business/statistics.(*Service).GetIncomeDetailsList.func2\n\tD:/work/code/fincore/go/src/app/business/statistics/service.go:597\ngolang.org/x/sync/errgroup.(*Group).add.func1\n\tC:/Users/<USER>/go/pkg/mod/golang.org/x/sync@v0.15.0/errgroup/errgroup.go:128"}
{"level":"dev.info","ts":"[2025-08-14 11:31:16.235]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10,"payment_method":"1"}}
{"level":"dev.info","ts":"[2025-08-14 11:31:16.307]","caller":"statistics/service.go:615","msg":"获取收入明细列表成功","total":4,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:51:40.562]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10,"payment_method":"1"}}
{"level":"dev.info","ts":"[2025-08-14 11:51:40.635]","caller":"statistics/service.go:613","msg":"获取收入明细列表成功","total":4,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:51:41.911]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10,"payment_method":"1"}}
{"level":"dev.info","ts":"[2025-08-14 11:51:41.948]","caller":"statistics/service.go:613","msg":"获取收入明细列表成功","total":4,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:54:04.109]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"fund_type":"1","page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 11:54:04.858]","caller":"statistics/service.go:613","msg":"获取收入明细列表成功","total":5,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:58:43.028]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"fund_type":"1","page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 11:59:08.788]","caller":"statistics/service.go:613","msg":"获取收入明细列表成功","total":5,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 11:59:52.956]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"fund_type":"1","page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 14:20:39.889]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 14:20:43.768]","caller":"statistics/service.go:613","msg":"获取收入明细列表成功","total":60,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 14:21:04.119]","caller":"statistics/service.go:563","msg":"开始获取收入明细列表","params":{"fund_type":"1","page":1,"page_size":10}}
{"level":"dev.info","ts":"[2025-08-14 14:21:04.159]","caller":"statistics/service.go:613","msg":"获取收入明细列表成功","total":4,"page":1,"pageSize":10}
{"level":"dev.info","ts":"[2025-08-14 16:21:43.478]","caller":"statistics/service.go:701","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 16:21:43.560]","caller":"statistics/service.go:773","msg":"渠道到期统计数据获取完成","duration":"82.0592ms","channels_count":1}
{"level":"dev.info","ts":"[2025-08-14 16:21:55.160]","caller":"statistics/service.go:701","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 16:21:55.223]","caller":"statistics/service.go:773","msg":"渠道到期统计数据获取完成","duration":"63.1159ms","channels_count":1}
{"level":"dev.info","ts":"[2025-08-14 16:22:03.620]","caller":"statistics/service.go:701","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 16:22:03.683]","caller":"statistics/service.go:773","msg":"渠道到期统计数据获取完成","duration":"62.9848ms","channels_count":0}
{"level":"dev.info","ts":"[2025-08-14 16:28:08.551]","caller":"statistics/service.go:701","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 16:28:08.604]","caller":"statistics/service.go:783","msg":"渠道到期统计数据获取完成","duration":"52.7949ms","channels_count":1}
{"level":"dev.info","ts":"[2025-08-14 16:33:04.892]","caller":"statistics/service.go:701","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 16:33:04.966]","caller":"statistics/service.go:783","msg":"渠道到期统计数据获取完成","duration":"74.3205ms","channels_count":1}
{"level":"dev.info","ts":"[2025-08-14 16:44:52.040]","caller":"statistics/service.go:700","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 16:44:52.103]","caller":"statistics/service.go:791","msg":"渠道到期统计数据获取完成","duration":"63.1791ms","channels_count":1}
{"level":"dev.info","ts":"[2025-08-14 16:51:35.828]","caller":"statistics/service.go:700","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 16:51:35.928]","caller":"statistics/service.go:791","msg":"渠道到期统计数据获取完成","duration":"99.8121ms","channels_count":1}
{"level":"dev.info","ts":"[2025-08-14 17:10:06.489]","caller":"statistics/service.go:700","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 17:10:06.622]","caller":"statistics/service.go:791","msg":"渠道到期统计数据获取完成","duration":"132.9454ms","channels_count":1}
{"level":"dev.info","ts":"[2025-08-14 17:29:04.759]","caller":"statistics/service.go:700","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 17:29:04.871]","caller":"statistics/service.go:791","msg":"渠道到期统计数据获取完成","duration":"112.4187ms","channels_count":1}
{"level":"dev.info","ts":"[2025-08-14 17:34:20.806]","caller":"statistics/service.go:700","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 17:34:20.860]","caller":"statistics/service.go:791","msg":"渠道到期统计数据获取完成","duration":"53.9034ms","channels_count":1}
{"level":"dev.info","ts":"[2025-08-14 17:40:05.700]","caller":"statistics/service.go:700","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 17:40:05.764]","caller":"statistics/service.go:791","msg":"渠道到期统计数据获取完成","duration":"63.9408ms","channels_count":1}
{"level":"dev.info","ts":"[2025-08-14 17:40:33.486]","caller":"statistics/service.go:700","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 17:40:33.544]","caller":"statistics/service.go:791","msg":"渠道到期统计数据获取完成","duration":"58.181ms","channels_count":1}
{"level":"dev.info","ts":"[2025-08-14 17:45:52.872]","caller":"statistics/service.go:700","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 17:45:52.940]","caller":"statistics/service.go:791","msg":"渠道到期统计数据获取完成","duration":"67.9254ms","channels_count":3}
{"level":"dev.info","ts":"[2025-08-14 17:47:29.556]","caller":"statistics/service.go:700","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 17:47:29.633]","caller":"statistics/service.go:791","msg":"渠道到期统计数据获取完成","duration":"77.2109ms","channels_count":2}
{"level":"dev.info","ts":"[2025-08-14 17:47:39.715]","caller":"statistics/service.go:700","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 17:47:39.736]","caller":"statistics/service.go:791","msg":"渠道到期统计数据获取完成","duration":"20.7887ms","channels_count":2}
{"level":"dev.info","ts":"[2025-08-14 17:54:32.479]","caller":"statistics/service.go:700","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 17:54:32.535]","caller":"statistics/service.go:791","msg":"渠道到期统计数据获取完成","duration":"55.9001ms","channels_count":0}
{"level":"dev.info","ts":"[2025-08-14 17:54:41.217]","caller":"statistics/service.go:700","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 17:54:41.249]","caller":"statistics/service.go:791","msg":"渠道到期统计数据获取完成","duration":"31.9516ms","channels_count":2}
{"level":"dev.info","ts":"[2025-08-14 17:54:44.844]","caller":"statistics/service.go:700","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 17:54:44.935]","caller":"statistics/service.go:791","msg":"渠道到期统计数据获取完成","duration":"91.0976ms","channels_count":2}
{"level":"dev.info","ts":"[2025-08-14 18:00:35.425]","caller":"statistics/service.go:700","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 18:00:35.520]","caller":"statistics/service.go:791","msg":"渠道到期统计数据获取完成","duration":"94.43ms","channels_count":2}
{"level":"dev.info","ts":"[2025-08-14 18:00:41.187]","caller":"statistics/service.go:700","msg":"开始获取渠道到期统计数据"}
{"level":"dev.info","ts":"[2025-08-14 18:00:41.222]","caller":"statistics/service.go:791","msg":"渠道到期统计数据获取完成","duration":"35.148ms","channels_count":2}
