package identity

import (
	"context"
	"encoding/json"
	"fincore/app/business/risk"
	Config "fincore/app/dianziqian/config"
	Service "fincore/app/dianziqian/service"
	Utils "fincore/app/dianziqian/utils"
	"fincore/global"
	"fincore/utils/oss"
	"fmt"
	"os"
	"time"

	"fincore/model"
	"fincore/route/middleware"
	"fincore/utils/gf"
	"fincore/utils/log"
	"fincore/utils/results"
	"reflect"

	"github.com/gin-gonic/gin"
)

type Index struct {
	NoNeedLogin []string //忽略登录接口配置-忽略全部传[*]
	NoNeedAuths []string //忽略RBAC权限认证接口配置-忽略全部传[*]
}

func init() {
	fpath := Index{NoNeedLogin: []string{}, NoNeedAuths: []string{}}
	gf.Register(&fpath, reflect.TypeOf(fpath).PkgPath())
}

/**
* 1 获取身份证信息
 */

// IdentityRequest 身份证识别请求参数
type IdentityRequest struct {
	File string `json:"file" binding:"required"` // 上传的文件
	Side string `json:"side" binding:"required"` // 证件面参数：front/back
}

// GetIdentity 身份证识别接口
func (api *Index) PostIdentity(c *gin.Context) {
	// 声明请求结构体
	var req IdentityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		results.Failed(c, "前端请求参数解析失败: "+err.Error(), nil)
		return
	}
	//log.Info("file: %s", req.File)
	// 兼容OSS和本地存储的Base64编码
	image64, err := oss.GetFileBase64(req.File)
	if err != nil {
		results.Failed(c, "获取图片失败："+err.Error(), nil)
		return
	}
	if image64 == "" {
		results.Failed(c, "未找到图片："+req.File, nil)
		return
	}

	// 获取用户身份信息
	claim := middleware.ParseToken(c.GetHeader("Authorization"))
	userID := claim.ID
	// 构建OCR请求
	var ocrConfig Config.OCRRequest
	ocrConfig.Base64Img = image64
	ocrConfig.Side = req.Side
	ocrConfig.Fileld = ""
	ocrConfig.Image = ""
	// 调用OCR服务
	ocrJson, err := json.Marshal(ocrConfig)
	if err != nil {
		results.Failed(c, "OCR请求参数转换失败", err)
		return
	}

	ocrResponse, err := Service.OCRWithSave(string(ocrJson), uint32(userID))
	if err != nil {
		results.Failed(c, "OCR服务调用失败: "+err.Error(), err)
		return
	}

	expireDate, err := Service.ParseDate(ocrResponse.Data.End)
	if err != nil {
		results.Failed(c, "解析结束日期失败: %v", err)
		return
	}
	if req.Side == "front" {
		userInfo, err := model.DB().
			Table("business_app_account").
			Where("id", "!=", userID).
			Where("idCard", ocrResponse.Data.IDCard).
			// 根据身份证号查询用户信息，排除当前用户，确保同一个身份证号码只能绑定一个用户
			First()
		if userInfo != nil {
			results.Failed(c, "该身份证已绑定用户，请勿重复绑定", err)
			return
		}
		// 将用户信息存入数据库
		model.DB().Table("business_app_account").Data(map[string]interface{}{
			"name":           ocrResponse.Data.RealName,
			"idCard":         ocrResponse.Data.IDCard,
			"idCardFrontUrl": req.File,
		}).Where("id", userID).Update()
		log.Info("userID: %d update success!!!", userID)
	} else {
		model.DB().Table("business_app_account").Data(map[string]interface{}{
			"idCardBackUrl": req.File,
			"idCardExpired": expireDate.Format("2006-01-02 15:04:05"),
		}).Where("id", userID).Update()
		log.Info("userID: %d update success!!!", userID)
	}
	results.Success(c, "身份证识别成功", ocrResponse.Data, nil)
	return
}

/**
* 2 人脸活体检测
 */
type FaceRequest struct {
	RealName string `json:"realName" binding:"required"` // 真实姓名
	IdCardNo string `json:"id_card" binding:"required"`  // 身份证号
}

type FaceResponse struct {
	FaceUrl string `json:"faceUrl"` // 人脸核身链接
}

func (api *Index) PostFaceAuth(c *gin.Context) {
	// 获取请求参数
	var req FaceRequest
	if err := c.ShouldBind(&req); err != nil {
		results.Failed(c, "请求参数解析失败: "+err.Error(), nil)
		return
	}
	if req.RealName == "" || req.IdCardNo == "" {
		results.Failed(c, "缺少必要参数：realName或idCardNo", nil)
		return
	}

	// 构建人脸核身请求
	var faceAuthReq Config.FaceAuthRequest
	faceAuthReq.RealName = req.RealName
	faceAuthReq.IdCardNo = req.IdCardNo
	faceAuthReq.IdCardType = 1                                                                                                                                                            // 身份证类型
	faceAuthReq.ShowResult = 1                                                                                                                                                            // 前段显示验证结果
	faceAuthReq.RedirectUrl = fmt.Sprintf("%s://%s:%s/#/pages/face-webview/face-webview", global.App.Config.App.H5Protocol, global.App.Config.App.Hostname, global.App.Config.App.H5port) // 重定向地址
	faceAuthReq.NotifyUrl = "https://slf.free.idcfengye.com/api/face-auth/notify"                                                                                                         // 通知地址
	faceAuthReq.IsIframe = 1
	rand, _ := Utils.GenerateRandomString()
	faceAuthReq.BizId = rand

	// 调用人脸核身服务
	faceJson, err := json.Marshal(faceAuthReq)
	if err != nil {
		results.Failed(c, "请求参数转换失败", err)
		return
	}
	byteData, err := Service.FaceAuth(string(faceJson))
	if err != nil {
		results.Failed(c, "人脸核身服务调用失败: "+err.Error(), err)
		return
	}

	log.Info("byteData: %s", string(byteData))

	// 解析响应
	var response Config.FaceAuthResponse
	if err := json.Unmarshal(byteData, &response); err != nil {
		results.Failed(c, "人脸核身结果解析失败", err)
		return
	}

	log.Info("response: %+v", response)

	var faceData FaceResponse
	faceData.FaceUrl = response.Data.FaceUrl

	// 返回标准化结果
	results.SuccessPureJson(c, "人脸核身url返回成功", faceData, nil)

	log.Info("人脸活体检测bizid: %s", faceAuthReq.BizId)
	/**
	为了方便测试将下面存入数据库的代码注释掉了
	*/
	// 将用户人脸验证的bizid存入数据库
	claim := middleware.ParseToken(c.GetHeader("Authorization"))
	// 获取用户ID
	userId := claim.ID
	model.DB().Table("business_app_account").Data(map[string]interface{}{
		"bizId":    faceAuthReq.BizId,
		"serialNo": response.Data.SerialNo,
	}).Where("id", userId).Update()
	log.Info("userId: %d update success!!!", userId)

}

/**
* 3 人脸核身结果查询，通过bizId查询，并更新数据库状态
 */

// 查询人脸核身结果请求
type CheckRequest struct {
	BizId string `json:"bizId,omitempty"` // 业务追踪ID
}

// 查询人脸核身结果响应
type CheckResponse struct {
	Status int `json:"status"` // 认证结果("1"-进行中 "2"-成功 "3"-失败)
}

// 认证状态
const (
	AuthStatusUnverified = 0 // 未认证
	AuthStatusVerifying  = 1 // 认证中
	AuthStatusVerified   = 2 // 认证成功
	AuthStatusFailed     = 3 // 认证失败
)

// GetCheckFaceResult 人脸识别结果查询接口
func (api *Index) GetCheckFaceResult(c *gin.Context) {
	// 获取用户身份信息
	claim := middleware.ParseToken(c.GetHeader("Authorization"))
	userID := claim.ID

	// 获取请求参数
	param, _ := gf.RequestParam(c)

	// 参数验证
	if param["bizId"] == nil {
		results.Failed(c, "缺少必要参数：bizId", nil)
		return
	}

	// 参数类型转换和验证
	bizID, ok := param["bizId"].(string)
	if !ok || bizID == "" {
		results.Failed(c, "bizId参数格式错误", nil)
		return
	}

	log.Info("查询人脸核身结果 - 用户ID: %d, bizId: %s", userID, bizID)

	// 构建查询请求
	checkReq := Config.CheckRequest{
		BizId: bizID,
	}
	// 调用查询服务
	checkJson, err := json.Marshal(checkReq)
	if err != nil {
		log.Error("请求参数序列化失败: %v", err)
		results.Failed(c, "请求参数转换失败", err)
		return
	}

	byteData, err := Service.CheckFaceResult(string(checkJson))
	if err != nil {
		log.Error("人脸核身服务调用失败: %v", err)
		results.Failed(c, "人脸核身结果查询失败："+err.Error(), err)
		return
	}

	log.Info("人脸核身服务响应: %s", string(byteData))

	// 解析响应
	var response Config.CheckResponse
	if err := json.Unmarshal(byteData, &response); err != nil {
		log.Error("响应解析失败: %v", err)
		results.Failed(c, "人脸核身结果解析失败", err)
		return
	}

	log.Info("解析后的响应: %+v", response)

	// 构建响应数据
	checkData := CheckResponse{}

	// 创建业务应用账户服务实例
	businessAppAccountService := model.NewBusinessAppAccountService()
	// 根据状态码处理结果
	switch response.Data.Status {
	case AuthStatusUnverified:
	case AuthStatusVerifying:
	case AuthStatusVerified:
		// 更新数据库认证状态
		if updateErr := businessAppAccountService.UpdateIdentityStatus(userID, AuthStatusVerified); updateErr != nil {
			log.Error("更新用户认证状态失败 - 用户ID: %d, 错误: %v", userID, updateErr)
		} else {
			log.Info("用户认证状态更新成功 - 用户ID: %d", userID)
		}
		// 触发风控评估
		go func() {
			ctx := context.Background()
			riskResult, riskErr := risk.NewRiskService(ctx).EvaluateRisk(ctx, model.DB(), userID)
			if riskErr != nil {
				log.Error("风控评估失败 - 用户ID: %d, 错误: %v", userID, riskErr)
			} else {
				log.Info("风控评估成功 - 用户ID: %d, 评估ID: %s, 风险分数: %f, 风险结果: %d",
					userID, riskResult.RiskReportID, riskResult.RiskScore, riskResult.RiskResult)
			}
		}()
	case AuthStatusFailed:
		// 更新数据库认证状态为失败
		if updateErr := businessAppAccountService.UpdateIdentityStatus(userID, AuthStatusFailed); updateErr != nil {
			log.Error("更新用户认证失败状态失败 - 用户ID: %d, 错误: %v", userID, updateErr)
		} else {
			log.Info("用户认证失败状态更新成功 - 用户ID: %d", userID)
		}

	default:
		log.Warn("未知的认证状态: %d", response.Data.Status)
	}
	checkData.Status = response.Data.Status

	// 返回标准化结果
	results.Success(c, "人脸核身结果查询成功", checkData, nil)
}

/**
* 5 获取用户信息
 */
func (api *Index) GetUserInfo(c *gin.Context) {
	// 获取用户ID
	claim := middleware.ParseToken(c.GetHeader("Authorization"))
	userId := claim.ID
	// 查询用户信息
	userInfo, err := model.DB().Table("business_app_account").Where("id", userId).First()
	if err != nil {
		results.Failed(c, "查找账号失败！", err)
		return
	}
	// 返回用户信息
	results.Success(c, "获取用户信息成功！", userInfo, nil)
	log.Info("用户信息: %+v", userInfo)
}

// 获取人脸识别认证截图
func (api *Index) GetFacePhoto(c *gin.Context) {
	// 获取用户ID
	claim := middleware.ParseToken(c.GetHeader("Authorization"))
	userId := claim.ID

	// 获取用户信息
	userInfo, err := model.DB().Table("business_app_account").Where("id", userId).First()
	if err != nil {
		results.Failed(c, "查找客户账号失败！", err)
		return
	}

	// 获取人脸截图数据
	photoData, photoMd5, err := api.getFacePhotoData(userInfo)
	if err != nil {
		results.Failed(c, err.Error(), err)
		return
	}

	// 保存图片并验证MD5
	filePath, err := api.saveAndVerifyImage(photoData, photoMd5, userInfo["serialNo"].(string))
	if err != nil {
		results.Failed(c, err.Error(), err)
		return
	}

	// 更新数据库
	if err := api.updateFacePhotoUrl(userId, filePath); err != nil {
		results.Failed(c, "更新数据库失败", err)
		return
	}

	log.Info("用户ID: %d 人脸截图保存成功，路径: %s", userId, filePath)
	results.Success(c, "人脸截图保存成功", map[string]interface{}{
		"facePhotoUrl": filePath,
		"md5":          photoMd5,
	}, nil)
}

// getFacePhotoData 获取人脸截图数据
func (api *Index) getFacePhotoData(userInfo map[string]interface{}) (string, string, error) {
	// 构建请求
	facePhotoReq := Config.FacePhotoRequest{
		SerialNo: userInfo["serialNo"].(string),
	}

	facePhotoJson, err := json.Marshal(facePhotoReq)
	if err != nil {
		return "", "", fmt.Errorf("请求参数转换失败: %v", err)
	}

	byteData, err := Service.FacePhoto(string(facePhotoJson))
	if err != nil {
		return "", "", fmt.Errorf("人脸识别认证截图服务调用失败: %v", err)
	}

	log.Info("byteData: %s", string(byteData))

	var result Config.FacePhotoResponse
	if err := json.Unmarshal(byteData, &result); err != nil {
		return "", "", fmt.Errorf("人脸识别认证截图结果解析失败: %v", err)
	}

	log.Info("result: %+v", result)

	return result.Data.Data, result.Data.MD5, nil
}

// saveAndVerifyImage 保存图片并验证MD5，兼容OSS和本地存储
func (api *Index) saveAndVerifyImage(photoData, photoMd5, serialNo string) (string, error) {
	// 重试机制：最多重试3次
	maxRetries := 3
	for retryCount := 0; retryCount < maxRetries; retryCount++ {
		// 使用OSS工具包保存图片
		filePath, err := oss.SaveImageWithOSS(photoData, serialNo)
		if err != nil {
			return "", err
		}

		// 验证MD5
		if oss.VerifyMD5WithPath(filePath, photoMd5) {
			log.Info("MD5验证成功，文件保存路径: %s", filePath)
			return filePath, nil
		}

		log.Warn("MD5验证失败，重试次数: %d/%d", retryCount+1, maxRetries)

		// 删除保存的文件，准备重试
		oss.RemoveFile(filePath)

		if retryCount == maxRetries-1 {
			return "", fmt.Errorf("图片验证失败，已达到最大重试次数")
		}

		// 等待一段时间后重试
		time.Sleep(time.Duration(retryCount+1) * time.Second)
	}

	return "", fmt.Errorf("图片验证失败，请重试")
}

// prepareFilePath 准备文件路径
func (api *Index) prepareFilePath(serialNo string) (string, error) {
	// 获取当前日期作为目录名
	currentDate := time.Now().Format("20060102")
	uploadDir := fmt.Sprintf("resource/uploads/%s", currentDate)

	// 创建目录（如果不存在）
	if err := api.createDirectory(uploadDir); err != nil {
		return "", err
	}

	// 使用serialNo作为文件名
	fileName := fmt.Sprintf("%s.jpg", serialNo)
	filePath := fmt.Sprintf("%s/%s", uploadDir, fileName)

	return filePath, nil
}

// createDirectory 创建目录
func (api *Index) createDirectory(dir string) error {
	if _, err := os.Stat(dir); err != nil {
		if !os.IsExist(err) {
			if err := os.MkdirAll(dir, os.ModePerm); err != nil {
				log.Error("创建目录失败: %v", err)
				return fmt.Errorf("创建上传目录失败: %v", err)
			}
		}
	}
	return nil
}

// saveImage 保存图片
func (api *Index) saveImage(photoData, filePath string) error {
	// 将BASE64字符串解码为字节数组
	imageBytes := Utils.Base64Decode(photoData)
	if len(imageBytes) == 0 {
		log.Error("BASE64解码失败，解码结果为空")
		return fmt.Errorf("图片解码失败")
	}

	// 保存为JPG文件
	err := os.WriteFile(filePath, imageBytes, 0644)
	if err != nil {
		log.Error("保存图片文件失败: %v", err)
		return fmt.Errorf("保存图片文件失败: %v", err)
	}

	return nil
}

// updateFacePhotoUrl 更新数据库中的facePhotoUrl字段
func (api *Index) updateFacePhotoUrl(userId int64, filePath string) error {
	updateData := map[string]interface{}{
		"facePhotoUrl": filePath,
	}

	_, err := model.DB().Table("business_app_account").
		Data(updateData).
		Where("id", userId).
		Update()

	if err != nil {
		log.Error("更新数据库facePhotoUrl失败: %v", err)
		return fmt.Errorf("更新数据库失败: %v", err)
	}

	return nil
}
