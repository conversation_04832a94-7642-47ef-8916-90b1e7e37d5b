openapi: 3.0.0
info:
  title: FinCore Order API
  description: API endpoints for order management
  version: 1.0.0
  
servers:
  - url: /api/v1
    description: API server

components:
  schemas:
    ApiResponse:
      type: object
      properties:
        status:
          type: integer
          description: 响应状态码，200为成功，其他为失败
        message:
          type: string
          description: 响应消息
        data:
          type: object
          description: 响应数据
        meta:
          type: object
          description: 元数据，例如分页信息
    
    OrderCreate:
      type: object
      required:
        - user_id
        - channel_id
        - loan_amount
      properties:
        user_id:
          type: integer
          format: int64
          minimum: 1
          description: 用户ID
        channel_id:
          type: integer
          format: int64
          minimum: 1
          description: 渠道ID
        product_rule_id:
          type: integer
          format: int64
          minimum: 1
          description: 产品规则ID（可选，系统将根据风控评分自动匹配）
        loan_amount:
          type: number
          format: double
          minimum: 0.01
          maximum: 999999999.99
          description: 申请贷款金额
        customer_origin:
          type: string
          maxLength: 50
          description: 客户来源
        initial_order_channel_id:
          type: integer
          format: int64
          minimum: 1
          description: 初始下单渠道ID
        payment_channel_id:
          type: integer
          format: int64
          minimum: 1
          description: 支付渠道ID
    
    OrderList:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 订单ID
        order_no:
          type: string
          maxLength: 32
          description: 订单编号
        user_id:
          type: integer
          format: int64
          minimum: 1
          description: 用户ID
        product_rule_id:
          type: integer
          format: int64
          minimum: 1
          description: 产品规则ID
        loan_amount_min:
          type: number
          format: double
          minimum: 0.01
          maximum: 999999999.99
          description: 申请贷款金额最小值
        loan_amount_max:
          type: number
          format: double
          minimum: 0.01
          maximum: 999999999.99
          description: 申请贷款金额最大值
        channel_id:
          type: integer
          format: int64
          minimum: 1
          description: 渠道ID
        customer_origin:
          type: string
          maxLength: 50
          description: 客户来源
        initial_order_channel_id:
          type: integer
          format: int64
          minimum: 1
          description: 初始下单渠道ID
        payment_channel_id:
          type: integer
          format: int64
          minimum: 1
          description: 支付渠道ID
        status:
          type: integer
          format: int32
          minimum: 0
          maximum: 8
          description: 订单状态 0-待审核, 1-审核拒绝, 2-待放款, 3-放款中, 4-还款中, 5-已结清, 6-已关闭, 7-放款失败, 8-状态更新异常
        is_freeze:
          type: integer
          format: int32
          minimum: 0
          maximum: 1
          description: 是否冻结 0-否, 1-是
        is_refund_needed:
          type: integer
          format: int32
          minimum: 0
          maximum: 1
          description: 是否需要退款 0-否, 1-是
        complaint_status:
          type: integer
          format: int32
          minimum: 0
          maximum: 1
          description: 投诉状态 0-否, 1-是
        audit_assignee_id:
          type: integer
          format: int64
          minimum: 1
          description: 审核员ID
        review_status:
          type: integer
          format: int32
          minimum: 0
          maximum: 2
          description: 复审状态 0-未复审, 1-复审通过, 2-复审拒绝
        sales_assignee_id:
          type: integer
          format: int64
          minimum: 1
          description: 跟进的业务员ID
        collection_assignee_id:
          type: integer
          format: int64
          minimum: 1
          description: 当前催收员ID
        submitted_at_start:
          type: string
          format: date-time
          description: 下单时间开始范围 (格式 YYYY-MM-DD HH:mm:ss)
        submitted_at_end:
          type: string
          format: date-time
          description: 下单时间结束范围 (格式 YYYY-MM-DD HH:mm:ss)
        disbursed_at_start:
          type: string
          format: date-time
          description: 放款时间开始范围 (格式 YYYY-MM-DD HH:mm:ss)
        disbursed_at_end:
          type: string
          format: date-time
          description: 放款时间结束范围 (格式 YYYY-MM-DD HH:mm:ss)
        completed_at_start:
          type: string
          format: date-time
          description: 结清/关闭时间开始范围 (格式 YYYY-MM-DD HH:mm:ss)
        completed_at_end:
          type: string
          format: date-time
          description: 结清/关闭时间结束范围 (格式 YYYY-MM-DD HH:mm:ss)
        page:
          type: integer
          format: int32
          minimum: 1
          default: 1
          description: 页码
        page_size:
          type: integer
          format: int32
          minimum: 1
          maximum: 100
          default: 20
          description: 每页数量
    
    CloseOrder:
      type: object
      required:
        - reason
      properties:
        reason:
          type: string
          maxLength: 200
          description: 关闭原因
        operatorId:
          type: integer
          format: int32
          minimum: 1
          description: 操作员ID
        operatorName:
          type: string
          maxLength: 50
          description: 操作员姓名
    
    DisbursementProcess:
      type: object
      required:
        - orderNo
      properties:
        orderNo:
          type: string
          minLength: 1
          maxLength: 50
          description: 订单编号
        operatorId:
          type: integer
          format: int32
          minimum: 1
          default: 0
          description: 操作员ID
    
    OrderAssign:
      type: object
      required:
        - orderId
        - salesId
      properties:
        orderId:
          type: integer
          format: int64
          minimum: 1
          description: 订单ID
        salesId:
          type: integer
          format: int32
          minimum: 1
          description: 业务员ID
        operatorId:
          type: integer
          format: int32
          minimum: 1
          default: 0
          description: 操作员ID
    
    OrderClaim:
      type: object
      required:
        - orderId
        - salesId
      properties:
        orderId:
          type: integer
          format: int64
          minimum: 1
          description: 订单ID
        salesId:
          type: integer
          format: int32
          minimum: 1
          description: 业务员ID
    
    ManualReview:
      type: object
      required:
        - orderId
        - salesId
        - approved
      properties:
        orderId:
          type: integer
          format: int64
          minimum: 1
          description: 订单ID
        salesId:
          type: integer
          format: int32
          minimum: 1
          description: 业务员ID
        approved:
          type: boolean
          description: 审核结果：true-通过，false-拒绝
        remarks:
          type: string
          maxLength: 500
          description: 审核备注

paths:
  /business/order:
    post:
      summary: 创建订单
      description: 创建新的贷款订单
      tags:
        - Order
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderCreate'
      responses:
        '200':
          description: 创建订单成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
  
  /business/order/list:
    post:
      summary: 获取订单列表
      description: 按条件查询订单列表，支持分页
      tags:
        - Order
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderList'
      responses:
        '200':
          description: 获取订单列表成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
  
  /business/order/status/{orderNo}:
    get:
      summary: 获取订单状态
      description: 根据订单编号获取订单状态
      tags:
        - Order
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          description: 订单编号
      responses:
        '200':
          description: 获取订单状态成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '404':
          description: 订单不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
  
  /business/order/close/{orderNo}:
    post:
      summary: 关闭订单
      description: 关闭指定的订单
      tags:
        - Order
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          description: 订单编号
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CloseOrder'
      responses:
        '200':
          description: 关闭订单成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '404':
          description: 订单不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
  
  /business/order/disbursement:
    post:
      summary: 处理订单放款
      description: 处理订单放款流程
      tags:
        - Order
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DisbursementProcess'
      responses:
        '200':
          description: 处理放款成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
  
  /business/order/assign:
    post:
      summary: 分配订单给业务员
      description: 将订单分配给指定的业务员
      tags:
        - Order
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderAssign'
      responses:
        '200':
          description: 分配订单成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
  
  /business/order/claim:
    post:
      summary: 业务员认领订单
      description: 业务员主动认领订单
      tags:
        - Order
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderClaim'
      responses:
        '200':
          description: 认领订单成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
  
  /business/order/review:
    post:
      summary: 人工审核订单
      description: 对订单进行人工审核
      tags:
        - Order
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManualReview'
      responses:
        '200':
          description: 人工审核成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
  
  /business/order/pending:
    get:
      summary: 获取待分配订单列表
      description: 获取所有待分配给业务员的订单列表
      tags:
        - Order
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: 页码（从1开始）
        - name: pageSize
          in: query
          required: false
          schema:
            type: integer
            default: 20
          description: 每页数量
      responses:
        '200':
          description: 获取待分配订单列表成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          description: 参数验证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse' 