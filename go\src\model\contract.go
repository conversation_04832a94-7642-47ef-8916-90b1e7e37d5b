package model

import (
	"context"
	"fmt"
	"time"
)

type Contract struct {
	ID              int    `json:"id" db:"id"`
	ContractID      string `json:"contract_id" db:"contract_id"`             // 合同编号
	UserID          int    `json:"user_id" db:"user_id"`                     // 用户ID
	Username        string `json:"username" db:"username"`                   // 用户名
	IDCard          string `json:"id_card" db:"id_card"`                     // 用户身份证号码
	ProductID       int    `json:"product_id" db:"product_id"`               // 产品ID
	BankCardID      int    `json:"bank_card_id" db:"bank_card_id"`           // 放款卡id
	ContractFileURL string `json:"contract_file_url" db:"contract_file_url"` // 合同文件地址
	ContractStatus  int    `json:"contract_status" db:"contract_status"`     // 合同状态：0-未签署 1-已签署 2-作废
	CreateTime      int64  `json:"create_time" db:"create_time"`             // 创建时间（Unix时间戳）
	UpdateTime      int64  `json:"update_time" db:"update_time"`             // 更新时间（Unix时间戳）
	ChannelCode     string `json:"channel_code" db:"channel_code"`           // 渠道ID
}

// TableName 指定表名
func (Contract) TableName() string {
	return "contracts"
}

// 合同状态常量
const (
	ContractStatusUnsigned = 0 // 未签署
	ContractStatusSigned   = 1 // 已签署
	ContractStatusVoided   = 2 // 作废
)

// 第三方合同状态常量（对应 QueryContractStatus 接口返回的状态）
const (
	ThirdPartyContractStatusWaiting   = 0 // 等待签约
	ThirdPartyContractStatusSigning   = 1 // 签约中
	ThirdPartyContractStatusSigned    = 2 // 已签约
	ThirdPartyContractStatusExpired   = 3 // 过期
	ThirdPartyContractStatusRejected  = 4 // 拒签
	ThirdPartyContractStatusVoided    = 6 // 作废
	ThirdPartyContractStatusCancelled = 7 // 撤销
)

// ContractService 合同服务
type ContractService struct {
	ctx context.Context
}

// NewContractService 创建合同服务实例
func NewContractService(ctx context.Context) *ContractService {
	return &ContractService{
		ctx: ctx,
	}
}

// GetContractByID 根据ID获取合同
func (s *ContractService) GetContractByID(id int) (*Contract, error) {
	data, err := DB(WithContext(s.ctx)).Table("contracts").Where("id", id).First()
	if err != nil {
		return nil, fmt.Errorf("查询合同失败: %v", err)
	}
	if len(data) == 0 {
		return nil, fmt.Errorf("合同不存在")
	}

	var contract Contract
	if err := mapToStruct(data, &contract); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &contract, nil
}

// UpdateContractToSigned 更新合同状态为已签署
func (s *ContractService) UpdateContractToSigned(id int) error {
	_, err := DB(WithContext(s.ctx)).Table("contracts").
		Where("id", id).
		Data(map[string]interface{}{
			"contract_status": ContractStatusSigned,
			"update_time":     time.Now().Unix(),
		}).Update()
	if err != nil {
		return fmt.Errorf("更新合同为已签署失败: %v", err)
	}
	return nil
}

// UpdateContractToVoided 更新合同状态为作废
func (s *ContractService) UpdateContractToVoided(id int) error {
	_, err := DB(WithContext(s.ctx)).Table("contracts").
		Where("id", id).
		Data(map[string]interface{}{
			"contract_status": ContractStatusVoided,
			"update_time":     time.Now().Unix(),
		}).Update()
	if err != nil {
		return fmt.Errorf("更新合同为作废失败: %v", err)
	}
	return nil
}

// VoidUnsignedContractsByUserID 批量作废用户未签署合同
func (s *ContractService) VoidUnsignedContractsByUserID(userID int) error {
	_, err := DB(WithContext(s.ctx)).Table("contracts").
		Where("user_id", userID).
		Where("contract_status", ContractStatusUnsigned).
		Data(map[string]interface{}{
			"contract_status": ContractStatusVoided,
			"update_time":     time.Now().Unix(),
		}).Update()
	if err != nil {
		return fmt.Errorf("批量作废未签署合同失败: %v", err)
	}
	return nil
}

// UpdateContractOrderID 更新合同对应的订单ID
func (s *ContractService) UpdateContractOrderID(contractID int, orderID int) error {
	_, err := DB(WithContext(s.ctx)).Table("contracts").
		Where("id", contractID).
		Data(map[string]interface{}{
			"order_id":    orderID,
			"update_time": time.Now().Unix(),
		}).Update()
	if err != nil {
		return fmt.Errorf("更新合同订单ID失败: %v", err)
	}
	return nil
}

// CreateContract 创建新合同
func (s *ContractService) CreateContract(contract *Contract) (int64, error) {
	now := time.Now().Unix()
	contract.CreateTime = now
	contract.UpdateTime = now
	contract.ContractStatus = ContractStatusUnsigned

	id, err := DB(WithContext(s.ctx)).Table("contracts").Data(map[string]interface{}{
		"contract_id":       contract.ContractID,
		"user_id":           contract.UserID,
		"username":          contract.Username,
		"id_card":           contract.IDCard,
		"bank_card_id":      contract.BankCardID,
		"product_id":        contract.ProductID,
		"contract_file_url": contract.ContractFileURL,
		"contract_status":   contract.ContractStatus,
		"create_time":       contract.CreateTime,
		"update_time":       contract.UpdateTime,
		"channel_code":      contract.ChannelCode,
	}).InsertGetId()
	if err != nil {
		return 0, fmt.Errorf("创建合同失败: %v", err)
	}
	return id, nil
}

// UpdateContractToSigned 更新合同状态为已签署
func (s *ContractService) UpdateToSignedbyContractNo(contract_no string) error {
	_, err := DB(WithContext(s.ctx)).Table("contracts").
		Where("contract_id", contract_no).
		Data(map[string]interface{}{
			"contract_status": ContractStatusSigned,
			"update_time":     time.Now().Unix(),
		}).Update()
	if err != nil {
		return fmt.Errorf("更新合同为已签署失败: %v", err)
	}
	return nil
}

// GetContractByID 根据合同ID获取合同
func (s *ContractService) GetContractByContractNo(id string) (*Contract, error) {
	data, err := DB(WithContext(s.ctx)).Table("contracts").Where("contract_id", id).First()
	if err != nil {
		return nil, fmt.Errorf("查询合同失败: %v", err)
	}
	if len(data) == 0 {
		return nil, fmt.Errorf("合同不存在")
	}

	var contract Contract
	if err := mapToStruct(data, &contract); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &contract, nil
}

// GetContractByUserIDAndProductID 根据用户ID和产品ID获取合同
func (s *ContractService) GetContractByUserIDAndProductID(userID int, productID int) (*Contract, error) {
	data, err := DB(WithContext(s.ctx)).Table("contracts").
		Where("user_id", userID).
		Where("product_id", productID).
		OrderBy("create_time DESC").
		First()
	if err != nil {
		return nil, fmt.Errorf("查询合同失败: %v", err)
	}
	if len(data) == 0 {
		return nil, nil // 返回 nil 表示没有找到合同
	}

	var contract Contract
	if err := mapToStruct(data, &contract); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &contract, nil
}

// UpdateContractStatus 更新合同状态
func (s *ContractService) UpdateContractStatus(contractID int, status int) error {
	_, err := DB(WithContext(s.ctx)).Table("contracts").
		Where("id", contractID).
		Data(map[string]interface{}{
			"contract_status": status,
			"update_time":     time.Now().Unix(),
		}).Update()
	if err != nil {
		return fmt.Errorf("更新合同状态失败: %v", err)
	}
	return nil
}

// UpdateContractStatusByContractNo 根据合同编号更新合同状态
func (s *ContractService) UpdateContractStatusByContractNo(contractNo string, status int) error {
	_, err := DB(WithContext(s.ctx)).Table("contracts").
		Where("contract_id", contractNo).
		Data(map[string]interface{}{
			"contract_status": status,
			"update_time":     time.Now().Unix(),
		}).Update()
	if err != nil {
		return fmt.Errorf("更新合同状态失败: %v", err)
	}
	return nil
}

// ConvertThirdPartyStatusToLocal 将第三方合同状态转换为本地状态
func ConvertThirdPartyStatusToLocal(thirdPartyStatus int) int {
	switch thirdPartyStatus {
	case ThirdPartyContractStatusWaiting, ThirdPartyContractStatusSigning:
		return ContractStatusUnsigned // 等待签约、签约中 -> 未签署
	case ThirdPartyContractStatusSigned:
		return ContractStatusSigned // 已签约 -> 已签署
	case ThirdPartyContractStatusExpired, ThirdPartyContractStatusRejected,
		ThirdPartyContractStatusVoided, ThirdPartyContractStatusCancelled:
		return ContractStatusVoided // 过期、拒签、作废、撤销 -> 作废
	default:
		return ContractStatusUnsigned // 默认为未签署
	}
}
