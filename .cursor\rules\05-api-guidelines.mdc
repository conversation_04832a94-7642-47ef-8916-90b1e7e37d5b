---
description: go/*
alwaysApply: false
---
# API 设计指南

## RESTful API 规范

1. **URI 设计**
   - 使用名词表示资源，例如 `/users`、`/products`
   - 使用 HTTP 方法表示操作：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
   - 资源路径使用复数形式，例如 `/users` 而不是 `/user`
   - 使用嵌套资源表示关系，例如 `/users/{id}/orders`

2. **请求参数**
   - GET 请求使用 URL 查询参数
   - POST/PUT/PATCH 请求使用 JSON 请求体
   - 统一参数命名风格，使用小驼峰（userName）或下划线（user_name）
   - 分页参数统一使用 `page`、`page_size` 或 `limit`、`offset`

3. **响应格式**
   - 统一使用 JSON 格式
   - 包含状态码、消息和数据三个基本字段
   - 示例：`{"code": 0, "msg": "success", "data": { ... }}`

## 身份验证

1. **Token 认证**
   - 使用 JWT（JSON Web Tokens）进行身份验证
   - Token 包含在请求头的 `Authorization` 字段中
   - 格式：`Authorization: Bearer {token}`

2. **权限控制**
   - 基于角色的访问控制（RBAC）
   - API 权限在路由层面进行控制
   - 用户权限信息存储在 Token 中

## 错误处理

1. **错误码规范**
   - 0：成功
   - 40x：客户端错误（401 未授权、404 不存在等）
   - 50x：服务端错误

2. **错误响应格式**
   ```json
   {
     "code": 401,
     "msg": "未授权访问",
     "data": null
   }
   ```

## API 文档

1. **文档维护**
   - 使用 Swagger 或其他工具生成和维护 API 文档
   - 每个 API 必须包含详细说明、请求参数、响应示例

2. **版本控制**
   - 在 URL 中包含版本信息，例如 `/v1/users`
   - 兼容性更改不需要新版本，破坏性更改需要新版本

遵循以上规范，确保 API 设计的一致性、可读性和可维护性。
