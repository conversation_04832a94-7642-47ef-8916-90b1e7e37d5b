<template>
	<view class="container">
		<!-- 表单区域 -->
		<view class="box">
			<view class="form-containera">
				<!-- 银行卡号输入 -->
				<view class="form-item">
					<text class="label">银行卡号:</text>
					<input v-model="formData.bankNumber" type="number" placeholder="请输入银行卡号" maxlength="19"
						@blur="formatCardNumber" class="right-align-input" />
				</view>

				<!-- 预留手机号 -->
				<view class="form-item">
					<text class="label">预留手机号:</text>
					<input v-model="formData.mobile" type="number" placeholder="请输入手机号" maxlength="11"
						class="right-align-input" />
				</view>

				<!-- 验证码 -->
				<view class="form-itema">
					<text class="labela">验证码:</text>
					<input v-model="formData.smsCode" type="number" maxlength="6" class="right-align-input" />
					<div class="sms-btn" :disabled="isSending" @click="sendSmsCode">
						{{ isSending ? `${countdown}s后重试` : "发送验证码" }}
					</div>
				</view>
			</view>
		</view>

		<!-- 银行不支持提示 -->
		<view class="bank-notice">
			<text>
				<image src="/static/icon/jg.png" mode=""></image>中国民生银行不支持当前服务
			</text>
		</view>

		<!-- 协议勾选 -->
		<view class="agreement">
			<checkbox-group @change="toggleAgreement">
				<checkbox :checked="isAgreed" color="#1A73E8" style="transform: scale(0.6)" />
				<text class="agreement-text">
					我已阅读并同意<text class="link" @click="linka">《扣款通知函》</text>
				</text>
			</checkbox-group>
		</view>

		<!-- 提交按钮 -->
		<div class="submit-btn" :disabled="!canSubmit" @click="handleSubmit">
			提交
		</div>
	</view>
</template>

<script>
	import bankApi from "@/api/bank";

	export default {
		data() {
			return {
				formData: {
					bankNumber: "",
					mobile: "",
					smsCode: "",
				},
				isSending: false,
				countdown: 60,
				showBankWarning: false,
				isAgreed: false,
			};
		},
		computed: {
			canSubmit() {
				const pureCardNo = this.formData.bankNumber.replace(/\s/g, "");
				return (
					/^\d{16,19}$/.test(pureCardNo) &&
					/^1[3-9]\d{9}$/.test(this.formData.mobile) &&
					/^\d{6}$/.test(this.formData.smsCode) &&
					this.isAgreed &&
					!this.showBankWarning
				);
			},
		},
		methods: {
			// 格式化银行卡号显示
			formatCardNumber() {
				this.formData.bankNumber = this.formData.bankNumber
					.replace(/\D/g, "")
					.replace(/(\d{4})(?=\d)/g, "$1 ");
			},
			// ****************
			// 发送验证码
			async sendSmsCode() {
				if (!/^1[3-9]\d{9}$/.test(this.formData.mobile)) {
					return uni.showToast({
						title: "请输入正确的手机号",
						icon: "none",
					});
				}

				const pureCardNo = this.formData.bankNumber.replace(/\s/g, "");
				if (!/^\d{16,19}$/.test(pureCardNo)) {
					return uni.showToast({
						title: "请输入正确的银行卡号",
						icon: "none",
					});
				}

				try {
					this.isSending = true;
					await bankApi.postBindBankCardSms({
						mobile: this.formData.mobile,
						bankNumber: pureCardNo,
					});

					// 开始倒计时
					this.countdown = 60;
					const timer = setInterval(() => {
						this.countdown--;
						if (this.countdown <= 0) {
							clearInterval(timer);
							this.isSending = false;
						}
					}, 1000);

					uni.showToast({
						title: "验证码已发送",
						icon: "none",
					});
				} catch (error) {
					this.isSending = false;
					uni.showToast({
						title: error.message || "验证码发送失败",
						icon: "none",
					});
				}
			},
			// 提交绑卡
			async handleSubmit() {
				try {
					const params = {
						mobile: this.formData.mobile,
						bankNumber: this.formData.bankNumber.replace(/\s/g, ""),
						smsCode: this.formData.smsCode, // 添加验证码参数
					};

					const res = await bankApi.postBindBankCard(params);

					uni.showToast({
						title: "绑卡成功",
						icon: "success",
						success: () => {
							setTimeout(() => {
								uni.navigateTo({
									url: '/pages/BorrowMoney/BorrowMoney'
								});
							}, 1500);
						},
					});
				} catch (error) {
					uni.showToast({
						title: error.message || "绑卡失败",
						icon: "none",
					});
				}
			},
			linka() {
				uni.navigateTo({
					url: "/pages/RepaymentCard/hlink",
				});
			},
			// 协议勾选
			toggleAgreement(e) {
				this.isAgreed = e.detail.value.length > 0;
			},

		},
	};
</script>

<style lang="scss">
	.container {
		width: 100%;
		height: 100%;
		padding: 10px;
		background-color: #eff2f7;
	}

	.form-containera {
		width: 90%;
		margin-left: 3%;
		height: 130px;
		border-radius: 8px;
		background-color: #fff;
	}

	.form-item {
		width: 100%;
		height: 30px;
		line-height: 30px;
		padding: 6px;
		border-bottom: 1px solid #f4f4f4;

		.label {
			float: left;
			font-size: 14px;
		}

		input {
			float: right;
			height: 30px;
			margin-right: 13px;
			line-height: 30px;
			font-size: 14px;

		}
	}

	.form-itema {
		width: 100%;
		height: 30px;
		line-height: 30px;
		padding: 6px;

		.labela {
			float: left;
			font-size: 14px;
		}

		input {
			float: left;
			font-size: 14px;
		}

		.sms-btn {
			float: right;
			margin-right: 13px;
			font-weight: 600;
			color: #2289ff;
			font-size: 14px;
		}
	}

	// 新增样式：右对齐输入框
	.right-align-input {
		text-align: right;
	}

	.bank-notice {
		width: 100%;
		min-height: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 10px;
		font-size: 15px;
		color: #222;

		text {
			display: flex;
			align-items: center;
		}

		image {
			width: 18px;
			height: 18px;
			margin-right: 6px;
			vertical-align: middle;
			position: relative;
			top: -2px;
		}
	}

	.agreement {
		width: 100%;
		height: 20px;
		font-size: 12px;
		float: left;
		margin-left: 83px;
		line-height: 20px;
		margin-top: 10px;

		checkbox {
			float: left;
		}
	}

	.agreement-text {
		float: left;
	}

	.submit-btn {
		width: 220px;
		height: 40px;
		float: left;
		text-align: center;
		line-height: 40px;
		margin-left: 80px;
		margin-top: 300px;
		background-color: #2289ff;
		color: #fff;
		border-radius: 20px;
	}
</style>