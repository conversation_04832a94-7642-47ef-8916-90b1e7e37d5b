package repayment

import "fincore/utils/jsonschema"

// ManualWithholdRequest 管理员手动代扣请求
type ManualWithholdRequest struct {
	BillID       int     `json:"bill_id" binding:"required"`       // 账单ID
	BankCardID   int     `json:"bank_card_id" binding:"required"`  // 银行卡ID
	Amount       float64 `json:"amount" binding:"required,gt=0"`   // 代扣金额（元）
	Remark       string  `json:"remark"`                           // 备注
	WithholdType string  `json:"withhold_type" binding:"required"` // 业务类型：asset(资管) 或 guarantee(担保)
}

// floatPtr 返回float64指针，用于jsonschema的Min/Max字段
func floatPtr(f float64) *float64 {
	return &f
}

// GetManualWithholdSchema 获取手动代扣的验证规则
func GetManualWithholdSchema() *jsonschema.Schema {
	return &jsonschema.Schema{
		Title: "手动代扣参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"bill_id": {
				Type:        "int",
				Required:    true,
				Min:         floatPtr(1),
				Description: "账单ID（必填，正整数）",
			},
			"bank_card_id": {
				Type:        "int",
				Required:    true,
				Min:         floatPtr(1),
				Description: "银行卡ID（必填，正整数）",
			},
			"amount": {
				Type:        "float",
				Required:    true,
				Min:         floatPtr(0.01),
				Max:         floatPtr(999999.99),
				Description: "代扣金额（元，必填，0.01-999999.99）",
			},
			"remark": {
				Type:        "string",
				Required:    false,
				Description: "备注（可选，最大500字符）",
			},
			"withhold_type": {
				Type:        "string",
				Required:    true,
				Description: "业务类型（必填，ASSET或GUARANTEE）",
			},
		},
		Required: []string{"bill_id", "bank_card_id", "amount", "withhold_type"},
	}
}

// GetPaymentCallbackSchema 支付回调请求参数
func GetPaymentCallbackSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "支付回调请求",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"resp_code": {
				Type:        "string",
				Required:    true,
				Description: "响应码",
			},
			"resp_msg": {
				Type:        "string",
				Required:    true,
				Description: "响应信息",
			},
			"sign_type": {
				Type:        "string",
				Required:    true,
				Description: "签名类型",
			},
			"sign": {
				Type:        "string",
				Required:    true,
				Description: "签名",
			},
			"mer_no": {
				Type:        "string",
				Required:    true,
				Description: "商户号",
			},
			"order_no": {
				Type:        "string",
				Required:    true,
				Description: "订单号",
			},
			"offset_amount": {
				Type:        "string",
				Required:    true,
				Description: "优惠金额",
			},
			"paid_amount": {
				Type:        "string",
				Required:    true,
				Description: "实付金额",
			},
			"trade_no": {
				Type:        "string",
				Required:    true,
				Description: "交易流水号",
			},
			"order_time": {
				Type:        "string",
				Required:    true,
				Description: "订单时间",
			},
			"status": {
				Type:        "string",
				Required:    true,
				Description: "状态",
			},
			"success_time": {
				Type:        "string",
				Required:    true,
				Description: "成功时间",
			},
			"success_amount": {
				Type:        "string",
				Required:    true,
				Description: "成功金额",
			},
		},
	}
}
