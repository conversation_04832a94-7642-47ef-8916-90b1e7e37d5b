---
description: go/*
alwaysApply: false
---
# 第三方集成指南

## 集成原则

FinCore 系统与多种第三方服务和 API 进行集成，所有第三方集成代码都应该位于 [thirdparty/](mdc:src/thirdparty) 目录下。

1. **封装原则**
   - 所有第三方 API 必须进行封装，不允许在业务代码中直接调用第三方 API
   - 为每个第三方服务创建独立的包或模块
   - 提供统一的接口，隐藏实现细节

2. **配置管理**
   - 第三方服务的配置应统一管理在配置文件中
   - 敏感信息（如密钥、密码）应加密存储
   - 支持不同环境（开发、测试、生产）的配置切换

3. **错误处理**
   - 统一处理第三方服务的异常和错误
   - 提供重试机制和容错处理
   - 详细记录错误信息，便于排查问题

## 常用集成

1. **支付集成**
   - 支付宝
   - 微信支付
   - 其他银行或支付渠道

2. **消息通知**
   - 短信服务
   - 邮件服务
   - 推送通知

3. **存储服务**
   - 对象存储
   - CDN 服务

4. **身份验证**
   - OAuth 服务
   - 单点登录
   - 数字证书服务

## 开发规范

1. **接口定义**
   - 使用接口定义第三方服务的功能
   - 实现可替换性，便于更换不同的服务提供商

2. **文档管理**
   - 记录第三方服务的 API 文档和使用说明
   - 提供调用示例和参数说明

3. **测试策略**
   - 使用 Mock 对象进行单元测试
   - 集成测试应使用测试环境的第三方服务
   - 定期验证第三方服务的可用性

## 安全考虑

1. **数据安全**
   - 传输数据加密
   - 存储敏感信息时进行加密
   - 定期更新密钥和凭证

2. **访问控制**
   - 使用最小权限原则
   - 实施 IP 白名单等访问限制
   - 审计和日志记录所有关键操作

遵循以上规范，确保第三方集成的安全性、可靠性和可维护性。
