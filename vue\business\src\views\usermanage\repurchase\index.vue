<template>
  <div class="container">
    <!-- 搜索条件区域 -->
    <a-card class="general-card" title="搜索条件">
      <a-form
        ref="queryFormRef"
        :model="queryForm"
        :label-col-props="{ span: 6 }"
        :wrapper-col-props="{ span: 18 }"
        label-align="left"
        auto-label-width
        @submit="handleSearch"
      >
        <!-- 第一行：姓名、手机号、身份证 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="name" label="姓名">
              <a-input
                v-model="queryForm.name"
                placeholder="请输入姓名"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="mobile" label="手机号">
              <a-input
                v-model="queryForm.mobile"
                placeholder="请输入手机号"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="idCard" label="身份证">
              <a-input
                v-model="queryForm.idCard"
                placeholder="请输入身份证号"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第二行：渠道来源、是否注销、在借订单数 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="channelId" label="渠道来源">
              <a-select
                v-model="queryForm.channelId"
                placeholder="请选择渠道"
                allow-clear
              >
                <a-option
                  v-for="item in channelOptions"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                />
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="isCancelled" label="是否注销">
              <a-select
                v-model="queryForm.isCancelled"
                placeholder="请选择"
                allow-clear
              >
                <a-option value="0" label="未注销" />
                <a-option value="1" label="已注销" />
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="borrowingOrderCount" label="在借订单数">
              <a-input
                v-model="queryForm.borrowingOrderCount"
                placeholder="请输入在借订单数"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第三行：订单总数、总额度范围 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="totalOrderCount" label="订单总数">
              <a-input
                v-model="queryForm.totalOrderCount"
                placeholder="请输入订单总数"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="totalAmount" label="总额度范围">
              <a-input-group>
                <a-input
                  v-model="queryForm.totalAmountMin"
                  placeholder="最小值"
                  style="width: 45%"
                />
                <a-input
                  style="
                    width: 10%;
                    text-align: center;
                    pointer-events: none;
                    background-color: #f7f8fa;
                  "
                  placeholder="~"
                  readonly
                />
                <a-input
                  v-model="queryForm.totalAmountMax"
                  placeholder="最大值"
                  style="width: 45%"
                />
              </a-input-group>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="availableAmount" label="可用额度范围">
              <a-input-group>
                <a-input
                  v-model="queryForm.availableAmountMin"
                  placeholder="最小值"
                  style="width: 45%"
                />
                <a-input
                  style="
                    width: 10%;
                    text-align: center;
                    pointer-events: none;
                    background-color: #f7f8fa;
                  "
                  placeholder="~"
                  readonly
                />
                <a-input
                  v-model="queryForm.availableAmountMax"
                  placeholder="最大值"
                  style="width: 45%"
                />
              </a-input-group>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第四行：最后还款时间、账单到期时间、排序方式 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="lastRepayTime" label="最后还款时间">
              <a-range-picker
                v-model="lastRepayTimeRange"
                style="width: 100%"
                @change="handleLastRepayTimeChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="billDueTime" label="账单到期时间">
              <a-range-picker
                v-model="billDueTimeRange"
                style="width: 100%"
                @change="handleBillDueTimeChange"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="sortType" label="排序方式">
              <a-select
                v-model="queryForm.sortType"
                placeholder="请选择排序方式"
                allow-clear
              >
                <a-option value="last_repay_time_desc" label="最后还款时间降序" />
                <a-option value="last_repay_time_asc" label="最后还款时间升序" />
                <a-option value="available_amount_desc" label="可用额度降序" />
                <a-option value="available_amount_asc" label="可用额度升序" />
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 操作按钮 -->
        <a-row>
          <a-col :span="24">
            <a-form-item>
              <a-space>
                <a-button type="primary" html-type="submit">
                  <template #icon>
                    <icon-search />
                  </template>
                  搜索
                </a-button>
                <a-button @click="handleReset">
                  <template #icon>
                    <icon-refresh />
                  </template>
                  重置
                </a-button>

              </a-space>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 表格区域 -->
    <a-card class="general-card" title="待复购客户列表">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="handleRefresh">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <a-table
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :data="tableData"
        :bordered="false"
        size="medium"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <template #columns>
          <a-table-column title="No" :width="60" align="center">
            <template #cell="{ rowIndex }">
              {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
            </template>
          </a-table-column>
          
          <a-table-column title="渠道名称" data-index="channelName" :width="120" />
          <a-table-column title="是否注销" data-index="isCancelledText" :width="100" align="center" />
          <a-table-column title="姓名" data-index="name" :width="100" />
          <a-table-column title="手机号" data-index="mobile" :width="120" />
          <a-table-column title="身份证" :width="180">
            <template #cell="{ record }">
              <a-tooltip :content="record.idCardFull">
                <span style="cursor: pointer; color: #1890ff;">
                  {{ record.idCardMasked }}
                </span>
              </a-tooltip>
            </template>
          </a-table-column>
          <a-table-column title="总额度" data-index="totalAmount" :width="100" align="right">
            <template #cell="{ record }">
              {{ formatCurrency(record.totalAmount) }}
            </template>
          </a-table-column>
          <a-table-column title="可用额度" data-index="availableAmount" :width="100" align="right">
            <template #cell="{ record }">
              {{ formatCurrency(record.availableAmount) }}
            </template>
          </a-table-column>
          <a-table-column title="在借订单数" data-index="borrowingOrderCount" :width="100" align="center" />
          <a-table-column title="订单总数" data-index="totalOrderCount" :width="100" align="center" />
          <a-table-column title="注册时间" data-index="registerTime" :width="150" />
          <a-table-column title="最后还款时间" data-index="lastRepayTime" :width="150" />
          <a-table-column title="账单到期时间" data-index="billDueTime" :width="150" />
          <a-table-column title="最近唤醒时间" data-index="lastAwakenTime" :width="150" />
          <a-table-column title="唤醒次数" data-index="awakenCount" :width="100" align="center" />
          <a-table-column title="唤醒备注" data-index="awakenRemark" :width="150" ellipsis tooltip />
          <a-table-column title="操作" :width="180" fixed="right" align="center">
            <template #cell="{ record }">
              <div class="action-buttons">
                <div class="action-row">
                  <a-button size="small" @click="handleSendSMS(record)">短信唤醒</a-button>
                  <a-button size="small" @click="handleRecordAwaken(record)">记录唤醒</a-button>
                </div>
                <div class="action-row">
                  <a-button size="small" @click="handleViewAwakenRecords(record)">唤醒记录</a-button>
                </div>
              </div>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>

    <!-- 短信唤醒弹窗 -->
    <a-modal
      v-model:visible="smsModalVisible"
      title="短信唤醒"
      @ok="handleSMSSubmit"
      @cancel="handleSMSCancel"
    >
      <a-form ref="smsFormRef" :model="smsForm" layout="vertical">
        <a-form-item field="templateType" label="短信模板" :rules="[{ required: true, message: '请选择短信模板' }]">
          <a-select v-model="smsForm.templateType" placeholder="请选择短信模板">
            <a-option value="repurchase_1" label="复购提醒模板1" />
            <a-option value="repurchase_2" label="复购提醒模板2" />
            <a-option value="repurchase_3" label="优惠活动模板" />
          </a-select>
        </a-form-item>
        <a-form-item field="remark" label="备注">
          <a-textarea
            v-model="smsForm.remark"
            placeholder="请输入备注"
            :max-length="200"
            show-word-limit
            :auto-size="{ minRows: 2, maxRows: 4 }"
          />
        </a-form-item>
        <a-form-item v-if="isBatchSMS" label="选中客户">
          <a-descriptions :column="1" bordered size="small">
            <a-descriptions-item v-for="customer in selectedCustomerDetails" :key="customer.id" :label="`客户${customer.id}`">
              {{ customer.name }} - {{ customer.mobile }}
            </a-descriptions-item>
          </a-descriptions>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 记录唤醒弹窗 -->
    <a-modal
      v-model:visible="awakenModalVisible"
      title="记录唤醒"
      @ok="handleAwakenSubmit"
      @cancel="handleAwakenCancel"
    >
      <a-form ref="awakenFormRef" :model="awakenForm" layout="vertical">
        <a-form-item field="awakenType" label="唤醒方式" :rules="[{ required: true, message: '请选择唤醒方式' }]">
          <a-select v-model="awakenForm.awakenType" placeholder="请选择唤醒方式">
            <a-option :value="1" label="短信唤醒" />
            <a-option :value="2" label="电话唤醒" />
            <a-option :value="3" label="人工记录" />
          </a-select>
        </a-form-item>
        <a-form-item field="awakenContent" label="唤醒内容" :rules="[{ required: true, message: '请输入唤醒内容' }]">
          <a-textarea
            v-model="awakenForm.awakenContent"
            placeholder="请输入详细的唤醒内容（联系结果、短信内容等）"
            :max-length="1000"
            show-word-limit
            :auto-size="{ minRows: 3, maxRows: 6 }"
          />
        </a-form-item>
        <a-form-item field="remark" label="备注小记">
          <a-textarea
            v-model="awakenForm.remark"
            placeholder="可输入补充说明或备注"
            :max-length="1000"
            show-word-limit
            :auto-size="{ minRows: 2, maxRows: 4 }"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 唤醒记录弹窗 -->
    <a-modal
      v-model:visible="awakenRecordsModalVisible"
      title="唤醒记录"
      width="800px"
      :footer="false"
    >
      <a-spin :loading="awakenRecordsLoading">
        <a-table
          :data="awakenRecords"
          :pagination="false"
          size="small"
        >
          <template #columns>
            <a-table-column title="唤醒方式" data-index="awakenTypeText" :width="100" />
            <a-table-column title="唤醒内容" data-index="awakenContent" ellipsis tooltip />
            <a-table-column title="备注" data-index="remark" ellipsis tooltip />
            <a-table-column title="操作员" data-index="operatorName" :width="100" />
            <a-table-column title="记录时间" data-index="recordTime" :width="150" />
          </template>
        </a-table>
      </a-spin>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import {
  IconSearch,
  IconRefresh,
  IconSend,
} from '@arco-design/web-vue/es/icon';
import type {
  RepurchaseCustomerQueryParams,
  RepurchaseCustomerItem,
  RepurchaseCustomerOptions,
  RepurchaseAwakenRecord,
  SendRepurchaseSMSParams,
  RecordRepurchaseAwakenParams,
} from '@/api/repurchase';
import {
  getRepurchaseCustomerList,
  getRepurchaseCustomerOptions,
  sendRepurchaseSMS,
  recordRepurchaseAwaken,
  getRepurchaseAwakenRecords,
} from '@/api/repurchase';

// 响应式数据
const loading = ref(false);
const tableData = ref<RepurchaseCustomerItem[]>([]);
const channelOptions = ref<{ value: string; label: string }[]>([]);

// 查询表单
const queryFormRef = ref();
const queryForm = reactive<RepurchaseCustomerQueryParams>({
  name: '',
  mobile: '',
  idCard: '',
  channelId: '',
  isCancelled: '',
  borrowingOrderCount: '',
  totalOrderCount: '',
  totalAmountMin: '',
  totalAmountMax: '',
  availableAmountMin: '',
  availableAmountMax: '',
  lastRepayTimeStart: '',
  lastRepayTimeEnd: '',
  billDueTimeStart: '',
  billDueTimeEnd: '',
  sortType: '',
  page: 1,
  pageSize: 20,
});

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
});

// 时间范围
const lastRepayTimeRange = ref<string[]>([]);
const billDueTimeRange = ref<string[]>([]);



// 短信唤醒弹窗
const smsModalVisible = ref(false);
const smsFormRef = ref();
const smsForm = reactive({
  templateType: '',
  remark: '',
});
const currentCustomer = ref<RepurchaseCustomerItem | null>(null);

// 记录唤醒弹窗
const awakenModalVisible = ref(false);
const awakenFormRef = ref();
const awakenForm = reactive({
  awakenContent: '',
  awakenType: 2,
  remark: '',
});

// 唤醒记录弹窗
const awakenRecordsModalVisible = ref(false);
const awakenRecordsLoading = ref(false);
const awakenRecords = ref<RepurchaseAwakenRecord[]>([]);
const awakenRecordsPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
});

// 批量短信相关（暂时用不到，但需要定义以避免编译错误）
const isBatchSMS = ref(false);
const selectedCustomerDetails = ref<RepurchaseCustomerItem[]>([]);

// 初始化
onMounted(() => {
  fetchOptions();
  fetchData();
});

// 获取筛选选项
const fetchOptions = async () => {
  try {
    const response = await getRepurchaseCustomerOptions();
    if (response && response.channelOptions) {
      channelOptions.value = response.channelOptions;
    }
  } catch (error) {
    console.error('获取筛选选项失败:', error);
  }
};

// 获取数据
const fetchData = async () => {
  try {
    loading.value = true;
    const params = {
      ...queryForm,
      page: pagination.current,
      pageSize: pagination.pageSize,
    };

    // 移除空值
    Object.keys(params).forEach((key: string) => {
      if (params[key as keyof typeof params] === '' || params[key as keyof typeof params] === undefined || params[key as keyof typeof params] === null) {
        delete params[key as keyof typeof params];
      }
    });

    const response = await getRepurchaseCustomerList(params);
    if (response && response.list) {
      tableData.value = response.list;
      pagination.total = response.total || 0;
    } else {
      tableData.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error('获取复购客户列表失败:', error);
    Message.error('获取复购客户列表失败');
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchData();
};

// 重置
const handleReset = () => {
  queryFormRef.value?.resetFields();
  lastRepayTimeRange.value = [];
  billDueTimeRange.value = [];
  pagination.current = 1;
  fetchData();
};

// 刷新
const handleRefresh = () => {
  fetchData();
};

// 分页变化
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchData();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchData();
};

// 时间范围变化
const handleLastRepayTimeChange = (value: string[]) => {
  if (value && value.length === 2) {
    queryForm.lastRepayTimeStart = value[0];
    queryForm.lastRepayTimeEnd = value[1];
  } else {
    queryForm.lastRepayTimeStart = '';
    queryForm.lastRepayTimeEnd = '';
  }
};

const handleBillDueTimeChange = (value: string[]) => {
  if (value && value.length === 2) {
    queryForm.billDueTimeStart = value[0];
    queryForm.billDueTimeEnd = value[1];
  } else {
    queryForm.billDueTimeStart = '';
    queryForm.billDueTimeEnd = '';
  }
};

// 短信唤醒
const handleSendSMS = (record: RepurchaseCustomerItem) => {
  currentCustomer.value = record;
  smsForm.templateType = '';
  smsForm.remark = '';
  smsModalVisible.value = true;
};



// 短信提交
const handleSMSSubmit = async () => {
  try {
    await smsFormRef.value?.validate();
    
    if (!currentCustomer.value) {
      Message.error('没有选择客户');
      return;
    }

    const params: SendRepurchaseSMSParams = {
      customerIds: [currentCustomer.value.id],
      templateType: smsForm.templateType,
      remark: smsForm.remark,
    };

    await sendRepurchaseSMS(params);
    Message.success('短信发送成功');
    smsModalVisible.value = false;
    fetchData();
  } catch (error) {
    Message.error('短信发送失败');
  }
};

const handleSMSCancel = () => {
  smsModalVisible.value = false;
  smsForm.templateType = '';
  smsForm.remark = '';
};

// 记录唤醒
const handleRecordAwaken = (record: RepurchaseCustomerItem) => {
  currentCustomer.value = record;
  awakenForm.awakenContent = '';
  awakenForm.awakenType = 2;
  awakenForm.remark = '';
  awakenModalVisible.value = true;
};

// 唤醒提交
const handleAwakenSubmit = async () => {
  try {
    await awakenFormRef.value?.validate();
    
    if (!currentCustomer.value) {
      Message.error('没有选择客户');
      return;
    }

    const params: RecordRepurchaseAwakenParams = {
      customerId: currentCustomer.value.id,
      awakenContent: awakenForm.awakenContent,
      awakenType: awakenForm.awakenType,
      remark: awakenForm.remark,
    };

    await recordRepurchaseAwaken(params);
    Message.success('唤醒记录保存成功');
    awakenModalVisible.value = false;
    fetchData();
  } catch (error) {
    Message.error('保存唤醒记录失败');
  }
};

const handleAwakenCancel = () => {
  awakenModalVisible.value = false;
  awakenForm.awakenContent = '';
  awakenForm.awakenType = 2;
  awakenForm.remark = '';
};

// 查看唤醒记录
const handleViewAwakenRecords = async (record: RepurchaseCustomerItem) => {
  currentCustomer.value = record;
  awakenRecordsModalVisible.value = true;
  awakenRecordsPagination.current = 1;
  await fetchAwakenRecords();
};

// 获取唤醒记录
const fetchAwakenRecords = async () => {
  if (!currentCustomer.value) return;
  
  try {
    awakenRecordsLoading.value = true;
    
    const response: any = await getRepurchaseAwakenRecords(
      currentCustomer.value.id,
      awakenRecordsPagination.current,
      awakenRecordsPagination.pageSize
    );
    
    // 处理不同的响应格式
    let dataList = null;
    if (Array.isArray(response)) {
      dataList = response;
    } else if (response && response.data && Array.isArray(response.data)) {
      dataList = response.data;
    } else if (response && Array.isArray(response.list)) {
      dataList = response.list;
    }
    
    if (dataList && dataList.length > 0) {
      // 转换数据格式
      awakenRecords.value = dataList.map((item: any) => ({
        ...item,
        awakenTypeText: getAwakenTypeText(item.awakenType),
        recordTime: formatTimestamp(item.createTime),
      }));
      awakenRecordsPagination.total = dataList.length;
    } else {
      awakenRecords.value = [];
      awakenRecordsPagination.total = 0;
    }
  } catch (error) {
    console.error('获取唤醒记录失败:', error);
    Message.error('获取唤醒记录失败');
    awakenRecords.value = [];
    awakenRecordsPagination.total = 0;
  } finally {
    awakenRecordsLoading.value = false;
  }
};

// 唤醒记录分页变化
const handleAwakenRecordsPageChange = (page: number) => {
  awakenRecordsPagination.current = page;
  fetchAwakenRecords();
};

// 工具函数
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
  }).format(amount);
};

// 获取唤醒类型文本
const getAwakenTypeText = (type: number): string => {
  switch (type) {
    case 1:
      return '短信唤醒';
    case 2:
      return '电话唤醒';
    case 3:
      return '人工记录';
    default:
      return '未知';
  }
};

// 格式化时间戳
const formatTimestamp = (timestamp: string | number): string => {
  if (!timestamp) return '-';
  
  // 如果已经是格式化的时间字符串，直接返回
  if (typeof timestamp === 'string' && timestamp.includes('-')) {
    return timestamp;
  }
  
  // 如果是时间戳，进行转换
  const time = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp;
  if (time === 0) return '-';
  
  const date = new Date(time * 1000);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};
</script>

<style scoped>
.container {
  padding: 0 20px 20px 20px;
}

.general-card {
  margin-bottom: 20px;
}

.general-card + .general-card {
  margin-top: 20px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  width: 100%;
}

.action-row {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-row .arco-btn {
  margin: 1px;
  font-size: 12px;
  padding: 2px 8px;
  height: 24px;
  line-height: 20px;
}
</style> 