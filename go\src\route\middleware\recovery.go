package middleware

import (
	"fincore/global"
	"fincore/utils/gf"
	"os"
	"path/filepath"
	"time"

	"github.com/gin-gonic/gin"
	"gopkg.in/natefinch/lumberjack.v2"
)

func CustomRecovery() gin.HandlerFunc {
	//加载配置
	conf := global.App.Config

	// 按月创建目录结构，与主日志系统保持一致
	now := time.Now()
	monthDir := now.Format("2006-01")   // 月份目录，如：2025-07
	timeStr := now.Format("2006-01-02") // 日期字符串，如：2025-07-17

	// 创建月份目录路径
	monthPath := filepath.Join(conf.Log.RootDir, monthDir)

	// 确保月份目录存在
	if err := os.MkdirAll(monthPath, os.ModePerm); err != nil {
		// 如果创建失败，使用根目录
		monthPath = conf.Log.RootDir
	}

	// 生成错误日志文件路径：log/2025-07/2025-07-17_err.log
	filename := filepath.Join(monthPath, timeStr+"_err.log")

	return gin.RecoveryWithWriter(
		&lumberjack.Logger{
			Filename:   filename,
			MaxSize:    conf.Log.MaxSize,
			MaxBackups: conf.Log.MaxBackups,
			MaxAge:     conf.Log.MaxAge,
			Compress:   conf.Log.Compress,
		},
		gf.ServerError)
}
