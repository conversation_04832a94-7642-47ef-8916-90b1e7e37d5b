package common

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"time"
)

// PostRequest 表示一个POST请求
type PostRequest struct {
	URL     string            // 请求URL
	Headers map[string]string // 请求头
	Body    interface{}       // 请求体
	Timeout time.Duration     // 超时时间
}

// PostResponse 表示POST请求的响应
type PostResponse struct {
	StatusCode int               // 状态码
	Headers    map[string]string // 响应头
	Body       []byte            // 响应体
}

// PostJSON 发送JSON格式的POST请求
func PostJSON(req PostRequest) (*PostResponse, error) {
	// 序列化请求体
	jsonBody, err := json.Marshal(req.Body)
	if err != nil {
		return nil, err
	}

	// 创建请求
	httpReq, err := http.NewRequest("POST", req.URL, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, err
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	for key, value := range req.Headers {
		httpReq.Header.Set(key, value)
	}

	// 创建HTTP客户端并设置超时
	client := &http.Client{
		Timeout: req.Timeout,
	}

	// 发送请求
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 处理响应头
	headers := make(map[string]string)
	for key, values := range resp.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	// 返回响应
	return &PostResponse{
		StatusCode: resp.StatusCode,
		Headers:    headers,
		Body:       body,
	}, nil
}

// PostForm 发送表单格式的POST请求
func PostForm(req PostRequest) (*PostResponse, error) {
	// 转换请求体为表单数据
	formData, ok := req.Body.(map[string]string)
	if !ok {
		return nil, &json.UnsupportedTypeError{}
	}

	// 准备表单数据
	values := make(map[string][]string)
	for key, value := range formData {
		values[key] = []string{value}
	}

	// 创建请求
	httpReq, err := http.NewRequest("POST", req.URL, nil)
	if err != nil {
		return nil, err
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	for key, value := range req.Headers {
		httpReq.Header.Set(key, value)
	}

	// 设置表单数据
	httpReq.Form = values

	// 创建HTTP客户端并设置超时
	client := &http.Client{
		Timeout: req.Timeout,
	}

	// 发送请求
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 处理响应头
	headers := make(map[string]string)
	for key, values := range resp.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	// 返回响应
	return &PostResponse{
		StatusCode: resp.StatusCode,
		Headers:    headers,
		Body:       body,
	}, nil
}