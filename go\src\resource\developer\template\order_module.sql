CREATE TABLE `business_collection_logs` (
                                            `id` int unsigned NOT NULL AUTO_INCREMENT,
                                            `order_id` int unsigned NOT NULL COMMENT '订单ID',
                                            `bill_id` int unsigned DEFAULT NULL COMMENT '催收针对的账单ID (可为空,表示对整个订单催收)',
                                            `collector_id` int unsigned NOT NULL COMMENT '催收员ID',
                                            `collector_name` varchar(50) NOT NULL COMMENT '催收员姓名',
                                            `action_type` varchar(50) NOT NULL COMMENT '催收方式 (如: 电话, 短信, 线下)',
                                            `summary` varchar(255) NOT NULL COMMENT '催收摘要/结果 (如: 承诺还款, 电话无人接听, 电话关机)',
                                            `remarks` text COMMENT '催收详情备注 (UI上的小记)',
                                            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                                            PRIMARY KEY (`id`),
                                            KEY `idx_order_id` (`order_id`),
                                            KEY `idx_collector_id` (`collector_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='催收记录表';



CREATE TABLE `business_loan_orders` (
                                        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                        `order_no` varchar(32) NOT NULL COMMENT '订单编号',
                                        `user_id` int unsigned NOT NULL COMMENT '用户ID',
                                        `product_rule_id` int unsigned NOT NULL COMMENT '产品规则ID',
                                        `loan_amount` decimal(10,2) NOT NULL COMMENT '申请贷款金额',
                                        `principal` decimal(10,2) NOT NULL COMMENT '实际放款本金',
                                        `total_interest` decimal(10,2) NOT NULL COMMENT '总利息',
                                        `total_guarantee_fee` decimal(10,2) NOT NULL COMMENT '总担保费',
                                        `total_other_fees` decimal(10,2) NOT NULL COMMENT '总其他费用',
                                        `total_repayable_amount` decimal(10,2) NOT NULL COMMENT '应还总额',
                                        `amount_paid` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '已付总额',
                                        `channel_id` int NOT NULL DEFAULT '0' COMMENT '渠道来源 (如公众号)',
                                        `customer_origin` varchar(50) NOT NULL DEFAULT '' COMMENT '客户来源',
                                        `initial_order_channel_id` int NOT NULL DEFAULT '0' COMMENT '初始下单渠道',
                                        `payment_channel_id` int unsigned DEFAULT NULL COMMENT '放款/代扣支付渠道ID (UI中的订单归属, 如TTF)',
                                        `status` tinyint NOT NULL DEFAULT '0' COMMENT '订单状态: 0-待放款(等待风控或人工审核), 1-放款中(已成功放款), 2-交易关闭(审核未通过), 3-交易完成(还款完成)',
                                        `is_freeze` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否冻结: 0-否; 1-是 (对应关闭订单弹窗)',
                                        `is_refund_needed` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否需要退款: 0-否; 1-是 (关闭订单时标记)',
                                        `complaint_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '投诉状态: 0-否; 1-是',
                                        `reason_for_closure` tinyint DEFAULT NULL COMMENT '订单关闭原因: 0-终审拒绝；1-法院涉案；2-纯白户；3-客户失联；4-不提供资料；5-多余订单；6-重新下单；7-客户不同意方案',
                                        `closure_remarks` text COMMENT '订单关闭备注 (对应关闭订单弹窗的小记)',
                                        `audit_assignee_id` int unsigned DEFAULT NULL COMMENT '审核员ID',
                                        `review_status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '复审状态: 0-未复审, 1-复审通过, 2-复审拒绝',
                                        `remarks_id` bigint DEFAULT NULL COMMENT '关联普通备注表ID，指向business_order_remarks表的主键',
                                        `sales_assignee_id` int unsigned DEFAULT NULL COMMENT '跟进的业务员ID',
                                        `collection_assignee_id` int unsigned DEFAULT NULL COMMENT '当前催收员ID',
                                        `receipt_url` varchar(255) NOT NULL DEFAULT '' COMMENT '回执单/合同URL',
                                        `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `disbursed_at` timestamp NULL DEFAULT NULL COMMENT '放款成功时间',
                                        `completed_at` timestamp NULL DEFAULT NULL COMMENT '结清/关闭时间',
                                        `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                        `review_remark` text COMMENT '复审备注，直接存储复审时的备注内容',
                                        PRIMARY KEY (`id`),
                                        UNIQUE KEY `uk_order_no` (`order_no`),
                                        KEY `idx_user_id` (`user_id`),
                                        KEY `idx_status` (`status`),
                                        KEY `idx_sales_assignee_id` (`sales_assignee_id`),
                                        KEY `idx_collection_assignee_id` (`collection_assignee_id`),
                                        KEY `idx_business_loan_orders_channel_id` (`channel_id`),
                                        KEY `idx_business_loan_orders_initial_order_channel_id` (`initial_order_channel_id`),
                                        KEY `idx_business_loan_orders_audit_assignee_id` (`audit_assignee_id`),
                                        KEY `idx_business_loan_orders_reason_for_closure` (`reason_for_closure`),
                                        KEY `idx_review_remarks_id` (`remarks_id`) COMMENT '备注ID索引'
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='贷款订单表';


CREATE TABLE `business_order_operation_logs` (
                                                 `id` int unsigned NOT NULL AUTO_INCREMENT,
                                                 `order_id` int unsigned NOT NULL COMMENT '订单ID',
                                                 `operator_id` int unsigned NOT NULL COMMENT '操作人ID (0代表系统)',
                                                 `operator_name` varchar(50) NOT NULL COMMENT '操作人姓名',
                                                 `action` varchar(100) NOT NULL COMMENT '操作动作 (如: 创建订单, 关闭订单, 结清减免, 分配催收)',
                                                 `details` text COMMENT '操作详情/备注 (JSON格式, 存操作上下文)',
                                                 `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                                                 PRIMARY KEY (`id`),
                                                 KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单操作日志表';


CREATE TABLE `business_order_remarks` (
                                          `id` bigint NOT NULL AUTO_INCREMENT COMMENT '备注ID，主键',
                                          `order_id` bigint NOT NULL COMMENT '订单ID，关联business_loan_orders表的id字段',
                                          `content` text COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注内容，支持长文本',
                                          `user_id` bigint NOT NULL COMMENT '备注人用户ID，关联business_account表的id字段',
                                          `create_time` bigint NOT NULL COMMENT '备注创建时间，Unix时间戳',
                                          `update_time` bigint NOT NULL COMMENT '备注更新时间，Unix时间戳',
                                          PRIMARY KEY (`id`),
                                          KEY `idx_order_id` (`order_id`) COMMENT '订单ID索引，用于快速查询某订单的所有备注',
                                          KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引，用于查询某用户的所有备注',
                                          KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引，用于按时间排序',
                                          KEY `idx_order_create` (`order_id`,`create_time`) COMMENT '复合索引，优化按订单ID和时间查询'
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单备注表，存储订单的详细备注信息';


CREATE TABLE `business_payment_channels` (
                                             `id` int unsigned NOT NULL AUTO_INCREMENT,
                                             `channel_name` varchar(50) NOT NULL COMMENT '渠道名称 (如: 统统付, 宝付)',
                                             `channel_code` varchar(20) NOT NULL COMMENT '渠道唯一编码 (如: TTF, baofu)',
                                             `config` json NOT NULL COMMENT '渠道配置 (JSON格式, 存储API密钥, 商户号, 回调地址等)',
                                             `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '渠道状态 0-未启用；1-启用',
                                             `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                                             `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                             PRIMARY KEY (`id`),
                                             UNIQUE KEY `uk_channel_code` (`channel_code`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='支付渠道表';



CREATE TABLE `business_repayment_bills` (
                                            `id` int unsigned NOT NULL AUTO_INCREMENT,
                                            `order_id` int unsigned NOT NULL COMMENT '订单ID',
                                            `user_id` int unsigned NOT NULL COMMENT '用户ID',
                                            `period_number` int unsigned NOT NULL COMMENT '期数 (从1开始)',
                                            `due_principal` decimal(15,2) NOT NULL COMMENT '当期实际放款金额',
                                            `due_interest` decimal(15,2) NOT NULL COMMENT '当期应还利息',
                                            `due_guarantee_fee` decimal(15,2) NOT NULL COMMENT '当期应还担保费',
                                            `due_other_fees` decimal(15,2) NOT NULL COMMENT '当期应还其他费用',
                                            `late_fee` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '逾期罚息',
                                            `paid_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '当期已还金额',
                                            `waived_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '当期减免金额',
                                            `status` tinyint NOT NULL DEFAULT '0' COMMENT '账单状态: 0-待支付；1-已支付；2-预期已支付；3-预期待支付；4-已取消；5-已结算；6-已退款；7-部分还款；8-提前结清',
                                            `due_date` date NOT NULL COMMENT '应还款日',
                                            `deduct_retry_count` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '自动扣款重试次数',
                                            `last_deduct_attempt_at` timestamp NULL DEFAULT NULL COMMENT '上次自动扣款尝试时间',
                                            `paid_at` timestamp NULL DEFAULT NULL COMMENT '当期结清时间',
                                            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                                            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                            `total_due_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '当期应还总额(担保费+资管费+逾期费)',
                                            `asset_management_entry` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '当期资管费用(本金+利息+其他费用)',
                                            PRIMARY KEY (`id`),
                                            KEY `idx_order_id_period` (`order_id`,`period_number`),
                                            KEY `idx_due_date_status` (`due_date`,`status`),
                                            KEY `idx_order_id_status` (`order_id`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='还款计划账单表';