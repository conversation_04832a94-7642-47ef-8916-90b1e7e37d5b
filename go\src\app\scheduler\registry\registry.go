package registry

import (
	"fincore/app/scheduler/tasks"
	"fincore/utils/log"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// TaskRegistry 任务注册中心
type TaskRegistry struct {
	tasks  map[string]tasks.TaskInterface // 注册的任务映射
	mutex  sync.RWMutex                   // 读写锁
	logger *log.Logger                    // 日志器
}

// TaskStatus 任务状态
type TaskStatus struct {
	Name            string                `json:"name"`
	Description     string                `json:"description"`
	Schedule        string                `json:"schedule"`
	ConcurrencyMode tasks.ConcurrencyMode `json:"concurrency_mode"`
	IsRunning       bool                  `json:"is_running"`
	RunningCount    int                   `json:"running_count"`
	LastRunTime     *time.Time            `json:"last_run_time,omitempty"`
	NextRunTime     *time.Time            `json:"next_run_time,omitempty"`
	RunCount        int64                 `json:"run_count"`
	SuccessCount    int64                 `json:"success_count"`
	FailureCount    int64                 `json:"failure_count"`
	LastError       string                `json:"last_error,omitempty"`
}

// NewTaskRegistry 创建任务注册中心
func NewTaskRegistry(logger *log.Logger) *TaskRegistry {
	return &TaskRegistry{
		tasks:  make(map[string]tasks.TaskInterface),
		logger: logger,
	}
}

// RegisterTask 注册任务
func (r *TaskRegistry) RegisterTask(task tasks.TaskInterface) error {
	if task == nil {
		return fmt.Errorf("任务不能为nil")
	}

	// 验证任务
	if err := tasks.ValidateTask(task); err != nil {
		return fmt.Errorf("任务验证失败: %w", err)
	}

	r.mutex.Lock()
	defer r.mutex.Unlock()

	taskName := task.GetName()

	// 检查是否已注册
	if _, exists := r.tasks[taskName]; exists {
		return fmt.Errorf("任务 %s 已经注册", taskName)
	}

	// 注册任务
	r.tasks[taskName] = task

	r.logger.Info("任务注册成功",
		zap.String("task_name", taskName),
		zap.String("description", task.GetDescription()),
		zap.String("schedule", task.GetSchedule()),
		zap.String("concurrency_mode", task.GetConcurrencyMode().String()),
		zap.Duration("timeout", task.GetTimeout()),
		zap.Int("retry_count", task.GetRetryCount()),
	)

	return nil
}

// UnregisterTask 注销任务
func (r *TaskRegistry) UnregisterTask(taskName string) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if _, exists := r.tasks[taskName]; !exists {
		return fmt.Errorf("任务 %s 未注册", taskName)
	}

	delete(r.tasks, taskName)

	r.logger.Info("任务注销成功",
		zap.String("task_name", taskName),
	)

	return nil
}

// GetTask 获取任务
func (r *TaskRegistry) GetTask(taskName string) (tasks.TaskInterface, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	task, exists := r.tasks[taskName]
	if !exists {
		return nil, fmt.Errorf("任务 %s 未找到", taskName)
	}

	return task, nil
}

// GetAllTasks 获取所有任务
func (r *TaskRegistry) GetAllTasks() map[string]tasks.TaskInterface {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	// 创建副本避免并发问题
	result := make(map[string]tasks.TaskInterface, len(r.tasks))
	for name, task := range r.tasks {
		result[name] = task
	}

	return result
}

// GetTaskNames 获取所有任务名称
func (r *TaskRegistry) GetTaskNames() []string {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	names := make([]string, 0, len(r.tasks))
	for name := range r.tasks {
		names = append(names, name)
	}

	return names
}

// GetTaskCount 获取任务数量
func (r *TaskRegistry) GetTaskCount() int {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	return len(r.tasks)
}

// HasTask 检查任务是否存在
func (r *TaskRegistry) HasTask(taskName string) bool {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	_, exists := r.tasks[taskName]
	return exists
}

// GetTaskMetadata 获取任务元数据
func (r *TaskRegistry) GetTaskMetadata(taskName string) (*tasks.TaskMetadata, error) {
	task, err := r.GetTask(taskName)
	if err != nil {
		return nil, err
	}

	metadata := tasks.GetMetadata(task)
	return &metadata, nil
}

// GetAllTasksMetadata 获取所有任务元数据
func (r *TaskRegistry) GetAllTasksMetadata() []tasks.TaskMetadata {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	metadata := make([]tasks.TaskMetadata, 0, len(r.tasks))
	for _, task := range r.tasks {
		metadata = append(metadata, tasks.GetMetadata(task))
	}

	return metadata
}

// ValidateAllTasks 验证所有已注册的任务
func (r *TaskRegistry) ValidateAllTasks() error {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	for name, task := range r.tasks {
		if err := tasks.ValidateTask(task); err != nil {
			return fmt.Errorf("任务 %s 验证失败: %w", name, err)
		}
	}

	return nil
}

// GetRegistryStats 获取注册中心统计信息
func (r *TaskRegistry) GetRegistryStats() map[string]interface{} {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	parallelCount := 0
	singletonCount := 0

	for _, task := range r.tasks {
		switch task.GetConcurrencyMode() {
		case tasks.ConcurrencyModeParallel:
			parallelCount++
		case tasks.ConcurrencyModeSingleton:
			singletonCount++
		}
	}

	return map[string]interface{}{
		"total_tasks":     len(r.tasks),
		"parallel_tasks":  parallelCount,
		"singleton_tasks": singletonCount,
		"registry_time":   time.Now(),
	}
}

// Clear 清空所有任务（主要用于测试）
func (r *TaskRegistry) Clear() {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	taskCount := len(r.tasks)
	r.tasks = make(map[string]tasks.TaskInterface)

	r.logger.Info("任务注册中心已清空",
		zap.Int("cleared_count", taskCount),
	)
}

// GetTasksByMode 根据并发模式获取任务
func (r *TaskRegistry) GetTasksByMode(mode tasks.ConcurrencyMode) []tasks.TaskInterface {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var result []tasks.TaskInterface
	for _, task := range r.tasks {
		if task.GetConcurrencyMode() == mode {
			result = append(result, task)
		}
	}

	return result
}

// GetParallelTasks 获取并行模式的任务
func (r *TaskRegistry) GetParallelTasks() []tasks.TaskInterface {
	return r.GetTasksByMode(tasks.ConcurrencyModeParallel)
}

// GetSingletonTasks 获取单例模式的任务
func (r *TaskRegistry) GetSingletonTasks() []tasks.TaskInterface {
	return r.GetTasksByMode(tasks.ConcurrencyModeSingleton)
}
