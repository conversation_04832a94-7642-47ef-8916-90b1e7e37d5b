server {
    listen 443 ssl;  # 监听 HTTPS 443 端口
    server_name localhost;  # 替换为你的域名或IP

    # SSL 证书配置（必须替换为你的证书路径）
    ssl_certificate /data/fincoreneal/fincore/conf/nginx/cert.pem;
    ssl_certificate_key /data/fincoreneal/fincore/conf/nginx/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    # 设置根目录
    root /data/fincoreneal/fincore/vue/uniapp/unpackage/dist/build/web;  # 替换为你的静态文件目录

    # 处理静态文件
    location / {
        try_files $uri $uri/ /index.html;  # 假设使用单页应用，可以重定向到 index.html
    }

    # 代理到后端服务（假设后端端口为 8108）
    location /uniapp/ {  # 假设所有 API 请求都以 /api/ 开头
        proxy_pass http://localhost:8108;  # 后端服务地址
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}