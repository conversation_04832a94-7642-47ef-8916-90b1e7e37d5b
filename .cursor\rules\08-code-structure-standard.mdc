---
description: go/*
alwaysApply: false
---
# 代码结构规范

## 标准模块结构

所有新增模块必须遵循 [app/business/customer](mdc:src/app/business/customer) 目录的结构和代码风格：

1. **文件组织**
   - `controller.go` - 控制器，处理 HTTP 请求和响应
   - `service.go` - 服务层，实现业务逻辑
   - `schema.go` - 数据结构和验证规则
   - `utils.go` - 模块内工具函数

2. **代码风格**
   - 使用大驼峰命名方法和结构体
   - 使用小驼峰命名变量
   - 方法和结构体需要添加注释说明功能
   - 统一错误处理方式
   - 遵循清晰的代码分层

## 分层结构

遵循以下分层结构进行代码组织：

### 控制器层 (controller.go)

- 负责处理 HTTP 请求和响应
- 参数验证
- 调用服务层方法
- 格式化返回结果
- 不包含业务逻辑

示例结构：
```go
// 控制器结构体
type CustomerController struct {
    Service *CustomerService
}

// API 处理方法
func (ctrl *CustomerController) List(c *gin.Context) {
    // 参数处理
    // 调用服务层
    // 返回结果
}
```

### 服务层 (service.go)

- 实现具体业务逻辑
- 处理数据库操作
- 调用其他服务
- 处理业务规则和流程

示例结构：
```go
// 服务结构体
type CustomerService struct {
    // 依赖项
}

// 业务方法
func (svc *CustomerService) GetCustomerList(params interface{}) (interface{}, error) {
    // 业务逻辑实现
}
```

### 数据结构 (schema.go)

- 定义请求和响应结构
- 定义数据验证规则
- 定义模型转换方法

示例结构：
```go
// 请求结构
type CustomerListRequest struct {
    Page     int    `json:"page" form:"page"`
    PageSize int    `json:"page_size" form:"page_size"`
    Keyword  string `json:"keyword" form:"keyword"`
}

// 响应结构
type CustomerListResponse struct {
    List  []CustomerInfo `json:"list"`
    Total int64          `json:"total"`
}
```

### 工具函数 (utils.go)

- 提供模块内部使用的辅助函数
- 处理特定领域的计算和转换
- 封装重复使用的逻辑

示例结构：
```go
// 格式化函数
func FormatCustomerData(data interface{}) interface{} {
    // 格式化逻辑
}

// 辅助计算函数
func CalculateCustomerMetrics(data interface{}) interface{} {
    // 计算逻辑
}
```

## 开发新模块

开发新模块时，应该：

1. 复制 `customer` 模块的文件结构作为模板
2. 保持相同的代码组织方式和命名规范
3. 遵循相同的注释风格和错误处理方式
4. 保持一致的函数签名和返回值处理

这将确保整个项目的代码风格统一，提高可维护性和代码质量。

