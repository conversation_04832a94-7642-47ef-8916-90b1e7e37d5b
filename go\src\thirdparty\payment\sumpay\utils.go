package sumpay

import (
	"fmt"
	"math/rand"
	"time"
)

// 根据商盟支付错误码返回错误信息
func GetErrorMessage(respCode string) string {
	switch respCode {
	case "000000":
		return "成功"
	case "000001":
		return "系统异常"
	case "000002":
		return "参数错误"
	case "000003":
		return "签名验证失败"
	case "000004":
		return "商户不存在"
	case "000005":
		return "商户状态异常"
	case "000006":
		return "交易金额超限"
	case "000007":
		return "余额不足"
	case "000008":
		return "订单不存在"
	case "000009":
		return "订单状态异常"
	case "000010":
		return "重复提交"
	case "000011":
		return "银行卡号格式错误"
	case "000012":
		return "手机号格式错误"
	case "000013":
		return "身份证号格式错误"
	case "000014":
		return "姓名格式错误"
	case "000015":
		return "短信验证码错误"
	case "000016":
		return "短信验证码已过期"
	case "000017":
		return "银行卡已绑定"
	case "000018":
		return "银行卡绑定失败"
	case "000019":
		return "银行卡解绑失败"
	case "000020":
		return "银行卡不支持"
	case "000021":
		return "用户信息不匹配"
	case "000022":
		return "交易处理中"
	case "000023":
		return "交易失败"
	case "000024":
		return "网络超时"
	case "000025":
		return "服务暂不可用"
	default:
		return fmt.Sprintf("未知错误码: %s", respCode)
	}
}

// GeneratePaymentTransactionNo 生成唯一支付流水号
func GeneratePaymentTransactionNo() string {
	return fmt.Sprintf("%d%d%d", time.Now().UnixNano(), rand.Intn(1000000), rand.Intn(1000000))
}
