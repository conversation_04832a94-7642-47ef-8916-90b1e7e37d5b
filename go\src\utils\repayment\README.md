# 还款计划计算器

## 概述

这个包提供了还款计划计算的公共逻辑，可以被多个场景复用，包括：
- 创建订单时生成实际的还款计划
- 创单前的预计算展示
- 其他需要计算还款计划的场景

## 主要特性

- **纯计算逻辑**：不涉及数据库操作，便于测试和复用
- **结构体数据**：使用结构体而不是 map 来承载数据，提高类型安全性
- **灵活配置**：支持自定义基准日期
- **完整测试**：包含完整的单元测试

## 核心结构体

### RepaymentPeriod - 单期还款计划
```go
type RepaymentPeriod struct {
    PeriodNumber            int     // 期数
    DuePrincipal            float64 // 应还本金（展示用）
    DueInterest             float64 // 应还利息
    DueGuaranteeFee         float64 // 应还担保费（进入担保账户）
    DueOtherFees            float64 // 应还其他费用
    AssetManagementFee      float64 // 资管费（进入资管账户）
    LateFee                 float64 // 逾期费
    TotalDueAmount          float64 // 当期应还总额
    DueDate                 string  // 到期日期
}
```

### RepaymentSchedule - 完整还款计划
```go
type RepaymentSchedule struct {
    TotalPeriods            int               // 总期数
    Periods                 []RepaymentPeriod // 各期还款计划
    TotalPrincipal          float64           // 总本金
    TotalInterest           float64           // 总利息
    TotalGuaranteeFee       float64           // 总担保费
    TotalOtherFees          float64           // 总其他费用
    TotalAssetManagementFee float64           // 总资管费
    TotalRepayableAmount    float64           // 总应还金额
    DisbursementAmount      float64           // 实际放款金额
    IsPrePayment            bool              // 是否为前置还款
}
```



## 核心方法

### CalculateRepaymentSchedule - 计算还款计划
```go
func CalculateRepaymentSchedule(productRule *model.ProductRules) (*RepaymentSchedule, error)
```

**参数说明：**
- `productRule`: 产品规则对象（包含贷款金额等所有必要信息）

**返回值：**
- `*RepaymentSchedule`: 完整的还款计划
- `error`: 错误信息

**计算逻辑：**
- 支持任意期数（从产品规则的 TotalPeriods 字段获取）
- 每期间隔天数 = LoanPeriod（借款周期天数）
- 总还款天数 = LoanPeriod × TotalPeriods
- 各项费用按期数均摊到每期

### ConvertToRepaymentBillMaps - 转换为数据库插入 map
```go
func ConvertToRepaymentBillMaps(schedule *RepaymentSchedule, orderID, userID int) []map[string]interface{}
```

**参数说明：**
- `schedule`: 还款计划
- `orderID`: 订单ID
- `userID`: 用户ID

**返回值：**
- `[]map[string]interface{}`: 数据库插入用的 map 数组

## 使用示例

### 1. 基本使用
```go
import "fincore/utils/repayment"

// 创建产品规则
productRule := &model.ProductRules{
    LoanAmount:         10000.00,
    LoanPeriod:         30,    // 借款周期30天，每期间隔30天
    TotalPeriods:       3,     // 支持任意期数，总还款天数=30*3=90天
    GuaranteeFee:       500.00,
    AnnualInterestRate: 15.0,
    OtherFees:          100.00,
    RepaymentMethod:    "后置付款",
}

// 计算还款计划
schedule, err := repayment.CalculateRepaymentSchedule(productRule)
if err != nil {
    log.Printf("计算失败: %v", err)
    return
}

// 输出结果
fmt.Printf("总期数: %d\n", schedule.TotalPeriods)
fmt.Printf("总应还金额: %.2f\n", schedule.TotalRepayableAmount)
```

### 2. 创建订单时使用
```go
// 在订单服务中使用
func (s *Service) createRepaymentScheduleInTransaction(tx gform.IOrm, orderID, userID int, productRule *model.ProductRules, amounts OrderAmounts) error {
    // 使用公共方法计算还款计划
    schedule, err := repayment.CalculateRepaymentSchedule(productRule)
    if err != nil {
        return fmt.Errorf("计算还款计划失败: %v", err)
    }

    // 转换为数据库插入 map
    billMaps := repayment.ConvertToRepaymentBillMaps(schedule, orderID, userID)

    // 插入数据库
    for i, billMap := range billMaps {
        _, err := tx.Table("business_repayment_bills").InsertGetId(billMap)
        if err != nil {
            return fmt.Errorf("创建第%d期还款计划失败: %v", i+1, err)
        }
    }

    return nil
}
```

### 3. 预计算展示使用
```go
// 在前端预览接口中使用
func PreviewRepaymentSchedule(loanAmount float64, productRuleID int) (*repayment.RepaymentSchedule, error) {
    // 查询产品规则
    productRule, err := getProductRuleByID(productRuleID)
    if err != nil {
        return nil, err
    }

    // 如果需要使用用户输入的金额，更新产品规则
    if loanAmount != productRule.LoanAmount {
        tempRule := *productRule
        tempRule.LoanAmount = loanAmount
        productRule = &tempRule
    }

    // 计算还款计划
    return repayment.CalculateRepaymentSchedule(productRule)
}
```

## 计算逻辑说明

### 费用计算
1. **利息计算**：本金 × 年利率 × (天数/365)
2. **资管费**：本金 + 利息 + 其他费用
3. **当期应还总额**：担保费 + 资管费

### 分期计算
- 各项费用按期数均摊
- 期间间隔天数 = 总天数 / 期数
- 到期日期 = 基准日期 + (期间间隔天数 × 期数)

### 前置/后置还款
- **前置还款**：放款时扣除利息和费用
- **后置还款**：放款金额等于申请金额

## 测试

运行测试：
```bash
go test ./utils/repayment -v
```

测试覆盖：
- 基本计算逻辑测试
- 数据结构转换测试
- 无效参数测试
- 边界条件测试

## 注意事项

1. **期数限制**：当前最多支持2期还款
2. **数据精度**：使用 float64 进行计算，注意浮点数精度问题
3. **时间处理**：日期格式统一为 "2006-01-02"
4. **错误处理**：所有公共方法都包含完整的错误处理

## 扩展性

这个设计支持未来的扩展：
- 添加新的还款方式
- 支持更多期数
- 添加复杂的费用计算规则
- 支持不同的利率计算方式
