package auth

import (
	"bytes"
	"context"
	"crypto"
	"crypto/md5"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha1"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"sort"
	"strconv"
	"time"
)

// SignatureAuth 签名认证器（电子签方式）
type SignatureAuth struct {
	appId      string
	privateKey string
	host       string
}

// NewSignatureAuth 创建签名认证器
func NewSignatureAuth(appId, privateKey, host string) *SignatureAuth {
	return &SignatureAuth{
		appId:      appId,
		privateKey: privateKey,
		host:       host,
	}
}

// GetAuthType 获取认证类型
func (s *SignatureAuth) GetAuthType() AuthType {
	return AuthTypeSignature
}

// Authenticate 执行认证，完全参考现有电子签实现
func (s *SignatureAuth) Authenticate(ctx context.Context, req *http.Request, bodyData []byte) error {
	// 获取当前时间戳并加10分钟（参考原实现）
	timestamp := time.Now().Add(10*time.Minute).UnixNano() / int64(time.Millisecond)

	// 对 bizData 进行排序（参考原实现）
	sortedJsonStr, err := s.sortJSON(string(bodyData))
	if err != nil {
		return fmt.Errorf("排序 JSON 失败: %v", err)
	}

	// 生成签名（完全参考原实现）
	rsaSuffix := sortedJsonStr + s.md5Hex(sortedJsonStr) + s.appId + strconv.FormatInt(timestamp, 10)

	sign, err := s.generateSHA1withRSASignature(rsaSuffix, s.privateKey)
	if err != nil {
		return fmt.Errorf("生成签名失败: %v", err)
	}

	// 重新构造multipart请求体（参考原实现）
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// 添加必要参数
	writer.WriteField("appId", s.appId)
	writer.WriteField("timestamp", strconv.FormatInt(timestamp, 10))
	writer.WriteField("bizData", string(bodyData))

	writer.Close()

	// 更新请求体
	req.Body = io.NopCloser(body)
	req.ContentLength = int64(body.Len())

	// 设置请求头（参考原实现）
	req.Header.Set("sign", sign)
	req.Header.Set("timestamp", strconv.FormatInt(timestamp, 10))
	req.Header.Set("Charset", "UTF-8")
	req.Header.Set("Content-Type", writer.FormDataContentType())

	return nil
}

// IsTokenExpired 签名认证不需要token刷新
func (s *SignatureAuth) IsTokenExpired(err error) bool {
	return false
}

// RefreshToken 签名认证不需要token刷新
func (s *SignatureAuth) RefreshToken(ctx context.Context) error {
	return nil
}

// sortJSON 对JSON进行排序（参考原实现）
func (s *SignatureAuth) sortJSON(jsonStr string) (string, error) {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", err
	}

	keys := make([]string, 0, len(data))
	for k := range data {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	sortedData := make(map[string]interface{})
	for _, k := range keys {
		sortedData[k] = data[k]
	}

	sortedJson, err := json.Marshal(sortedData)
	if err != nil {
		return "", err
	}

	return string(sortedJson), nil
}

// md5Hex MD5哈希（参考原实现）
func (s *SignatureAuth) md5Hex(str string) string {
	hash := md5.New()
	hash.Write([]byte(str))
	return fmt.Sprintf("%x", hash.Sum(nil))
}

// generateSHA1withRSASignature 生成RSA签名（完全参考原实现）
func (s *SignatureAuth) generateSHA1withRSASignature(data, priKey string) (string, error) {
	// privateKey 转base64
	block, err := base64.StdEncoding.DecodeString(priKey)
	if err != nil {
		return "", fmt.Errorf("Failed to decode Base64 string: %v", err)
	}

	var privateKey *rsa.PrivateKey
	privateKeyInterface, err := x509.ParsePKCS8PrivateKey(block)
	if err != nil {
		return "", fmt.Errorf("Failed to parse private key: %v", err)
	}

	privateKey, ok := privateKeyInterface.(*rsa.PrivateKey)
	if !ok {
		return "", fmt.Errorf("Private key is not of type *rsa.PrivateKey")
	}

	// 计算哈希值
	hash := sha1.New()
	hash.Write([]byte(data))
	hashed := hash.Sum(nil)

	// 计算签名
	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA1, hashed)
	if err != nil {
		return "", fmt.Errorf("Failed to sign the message: %v", err)
	}

	// 将签名转换为 Base64 编码的字符串
	return base64.StdEncoding.EncodeToString(signature), nil
}
