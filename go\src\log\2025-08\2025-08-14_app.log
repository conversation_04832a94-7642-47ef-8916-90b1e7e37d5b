{"level":"dev.info","ts":"[2025-08-14 09:36:24.804]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.808]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.840]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 09:36:24.846]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.790]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.816]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.857]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 09:38:29.870]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"src/main.go:58","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"src/main.go:58","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.897]","caller":"src/main.go:58","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-14 09:38:53.898]","caller":"src/main.go:58","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.070]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.095]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.138]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 09:39:01.146]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.309]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.328]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.371]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 09:39:30.376]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.error","ts":"[2025-08-14 09:39:30.378]","caller":"runtime/asm_amd64.s:1700","msg":"HTTP服务器启动失败: listen tcp :8108: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.\n"}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.520]","caller":"src/main.go:58","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.521]","caller":"src/main.go:58","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.521]","caller":"src/main.go:58","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-14 09:40:24.521]","caller":"src/main.go:58","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.697]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.715]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.740]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 09:40:42.745]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.error","ts":"[2025-08-14 09:40:42.746]","caller":"runtime/asm_amd64.s:1700","msg":"HTTP服务器启动失败: listen tcp :8108: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.\n"}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.428]","caller":"src/main.go:58","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.429]","caller":"src/main.go:58","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.429]","caller":"src/main.go:58","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-14 09:41:38.429]","caller":"src/main.go:58","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.269]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.286]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.338]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 09:42:41.345]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.debug","ts":"[2025-08-14 09:43:07.378]","caller":"results/rebjson.go:48","msg":"请求成功: msg: 获取收入明细成功","data":"{\"statistics\":{\"total_income\":1163.21,\"total_refund\":60.14,\"due_income\":0,\"overdue_income\":65.83,\"early_income\":999.06,\"total_customers\":3,\"total_orders\":19},\"list\":{\"total\":111,\"page\":1,\"pageSize\":10,\"totalPages\":12,\"hasNext\":true,\"hasPrev\":false,\"data\":[{\"amount\":\"50.00\",\"completed_at\":\"2025-08-12 14:42:24\",\"due_date\":null,\"fund_type_text\":\"收款\",\"id\":226,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250812OY6SMGWH\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"未知\",\"period_number\":null,\"sequence\":1,\"transaction_no\":\"D1754980939743087000190908\",\"type\":\"DISBURSEMENT\",\"user_name\":\"尹渡\",\"withhold_type\":null},{\"amount\":\"10.45\",\"completed_at\":\"2025-08-08 18:25:12\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"收款\",\"id\":225,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808WINQOFWG\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":2,\"transaction_no\":\"RP1754648705200346435\",\"type\":\"REPAYMENT\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"},{\"amount\":\"35.37\",\"completed_at\":\"2025-08-08 18:25:06\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"收款\",\"id\":224,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808WINQOFWG\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":3,\"transaction_no\":\"RP1754648704706425729\",\"type\":\"REPAYMENT\",\"user_name\":\"尹渡\",\"withhold_type\":\"ASSET\"},{\"amount\":\"15.00\",\"completed_at\":\"2025-08-08 18:22:53\",\"due_date\":\"2025-08-23\",\"fund_type_text\":\"收款\",\"id\":223,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808WINQOFWG\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":1,\"sequence\":4,\"transaction_no\":\"RP1754648567011957854\",\"type\":\"REPAYMENT\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"},{\"amount\":\"50.83\",\"completed_at\":\"2025-08-08 18:22:48\",\"due_date\":\"2025-08-23\",\"fund_type_text\":\"收款\",\"id\":222,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808WINQOFWG\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":1,\"sequence\":5,\"transaction_no\":\"RP1754648566462103503\",\"type\":\"REPAYMENT\",\"user_name\":\"尹渡\",\"withhold_type\":\"ASSET\"},{\"amount\":\"100.00\",\"completed_at\":\"2025-08-08 18:21:25\",\"due_date\":null,\"fund_type_text\":\"收款\",\"id\":221,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808WINQOFWG\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"未知\",\"period_number\":null,\"sequence\":6,\"transaction_no\":\"D1754648481672042200791568\",\"type\":\"DISBURSEMENT\",\"user_name\":\"尹渡\",\"withhold_type\":null},{\"amount\":\"15.00\",\"completed_at\":\"2025-08-08 17:02:22\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"收款\",\"id\":220,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808B1HFABZU\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":7,\"transaction_no\":\"RP1754643736189767889\",\"type\":\"REPAYMENT\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"},{\"amount\":\"15.00\",\"completed_at\":\"2025-08-08 17:01:48\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"退款\",\"id\":219,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808B1HFABZU\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":8,\"transaction_no\":\"RF1754643640606253600330888\",\"type\":\"REFUND\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"},{\"amount\":\"15.00\",\"completed_at\":\"2025-08-08 16:52:50\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"收款\",\"id\":218,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808B1HFABZU\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":9,\"transaction_no\":\"RP1754643164436521169\",\"type\":\"MANUAL_WITHHOLD\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"},{\"amount\":\"15.00\",\"completed_at\":\"2025-08-08 16:51:32\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"退款\",\"id\":217,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808B1HFABZU\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":10,\"transaction_no\":\"RF1754642896095154500290576\",\"type\":\"REFUND\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"}]}}","exdata":"null"}
{"level":"dev.debug","ts":"[2025-08-14 09:43:16.692]","caller":"results/rebjson.go:48","msg":"请求成功: msg: 获取收入明细成功","data":"{\"statistics\":{\"total_income\":1163.21,\"total_refund\":60.14,\"due_income\":0,\"overdue_income\":65.83,\"early_income\":999.06,\"total_customers\":3,\"total_orders\":19},\"list\":{\"total\":111,\"page\":1,\"pageSize\":10,\"totalPages\":12,\"hasNext\":true,\"hasPrev\":false,\"data\":[{\"amount\":\"50.00\",\"completed_at\":\"2025-08-12 14:42:24\",\"due_date\":null,\"fund_type_text\":\"收款\",\"id\":226,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250812OY6SMGWH\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"未知\",\"period_number\":null,\"sequence\":1,\"transaction_no\":\"D1754980939743087000190908\",\"type\":\"DISBURSEMENT\",\"user_name\":\"尹渡\",\"withhold_type\":null},{\"amount\":\"10.45\",\"completed_at\":\"2025-08-08 18:25:12\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"收款\",\"id\":225,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808WINQOFWG\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":2,\"transaction_no\":\"RP1754648705200346435\",\"type\":\"REPAYMENT\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"},{\"amount\":\"35.37\",\"completed_at\":\"2025-08-08 18:25:06\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"收款\",\"id\":224,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808WINQOFWG\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":3,\"transaction_no\":\"RP1754648704706425729\",\"type\":\"REPAYMENT\",\"user_name\":\"尹渡\",\"withhold_type\":\"ASSET\"},{\"amount\":\"15.00\",\"completed_at\":\"2025-08-08 18:22:53\",\"due_date\":\"2025-08-23\",\"fund_type_text\":\"收款\",\"id\":223,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808WINQOFWG\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":1,\"sequence\":4,\"transaction_no\":\"RP1754648567011957854\",\"type\":\"REPAYMENT\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"},{\"amount\":\"50.83\",\"completed_at\":\"2025-08-08 18:22:48\",\"due_date\":\"2025-08-23\",\"fund_type_text\":\"收款\",\"id\":222,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808WINQOFWG\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":1,\"sequence\":5,\"transaction_no\":\"RP1754648566462103503\",\"type\":\"REPAYMENT\",\"user_name\":\"尹渡\",\"withhold_type\":\"ASSET\"},{\"amount\":\"100.00\",\"completed_at\":\"2025-08-08 18:21:25\",\"due_date\":null,\"fund_type_text\":\"收款\",\"id\":221,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808WINQOFWG\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"未知\",\"period_number\":null,\"sequence\":6,\"transaction_no\":\"D1754648481672042200791568\",\"type\":\"DISBURSEMENT\",\"user_name\":\"尹渡\",\"withhold_type\":null},{\"amount\":\"15.00\",\"completed_at\":\"2025-08-08 17:02:22\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"收款\",\"id\":220,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808B1HFABZU\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":7,\"transaction_no\":\"RP1754643736189767889\",\"type\":\"REPAYMENT\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"},{\"amount\":\"15.00\",\"completed_at\":\"2025-08-08 17:01:48\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"退款\",\"id\":219,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808B1HFABZU\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":8,\"transaction_no\":\"RF1754643640606253600330888\",\"type\":\"REFUND\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"},{\"amount\":\"15.00\",\"completed_at\":\"2025-08-08 16:52:50\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"收款\",\"id\":218,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808B1HFABZU\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":9,\"transaction_no\":\"RP1754643164436521169\",\"type\":\"MANUAL_WITHHOLD\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"},{\"amount\":\"15.00\",\"completed_at\":\"2025-08-08 16:51:32\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"退款\",\"id\":217,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808B1HFABZU\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":10,\"transaction_no\":\"RF1754642896095154500290576\",\"type\":\"REFUND\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"}]}}","exdata":"null"}
{"level":"dev.debug","ts":"[2025-08-14 09:43:25.191]","caller":"results/rebjson.go:48","msg":"请求成功: msg: 获取收入明细成功","data":"{\"statistics\":{\"total_income\":1163.21,\"total_refund\":60.14,\"due_income\":0,\"overdue_income\":65.83,\"early_income\":999.06,\"total_customers\":3,\"total_orders\":19},\"list\":{\"total\":111,\"page\":1,\"pageSize\":10,\"totalPages\":12,\"hasNext\":true,\"hasPrev\":false,\"data\":[{\"amount\":\"50.00\",\"completed_at\":\"2025-08-12 14:42:24\",\"due_date\":null,\"fund_type_text\":\"收款\",\"id\":226,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250812OY6SMGWH\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"未知\",\"period_number\":null,\"sequence\":1,\"transaction_no\":\"D1754980939743087000190908\",\"type\":\"DISBURSEMENT\",\"user_name\":\"尹渡\",\"withhold_type\":null},{\"amount\":\"10.45\",\"completed_at\":\"2025-08-08 18:25:12\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"收款\",\"id\":225,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808WINQOFWG\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":2,\"transaction_no\":\"RP1754648705200346435\",\"type\":\"REPAYMENT\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"},{\"amount\":\"35.37\",\"completed_at\":\"2025-08-08 18:25:06\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"收款\",\"id\":224,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808WINQOFWG\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":3,\"transaction_no\":\"RP1754648704706425729\",\"type\":\"REPAYMENT\",\"user_name\":\"尹渡\",\"withhold_type\":\"ASSET\"},{\"amount\":\"15.00\",\"completed_at\":\"2025-08-08 18:22:53\",\"due_date\":\"2025-08-23\",\"fund_type_text\":\"收款\",\"id\":223,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808WINQOFWG\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":1,\"sequence\":4,\"transaction_no\":\"RP1754648567011957854\",\"type\":\"REPAYMENT\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"},{\"amount\":\"50.83\",\"completed_at\":\"2025-08-08 18:22:48\",\"due_date\":\"2025-08-23\",\"fund_type_text\":\"收款\",\"id\":222,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808WINQOFWG\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":1,\"sequence\":5,\"transaction_no\":\"RP1754648566462103503\",\"type\":\"REPAYMENT\",\"user_name\":\"尹渡\",\"withhold_type\":\"ASSET\"},{\"amount\":\"100.00\",\"completed_at\":\"2025-08-08 18:21:25\",\"due_date\":null,\"fund_type_text\":\"收款\",\"id\":221,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808WINQOFWG\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"未知\",\"period_number\":null,\"sequence\":6,\"transaction_no\":\"D1754648481672042200791568\",\"type\":\"DISBURSEMENT\",\"user_name\":\"尹渡\",\"withhold_type\":null},{\"amount\":\"15.00\",\"completed_at\":\"2025-08-08 17:02:22\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"收款\",\"id\":220,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808B1HFABZU\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":7,\"transaction_no\":\"RP1754643736189767889\",\"type\":\"REPAYMENT\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"},{\"amount\":\"15.00\",\"completed_at\":\"2025-08-08 17:01:48\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"退款\",\"id\":219,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808B1HFABZU\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":8,\"transaction_no\":\"RF1754643640606253600330888\",\"type\":\"REFUND\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"},{\"amount\":\"15.00\",\"completed_at\":\"2025-08-08 16:52:50\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"收款\",\"id\":218,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808B1HFABZU\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":9,\"transaction_no\":\"RP1754643164436521169\",\"type\":\"MANUAL_WITHHOLD\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"},{\"amount\":\"15.00\",\"completed_at\":\"2025-08-08 16:51:32\",\"due_date\":\"2025-09-07\",\"fund_type_text\":\"退款\",\"id\":217,\"mobile\":\"19194253882\",\"offline_payment_channel_detail\":null,\"order_no\":\"LO20250808B1HFABZU\",\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"提前收款\",\"period_number\":2,\"sequence\":10,\"transaction_no\":\"RF1754642896095154500290576\",\"type\":\"REFUND\",\"user_name\":\"尹渡\",\"withhold_type\":\"GUARANTEE\"}]}}","exdata":"null"}
{"level":"dev.debug","ts":"[2025-08-14 09:43:36.179]","caller":"results/rebjson.go:48","msg":"请求成功: msg: 获取收入明细成功","data":"{\"statistics\":{\"total_income\":1.67,\"total_refund\":0,\"due_income\":0,\"overdue_income\":0,\"early_income\":0,\"total_customers\":1,\"total_orders\":1},\"list\":{\"total\":1,\"page\":1,\"pageSize\":5,\"totalPages\":1,\"hasNext\":false,\"hasPrev\":false,\"data\":[{\"amount\":\"1.67\",\"completed_at\":\"2025-08-06 19:45:24\",\"due_date\":null,\"fund_type_text\":\"收款\",\"id\":111,\"mobile\":\"18807476306\",\"offline_payment_channel_detail\":null,\"order_no\":null,\"payment_method_text\":\"线下支付\",\"payment_status_text\":\"未知\",\"period_number\":null,\"sequence\":1,\"transaction_no\":\"RP1754480700602157797\",\"type\":\"WITHHOLD\",\"user_name\":\"陈乐\",\"withhold_type\":\"GUARANTEE\"}]}}","exdata":"null"}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"src/main.go:58","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"src/main.go:58","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"src/main.go:58","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-14 09:44:15.165]","caller":"src/main.go:58","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.878]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.881]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.933]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 09:44:25.949]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.374]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.377]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.420]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 09:45:17.424]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.356]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.359]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.383]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 09:46:23.390]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 09:46:39.117]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 开始时间格式错误","data":"null"}
{"level":"dev.info","ts":"[2025-08-14 09:47:08.426]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 开始时间不能大于结束时间","data":"null"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.926]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.929]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.962]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 09:49:45.966]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.266]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.269]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.308]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 09:58:22.313]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.095]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.098]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.130]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 09:59:04.138]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.122]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.126]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.170]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 09:59:55.187]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.590]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.594]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.619]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 10:10:44.625]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.555]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.557]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.607]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 10:56:21.611]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.892]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.894]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.942]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 11:01:40.948]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.177]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.182]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.207]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 11:02:16.212]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.986]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:06:04.990]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 11:06:05.012]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 11:06:05.017]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.044]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.047]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.071]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 11:06:57.075]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.240]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.244]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.317]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 11:08:08.323]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.514]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.518]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.542]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 11:10:07.546]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.546]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.549]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.588]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 11:17:02.593]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 11:19:21.436]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 获取收入明细失败","data":"\"获取收入明细列表失败: 查询收入明细列表失败: 获取总记录数失败: where data format is wrong\""}
{"level":"dev.info","ts":"[2025-08-14 11:19:26.478]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 获取收入明细失败","data":"\"获取收入统计数据失败: 查询收入统计数据失败: where data format is wrong\""}
{"level":"dev.info","ts":"[2025-08-14 11:19:54.735]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 获取收入明细失败","data":"\"获取收入统计数据失败: 查询收入统计数据失败: where data format is wrong\""}
{"level":"dev.info","ts":"[2025-08-14 11:20:05.836]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 获取收入明细失败","data":"\"获取收入明细列表失败: 查询收入明细列表失败: 获取总记录数失败: where data format is wrong\""}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.799]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.802]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.857]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 11:23:25.863]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 11:23:32.690]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 获取收入明细失败","data":"\"获取收入统计数据失败: 查询收入统计数据失败: where data format is wrong\""}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.529]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.531]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.562]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 11:31:13.566]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.027]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.030]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.078]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 11:35:54.083]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.969]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:51:37.972]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 11:51:38.018]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 11:51:38.026]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.566]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.570]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.600]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 11:54:02.606]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.270]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.272]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.305]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 11:59:38.313]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.616]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.622]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.666]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 14:20:36.670]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.296]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.314]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.345]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 16:21:07.353]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.debug","ts":"[2025-08-14 16:21:43.560]","caller":"results/rebjson.go:48","msg":"请求成功: msg: 获取渠道到期统计成功","data":"{\"statistics\":{\"total_due_bills\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"total_due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"total_repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\"},\"list\":[{\"channel_name\":\"qd0806_2\",\"due_bills_count\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"avg_bill_amount\":\"28.40\",\"repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\",\"repayment_users\":1,\"repeat_purchase_users\":1,\"repeat_purchase_rate\":\"100.00\"}],\"pagination\":{\"hasNext\":false,\"hasPrev\":false,\"page\":1,\"pageSize\":5,\"total\":1,\"totalPages\":1}}","exdata":"null"}
{"level":"dev.debug","ts":"[2025-08-14 16:21:55.223]","caller":"results/rebjson.go:48","msg":"请求成功: msg: 获取渠道到期统计成功","data":"{\"statistics\":{\"total_due_bills\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"total_due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"total_repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\"},\"list\":[{\"channel_name\":\"qd0806_2\",\"due_bills_count\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"avg_bill_amount\":\"28.40\",\"repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\",\"repayment_users\":1,\"repeat_purchase_users\":1,\"repeat_purchase_rate\":\"100.00\"}],\"pagination\":{\"hasNext\":false,\"hasPrev\":false,\"page\":1,\"pageSize\":5,\"total\":1,\"totalPages\":1}}","exdata":"null"}
{"level":"dev.debug","ts":"[2025-08-14 16:22:03.683]","caller":"results/rebjson.go:48","msg":"请求成功: msg: 获取渠道到期统计成功","data":"{\"statistics\":{\"total_due_bills\":0,\"new_user_bills\":0,\"old_user_bills\":0,\"total_due_amount\":\"0.00\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"0.00\",\"total_repayment_rate\":\"0.00\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"0.00\"},\"list\":null,\"pagination\":{\"hasNext\":false,\"hasPrev\":false,\"page\":1,\"pageSize\":10,\"total\":0,\"totalPages\":0}}","exdata":"null"}
{"level":"dev.info","ts":"[2025-08-14 16:22:10.430]","caller":"src/main.go:58","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-14 16:22:10.430]","caller":"src/main.go:58","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 16:22:10.430]","caller":"src/main.go:58","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-14 16:22:10.431]","caller":"src/main.go:58","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.842]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.859]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.890]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 16:27:39.895]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.debug","ts":"[2025-08-14 16:28:08.604]","caller":"results/rebjson.go:48","msg":"请求成功: msg: 获取渠道到期统计成功","data":"{\"statistics\":{\"total_due_bills\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"total_due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"total_repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\"},\"list\":[{\"channel_name\":\"qd0806_2\",\"due_bills_count\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"avg_bill_amount\":\"28.40\",\"repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\",\"repayment_users\":1,\"repeat_purchase_users\":1,\"repeat_purchase_rate\":\"100.00\"}],\"pagination\":{\"total\":38,\"page\":1,\"pageSize\":5,\"totalPages\":8,\"hasNext\":true,\"hasPrev\":false,\"data\":[{\"channel_id\":2,\"channel_name\":\"qd0806_2\"}]}}","exdata":"null"}
{"level":"dev.info","ts":"[2025-08-14 16:28:18.511]","caller":"src/main.go:58","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-14 16:28:18.511]","caller":"src/main.go:58","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 16:28:18.511]","caller":"src/main.go:58","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-14 16:28:18.511]","caller":"src/main.go:58","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.087]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.091]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.114]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 16:33:01.120]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.029]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.054]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.116]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 16:44:19.121]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.debug","ts":"[2025-08-14 16:44:52.103]","caller":"results/rebjson.go:48","msg":"请求成功: msg: 获取渠道到期统计成功","data":"{\"statistics\":{\"total_due_bills\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"total_due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"total_repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\"},\"pagination\":{\"total\":38,\"page\":1,\"pageSize\":5,\"totalPages\":8,\"hasNext\":true,\"hasPrev\":false,\"data\":[{\"channel_name\":\"qd0806_2\",\"due_bills_count\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"avg_bill_amount\":\"28.40\",\"repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\",\"repayment_users\":1,\"repeat_purchase_users\":1,\"repeat_purchase_rate\":\"100.00\"}]}}","exdata":"null"}
{"level":"dev.info","ts":"[2025-08-14 16:46:44.591]","caller":"src/main.go:58","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-14 16:46:44.593]","caller":"src/main.go:58","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 16:46:44.593]","caller":"src/main.go:58","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-14 16:46:44.593]","caller":"src/main.go:58","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.096]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.113]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.142]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 16:51:01.147]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.debug","ts":"[2025-08-14 16:51:35.928]","caller":"results/rebjson.go:48","msg":"请求成功: msg: 获取渠道到期统计成功","data":"{\"statistics\":{\"total_due_bills\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"total_due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"total_repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\"},\"pagination\":{\"total\":38,\"page\":1,\"pageSize\":5,\"totalPages\":8,\"hasNext\":true,\"hasPrev\":false,\"data\":[{\"channel_name\":\"qd0806_2\",\"due_bills_count\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"avg_bill_amount\":\"28.40\",\"repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\",\"repayment_users\":1,\"repeat_purchase_users\":1,\"repeat_purchase_rate\":\"100.00\"}]}}","exdata":"null"}
{"level":"dev.info","ts":"[2025-08-14 16:51:43.412]","caller":"src/main.go:58","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-14 16:51:43.413]","caller":"src/main.go:58","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 16:51:43.413]","caller":"src/main.go:58","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-14 16:51:43.413]","caller":"src/main.go:58","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.535]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.556]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.586]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 17:09:38.591]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.debug","ts":"[2025-08-14 17:10:06.623]","caller":"results/rebjson.go:48","msg":"请求成功: msg: 获取渠道到期统计成功","data":"{\"statistics\":{\"total_due_bills\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"total_due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"total_repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\"},\"pagination\":{\"total\":38,\"page\":1,\"pageSize\":5,\"totalPages\":8,\"hasNext\":true,\"hasPrev\":false,\"data\":[{\"channel_name\":\"qd0806_2\",\"due_bills_count\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"avg_bill_amount\":\"28.40\",\"repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\",\"repayment_users\":1,\"repeat_purchase_users\":1,\"repeat_purchase_rate\":\"100.00\"}]}}","exdata":"null"}
{"level":"dev.info","ts":"[2025-08-14 17:10:14.046]","caller":"src/main.go:58","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-14 17:10:14.047]","caller":"src/main.go:58","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 17:10:14.047]","caller":"src/main.go:58","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-14 17:10:14.048]","caller":"src/main.go:58","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.455]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.474]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.508]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 17:28:34.514]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.debug","ts":"[2025-08-14 17:29:04.872]","caller":"results/rebjson.go:48","msg":"请求成功: msg: 获取渠道到期统计成功","data":"{\"statistics\":{\"total_due_bills\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"total_due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"total_repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\"},\"pagination\":{\"total\":38,\"page\":1,\"pageSize\":5,\"totalPages\":8,\"hasNext\":true,\"hasPrev\":false,\"data\":[{\"channel_name\":\"qd0806_2\",\"due_bills_count\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"avg_bill_amount\":\"28.40\",\"repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\",\"repayment_users\":1,\"repeat_purchase_users\":1,\"repeat_purchase_rate\":\"100.00\"}]}}","exdata":"null"}
{"level":"dev.info","ts":"[2025-08-14 17:29:13.039]","caller":"src/main.go:58","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-14 17:29:13.041]","caller":"src/main.go:58","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 17:29:13.041]","caller":"src/main.go:58","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-14 17:29:13.042]","caller":"src/main.go:58","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.803]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.811]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.834]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 17:34:11.838]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.511]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.532]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.561]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 17:39:36.567]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.debug","ts":"[2025-08-14 17:40:05.764]","caller":"results/rebjson.go:48","msg":"请求成功: msg: 获取渠道到期统计成功","data":"{\"statistics\":{\"total_due_bills\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"total_due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"total_repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\"},\"pagination\":{\"total\":1,\"page\":1,\"pageSize\":10,\"totalPages\":1,\"hasNext\":false,\"hasPrev\":false,\"data\":[{\"channel_name\":\"qd0806_2\",\"due_bills_count\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"avg_bill_amount\":\"28.40\",\"repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\",\"repayment_users\":1,\"repeat_purchase_users\":1,\"repeat_purchase_rate\":\"100.00\"}]}}","exdata":"null"}
{"level":"dev.info","ts":"[2025-08-14 17:40:14.657]","caller":"src/main.go:58","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-14 17:40:14.658]","caller":"src/main.go:58","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 17:40:14.659]","caller":"src/main.go:58","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-14 17:40:14.659]","caller":"src/main.go:58","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.429]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.434]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.462]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 17:40:32.477]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.243]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.263]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.298]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 17:45:22.304]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.debug","ts":"[2025-08-14 17:45:52.940]","caller":"results/rebjson.go:48","msg":"请求成功: msg: 获取渠道到期统计成功","data":"{\"statistics\":{\"total_due_bills\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"total_due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"total_repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\"},\"pagination\":{\"total\":3,\"page\":1,\"pageSize\":10,\"totalPages\":1,\"hasNext\":false,\"hasPrev\":false,\"data\":[{\"channel_name\":\"qd0805\",\"due_bills_count\":0,\"new_user_bills\":0,\"old_user_bills\":0,\"due_amount\":\"0.00\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"0.00\",\"avg_bill_amount\":\"0.00\",\"repayment_rate\":\"0.00\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"0.00\",\"repayment_users\":0,\"repeat_purchase_users\":0,\"repeat_purchase_rate\":\"\"},{\"channel_name\":\"qd0806_2\",\"due_bills_count\":38,\"new_user_bills\":0,\"old_user_bills\":38,\"due_amount\":\"1079.10\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"1079.10\",\"avg_bill_amount\":\"28.40\",\"repayment_rate\":\"82.44\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"82.44\",\"repayment_users\":1,\"repeat_purchase_users\":1,\"repeat_purchase_rate\":\"100.00\"},{\"channel_name\":\"qd0805\",\"due_bills_count\":0,\"new_user_bills\":0,\"old_user_bills\":0,\"due_amount\":\"0.00\",\"new_user_due_amount\":\"0.00\",\"old_user_due_amount\":\"0.00\",\"avg_bill_amount\":\"0.00\",\"repayment_rate\":\"0.00\",\"new_user_repayment_rate\":\"0.00\",\"old_user_repayment_rate\":\"0.00\",\"repayment_users\":0,\"repeat_purchase_users\":0,\"repeat_purchase_rate\":\"\"}]}}","exdata":"null"}
{"level":"dev.info","ts":"[2025-08-14 17:46:02.715]","caller":"src/main.go:58","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-14 17:46:02.718]","caller":"src/main.go:58","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-14 17:46:02.718]","caller":"src/main.go:58","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-14 17:46:02.718]","caller":"src/main.go:58","msg":"HTTP服务器已关闭"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.111]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.114]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.162]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 17:47:04.177]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.870]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.882]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.939]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 17:54:27.943]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.227]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.232]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.258]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-14 18:00:32.264]","caller":"src/main.go:58","msg":"启动端口：8108"}
