-- 为渠道表添加自动放款配置字段
-- 执行时间：2024-12-30

-- 添加自动放款配置字段
ALTER TABLE `channel` ADD COLUMN `auto_disbursement` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否自动放款: 0-否(人工审核), 1-是(自动放款)' AFTER `loan_limits`;

-- 更新表注释
ALTER TABLE `channel` COMMENT = '渠道管理表 - 包含自动放款配置';

-- 为新字段添加索引（可选，用于查询优化）
ALTER TABLE `channel` ADD INDEX `idx_auto_disbursement` (`auto_disbursement`);

-- 示例数据更新（可选）
-- UPDATE `channel` SET `auto_disbursement` = 1 WHERE `channel_code` IN ('AUTO001', 'AUTO002');
-- UPDATE `channel` SET `auto_disbursement` = 0 WHERE `channel_code` IN ('MANUAL001', 'MANUAL002');
