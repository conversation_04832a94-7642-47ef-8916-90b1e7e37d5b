package goredis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
)

/**********2.常用操作工具类**********/
var ctx = context.Background()

/*------------------------------------ 字符 操作 ------------------------------------*/

// Set 设置 key的值
func Set(key, value string, ex time.Duration) (bool, error) {

	var client = GetRedisClient() //包级变量初始化早于init
	result, err := client.Set(ctx, key, value, ex).Result()
	if err != nil {
		return false, err
	}
	return result == "OK", nil
}

// SetEX 设置 key的值并指定过期时间
func SetEX(key, value string, ex time.Duration) (bool, error) {

	var client = GetRedisClient() //包级变量初始化早于init
	result, err := client.Set(ctx, key, value, ex).Result()
	if err != nil {
		fmt.Println(err)
		return false, err
	}
	return result == "OK", nil
}

// SetNX 设置 key的值并指定过期时间
func SetNX(key, value string, ex time.Duration) (bool, error) {

	//调用redis的时候无法直接获取实例，因为redis实例是全局变量，所以需要重新获取实例
	// //TODO：优化redis初始化为项目启动时自动初始化，不需要每次调用redis时都重新获取实例
	client := GetRedisClient()
	result, err := client.SetNX(ctx, key, value, ex).Result()
	if err != nil {
		return false, err
	}
	return result, nil
}

// Get 获取 key的值
func Get(key string) (bool, string) {
	client := GetRedisClient()
	result, err := client.Get(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
		return false, ""
	}
	return true, result
}

// GetSet 设置新值获取旧值
func GetSet(key, value string) (bool, string) {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	oldValue, err := client.GetSet(ctx, key, value).Result()
	if err != nil {
		fmt.Println(err)
		return false, ""
	}
	return true, oldValue
}

// Incr key值每次加一 并返回新值
func Incr(key string) int64 {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	val, err := client.Incr(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
	}
	return val
}

// IncrBy key值每次加指定数值 并返回新值
func IncrBy(key string, incr int64) int64 {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	val, err := client.IncrBy(ctx, key, incr).Result()
	if err != nil {
		fmt.Println(err)
	}
	return val
}

// IncrByFloat key值每次加指定浮点型数值 并返回新值
func IncrByFloat(key string, incrFloat float64) float64 {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	val, err := client.IncrByFloat(ctx, key, incrFloat).Result()
	if err != nil {
		fmt.Println(err)
	}
	return val
}

// Decr key值每次递减 1 并返回新值
func Decr(key string) int64 {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	val, err := client.Decr(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
	}
	return val
}

// DecrBy key值每次递减指定数值 并返回新值
func DecrBy(key string, incr int64) int64 {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	val, err := client.DecrBy(ctx, key, incr).Result()
	if err != nil {
		fmt.Println(err)
	}
	return val
}

// Del 删除 key
func Del(key string) (bool, error) {
	client := GetRedisClient() // 新增：初始化 Redis 客户端

	_, err := client.Del(ctx, key).Result()
	if err != nil {
		return false, err
	}
	return true, nil
}

// Expire 设置 key的过期时间
func Expire(key string, ex time.Duration) bool {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	result, err := client.Expire(ctx, key, ex).Result()
	if err != nil {
		return false
	}
	return result
}

// LPush 从列表左边插入数据，并返回列表长度
func LPush(key string, date ...interface{}) int64 {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	result, err := client.LPush(ctx, key, date).Result()
	if err != nil {
		fmt.Println(err)
	}
	return result
}

// RPush 从列表右边插入数据，并返回列表长度
func RPush(key string, date ...interface{}) int64 {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	result, err := client.RPush(ctx, key, date).Result()
	if err != nil {
		fmt.Println(err)
	}
	return result
}

// LPop 从列表左边删除第一个数据，并返回删除的数据
func LPop(key string) (bool, string) {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	val, err := client.LPop(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
		return false, ""
	}
	return true, val
}

// RPop 从列表右边删除第一个数据，并返回删除的数据
func RPop(key string) (bool, string) {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	val, err := client.RPop(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
		return false, ""
	}
	return true, val
}

// LIndex 根据索引坐标，查询列表中的数据
func LIndex(key string, index int64) (bool, string) {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	val, err := client.LIndex(ctx, key, index).Result()
	if err != nil {
		fmt.Println(err)
		return false, ""
	}
	return true, val
}

// LLen 返回列表长度
func LLen(key string) int64 {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	val, err := client.LLen(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
	}
	return val
}

/*------------------------------------ 风控缓存操作 ------------------------------------*/

// SetRiskScore 设置用户风控评分缓存
func SetRiskScore(customerID uint64, riskData map[string]interface{}, expiration time.Duration) error {
	client := GetRedisClient()
	key := fmt.Sprintf("risk:score:%d", customerID)

	// 序列化风控数据
	jsonData, err := json.Marshal(riskData)
	if err != nil {
		return fmt.Errorf("序列化风控数据失败: %v", err)
	}

	_, err = client.Set(ctx, key, string(jsonData), expiration).Result()
	if err != nil {
		return fmt.Errorf("设置风控评分缓存失败: %v", err)
	}

	return nil
}

// GetRiskScore 获取用户风控评分缓存
func GetRiskScore(customerID uint64) (map[string]interface{}, error) {
	client := GetRedisClient()
	key := fmt.Sprintf("risk:score:%d", customerID)

	result, err := client.Get(ctx, key).Result()
	if err != nil {
		if err.Error() == "redis: nil" {
			return nil, nil // 缓存未命中
		}
		return nil, fmt.Errorf("获取风控评分缓存失败: %v", err)
	}

	// 反序列化数据
	var riskData map[string]interface{}
	err = json.Unmarshal([]byte(result), &riskData)
	if err != nil {
		return nil, fmt.Errorf("反序列化风控数据失败: %v", err)
	}

	return riskData, nil
}

// DelRiskScore 删除用户风控评分缓存
func DelRiskScore(customerID string) error {
	client := GetRedisClient()
	key := fmt.Sprintf("risk:score:%s", customerID)

	_, err := client.Del(ctx, key).Result()
	if err != nil {
		return fmt.Errorf("删除风控评分缓存失败: %v", err)
	}

	return nil
}

// SetLoanProducts 设置贷款产品缓存
func SetLoanProducts(products []map[string]interface{}, expiration time.Duration) error {
	client := GetRedisClient()
	key := "loan:products:all"

	// 序列化产品数据
	jsonData, err := json.Marshal(products)
	if err != nil {
		return fmt.Errorf("序列化产品数据失败: %v", err)
	}

	_, err = client.Set(ctx, key, string(jsonData), expiration).Result()
	if err != nil {
		return fmt.Errorf("设置产品缓存失败: %v", err)
	}

	return nil
}

// GetLoanProducts 获取贷款产品缓存
func GetLoanProducts() ([]map[string]interface{}, error) {
	client := GetRedisClient()
	key := "loan:products:all"

	result, err := client.Get(ctx, key).Result()
	if err != nil {
		if err.Error() == "redis: nil" {
			return nil, nil // 缓存未命中
		}
		return nil, fmt.Errorf("获取产品缓存失败: %v", err)
	}

	// 反序列化数据
	var products []map[string]interface{}
	err = json.Unmarshal([]byte(result), &products)
	if err != nil {
		return nil, fmt.Errorf("反序列化产品数据失败: %v", err)
	}

	return products, nil
}

// DelLoanProducts 删除贷款产品缓存
func DelLoanProducts() error {
	client := GetRedisClient()
	key := "loan:products:all"

	_, err := client.Del(ctx, key).Result()
	if err != nil {
		return fmt.Errorf("删除产品缓存失败: %v", err)
	}

	return nil
}

// LRange 返回列表的一个范围内的数据，也可以返回全部数据
func LRange(key string, start, stop int64) []string {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	vales, err := client.LRange(ctx, key, start, stop).Result()
	if err != nil {
		fmt.Println(err)
	}
	return vales
}

// LRem 从列表左边开始，删除元素data， 如果出现重复元素，仅删除 count次
func LRem(key string, count int64, data interface{}) bool {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	_, err := client.LRem(ctx, key, count, data).Result()
	if err != nil {
		fmt.Println(err)
	}
	return true
}

// LInsert 在列表中 pivot 元素的后面插入 data
func LInsert(key string, pivot int64, data interface{}) bool {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	err := client.LInsert(ctx, key, "after", pivot, data).Err()
	if err != nil {
		fmt.Println(err)
		return false
	}
	return true
}

// SAdd 添加元素到集合中
func SAdd(key string, data ...interface{}) bool {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	err := client.SAdd(ctx, key, data).Err()
	if err != nil {
		fmt.Println(err)
		return false
	}
	return true
}

// SCard 获取集合元素个数（注意原代码中 "key" 应为变量 key）
func SCard(key string) int64 {
	client := GetRedisClient()                   // 新增：初始化 Redis 客户端
	size, err := client.SCard(ctx, key).Result() // 修正：原代码中 "key" 应为变量 key
	if err != nil {
		fmt.Println(err)
	}
	return size
}

// SIsMember 判断元素是否在集合中
func SIsMember(key string, data interface{}) bool {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	ok, err := client.SIsMember(ctx, key, data).Result()
	if err != nil {
		fmt.Println(err)
	}
	return ok
}

// SMembers 获取集合所有元素
func SMembers(key string) []string {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	es, err := client.SMembers(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
	}
	return es
}

// SRem 删除 key集合中的 data元素
func SRem(key string, data ...interface{}) bool {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	_, err := client.SRem(ctx, key, data).Result()
	if err != nil {
		fmt.Println(err)
		return false
	}
	return true
}

// SPopN 随机返回集合中的 count个元素，并且删除这些元素
func SPopN(key string, count int64) []string {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	vales, err := client.SPopN(ctx, key, count).Result()
	if err != nil {
		fmt.Println(err)
	}
	return vales
}

// HSet 根据 key和 field字段设置，field字段的值
func HSet(key, field, value string) bool {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	err := client.HSet(ctx, key, field, value).Err()
	return err == nil
}

// HGet 根据 key和 field字段，查询field字段的值
func HGet(key, field string) string {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	val, err := client.HGet(ctx, key, field).Result()
	if err != nil {
		fmt.Println(err)
	}
	return val
}

// HMGet 根据key和多个字段名，批量查询多个 hash字段值
func HMGet(key string, fields ...string) []interface{} {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	vales, err := client.HMGet(ctx, key, fields...).Result()
	if err != nil {
		panic(err)
	}
	return vales
}

// HGetAll 根据 key查询所有字段和值
func HGetAll(key string) map[string]string {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	data, err := client.HGetAll(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
	}
	return data
}

// HKeys 根据 key返回所有字段名
func HKeys(key string) []string {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	fields, err := client.HKeys(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
	}
	return fields
}

// HLen 根据 key，查询hash的字段数量
func HLen(key string) int64 {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	size, err := client.HLen(ctx, key).Result()
	if err != nil {
		fmt.Println(err)
	}
	return size
}

// HMSet 根据 key和多个字段名和字段值，批量设置 hash字段值
func HMSet(key string, data map[string]interface{}) bool {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	result, err := client.HMSet(ctx, key, data).Result()
	if err != nil {
		fmt.Println(err)
		return false
	}
	return result
}

// HSetNX 如果 field字段不存在，则设置 hash字段值
func HSetNX(key, field string, value interface{}) bool {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	result, err := client.HSetNX(ctx, key, field, value).Result()
	if err != nil {
		fmt.Println(err)
		return false
	}
	return result
}

// HDel 根据 key和字段名，删除 hash字段，支持批量删除
func HDel(key string, fields ...string) bool {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	_, err := client.HDel(ctx, key, fields...).Result()
	if err != nil {
		fmt.Println(err)
		return false
	}
	return true
}

// HExists 检测 hash字段名是否存在
func HExists(key, field string) bool {
	client := GetRedisClient() // 新增：初始化 Redis 客户端
	result, err := client.HExists(ctx, key, field).Result()
	if err != nil {
		fmt.Println(err)
		return false
	}
	return result
}
