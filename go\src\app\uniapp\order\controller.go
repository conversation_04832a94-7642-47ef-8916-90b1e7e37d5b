package order

import (
	"reflect"
	"strconv"

	businessservice "fincore/app/business/order"
	"fincore/route/middleware"
	"fincore/utils/convert"
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/results"

	"github.com/gin-gonic/gin"
)

// Index 订单控制器
type Index struct{}

func init() {
	// 注册路由到自动路由系统
	gf.Register(&Index{}, reflect.TypeOf(Index{}).PkgPath())
}

// Get_repayment_preview 获取还款计划预览接口
// 路由: GET /uniapp/order/get_repayment_preview
func (oc *Index) Get_repayment_preview(c *gin.Context) {
	// 1. 参数校验
	productRuleID := c.Query("product_rule_id")
	if productRuleID == "" {
		results.Failed(c, "参数验证失败", "产品规则ID不能为空")
		return
	}

	queryData := map[string]interface{}{
		"product_rule_id": productRuleID,
	}

	schema := GetRepaymentPreviewSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(queryData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 转换参数类型
	productRuleIDInt, err := strconv.Atoi(productRuleID)
	if err != nil {
		results.Failed(c, "参数验证失败", "产品规则ID格式错误")
		return
	}

	// 3. 业务逻辑处理
	service := NewOrderService(c, WithProductRuleService())
	result, err := service.GetRepaymentPreview(productRuleIDInt)
	if err != nil {
		results.Failed(c, "获取还款计划预览失败", err.Error())
		return
	}

	// 4. 返回结果
	results.Success(c, "获取还款计划预览成功", result, nil)
}

// Get_order_bills 获取用户所有在途订单账单接口(用户还款页)
// 路由: GET /uniapp/order/get_order_bills
func (oc *Index) Get_order_bills(c *gin.Context) {
	// 1. 获取当前用户ID
	claim := middleware.ParseToken(c.GetHeader("Authorization"))
	if claim == nil {
		results.Failed(c, "用户身份验证失败", "请重新登录")
		return
	}
	userID := int(claim.ID)

	// 2. 业务逻辑处理
	service := NewOrderService(c)
	result, err := service.GetAllInProgressOrderBills(userID)
	if err != nil {
		results.Failed(c, "获取订单账单失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(c, "获取订单账单成功", result, nil)
}

// CreateOrder 创建订单
func (c *Index) CreateOrder(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetOrderCreateSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 从登录态获取用户ID
	claim := middleware.ParseToken(ctx.GetHeader("Authorization"))
	userID := int(claim.ID)

	// 2. 业务逻辑处理
	// todo 原始创建订单逻辑不通过接口，暂放在 business/order 中
	service := businessservice.NewOrderService(ctx)
	createOrderParams := businessservice.CreateOrderParams{
		UserID:           userID,
		LoanAmount:       convert.MustConvertToFloat(validationResult.Data["loan_amount"], 0.0),
		ProductRuleID:    convert.MustConvertToInt(validationResult.Data["product_rule_id"], 0),
		ChannelCode:      validationResult.Data["channel_code"].(string),
		CustomerOrigin:   validationResult.Data["customer_origin"].(string),
		PaymentChannelID: convert.MustConvertToInt(validationResult.Data["payment_channel_id"], 0),
	}

	result, err := service.CreateOrder(ctx, createOrderParams)
	if err != nil {
		if result == nil {
			results.Failed(ctx, "创建订单失败", err.Error())
			return
		} else {
			results.Success(ctx, "创建订单成功,后续流程执行失败 - "+err.Error(), result, nil)
			return
		}
	}

	// 3. 返回结果
	results.Success(ctx, "创建订单成功", result, nil)
}

// CheckCanCreate 检查用户是否可创建订单
// 路由: POST /uniapp/order/checkCanCreate
func (c *Index) CheckCanCreate(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetCheckCanCreateSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 获取当前用户ID
	claim := middleware.ParseToken(ctx.GetHeader("Authorization"))
	userID := int(claim.ID)

	// 3. 业务逻辑处理
	service := NewOrderService(ctx, WithLoanOrderService(), WithAccountService())
	loanAmount := validationResult.Data["loan_amount"].(float64)

	result, err := service.CheckCanCreate(userID, loanAmount)
	if err != nil {
		results.Failed(ctx, "检查是否可创建订单失败", err.Error())
		return
	}

	// 4. 返回结果
	results.Success(ctx, "检查完成", result, nil)
}

// Get_user_order_history 获取用户待放款和放款中的订单列表接口(用户借还记录)
// 路由: GET /uniapp/order/get_user_order_history
func (oc *Index) Get_user_order_history(c *gin.Context) {
	// 1. 从登录态获取用户ID
	claim := middleware.ParseToken(c.GetHeader("Authorization"))
	userID := int(claim.ID)

	// 2. 业务逻辑处理
	service := NewOrderService(c)
	result, err := service.GetUserOrderHistory(userID)
	if err != nil {
		results.Failed(c, "获取用户待放款和放款中订单失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(c, "获取用户待放款和放款中订单成功", result, nil)
}
