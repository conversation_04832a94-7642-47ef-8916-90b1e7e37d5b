---
description: go/*
alwaysApply: false
---
# 开发指南

## 代码规范

1. **命名规范**
   - 包名：全小写，简短有意义，例如 `model`、`controller`
   - 文件名：小写下划线分隔，例如 `user_service.go`
   - 结构体名：大驼峰，例如 `UserService`
   - 方法名：大驼峰，例如 `GetUserInfo`
   - 变量名：小驼峰，例如 `userName`

2. **注释规范**
   - 包注释：每个包都应该有注释说明其功能
   - 函数注释：公开函数必须有注释，说明功能、参数和返回值
   - 结构体注释：公开结构体必须有注释，说明其用途

3. **错误处理**
   - 使用统一的错误处理方式
   - 错误信息应该明确表明错误发生的位置和原因
   - 使用 `errors.Wrap` 包装错误传递上下文信息

## 开发流程

1. **新增功能**
   - 在相应模块下创建必要的控制器、服务和模型
   - 完善路由配置
   - 编写单元测试

2. **修改功能**
   - 确认修改影响范围
   - 修改相应的代码
   - 更新测试用例

3. **提交代码**
   - 代码应该通过所有测试
   - 提交前进行代码自查
   - 提交信息应该清晰说明修改内容

## 调试方法

1. **日志调试**
   - 使用 `global.App.Log.Info/Error/Debug` 输出日志
   - 日志按级别记录到相应文件

2. **本地调试**
   - 运行 `go run main.go` 启动服务
   - 使用 Postman 或其他工具测试 API

3. **错误排查**
   - 查看日志文件
   - 使用 `go test` 运行单元测试
   - 检查数据一致性

遵循以上规范和流程，保证代码质量和项目可维护性。

## 目录操作规范

- 开发时主要在 app 目录下添加新的需求
- app 外部文件建议不要修改（除了 config 配置）
- 框架已经封装好路由、访问权限、跨域、限流、Token验证、ORM 等功能

## 数据库操作

框架集成了简单易用的 ORM，操作数据库非常简单，例如：

```go
// 查找一条数据
db.Table("users").Fields("uid,name,age").First()
```

## 打包部署

1. 初始化依赖
```
go mod tidy
```

2. 打包项目
```
bash build.sh
```

3. 启动服务
```
cd bin
./fincore
```

## 代码检查清单

参考 [code_checklist.md](mdc:src/code_checklist.md) 进行代码审查和质量控制。
