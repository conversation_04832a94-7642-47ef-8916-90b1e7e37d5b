---
description: go/*
alwaysApply: false
---
# FinCore 项目结构

FinCore 是一个基于 Go 的金融核心系统，采用前后端分离架构。以 Go 作为后端开发语言，使用 Gin 框架处理 HTTP 请求。

## 主要目录结构

- [src/](mdc:src) - 源代码目录
  - [main.go](mdc:src/main.go) - 项目入口文件
  - [app/](mdc:src/app) - 应用目录，包含各个模块的业务逻辑
    - [admin/](mdc:src/app/admin) - 后台管理应用模块
    - [business/](mdc:src/app/business) - 业务端应用模块
    - [dianziqian/](mdc:src/app/dianziqian) - 电子签应用模块
    - [fengkong/](mdc:src/app/fengkong) - 风控应用模块
    - [uniapp/](mdc:src/app/uniapp) - UniApp 应用模块
    - [common/](mdc:src/app/common) - 公共应用模块
    - [controller.go](mdc:src/app/controller.go) - 应用控制器
  - [bootstrap/](mdc:src/bootstrap) - 启动相关代码和工具方法
    - [router.go](mdc:src/bootstrap/router.go) - 路由初始化
    - [log.go](mdc:src/bootstrap/log.go) - 日志初始化
  - [global/](mdc:src/global) - 全局变量和配置
  - [model/](mdc:src/model) - 数据模型定义
  - [resource/](mdc:src/resource) - 静态资源和配置文件
  - [route/](mdc:src/route) - 路由定义
  - [runtime/](mdc:src/runtime) - 运行时生成的文件
  - [utils/](mdc:src/utils) - 工具包与辅助函数
  - [thirdparty/](mdc:src/thirdparty) - 第三方集成
  - [log/](mdc:src/log) - 日志文件
- [bin/](mdc:bin) - 编译产出目录
- [utils/](mdc:utils) - 辅助工具

## 文件说明
- [go.mod](mdc:src/go.mod) - Go 模块依赖管理
- [go.sum](mdc:src/go.sum) - Go 模块校验和
- [Makefile](mdc:src/Makefile) - 构建脚本
- [build.sh](mdc:src/build.sh) - 构建脚本
- [README.md](mdc:src/README.md) - 项目说明文档

## 开发指南

开发时主要在 app 目录下添加新的业务逻辑。系统使用 Gin 框架处理 HTTP 请求，已封装了路由、权限、跨域、限流、Token验证等功能，开发者只需专注于业务逻辑开发。


