package lock

import (
	"context"
	"fmt"
	"sync"
	"time"

	"fincore/utils/log"
)

// Lock 锁接口，支持链式操作
type Lock interface {
	Lock() Lock                             // 加锁，返回自身支持链式调用
	Unlock() Lock                           // 解锁，返回自身支持链式调用
	TryLock() (bool, Lock)                  // 尝试加锁，返回是否成功和自身
	WithTimeout(timeout time.Duration) Lock // 设置超时时间，支持链式调用
	WithContext(ctx context.Context) Lock   // 设置上下文，支持链式调用
	WithLogger(logger *log.Logger) Lock     // 设置日志对象，支持链式调用
	GetKey() string                         // 获取锁的key
}

// LockManager 锁管理器接口
type LockManager interface {
	GetLock(key string, expiration ...time.Duration) Lock                               // 获取指定key的锁，可选过期时间
	GetLockWithLogger(key string, logger *log.Logger, expiration ...time.Duration) Lock // 获取指定key的锁，使用指定日志对象
	UnlockByKey(key string) error                                                       // 通过key解锁
	CleanExpiredLocks() int                                                             // 清理过期锁
	GetStats() map[string]LockStats                                                     // 获取锁统计信息
}

// LockStats 锁统计信息
type LockStats struct {
	Key       string    // 锁的键
	CreatedAt time.Time // 创建时间
	ExpiresAt time.Time // 过期时间
	IsLocked  bool      // 是否被锁定
	UseCount  int64     // 使用次数
}

// lockEntry 内部锁条目
type lockEntry struct {
	mutex     *sync.Mutex
	key       string
	createdAt time.Time
	expiresAt time.Time
	isLocked  bool
	useCount  int64
	timeout   time.Duration
}

// simpleLock 简单锁实现
type simpleLock struct {
	entry   *lockEntry
	manager *SimpleLockManager
	ctx     context.Context // 上下文
	logger  *log.Logger     // 日志记录器
}

// SimpleLockManager 简单锁管理器实现
type SimpleLockManager struct {
	locks sync.Map // key: string, value: *lockEntry
}

// NewSimpleLockManager 创建新的简单锁管理器
func NewSimpleLockManager() LockManager {
	return &SimpleLockManager{}
}

// GetLock 获取指定key的锁
func (slm *SimpleLockManager) GetLock(key string, expiration ...time.Duration) Lock {
	var exp time.Duration
	if len(expiration) > 0 {
		exp = expiration[0]
	} else {
		exp = 5 * time.Minute // 默认5分钟过期
	}

	// 尝试从缓存中获取锁
	if value, exists := slm.locks.Load(key); exists {
		entry := value.(*lockEntry)
		// 检查是否过期
		if time.Now().Before(entry.expiresAt) {
			entry.useCount++
			return slm.createSimpleLock(entry)
		}
		// 过期了，删除旧的
		slm.locks.Delete(key)
	}

	// 创建新锁
	entry := &lockEntry{
		mutex:     &sync.Mutex{},
		key:       key,
		createdAt: time.Now(),
		expiresAt: time.Now().Add(exp),
		isLocked:  false,
		useCount:  1,
		timeout:   30 * time.Second, // 默认30秒超时
	}

	// 存储锁
	actual, loaded := slm.locks.LoadOrStore(key, entry)
	if loaded {
		// 如果已存在，使用已存在的
		entry = actual.(*lockEntry)
		entry.useCount++
	}

	return slm.createSimpleLock(entry)
}

// GetLockWithLogger 获取指定key的锁，使用指定日志对象
func (slm *SimpleLockManager) GetLockWithLogger(key string, logger *log.Logger, expiration ...time.Duration) Lock {
	var exp time.Duration
	if len(expiration) > 0 {
		exp = expiration[0]
	} else {
		exp = 5 * time.Minute // 默认5分钟过期
	}

	// 尝试从缓存中获取锁
	if value, exists := slm.locks.Load(key); exists {
		entry := value.(*lockEntry)
		// 检查是否过期
		if time.Now().Before(entry.expiresAt) {
			entry.useCount++
			return slm.createSimpleLockWithLogger(entry, logger)
		}
		// 过期了，删除旧的
		slm.locks.Delete(key)
	}

	// 创建新锁
	entry := &lockEntry{
		mutex:     &sync.Mutex{},
		key:       key,
		createdAt: time.Now(),
		expiresAt: time.Now().Add(exp),
		isLocked:  false,
		useCount:  1,
		timeout:   30 * time.Second, // 默认30秒超时
	}

	// 存储锁
	actual, loaded := slm.locks.LoadOrStore(key, entry)
	if loaded {
		// 如果已存在，使用已存在的
		entry = actual.(*lockEntry)
		entry.useCount++
	}

	return slm.createSimpleLockWithLogger(entry, logger)
}

// createSimpleLock 创建simpleLock实例
func (slm *SimpleLockManager) createSimpleLock(entry *lockEntry) Lock {
	return slm.createSimpleLockWithLogger(entry, nil)
}

// createSimpleLockWithLogger 创建带指定日志对象的simpleLock实例
func (slm *SimpleLockManager) createSimpleLockWithLogger(entry *lockEntry, logger *log.Logger) Lock {
	return &simpleLock{
		entry:   entry,
		manager: slm,
		ctx:     context.Background(),
		logger:  logger, // 直接使用传入的日志对象，可能为nil
	}
}

// UnlockByKey 通过key解锁
func (slm *SimpleLockManager) UnlockByKey(key string) error {
	if value, exists := slm.locks.Load(key); exists {
		entry := value.(*lockEntry)
		if entry.isLocked {
			entry.mutex.Unlock()
			entry.isLocked = false
			return nil
		}
		return fmt.Errorf("锁 %s 未被锁定", key)
	}
	return fmt.Errorf("锁 %s 不存在", key)
}

// CleanExpiredLocks 清理过期锁
func (slm *SimpleLockManager) CleanExpiredLocks() int {
	count := 0
	now := time.Now()

	slm.locks.Range(func(key, value interface{}) bool {
		entry := value.(*lockEntry)
		if now.After(entry.expiresAt) {
			slm.locks.Delete(key)
			count++
		}
		return true
	})

	return count
}

// GetStats 获取锁统计信息
func (slm *SimpleLockManager) GetStats() map[string]LockStats {
	stats := make(map[string]LockStats)

	slm.locks.Range(func(key, value interface{}) bool {
		entry := value.(*lockEntry)
		stats[entry.key] = LockStats{
			Key:       entry.key,
			CreatedAt: entry.createdAt,
			ExpiresAt: entry.expiresAt,
			IsLocked:  entry.isLocked,
			UseCount:  entry.useCount,
		}
		return true
	})

	return stats
}

// Lock 加锁
func (sl *simpleLock) Lock() Lock {
	startTime := time.Now()

	// 记录加锁开始日志（仅当有日志对象时）
	if sl.logger != nil {
		sl.logger.WithFields(
			log.String("key", sl.entry.key),
			log.String("action", "lock_start"),
			log.Duration("timeout", sl.entry.timeout),
		).Debug("开始加锁")
	}

	if sl.entry.timeout > 0 {
		// 带超时的加锁
		ctx, cancel := context.WithTimeout(context.Background(), sl.entry.timeout)
		defer cancel()

		done := make(chan bool, 1)
		go func() {
			sl.entry.mutex.Lock()
			done <- true
		}()

		select {
		case <-done:
			sl.entry.isLocked = true
		case <-ctx.Done():
			// 超时了，但这里我们仍然需要等待锁释放以避免goroutine泄漏
			sl.entry.mutex.Lock()
			sl.entry.isLocked = true
		}
	} else {
		sl.entry.mutex.Lock()
		sl.entry.isLocked = true
	}

	// 记录加锁成功日志（仅当有日志对象时）
	if sl.logger != nil {
		duration := time.Since(startTime)
		sl.logger.WithFields(
			log.String("key", sl.entry.key),
			log.String("action", "lock_success"),
			log.Duration("duration", duration),
			log.Bool("is_locked", sl.entry.isLocked),
		).Info("加锁成功")
	}

	return sl
}

// Unlock 解锁
func (sl *simpleLock) Unlock() Lock {
	if sl.entry.isLocked {
		// 记录解锁日志（仅当有日志对象时）
		if sl.logger != nil {
			sl.logger.WithFields(
				log.String("key", sl.entry.key),
				log.String("action", "unlock"),
			).Info("开始解锁")
		}

		sl.entry.mutex.Unlock()
		sl.entry.isLocked = false

		// 记录解锁成功日志（仅当有日志对象时）
		if sl.logger != nil {
			sl.logger.WithFields(
				log.String("key", sl.entry.key),
				log.String("action", "unlock_success"),
				log.Bool("is_locked", sl.entry.isLocked),
			).Info("解锁成功")
		}
	} else {
		// 记录重复解锁警告（仅当有日志对象时）
		if sl.logger != nil {
			sl.logger.WithFields(
				log.String("key", sl.entry.key),
				log.String("action", "unlock_duplicate"),
			).Warn("尝试解锁未锁定的锁")
		}
	}
	return sl
}

// TryLock 尝试加锁
func (sl *simpleLock) TryLock() (bool, Lock) {
	// 记录尝试加锁日志（仅当有日志对象时）
	if sl.logger != nil {
		sl.logger.WithFields(
			log.String("key", sl.entry.key),
			log.String("action", "try_lock"),
		).Info("尝试加锁")
	}

	if sl.entry.mutex.TryLock() {
		sl.entry.isLocked = true

		// 记录尝试加锁成功日志（仅当有日志对象时）
		if sl.logger != nil {
			sl.logger.WithFields(
				log.String("key", sl.entry.key),
				log.String("action", "try_lock_success"),
				log.Bool("is_locked", sl.entry.isLocked),
			).Info("尝试加锁成功")
		}

		return true, sl
	}

	// 记录尝试加锁失败日志（仅当有日志对象时）
	if sl.logger != nil {
		sl.logger.WithFields(
			log.String("key", sl.entry.key),
			log.String("action", "try_lock_failed"),
		).Info("尝试加锁失败，锁被占用")
	}

	return false, sl
}

// WithTimeout 设置超时时间
func (sl *simpleLock) WithTimeout(timeout time.Duration) Lock {
	sl.entry.timeout = timeout
	return sl
}

// WithContext 设置上下文
func (sl *simpleLock) WithContext(ctx context.Context) Lock {
	var newLogger *log.Logger
	if sl.logger != nil {
		newLogger = sl.logger.WithContext(ctx)
	}

	return &simpleLock{
		entry:   sl.entry,
		manager: sl.manager,
		ctx:     ctx,
		logger:  newLogger,
	}
}

// WithLogger 设置日志对象
func (sl *simpleLock) WithLogger(logger *log.Logger) Lock {
	newLogger := logger
	if sl.ctx != nil && sl.ctx != context.Background() {
		newLogger = logger.WithContext(sl.ctx)
	}

	return &simpleLock{
		entry:   sl.entry,
		manager: sl.manager,
		ctx:     sl.ctx,
		logger:  newLogger,
	}
}

// GetKey 获取锁的key
func (sl *simpleLock) GetKey() string {
	return sl.entry.key
}

// 全局锁管理器实例
var GlobalLockManager = NewSimpleLockManager()

// 便捷方法，直接使用全局实例

// GetLock 获取锁（全局方法）
func GetLock(key string, expiration ...time.Duration) Lock {
	return GlobalLockManager.GetLock(key, expiration...)
}

// GetLockWithLogger 获取锁，使用指定日志对象（全局方法）
func GetLockWithLogger(key string, logger *log.Logger, expiration ...time.Duration) Lock {
	return GlobalLockManager.GetLockWithLogger(key, logger, expiration...)
}

// UnlockByKey 通过key解锁（全局方法）
func UnlockByKey(key string) error {
	return GlobalLockManager.UnlockByKey(key)
}

// CleanExpiredLocks 清理过期锁（全局方法）
func CleanExpiredLocks() int {
	return GlobalLockManager.CleanExpiredLocks()
}

// GetStats 获取锁统计信息（全局方法）
func GetStats() map[string]LockStats {
	return GlobalLockManager.GetStats()
}
