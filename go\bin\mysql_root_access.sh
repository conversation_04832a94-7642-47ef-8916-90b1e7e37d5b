#!/bin/bash
# MySQL Root权限控制脚本
# 功能：切换root用户远程访问权限
# 参数：permit（允许所有IP） / close（仅允许localhost）
# 用法：sudo ./mysql_root_access.sh [permit|close]
# 日期：2025-04-12

# 配置区（可根据实际情况修改）
MYSQL_USER="root"
MYSQL_CONF="/etc/mysql/mysql.conf.d/mysqld.cnf"
LOG_FILE="/var/log/mysql_access.log"

# 日志记录函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

# 执行SQL语句（带错误重试）
execute_sql() {
    for i in {1..3}; do
        if mysql -u root -proot -e "$1" 2>>$LOG_FILE; then
            return 0
        else
            log "第$i次执行失败: $1"
            sleep 2
        fi
    done
    log "SQL执行失败: $1"
    return 1
}

# 放通所有IP
permit_all() {
    log "开始放通所有IP访问权限"
    
    # 修改MySQL配置文件
    sed -i "s/bind-address.*/bind-address = 0.0.0.0/" $MYSQL_CONF
    
    # 权限设置（兼容MySQL 5.7+）
    execute_sql "CREATE USER IF NOT EXISTS '$MYSQL_USER'@'%' IDENTIFIED WITH mysql_native_password BY 'root'" || return 1
    execute_sql "GRANT ALL PRIVILEGES ON *.* TO '$MYSQL_USER'@'%' WITH GRANT OPTION" || return 1
    execute_sql "FLUSH PRIVILEGES" || return 1
    
    # 重启    
    if systemctl restart mysql 2>>$LOG_FILE; then
        log "服务重启成功"
    else
        log "服务重启失败"
        return 1
    fi
}

# 仅允许本地访问
close_remote() {
    log "开始限制本地访问"
    
    # 恢复MySQL配置
    sed -i "s/bind-address.*/bind-address = 127.0.0.1/" $MYSQL_CONF
    
    # 权限回收
    execute_sql "DROP USER IF EXISTS '$MYSQL_USER'@'%'" || return 1
    execute_sql "FLUSH PRIVILEGES" || return 1
    
    # 重启服务
    if systemctl restart mysql 2>>$LOG_FILE; then
        log "服务重启成功"
    else
        log "服务重启失败"
        return 1
    fi
}

# 主逻辑
case "$1" in
    permit)
        if permit_all; then
            log "配置成功：root已允许所有IP访问"
            exit 0
        else
            log "配置失败，请检查日志"
            exit 1
        fi
        ;;
    close)
        if close_remote; then
            log "配置成功：root仅允许本地访问"
            exit 0
        else
            log "配置失败，请检查日志"
            exit 1
        fi
        ;;
    *)
        echo "使用方法："
        echo "  sudo $0 permit  开放所有IP访问"
        echo "  sudo $0 close   仅允许本地访问"
        exit 1
        ;;
esac