package uniapp

/**
* 引入控制器
* 请把您使用包用 _ "fincore/app/home/<USER>"导入您编写的包 自动生成路由
* 不是使用则注释掉
* 路由规则：包路径"home/article" + 包中结构体"Cate"转小写+方法名
 */
import (
	_ "fincore/app/uniapp/bankcard"
	_ "fincore/app/uniapp/captcha"
	_ "fincore/app/uniapp/common"
	_ "fincore/app/uniapp/hetong"
	_ "fincore/app/uniapp/home"
	_ "fincore/app/uniapp/identity"
	_ "fincore/app/uniapp/order"
	_ "fincore/app/uniapp/repayment"
	_ "fincore/app/uniapp/risk"
	_ "fincore/app/uniapp/user"
)
