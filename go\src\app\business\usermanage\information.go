package usermanage

import (
	"fincore/model"
	"fincore/utils/gf"
	"fincore/utils/gform"
	"fincore/utils/results"
	"fmt"
	"reflect"

	"github.com/gin-gonic/gin"
)

type Information struct{}

func init() {
	fpath := Information{}
	gf.Register(&fpath, reflect.TypeOf(fpath).PkgPath())
}

type GetInfoRequest struct {
	Name              string `json:"name"`              //姓名
	Mobile            string `json:"mobile"`            //手机号
	IDCard            string `json:"idcard"`            //身份证号
	Reviewer          string `json:"reviewer"`          //审核人
	Channel           string `json:"channel"`           //渠道来源
	IsOrNotBlack      string `json:"isOrNotHack"`       //是否风控
	MaxRiskScore      string `json:"riskSorce"`         //最高风控分
	MinRiskScore      string `json:"minRiskSorce"`      //最低风控分
	RiskNum           string `json:"riskNum"`           //风控流水号
	MaxRemainingQuota string `json:"maxRemainingQuota"` //最高剩余额度
	MinRemainingQuota string `json:"minRemainingQuota"` //最低剩余额度
	VerifyStatus      string `json:"verifyStatus"`      //认证状态
	OrderStatus       string `json:"orderStatus"`       //订单状态
	IsOrNotVerify     string `json:"isOrNotVerify"`     //注册未实名？
	IsOrNotVorder     string `json:"isOrNotOrder"`      //实名未下单？
	IsOrNotQorder     string `json:"isOrNotQorder"`     //有额度未下单？
	Remarks           string `json:"remarks"`           //备注
	DisburseNum       string `json:"disburseNum"`       //放款次数
	IsOrNotNewUser    string `json:"isOrNotNewUser"`    //是否新用户
	IsOrNotComplaint  string `json:"isOrNotComplaint"`  //是否投诉
	BuyStartTime      string `json:"buyStartTime"`      //进件开始时间
	BuyEndTime        string `json:"buyEndTime"`        //进件结束时间
	RegisterStartTime string `json:"registerStartTime"` //注册开始时间
	RegisterEndTime   string `json:"registerEndTime"`   //注册结束时间

}

type GetInfoResponse struct {
	Id               int    `json:"id" `
	Channel          string `json:"channel"`          //渠道来源
	Reviewer         string `json:"reviewer"`         //审核人
	UserFrom         string `json:"userFrom"`         //客户来源
	Name             string `json:"name"`             //姓名
	Mobile           string `json:"mobile"`           //手机号
	Remark           string `json:"remark"`           //用户备注
	BuyTime          string `json:"buyTime"`          //进件时间
	Createtime       string `json:"createtime"`       //注册时间
	DisburseNum      int    `json:"disburseNum"`      //放款次数
	VerifyStatus     string `json:"verifyStatus"`     //认证状态
	OrderStatus      string `json:"orderStatus"`      //订单状态
	ComplaintStatus  string `json:"complaintStatus"`  //投诉状态
	ComplaintRemarks string `json:"complaintRemarks"` //投诉备注
	AllQuota         string `json:"allQuota"`         //总额度
	RemainingQuota   string `json:"remainingQuota"`   //剩余额度
	ModelSorce       string `json:"modelSorce"`       //模型分
	Idcard           string `json:"idcard"`           //身份证号

}

func (api *Information) GetInfoList(c *gin.Context) {

	// jsonschema

	// action

	// 分页
}

func (api *Information) GetInfo(c *gin.Context) {
	var req GetInfoRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		results.Failed(c, "参数错误", err)
		return
	}
	fmt.Println(req)
	user, err := GetUserinfoFromDB(req)
	if err != nil {
		results.Failed(c, "查询用户信息失败", err)
		return
	}
	fmt.Println("**********")
	results.Success(c, "查询用户信息成功", user, nil)
}

func GetUserinfoFromDB(request GetInfoRequest) ([]GetInfoResponse, error) {

	users := make([]GetInfoResponse, 10)
	query := model.DB().Table("business_app_account")
	//姓名
	if request.Name != "" {
		query = query.Where("name", request.Name)
	}
	//手机号
	if request.Mobile != "" {
		query.Where("mobile", request.Mobile)

	}
	// fmt.Println("mobile:", request.Mobile)
	//身份证号
	if request.IDCard != "" {
		query = query.Where("idCard", request.IDCard)
	}

	/* 审核人 查询完用户表再去查订单表*/

	// 渠道来源
	if request.Channel != "" {
		query = query.Where("channel", request.Channel)
	}
	//是否风控这里前端需要传数字 '状态：0-正常 1-白名单 2-黑名单 4-风控'
	if request.IsOrNotBlack != "" {
		query = query.Where("status", request.IsOrNotBlack)
	}

	/*风控分 范围查询 查询的是订单表*/

	//剩余额度 范围查询
	if request.MaxRemainingQuota != "" && request.MinRemainingQuota != "" {
		query = query.WhereBetween("reminderQuota", []interface{}{request.MinRemainingQuota, request.MaxRemainingQuota})
	}
	//认证状态 前端传数字 0-未认证 1-认证中 2-认证通过 3-认证失败
	if request.VerifyStatus != "" {
		query = query.Where("verifyStatus", request.VerifyStatus)
	}
	/*订单状态 前端传数字 */

	//注册未实名？ 前端传数字 0-未实名 1-实名
	if request.IsOrNotVerify != "" {
		query = query.Where("isIdentity", request.IsOrNotVerify)
	}

	//实名未下单这个要和订单表查

	//有额度未下单这个要和订单表查

	//备注
	if request.Remarks != "" {
		query = query.Where("remarks", request.Remarks)
	}

	//放款次数 就是用户的订单个数，所以要和查订单表

	//是否为新用户 前端传数字 0-是 1-否
	if request.IsOrNotNewUser != "" {
		query = query.Where("isIdentity", request.IsOrNotNewUser)
	}
	//是否投诉 前端传数字 0-未投诉 1-投诉 查询订单表

	//进件时间 范围查询订单表用户最新的订单时间

	//注册时间 范围查询用户表的注册时间
	if request.RegisterStartTime != "" && request.RegisterEndTime != "" {
		query = query.WhereBetween("registerTime", []interface{}{request.RegisterStartTime, request.RegisterEndTime})
	}
	//声明一个切片 用于存取查询到的数据
	var mp []gform.Data
	mp, _ = query.Get()
	for i, m := range mp {
		if id64, ok := m["id"].(int64); ok {
			users[i].Id = int(id64)
		}
		if channel, ok := m["channel"].(string); ok {
			users[i].Channel = channel
		}
		//
		if userfrom, ok := m["userFrom"].(string); ok {
			users[i].UserFrom = userfrom
		}
		if name, ok := m["name"].(string); ok {
			users[i].Name = name
		}
		if mobile, ok := m["mobile"].(string); ok {
			users[i].Mobile = mobile
		}
		if remarks, ok := m["remark"].(string); ok {
			users[i].Remark = remarks
		}
		if createtime, ok := m["createtime"].(string); ok {
			users[i].Createtime = createtime
		}
		if verifyStatus, ok := m["verifyStatus"].(string); ok {
			users[i].VerifyStatus = verifyStatus
		}
		if complaintStatus, ok := m["complaintStatus"].(string); ok {
			users[i].ComplaintStatus = complaintStatus
		}
		if complaintRemarks, ok := m["complaintRemarks"].(string); ok {
			users[i].ComplaintRemarks = complaintRemarks
		}
		if allQuota, ok := m["allQuota"].(string); ok {
			users[i].AllQuota = allQuota
		}
		if remainingQuota, ok := m["remainingQuota"].(string); ok {
			users[i].RemainingQuota = remainingQuota
		}
		if modelSorce, ok := m["modelPoint"].(string); ok {
			users[i].ModelSorce = modelSorce
		}
		if idcard, ok := m["idCard"].(string); ok {
			users[i].Idcard = idcard
		}
	}
	return GetUserOrderInfoFromDB(users, request)
}
func GetUserOrderInfoFromDB(users []GetInfoResponse, request GetInfoRequest) ([]GetInfoResponse, error) {

	// type UserOrder struct {
	// }
	mp1 := make(map[int]int64, len(users))
	mp2 := make(map[int]int64, len(users))
	for i, user := range users {
		//实名未下单
		//查询订单个数
		query := model.DB().Table("business_app_order")
		counT, _ := model.DB().Table("business_user_order").Where("id", user.Id).Count()
		if counT == 0 {
			mp1[user.Id] = -1
			continue
		}
		//有额度未下单
		quota, _ := model.DB().Table("business_app_account").Where("id", user.Id).Fields("reminingQuota").First()
		if counT == 0 && quota["reminingQuota"].(float64) > 0 {
			mp2[user.Id] = -2
			continue
		}
		//查询订单时间
		if request.BuyStartTime != "" && request.BuyEndTime != "" {
			query = query.WhereBetween("order_time", []interface{}{request.BuyStartTime, request.BuyEndTime})
		}
		// query := model.DB().Table("business_app_order").Where("user_id", users[i].Id).First()
		//查询审核人
		if request.Reviewer != "" {
			query = query.Where("examine", request.Reviewer)
		}
		//查询风控分
		if request.MaxRiskScore != "" && request.MinRiskScore != "" {
			query = query.WhereBetween("risk_rating", []interface{}{request.MinRiskScore, request.MaxRiskScore}).OrderBy("risk_rating")
		}
		//查询订单状态
		if request.OrderStatus != "" {
			_ = query.Where("order_status", request.OrderStatus)
		}
		//查询是否投诉
		if request.IsOrNotComplaint != "" {
			_ = query.Where("complaint_status", request.IsOrNotComplaint)
		}
		data, _ := query.Where("user_id", users[i].Id).First()
		user.BuyTime = data["order_time"].(string)
		user.DisburseNum = int(counT)
		user.OrderStatus = data["order_status"].(string)
	}

	return users, nil
}
