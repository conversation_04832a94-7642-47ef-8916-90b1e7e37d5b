package external

import (
	"bytes"
	"crypto/aes"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"time"
)

// AESUtils AES加密解密工具
type AESUtils struct{}

// NewAESUtils 创建AES工具实例
func NewAESUtils() *AESUtils {
	return &AESUtils{}
}

// Encrypt AES加密
func (a *AESUtils) Encrypt(plaintext, key string) (string, error) {
	// 直接使用key的字节，与Java版本保持一致
	keyBytes := []byte(key)

	// 创建AES cipher
	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %v", err)
	}

	// PKCS5Padding
	plaintextBytes := []byte(plaintext)
	padding := aes.BlockSize - len(plaintextBytes)%aes.BlockSize
	padtext := make([]byte, padding)
	for i := range padtext {
		padtext[i] = byte(padding)
	}
	
	plaintextBytes = append(plaintextBytes, padtext...)
	// ECB模式加密
	ciphertext := make([]byte, len(plaintextBytes))
	for i := 0; i < len(plaintextBytes); i += aes.BlockSize {
		block.Encrypt(ciphertext[i:i+aes.BlockSize], plaintextBytes[i:i+aes.BlockSize])
	}

	// Base64编码
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// Decrypt AES解密
func (a *AESUtils) Decrypt(ciphertext, key string) (string, error) {
	// 直接使用key的字节，与Java版本保持一致
	keyBytes := []byte(key)

	// 解码base64密文
	ciphertextBytes, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", fmt.Errorf("failed to decode ciphertext: %v", err)
	}

	// 创建AES cipher
	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %v", err)
	}

	// ECB模式解密
	plaintext := make([]byte, len(ciphertextBytes))
	for i := 0; i < len(ciphertextBytes); i += aes.BlockSize {
		block.Decrypt(plaintext[i:i+aes.BlockSize], ciphertextBytes[i:i+aes.BlockSize])
	}

	// 去除PKCS5Padding
	if len(plaintext) == 0 {
		return "", errors.New("invalid padding")
	}
	padding := int(plaintext[len(plaintext)-1])
	if padding > aes.BlockSize || padding == 0 {
		return "", errors.New("invalid padding")
	}
	for i := len(plaintext) - padding; i < len(plaintext); i++ {
		if plaintext[i] != byte(padding) {
			return "", errors.New("invalid padding")
		}
	}
	plaintext = plaintext[:len(plaintext)-padding]

	return string(plaintext), nil
}

// HTTPUtils HTTP请求工具
type HTTPUtils struct {
	client *http.Client
}

// NewHTTPUtils 创建HTTP工具实例
func NewHTTPUtils(timeout int) *HTTPUtils {
	return &HTTPUtils{
		client: &http.Client{
			Timeout: time.Duration(timeout) * time.Second,
		},
	}
}

// SendPostJSON 发送POST JSON请求
func (h *HTTPUtils) SendPostJSON(url string, data interface{}) (string, error) {
	// 序列化JSON数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", fmt.Errorf("failed to marshal JSON: %v", err)
	}

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "Fincore-Risk-Service/1.0")

	// 发送请求
	resp, err := h.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %v", err)
	}

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP error: %d %s, response: %s", resp.StatusCode, resp.Status, string(body))
	}

	return string(body), nil
}