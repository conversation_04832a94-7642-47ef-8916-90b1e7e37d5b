# 定时任务调度器 API 文档

## 📋 概述

本文档描述了定时任务调度器模块的所有公开 API 接口，包括函数签名、参数说明、返回值和使用示例。

## 🔧 核心 API

### 调度器管理

#### `scheduler.Initialize() error`

初始化调度器模块。

**返回值**:
- `error`: 初始化失败时返回错误

**示例**:
```go
if err := scheduler.Initialize(); err != nil {
    log.Fatalf("初始化调度器失败: %v", err)
}
```

#### `scheduler.Start() error`

启动调度器，开始执行已注册的任务。

**返回值**:
- `error`: 启动失败时返回错误

**示例**:
```go
if err := scheduler.Start(); err != nil {
    log.Fatalf("启动调度器失败: %v", err)
}
```

#### `scheduler.Stop() error`

停止调度器，等待当前运行的任务完成。

**返回值**:
- `error`: 停止失败时返回错误

**示例**:
```go
if err := scheduler.Stop(); err != nil {
    log.Printf("停止调度器失败: %v", err)
}
```

#### `scheduler.StopAndCleanup() error`

停止调度器并清理所有资源。

**返回值**:
- `error`: 清理失败时返回错误

**示例**:
```go
defer func() {
    if err := scheduler.StopAndCleanup(); err != nil {
        log.Printf("清理调度器失败: %v", err)
    }
}()
```

### 任务管理

#### `scheduler.RegisterTask(task TaskInterface) error`

注册一个新任务到调度器。

**参数**:
- `task TaskInterface`: 实现了 TaskInterface 接口的任务实例

**返回值**:
- `error`: 注册失败时返回错误

**示例**:
```go
task := NewMyTask()
if err := scheduler.RegisterTask(task); err != nil {
    log.Printf("注册任务失败: %v", err)
}
```

#### `scheduler.UnregisterTask(taskName string) error`

从调度器中注销指定任务。

**参数**:
- `taskName string`: 任务名称

**返回值**:
- `error`: 注销失败时返回错误

**示例**:
```go
if err := scheduler.UnregisterTask("my_task"); err != nil {
    log.Printf("注销任务失败: %v", err)
}
```

#### `scheduler.GetManager() *SchedulerManager`

获取调度器管理器实例。

**返回值**:
- `*SchedulerManager`: 调度器管理器实例，如果未初始化则返回 nil

**示例**:
```go
manager := scheduler.GetManager()
if manager != nil {
    stats := manager.GetStats()
    fmt.Printf("运行中任务: %d\n", stats.RunningTasks)
}
```

## 📝 任务接口

### TaskInterface

所有任务必须实现的核心接口。

```go
type TaskInterface interface {
    // 基本信息
    GetName() string
    GetDescription() string
    GetSchedule() string
    GetTimeout() time.Duration
    
    // 执行逻辑
    Execute(ctx context.Context) error
    
    // 配置属性
    GetConcurrencyMode() ConcurrencyMode
    GetRetryCount() int
    GetRetryInterval() time.Duration
    
    // 生命周期钩子（可选实现）
    OnStart(ctx context.Context) error
    OnSuccess(ctx context.Context) error
    OnError(ctx context.Context, err error) error
    OnComplete(ctx context.Context) error
}
```

### BaseTask

提供任务接口的基础实现。

#### `tasks.NewBaseTask(name, description, schedule string, timeout time.Duration) *BaseTask`

创建基础任务实例。

**参数**:
- `name string`: 任务名称（唯一标识）
- `description string`: 任务描述
- `schedule string`: Cron 表达式
- `timeout time.Duration`: 任务超时时间

**返回值**:
- `*BaseTask`: 基础任务实例

**示例**:
```go
baseTask := tasks.NewBaseTask(
    "data_sync",
    "数据同步任务",
    "0 */6 * * *",
    30*time.Minute,
)
```

#### `(*BaseTask).SetConcurrencyMode(mode ConcurrencyMode) *BaseTask`

设置并发模式。

**参数**:
- `mode ConcurrencyMode`: 并发模式（Singleton 或 Parallel）

**返回值**:
- `*BaseTask`: 返回自身，支持链式调用

**示例**:
```go
baseTask.SetConcurrencyMode(tasks.ConcurrencyModeSingleton)
```

#### `(*BaseTask).SetRetryCount(count int) *BaseTask`

设置重试次数。

**参数**:
- `count int`: 重试次数

**返回值**:
- `*BaseTask`: 返回自身，支持链式调用

**示例**:
```go
baseTask.SetRetryCount(3)
```

#### `(*BaseTask).SetRetryInterval(interval time.Duration) *BaseTask`

设置重试间隔。

**参数**:
- `interval time.Duration`: 重试间隔时间

**返回值**:
- `*BaseTask`: 返回自身，支持链式调用

**示例**:
```go
baseTask.SetRetryInterval(5 * time.Second)
```

## 📊 管理器 API

### SchedulerManager

调度器管理器提供的管理接口。

#### `(*SchedulerManager).GetStats() *SchedulerStats`

获取调度器统计信息。

**返回值**:
- `*SchedulerStats`: 统计信息结构体

**示例**:
```go
manager := scheduler.GetManager()
stats := manager.GetStats()

fmt.Printf("总任务数: %d\n", stats.TotalTasks)
fmt.Printf("运行中: %d\n", stats.RunningTasks)
fmt.Printf("成功次数: %d\n", stats.SuccessCount)
fmt.Printf("失败次数: %d\n", stats.FailureCount)
```

#### `(*SchedulerManager).IsRunning() bool`

检查调度器是否正在运行。

**返回值**:
- `bool`: 运行状态

**示例**:
```go
manager := scheduler.GetManager()
if manager.IsRunning() {
    fmt.Println("调度器正在运行")
}
```

#### `(*SchedulerManager).GetRegistry() *TaskRegistry`

获取任务注册中心。

**返回值**:
- `*TaskRegistry`: 任务注册中心实例

**示例**:
```go
manager := scheduler.GetManager()
registry := manager.GetRegistry()
allTasks := registry.GetAllTasks()
```

## 🔍 注册中心 API

### TaskRegistry

任务注册中心提供的管理接口。

#### `(*TaskRegistry).GetAllTasks() map[string]TaskInterface`

获取所有已注册的任务。

**返回值**:
- `map[string]TaskInterface`: 任务名称到任务实例的映射

**示例**:
```go
registry := manager.GetRegistry()
tasks := registry.GetAllTasks()

for name, task := range tasks {
    fmt.Printf("任务: %s, 描述: %s\n", name, task.GetDescription())
}
```

#### `(*TaskRegistry).GetTask(name string) (TaskInterface, bool)`

根据名称获取特定任务。

**参数**:
- `name string`: 任务名称

**返回值**:
- `TaskInterface`: 任务实例
- `bool`: 是否找到任务

**示例**:
```go
registry := manager.GetRegistry()
if task, exists := registry.GetTask("my_task"); exists {
    fmt.Printf("找到任务: %s\n", task.GetDescription())
}
```

#### `(*TaskRegistry).GetTaskCount() int`

获取已注册任务的数量。

**返回值**:
- `int`: 任务数量

**示例**:
```go
registry := manager.GetRegistry()
count := registry.GetTaskCount()
fmt.Printf("已注册 %d 个任务\n", count)
```

## 📈 统计信息

### SchedulerStats

调度器统计信息结构体。

```go
type SchedulerStats struct {
    TotalTasks       int           `json:"total_tasks"`
    RunningTasks     int           `json:"running_tasks"`
    SuccessCount     int64         `json:"success_count"`
    FailureCount     int64         `json:"failure_count"`
    FailureRate      float64       `json:"failure_rate"`
    AvgExecutionTime time.Duration `json:"avg_execution_time"`
    LastUpdateTime   time.Time     `json:"last_update_time"`
}
```

## 🚨 错误类型

### 常见错误

```go
var (
    ErrSchedulerNotInitialized = errors.New("调度器未初始化")
    ErrSchedulerAlreadyRunning = errors.New("调度器已在运行")
    ErrSchedulerNotRunning     = errors.New("调度器未运行")
    ErrTaskAlreadyExists       = errors.New("任务已存在")
    ErrTaskNotFound           = errors.New("任务不存在")
    ErrInvalidCronExpression  = errors.New("无效的Cron表达式")
    ErrTaskTimeout            = errors.New("任务执行超时")
    ErrTaskPanic              = errors.New("任务执行发生panic")
)
```

## 🔧 配置 API

### config.GetConfig() *SchedulerConfig

获取调度器配置。

**返回值**:
- `*SchedulerConfig`: 配置实例

**示例**:
```go
config := config.GetConfig()
fmt.Printf("调度器名称: %s\n", config.Scheduler.Name)
fmt.Printf("时区: %s\n", config.Scheduler.Timezone)
```

---

**注意**: 所有 API 都是线程安全的，可以在多个 goroutine 中并发调用。
