// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMigrateTimeline = "migrate_timeline"

// MigrateTimeline mapped from table <migrate_timeline>
type MigrateTimeline struct {
	Record string `gorm:"column:record;primaryKey;comment:数据迁移记录名称" json:"record"` // 数据迁移记录名称
}

// TableName MigrateTimeline's table name
func (*MigrateTimeline) TableName() string {
	return TableNameMigrateTimeline
}
