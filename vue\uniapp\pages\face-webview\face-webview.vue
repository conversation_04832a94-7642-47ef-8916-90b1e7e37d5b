<template>
	<!-- <web-view :src="faceUrl" @message="onFaceResult"></web-view> -->
	<view></view>
</template>

<script>
	import user from '@/store/user.js';
	import userApi from '@/api/user.js';
	import {
		storeToRefs
	} from 'pinia';
	export default {

		async onLoad() {

			// 全平台兼容的URL参数解析
			const hashQuery = window.location.hash.split('?')[1];
			const params = new URLSearchParams(hashQuery);
			const result = params.get('result');
			console.log('result', result)
			uni.webView.postMessage({
				type: 'message',
				data: result
			}, '*');
			// #ifdef WEB
			const userStore = user();
			await userStore.getInfo();
			const {
				userInfo
			} = storeToRefs(userStore);
			if (userInfo.value.bizId) {
				userApi.getCheckFaceResult({
					bizId: userInfo.value.bizId
				});
			}
			if (result === '1') {
				userApi.getFacePhoto({});
				uni.redirectTo({
					url: '/pages/FaceRecognition/FaceRecognition'
				});
			} else {
				uni.redirectTo({
					url: '/pages/Authentication/Authentication'
				});
			}
			// #endif
		}
	}
</script>