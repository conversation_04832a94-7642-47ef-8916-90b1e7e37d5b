package customer

import (
	"fmt"
	"strconv"
	"strings"
	"time"
)

// IdentityStatusStage 认证状态阶段
type IdentityStatusStage int

const (
	StageBeforeApply   IdentityStatusStage = 1 // 申请前（1-8位）
	StageWaitingResult IdentityStatusStage = 2 // 等待申请结果（9-16位）
	StageApplySuccess  IdentityStatusStage = 3 // 申请通过（17-24位）
	StageApplyFailed   IdentityStatusStage = 4 // 申请不通过（25-32位）
)

// 申请前阶段位定义（1-8位）
const (
	BitRegisterPass = 1 // 注册通过
	BitOrcPass      = 2 // ORC通过
	BitLivenessPass = 3 // 活体通过
	BitContactPass  = 4 // 联系人通过
)

// 等待申请结果阶段位定义（9-16位）
const (
	BitWaitManualReview = 9  // 待人审
	BitWaitRiskCallback = 10 // 等待分控回调
	BitWaitRiskControl  = 11 // 等待分控
)

// 申请通过阶段位定义（17-24位）
const (
	BitAuditPass   = 17 // 审核通过
	BitFrozen      = 18 // 冻结
	BitRiskExpired = 19 // 风控过期
)

// 申请不通过阶段位定义（25-32位）
const (
	BitRiskFailed  = 25 // 风控未通过
	BitAuditFailed = 26 // 审核未通过
	BitRiskError   = 27 // 风控异常
)

// CheckIdentityStatusBit 检查identityStatus指定位是否为1
func CheckIdentityStatusBit(status uint64, position int) bool {
	if position < 1 || position > 64 {
		return false
	}
	return status&(1<<(position-1)) != 0
}

// GetIdentityStatusValue 根据位置获取identityStatus的位值
func GetIdentityStatusValue(position int) uint64 {
	if position < 1 || position > 64 {
		return 0
	}
	return 1 << (position - 1)
}

// IsRegisterIncomplete 判断是否注册未实名
// 条件：ORC通过、活体通过、联系人通过均为0
func IsRegisterIncomplete(status uint64) bool {
	return !CheckIdentityStatusBit(status, BitOrcPass) &&
		!CheckIdentityStatusBit(status, BitLivenessPass) &&
		!CheckIdentityStatusBit(status, BitContactPass)
}

// IsRealNameIncomplete 判断是否实名未下单
// 条件：1-4位均为1且orderStatus=0
func IsRealNameIncomplete(status uint64, orderStatus int) bool {
	return CheckIdentityStatusBit(status, BitRegisterPass) &&
		CheckIdentityStatusBit(status, BitOrcPass) &&
		CheckIdentityStatusBit(status, BitLivenessPass) &&
		CheckIdentityStatusBit(status, BitContactPass) &&
		orderStatus == 0
}

// HasQuotaNoOrder 判断是否有额度未下单
// 条件：reminderQuota>0且orderStatus=0
func HasQuotaNoOrder(reminderQuota float64, orderStatus int) bool {
	return reminderQuota > 0 && orderStatus == 0
}

// IsNewUser 判断是否新用户
// 条件：仅注册通过就表示新用户
func IsNewUser(status uint64) bool {
	return CheckIdentityStatusBit(status, BitRegisterPass)
}

// GetIdentityStatusText 获取认证状态文本描述
func GetIdentityStatusText(status uint64) string {
	// 检查各个阶段的状态
	if CheckIdentityStatusBit(status, BitAuditPass) {
		return "审核通过"
	}
	if CheckIdentityStatusBit(status, BitFrozen) {
		return "冻结"
	}
	if CheckIdentityStatusBit(status, BitRiskExpired) {
		return "风控过期"
	}
	if CheckIdentityStatusBit(status, BitRiskFailed) {
		return "风控未通过"
	}
	if CheckIdentityStatusBit(status, BitAuditFailed) {
		return "审核未通过"
	}
	if CheckIdentityStatusBit(status, BitRiskError) {
		return "风控异常"
	}
	if CheckIdentityStatusBit(status, BitWaitManualReview) {
		return "待人审"
	}
	if CheckIdentityStatusBit(status, BitWaitRiskCallback) {
		return "等待分控回调"
	}
	if CheckIdentityStatusBit(status, BitWaitRiskControl) {
		return "等待分控"
	}
	if CheckIdentityStatusBit(status, BitContactPass) {
		return "联系人通过"
	}
	if CheckIdentityStatusBit(status, BitLivenessPass) {
		return "活体通过"
	}
	if CheckIdentityStatusBit(status, BitOrcPass) {
		return "ORC通过"
	}
	if CheckIdentityStatusBit(status, BitRegisterPass) {
		return "注册通过"
	}
	return "未认证"
}

// GetOrderStatusText 获取订单状态文本描述
func GetOrderStatusText(status int) string {
	switch status {
	case 0:
		return "无订单"
	case 1:
		return "进行中"
	case 2:
		return "已完成"
	case 3:
		return "已取消"
	default:
		return "未知"
	}
}

// GetComplaintStatusText 获取投诉状态文本描述
func GetComplaintStatusText(status int) string {
	switch status {
	case 0:
		return "否"
	case 1:
		return "是"
	default:
		return "未知"
	}
}

// MaskIdCard 身份证脱敏处理
func MaskIdCard(idCard string) string {
	if len(idCard) < 8 {
		return idCard
	}
	return idCard[:3] + "****" + idCard[len(idCard)-4:]
}

// FormatTime 格式化时间戳为字符串
func FormatTime(timestamp int64) string {
	if timestamp == 0 {
		return ""
	}
	return time.Unix(timestamp, 0).Format("2006-01-02 15:04:05")
}

// ParseTimeRange 解析时间范围字符串为时间戳
func ParseTimeRange(timeStr string) (int64, error) {
	if timeStr == "" {
		return 0, nil
	}

	// 尝试解析不同格式
	layouts := []string{
		"2006-01-02 15:04:05",
		"2006-01-02",
	}

	for _, layout := range layouts {
		if t, err := time.Parse(layout, timeStr); err == nil {
			return t.Unix(), nil
		}
	}

	return 0, fmt.Errorf("invalid time format: %s", timeStr)
}

// ParseFloat64 安全解析字符串为float64
func ParseFloat64(str string) (float64, error) {
	if str == "" {
		return 0, nil
	}
	return strconv.ParseFloat(str, 64)
}

// ParseInt64 安全解析字符串为int64
func ParseInt64(str string) (int64, error) {
	if str == "" {
		return 0, nil
	}
	return strconv.ParseInt(str, 10, 64)
}

// ParseInt 安全解析字符串为int
func ParseInt(str string) (int, error) {
	if str == "" {
		return 0, nil
	}
	return strconv.Atoi(str)
}

// BuildLikeCondition 构建LIKE查询条件
func BuildLikeCondition(value string) string {
	if value == "" {
		return ""
	}
	return "%" + strings.ReplaceAll(value, "%", "\\%") + "%"
}

// IsRiskUser 判断是否风控用户
func IsRiskUser(riskFlowNumber string) bool {
	return riskFlowNumber != ""
}

// GetDeviceSourceText 获取设备来源文本
func GetDeviceSourceText(source string) string {
	switch source {
	case "h5":
		return "H5"
	case "android":
		return "Android"
	case "ios":
		return "iOS"
	case "other":
		return "其他"
	default:
		return source
	}
}

// ValidateIdentityStatusCondition 验证认证状态查询条件
func ValidateIdentityStatusCondition(stage, subStatus string) (uint64, error) {
	if stage == "" {
		return 0, nil
	}

	stageInt, err := strconv.Atoi(stage)
	if err != nil {
		return 0, fmt.Errorf("invalid stage: %s", stage)
	}

	if stageInt < 1 || stageInt > 4 {
		return 0, fmt.Errorf("stage must be 1-4, got: %d", stageInt)
	}

	if subStatus == "" {
		return 0, nil
	}

	subStatusInt, err := strconv.Atoi(subStatus)
	if err != nil {
		return 0, fmt.Errorf("invalid sub status: %s", subStatus)
	}

	// 根据阶段计算实际的位值
	var actualBit int
	switch IdentityStatusStage(stageInt) {
	case StageBeforeApply:
		actualBit = subStatusInt // 1-8位
	case StageWaitingResult:
		actualBit = 8 + subStatusInt // 9-16位
	case StageApplySuccess:
		actualBit = 16 + subStatusInt // 17-24位
	case StageApplyFailed:
		actualBit = 24 + subStatusInt // 25-32位
	}

	if actualBit < 1 || actualBit > 32 {
		return 0, fmt.Errorf("invalid bit position: %d", actualBit)
	}

	return GetIdentityStatusValue(actualBit), nil
}
