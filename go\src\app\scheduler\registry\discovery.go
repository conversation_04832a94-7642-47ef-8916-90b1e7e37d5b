package registry

import (
	"fincore/app/scheduler/tasks"
	"fincore/utils/log"
	"fmt"
	"reflect"
	"strings"

	"go.uber.org/zap"
)

// TaskDiscovery 任务发现器
type TaskDiscovery struct {
	registry *TaskRegistry
	logger   *log.Logger
}

// NewTaskDiscovery 创建任务发现器
func NewTaskDiscovery(registry *TaskRegistry, logger *log.Logger) *TaskDiscovery {
	return &TaskDiscovery{
		registry: registry,
		logger:   logger,
	}
}

// DiscoveryResult 发现结果
type DiscoveryResult struct {
	TotalFound     int      `json:"total_found"`
	SuccessCount   int      `json:"success_count"`
	FailureCount   int      `json:"failure_count"`
	SuccessTasks   []string `json:"success_tasks"`
	FailureTasks   []string `json:"failure_tasks"`
	FailureReasons []string `json:"failure_reasons"`
}

// AutoDiscoverTasks 自动发现并注册任务
// 这个方法会扫描已经实例化的任务并自动注册
func (d *TaskDiscovery) AutoDiscoverTasks(taskInstances ...tasks.TaskInterface) *DiscoveryResult {
	result := &DiscoveryResult{
		TotalFound:     len(taskInstances),
		SuccessTasks:   make([]string, 0),
		FailureTasks:   make([]string, 0),
		FailureReasons: make([]string, 0),
	}

	d.logger.Info("开始自动发现任务",
		zap.Int("task_count", len(taskInstances)),
	)

	for _, task := range taskInstances {
		if task == nil {
			result.FailureCount++
			result.FailureTasks = append(result.FailureTasks, "nil")
			result.FailureReasons = append(result.FailureReasons, "任务实例为nil")
			continue
		}

		taskName := task.GetName()
		if err := d.registry.RegisterTask(task); err != nil {
			result.FailureCount++
			result.FailureTasks = append(result.FailureTasks, taskName)
			result.FailureReasons = append(result.FailureReasons, err.Error())

			d.logger.Warn("任务注册失败",
				zap.String("task_name", taskName),
				zap.Error(err),
			)
		} else {
			result.SuccessCount++
			result.SuccessTasks = append(result.SuccessTasks, taskName)

			d.logger.Info("任务发现并注册成功",
				zap.String("task_name", taskName),
				zap.String("task_type", d.getTaskType(task)),
			)
		}
	}

	d.logger.Info("任务自动发现完成",
		zap.Int("total", result.TotalFound),
		zap.Int("success", result.SuccessCount),
		zap.Int("failure", result.FailureCount),
	)

	return result
}

// RegisterTasksFromMap 从映射中注册任务
func (d *TaskDiscovery) RegisterTasksFromMap(taskMap map[string]tasks.TaskInterface) *DiscoveryResult {
	result := &DiscoveryResult{
		TotalFound:     len(taskMap),
		SuccessTasks:   make([]string, 0),
		FailureTasks:   make([]string, 0),
		FailureReasons: make([]string, 0),
	}

	d.logger.Info("开始从映射注册任务",
		zap.Int("task_count", len(taskMap)),
	)

	for name, task := range taskMap {
		if task == nil {
			result.FailureCount++
			result.FailureTasks = append(result.FailureTasks, name)
			result.FailureReasons = append(result.FailureReasons, "任务实例为nil")
			continue
		}

		// 验证任务名称是否一致
		if task.GetName() != name {
			result.FailureCount++
			result.FailureTasks = append(result.FailureTasks, name)
			result.FailureReasons = append(result.FailureReasons,
				fmt.Sprintf("任务名称不一致: 映射键=%s, 任务名称=%s", name, task.GetName()))
			continue
		}

		if err := d.registry.RegisterTask(task); err != nil {
			result.FailureCount++
			result.FailureTasks = append(result.FailureTasks, name)
			result.FailureReasons = append(result.FailureReasons, err.Error())

			d.logger.Warn("任务注册失败",
				zap.String("task_name", name),
				zap.Error(err),
			)
		} else {
			result.SuccessCount++
			result.SuccessTasks = append(result.SuccessTasks, name)
		}
	}

	d.logger.Info("从映射注册任务完成",
		zap.Int("total", result.TotalFound),
		zap.Int("success", result.SuccessCount),
		zap.Int("failure", result.FailureCount),
	)

	return result
}

// ValidateDiscoveredTasks 验证已发现的任务
func (d *TaskDiscovery) ValidateDiscoveredTasks() error {
	return d.registry.ValidateAllTasks()
}

// GetDiscoveryStats 获取发现统计信息
func (d *TaskDiscovery) GetDiscoveryStats() map[string]interface{} {
	stats := d.registry.GetRegistryStats()

	// 添加发现相关的统计信息
	taskTypes := make(map[string]int)
	for _, task := range d.registry.GetAllTasks() {
		taskType := d.getTaskType(task)
		taskTypes[taskType]++
	}

	stats["task_types"] = taskTypes
	return stats
}

// getTaskType 获取任务类型名称
func (d *TaskDiscovery) getTaskType(task tasks.TaskInterface) string {
	taskType := reflect.TypeOf(task)
	if taskType.Kind() == reflect.Ptr {
		taskType = taskType.Elem()
	}

	// 获取包名和类型名
	pkgPath := taskType.PkgPath()
	typeName := taskType.Name()

	// 简化包路径显示
	if pkgPath != "" {
		parts := strings.Split(pkgPath, "/")
		if len(parts) > 0 {
			pkgName := parts[len(parts)-1]
			return fmt.Sprintf("%s.%s", pkgName, typeName)
		}
	}

	return typeName
}

// PrintDiscoveryReport 打印发现报告
func (d *TaskDiscovery) PrintDiscoveryReport(result *DiscoveryResult) {
	d.logger.Info("=== 任务发现报告 ===")
	d.logger.Info("发现统计",
		zap.Int("总计", result.TotalFound),
		zap.Int("成功", result.SuccessCount),
		zap.Int("失败", result.FailureCount),
	)

	if len(result.SuccessTasks) > 0 {
		d.logger.Info("成功注册的任务",
			zap.Strings("tasks", result.SuccessTasks),
		)
	}

	if len(result.FailureTasks) > 0 {
		d.logger.Warn("注册失败的任务",
			zap.Strings("tasks", result.FailureTasks),
			zap.Strings("reasons", result.FailureReasons),
		)
	}

	// 打印注册中心统计
	stats := d.GetDiscoveryStats()
	d.logger.Info("注册中心状态",
		zap.Any("stats", stats),
	)
}

// GetTaskInfo 获取任务详细信息
func (d *TaskDiscovery) GetTaskInfo(taskName string) (map[string]interface{}, error) {
	task, err := d.registry.GetTask(taskName)
	if err != nil {
		return nil, err
	}

	info := map[string]interface{}{
		"name":             task.GetName(),
		"description":      task.GetDescription(),
		"schedule":         task.GetSchedule(),
		"timeout":          task.GetTimeout(),
		"retry_count":      task.GetRetryCount(),
		"retry_interval":   task.GetRetryInterval(),
		"concurrency_mode": task.GetConcurrencyMode().String(),
		"task_type":        d.getTaskType(task),
	}

	// 如果任务实现了生命周期接口，添加相关信息
	if _, ok := task.(tasks.TaskLifecycleInterface); ok {
		info["has_lifecycle"] = true
	} else {
		info["has_lifecycle"] = false
	}

	return info, nil
}

// GetAllTasksInfo 获取所有任务的详细信息
func (d *TaskDiscovery) GetAllTasksInfo() map[string]interface{} {
	allTasks := d.registry.GetAllTasks()
	tasksInfo := make(map[string]interface{}, len(allTasks))

	for name := range allTasks {
		if info, err := d.GetTaskInfo(name); err == nil {
			tasksInfo[name] = info
		}
	}

	return map[string]interface{}{
		"total_count": len(allTasks),
		"tasks":       tasksInfo,
		"stats":       d.GetDiscoveryStats(),
	}
}

// CheckTaskConflicts 检查任务冲突
func (d *TaskDiscovery) CheckTaskConflicts() []string {
	var conflicts []string
	allTasks := d.registry.GetAllTasks()

	// 检查任务名称冲突（这个在注册时已经检查了，这里主要是为了完整性）
	nameCount := make(map[string]int)
	for name := range allTasks {
		nameCount[name]++
		if nameCount[name] > 1 {
			conflicts = append(conflicts, fmt.Sprintf("任务名称冲突: %s", name))
		}
	}

	// 检查调度时间冲突（相同时间的单例任务可能有问题）
	scheduleGroups := make(map[string][]string)
	for name, task := range allTasks {
		if task.GetConcurrencyMode() == tasks.ConcurrencyModeSingleton {
			schedule := task.GetSchedule()
			scheduleGroups[schedule] = append(scheduleGroups[schedule], name)
		}
	}

	for schedule, taskNames := range scheduleGroups {
		if len(taskNames) > 1 {
			conflicts = append(conflicts,
				fmt.Sprintf("单例任务调度时间冲突 [%s]: %v", schedule, taskNames))
		}
	}

	return conflicts
}
