package ipUtil

import (
	"fmt"

	"github.com/xiaoqidun/qqwry"
)

func InitIPUtil(path string) error {
	// 从文件加载IP数据库
	if err := qqwry.LoadFile(path); err != nil {
		return fmt.Errorf("加载IP数据库失败: %w", err)
	}
	return nil
}

func GetIpLocation(ip string) (string, error) {

	// 从内存或缓存查询IP
	location, err := qqwry.QueryIP(ip)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s|%s|%s|%s|%s", location.Country, location.Province, location.City, location.District, location.ISP), nil
}
