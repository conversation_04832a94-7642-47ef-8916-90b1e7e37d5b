package risk

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/xuri/excelize/v2"

	"fincore/model"
	logutil "fincore/utils/log"
)

// ExcelTestData Excel测试数据结构
type ExcelTestData struct {
	Name           string `json:"name"`           // 姓名
	Mobile         string `json:"mobile"`         // 电话
	IDCard         string `json:"idCard"`         // 身份证号码
	ExpectedResult string `json:"expectedResult"` // 他方风控结论
	RowIndex       int    `json:"rowIndex"`       // Excel行号
}

// TestResult 测试结果结构
type TestResult struct {
	ExcelTestData
	CustomerID    int64  `json:"customerId"`    // 插入后的用户ID
	ActualResult  int    `json:"actualResult"`  // 实际风控结果
	RiskScore     int    `json:"riskScore"`     // 风险分数
	FailureType   string `json:"failureType"`   // 失败类型
	FailureReason string `json:"failureReason"` // 失败原因
	IsMatch       bool   `json:"isMatch"`       // 是否匹配
	ErrorMessage  string `json:"errorMessage"`  // 错误信息
}

// FailureTypeAnalysis 失败类型分析结果
type FailureTypeAnalysis struct {
	FailureType string  `json:"failureType"`
	Count       int     `json:"count"`
	Percentage  float64 `json:"percentage"`
}

// ExcelRiskTester Excel风险评估测试器
type ExcelRiskTester struct {
	riskService    *RiskService
	accountService *model.BusinessAppAccountService
	logger         *logutil.Logger
}

// NewExcelRiskTester 创建Excel风险评估测试器
func NewExcelRiskTester() *ExcelRiskTester {
	// 初始化服务
	riskService := NewRiskService(context.Background())

	accountService := model.NewBusinessAppAccountService()

	return &ExcelRiskTester{
		riskService:    riskService,
		accountService: accountService,
	}
}

// ReadExcelData 读取Excel数据
func (e *ExcelRiskTester) ReadExcelData(filePath string) ([]ExcelTestData, error) {
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开Excel文件失败: %w", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			log.Printf("关闭Excel文件失败: %v", err)
		}
	}()

	// 获取第一个工作表
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("读取Excel行数据失败: %w", err)
	}

	var testData []ExcelTestData
	// 跳过标题行，从第2行开始读取
	for i, row := range rows {
		if i == 0 {
			continue // 跳过标题行
		}

		// 确保行有足够的列
		if len(row) < 4 {
			continue
		}

		// 跳过空行
		if strings.TrimSpace(row[0]) == "" {
			continue
		}

		testData = append(testData, ExcelTestData{
			Name:           strings.TrimSpace(row[0]), // 假设第1列是姓名
			Mobile:         strings.TrimSpace(row[1]), // 假设第2列是电话
			IDCard:         strings.TrimSpace(row[2]), // 假设第3列是身份证号码
			ExpectedResult: strings.TrimSpace(row[4]), // 假设第4列是他方风控结论
			RowIndex:       i + 1,
		})
	}

	return testData, nil
}

// InsertTestUser 插入测试用户到数据库
func (e *ExcelRiskTester) InsertTestUser(data ExcelTestData) (int64, error) {
	// 创建用户数据map
	businessAppAccountData := map[string]interface{}{
		"name":       data.Name,
		"mobile":     data.Mobile,
		"idCard":     data.IDCard,
		"createTime": time.Now().Unix(),
		"updateTime": time.Now().Unix(),
		"status":     1, // 正常状态
		"channelId":  1, // 默认渠道
	}

	// 插入到数据库
	insertID, err := model.DB().Table("business_app_account").InsertGetId(businessAppAccountData)
	if err != nil {
		return 0, fmt.Errorf("插入用户失败: %w", err)
	}

	return insertID, nil
}

// EvaluateRisk 调用风险评估
func (e *ExcelRiskTester) EvaluateRisk(ctx context.Context, customerID int64) (*RiskEvaluationResponse, error) {
	return e.riskService.EvaluateRisk(ctx, model.DB(), customerID)
}

// CompareResults 比较结果
func (e *ExcelRiskTester) CompareResults(expected string, actual int) bool {
	// 将期望结果转换为数字进行比较
	// 假设Excel中的结果格式："通过"=0, "审核"=1, "拒绝"=2
	expectedInt := e.parseExpectedResult(expected)
	return expectedInt == actual
}

// parseExpectedResult 解析期望结果
func (e *ExcelRiskTester) parseExpectedResult(expected string) int {
	expected = strings.TrimSpace(expected)
	switch expected {
	case "通过", "AUTO_PASS", "APPROVED", "0":
		return 0
	case "审核", "REVIEW", "1":
		return 1
	case "拒绝", "AUTO_REJECT", "REJECTED", "2":
		return 2
	case "CALLRISKMODELFAILED", "3":
		return 3
	default:
		// 尝试直接解析为数字
		if num, err := strconv.Atoi(expected); err == nil {
			return num
		}
		return -1 // 未知结果
	}
}

// formatActualResult 将实际结果数字转换为常量名称
func (e *ExcelRiskTester) formatActualResult(result int) string {
	switch result {
	case 0:
		return "APPROVED"
	case 1:
		return "REVIEW"
	case 2:
		return "REJECTED"
	case 3:
		return "CALLRISKMODELFAILED"
	default:
		return fmt.Sprintf("UNKNOWN(%d)", result)
	}
}

// RunTest 运行完整测试
func (e *ExcelRiskTester) RunTest(ctx context.Context, filePath string) ([]TestResult, error) {
	// 1. 读取Excel数据
	testData, err := e.ReadExcelData(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取Excel数据失败: %w", err)
	}

	e.logger.WithFields(
		logutil.Int("count", len(testData)),
		logutil.String("action", "read_excel_data_complete"),
	).Info("读取Excel数据完成")

	var results []TestResult

	// 2. 逐个处理测试数据
	for i, data := range testData {
		result := TestResult{
			ExcelTestData: data,
		}
		e.logger.WithFields(
			logutil.Int("index", i+1),
			logutil.String("name", data.Name),
			logutil.String("mobile", data.Mobile),
			logutil.String("action", "process_test_data"),
		).Info("处理测试数据")

		// 3. 插入用户
		customerID, err := e.InsertTestUser(data)
		if err != nil {
			result.ErrorMessage = fmt.Sprintf("插入用户失败: %v", err)
			results = append(results, result)
			continue
		}
		result.CustomerID = customerID

		// 4. 调用风险评估
		evalResp, err := e.EvaluateRisk(ctx, customerID)
		if err != nil {
			result.ErrorMessage = fmt.Sprintf("风险评估失败: %v", err)
			results = append(results, result)
			continue
		}

		// 5. 检查响应是否为空
		if evalResp == nil {
			result.ErrorMessage = "风险评估响应为空"
			results = append(results, result)
			continue
		}

		// 6. 填充结果
		result.ActualResult = evalResp.RiskResult
		result.RiskScore = evalResp.RiskScore
		if evalResp.FailureType != nil {
			result.FailureType = *evalResp.FailureType
		}
		if evalResp.FailureReason != nil {
			result.FailureReason = *evalResp.FailureReason
		}
		result.IsMatch = e.CompareResults(data.ExpectedResult, evalResp.RiskResult)

		results = append(results, result)

		//添加延迟避免请求过快
		time.Sleep(100 * time.Millisecond)
	}

	return results, nil
}

// AnalyzeFailureTypes 分析失败类型分布
func (e *ExcelRiskTester) AnalyzeFailureTypes(results []TestResult) []FailureTypeAnalysis {
	failureTypeCount := make(map[string]int)
	totalCount := len(results)

	// 统计失败类型
	for _, result := range results {
		if result.FailureType != "" {
			failureTypeCount[result.FailureType]++
		} else if result.ActualResult == 2 { // 拒绝但没有失败类型
			failureTypeCount["unknown"]++
		}
	}

	// 转换为分析结果
	var analysis []FailureTypeAnalysis
	for failureType, count := range failureTypeCount {
		percentage := float64(count) / float64(totalCount) * 100
		analysis = append(analysis, FailureTypeAnalysis{
			FailureType: failureType,
			Count:       count,
			Percentage:  percentage,
		})
	}

	return analysis
}

// SaveResultsToExcel 保存结果到Excel
func (e *ExcelRiskTester) SaveResultsToExcel(results []TestResult, analysis []FailureTypeAnalysis, outputPath string) error {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			log.Printf("关闭Excel文件失败: %v", err)
		}
	}()

	// 创建测试结果工作表
	sheetName := "测试结果"
	f.SetSheetName("Sheet1", sheetName)

	// 设置标题行
	headers := []string{
		"行号", "姓名", "电话", "身份证号码", "期望结果", "用户ID",
		"实际结果", "风险分数", "失败类型", "失败原因", "是否匹配", "错误信息",
	}

	for i, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// 填充数据
	for i, result := range results {
		row := i + 2
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), result.RowIndex)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), result.Name)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), result.Mobile)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), result.IDCard)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), result.ExpectedResult)
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), result.CustomerID)
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), e.formatActualResult(result.ActualResult))
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", row), result.RiskScore)
		f.SetCellValue(sheetName, fmt.Sprintf("I%d", row), result.FailureType)
		f.SetCellValue(sheetName, fmt.Sprintf("J%d", row), result.FailureReason)
		f.SetCellValue(sheetName, fmt.Sprintf("K%d", row), result.IsMatch)
		f.SetCellValue(sheetName, fmt.Sprintf("L%d", row), result.ErrorMessage)
	}

	// 创建失败类型分析工作表
	analysisSheet := "失败类型分析"
	f.NewSheet(analysisSheet)

	// 设置分析标题行
	analysisHeaders := []string{"失败类型", "数量", "百分比(%)"}
	for i, header := range analysisHeaders {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		f.SetCellValue(analysisSheet, cell, header)
	}

	// 填充分析数据
	for i, item := range analysis {
		row := i + 2
		f.SetCellValue(analysisSheet, fmt.Sprintf("A%d", row), item.FailureType)
		f.SetCellValue(analysisSheet, fmt.Sprintf("B%d", row), item.Count)
		f.SetCellValue(analysisSheet, fmt.Sprintf("C%d", row), fmt.Sprintf("%.2f", item.Percentage))
	}

	// 保存文件
	if err := f.SaveAs(outputPath); err != nil {
		return fmt.Errorf("保存Excel文件失败: %w", err)
	}

	return nil
}

// TestNewExcelRiskTester 测试创建Excel风险评估测试器
func TestNewExcelRiskTester(t *testing.T) {
	tester := NewExcelRiskTester()
	if tester == nil {
		t.Fatal("创建Excel风险评估测试器失败")
	}
	t.Log("Excel风险评估测试器创建成功")
}

// PrintSummary 打印测试摘要
func (e *ExcelRiskTester) PrintSummary(results []TestResult, analysis []FailureTypeAnalysis) {
	totalCount := len(results)
	matchCount := 0
	errorCount := 0

	for _, result := range results {
		if result.ErrorMessage != "" {
			errorCount++
		} else if result.IsMatch {
			matchCount++
		}
	}

	accuracy := float64(matchCount) / float64(totalCount-errorCount) * 100

	fmt.Printf("\n=== 测试结果摘要 ===\n")
	fmt.Printf("总测试数量: %d\n", totalCount)
	fmt.Printf("成功测试数量: %d\n", totalCount-errorCount)
	fmt.Printf("错误数量: %d\n", errorCount)
	fmt.Printf("匹配数量: %d\n", matchCount)
	fmt.Printf("准确率: %.2f%%\n", accuracy)

	fmt.Printf("\n=== 失败类型分布 ===\n")
	for _, item := range analysis {
		fmt.Printf("%s: %d次 (%.2f%%)\n", item.FailureType, item.Count, item.Percentage)
	}
}
