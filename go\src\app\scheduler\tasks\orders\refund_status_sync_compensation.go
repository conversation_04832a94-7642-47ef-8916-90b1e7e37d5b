package orders

import (
	"context"
	"fmt"
	"time"

	"fincore/app/business/payment"
	"fincore/app/scheduler/tasks"
	"fincore/model"
	"fincore/utils/convert"
	"fincore/utils/gform"
	"fincore/utils/log"
)

// RefundStatusSyncCompensationTask 退款状态同步补偿任务
// 用于处理已提交退款流水的状态同步场景
type RefundStatusSyncCompensationTask struct {
	*tasks.BaseTask
	logger *log.Logger
	ctx    context.Context
}

// NewRefundStatusSyncCompensationTask 创建退款状态同步补偿任务
func NewRefundStatusSyncCompensationTask() *RefundStatusSyncCompensationTask {
	baseTask := tasks.NewBaseTask(
		"refund-status-sync-compensation",
		"退款状态同步补偿任务 - 处理已提交退款流水的状态同步",
		"0 */1 * * * *", // 每1分钟执行一次
		5*time.Minute,   // 超时时间5分钟
	)

	// 设置为单例模式，避免重复执行
	baseTask.SetConcurrencyMode(tasks.ConcurrencyModeSingleton)
	// 设置重试次数和间隔
	baseTask.SetRetryCount(3).SetRetryInterval(30 * time.Second)

	logger := log.RegisterModule("refund_sync_task", "退款状态同步任务")
	ctx := context.Background()
	return &RefundStatusSyncCompensationTask{
		BaseTask: baseTask,
		logger:   logger,
		ctx:      ctx,
	}
}

// Execute 执行退款状态同步补偿任务
func (t *RefundStatusSyncCompensationTask) Execute(ctx context.Context) error {

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "start_execution"),
	).Info("开始执行退款状态同步补偿任务")

	startTime := time.Now()
	var processedCount, successCount, failureCount int

	// 查询符合条件的退款流水
	refundTransactions, err := t.getPendingRefundTransactions()
	if err != nil {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "query_transactions"),
			log.String("error", err.Error()),
		).Error("查询待同步状态的退款流水失败")
		return fmt.Errorf("查询待同步状态的退款流水失败: %v", err)
	}

	if len(refundTransactions) == 0 {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "no_transactions_found"),
		).Info("未找到需要同步状态的退款流水")
		return nil
	}

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "found_transactions"),
		log.Int("transaction_count", len(refundTransactions)),
	).Info("找到需要同步状态的退款流水")

	// 遍历处理每条退款流水
	for _, transactionData := range refundTransactions {
		processedCount++

		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			t.logger.WithFields(
				log.String("task", t.GetName()),
				log.String("operation", "context_cancelled"),
				log.Int("processed_count", processedCount-1),
			).Warn("任务被取消，停止处理")
			return ctx.Err()
		default:
		}

		transactionID := convert.GetIntFromMap(transactionData, "id", 0)
		transactionNo := convert.GetStringFromMap(transactionData, "transaction_no")
		orderNo := convert.GetStringFromMap(transactionData, "order_no")
		amount := convert.GetFloatFromMap(transactionData, "amount", 0)

		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("transaction_no", transactionNo),
			log.String("order_no", orderNo),
			log.Int("transaction_id", transactionID),
			log.Float64("amount", amount),
			log.String("operation", "sync_status"),
		).Info("开始同步退款流水状态")

		// 调用退款状态查询方法
		err := t.syncRefundTransactionStatus(transactionID, transactionNo, orderNo)
		if err != nil {
			failureCount++
			t.logger.WithFields(
				log.String("task", t.GetName()),
				log.String("transaction_no", transactionNo),
				log.String("order_no", orderNo),
				log.Int("transaction_id", transactionID),
				log.String("operation", "sync_status_failed"),
				log.String("error", err.Error()),
			).Error("退款流水状态同步失败")
			continue
		}

		successCount++
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("transaction_no", transactionNo),
			log.String("order_no", orderNo),
			log.Int("transaction_id", transactionID),
			log.String("operation", "sync_status_success"),
		).Info("退款流水状态同步成功")
	}

	// 记录执行统计
	duration := time.Since(startTime)
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "execution_completed"),
		log.Int("processed_count", processedCount),
		log.Int("success_count", successCount),
		log.Int("failure_count", failureCount),
		log.String("duration", duration.String()),
	).Info("退款状态同步补偿任务执行完成")

	return nil
}

// getPendingRefundTransactions 获取待同步状态的退款流水
func (t *RefundStatusSyncCompensationTask) getPendingRefundTransactions() ([]gform.Data, error) {
	// 查询条件：
	// 1. 流水类型为退款 (type = 'REFUND')
	// 2. 流水状态为已提交 (status = 1)
	// 3. 按创建时间升序排序，优先处理较早的记录
	// 4. 限制查询数量，避免一次处理过多数据
	query := `
		SELECT 
			id,
			transaction_no,
			order_no,
			amount,
			created_at
		FROM business_payment_transactions 
		WHERE type = ?
		  AND status = ?
		  AND deleted_at IS NULL
		ORDER BY id ASC 
		LIMIT 100
	`

	result, err := model.DB(model.WithContext(t.ctx)).Query(query,
		model.TransactionTypeRefund,
		model.TransactionStatusSubmitted,
	)
	if err != nil {
		return nil, fmt.Errorf("查询数据库失败: %v", err)
	}

	return result, nil
}

// syncRefundTransactionStatus 同步退款流水状态
// 调用 PaymentService 的 QueryRefundStatus 方法
func (t *RefundStatusSyncCompensationTask) syncRefundTransactionStatus(transactionID int, transactionNo, orderNo string) error {
	// 调用 PaymentService 的 QueryRefundStatus 方法
	paymentService := payment.NewPaymentServiceWithOptions(
		t.ctx,
		payment.WithLogger(t.logger),
		payment.WithRepository(),
		payment.WithTransactionModel(),
		payment.WithOrderModel(),
		payment.WithBillModel(),
	)

	err := paymentService.QueryRefundStatus(transactionID)
	if err != nil {
		return fmt.Errorf("调用退款状态查询失败: %v", err)
	}

	t.logger.WithFields(
		log.String("transaction_no", transactionNo),
		log.String("order_no", orderNo),
		log.Int("transaction_id", transactionID),
		log.String("operation", "query_refund_status_success"),
	).Info("退款状态查询成功")

	return nil
}

// OnStart 任务开始执行前的回调
func (t *RefundStatusSyncCompensationTask) OnStart(ctx context.Context) error {

	// task_ 开头，记录整个周期所有 sql 执行日志
	requestID := "task_" + t.GetName() + "_" + time.Now().Format("20060102150405")
	t.ctx = context.WithValue(t.ctx, log.RequestIDKey, requestID)
	t.logger = t.logger.WithRequestID(requestID)

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_starting"),
	).Info("退款状态同步补偿任务即将开始")
	return nil
}

// OnSuccess 任务执行成功后的回调
func (t *RefundStatusSyncCompensationTask) OnSuccess(ctx context.Context) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_success"),
	).Info("退款状态同步补偿任务执行成功")
	return nil
}

// OnError 任务执行失败后的回调
func (t *RefundStatusSyncCompensationTask) OnError(ctx context.Context, err error) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_error"),
		log.String("error", err.Error()),
	).Error("退款状态同步补偿任务执行失败")
	return nil
}
