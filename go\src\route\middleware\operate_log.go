package middleware

import (
	"bytes"
	"encoding/json"
	"fincore/model"
	"fincore/utils/gf"
	"fincore/utils/ipUtil"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type Operate struct {
	OperateName string                                                   `json:"name"`  // 操作名称
	Table       string                                                   `json:"table"` // 表面：用户user_operate_log 管理员：admin_operate_log
	OnSuccess   func(c *gin.Context, resp map[string]interface{}) string `json:"-"`     // 操作成功回调
	OnError     func(c *gin.Context, resp map[string]interface{}) string `json:"-"`     // 操作失败回调
}

type OperateLogConfig struct {
	GET  map[string]Operate `json:"GET"`
	POST map[string]Operate `json:"POST"`
}

// 默认成功详情函数
func defaultSuccessDetail(c *gin.Context, resp map[string]interface{}) string {
	return "成功"
}

// 默认失败详情函数
func defaultErrorDetail(c *gin.Context, resp map[string]interface{}) string {
	return fmt.Sprintf("失败：%s", resp["message"].(string))
}

var config OperateLogConfig = OperateLogConfig{
	GET: map[string]Operate{
		"/api/v1/example": {
			OperateName: "查询",
			Table:       "admin_operate_log",
			OnSuccess: func(c *gin.Context, resp map[string]interface{}) string {
				fmt.Printf("获取查询参数： %v\n", c.Request.URL.Query())
				reqBody, _ := c.Get("request_body")
				fmt.Printf("获取请求体参数： %v \n", reqBody)
				fmt.Printf("获取响应数据: %v\n", resp)
				return "操作成功"
			},
			OnError: func(c *gin.Context, resp map[string]interface{}) string {
				fmt.Printf("获取查询参数： %v\n", c.Request.URL.Query())
				reqBody, _ := c.Get("request_body")
				fmt.Printf("获取请求体参数： %v \n", reqBody)
				fmt.Printf("获取响应数据: %v\n", resp)
				return "操作失败"
			},
		},
	},
	POST: map[string]Operate{
		"/business/user/login": {
			OperateName: "登录",
			Table:       "admin_operate_log",
			OnSuccess:   defaultSuccessDetail,
			OnError:     defaultErrorDetail,
		},
		"/uniapp/user/postBySms": {
			OperateName: "短信登录",
			Table:       "user_operate_log",
			OnSuccess:   defaultSuccessDetail,
			OnError:     defaultErrorDetail,
		},
	},
}

func getOperate(method, path string) Operate {
	switch method {
	case "GET":
		return config.GET[path]
	case "POST":
		return config.POST[path]
	}
	return Operate{}
}

// 自定义ResponseWriter捕获响应
type ResponseRecorder struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *ResponseRecorder) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

func OperateLogHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		operate := getOperate(c.Request.Method, c.Request.URL.Path)
		if operate.OperateName == "" {
			c.Next()
			return
		}
		// 创建响应记录器
		recorder := &ResponseRecorder{
			ResponseWriter: c.Writer,
			body:           bytes.NewBufferString(""),
		}
		c.Writer = recorder

		// 读取并缓存请求体
		var reqBody []byte
		if c.Request.Body != nil {
			// 读取原始请求体
			reqBody, _ = io.ReadAll(c.Request.Body)
			// 重置请求体，使后续处理器可再次读取
			c.Request.Body = io.NopCloser(bytes.NewBuffer(reqBody))
		}

		// 解析请求体JSON格式
		var reqData map[string]interface{}
		if len(reqBody) > 0 && c.ContentType() == "application/json" {
			if err := json.Unmarshal(reqBody, &reqData); err == nil {
				// 存储到Context
				c.Set("request_body", reqData)
			}
		}

		c.Next() // 执行后续处理

		// 解析响应内容
		var resp map[string]interface{}
		// 不处理非正常返回值
		if err := json.Unmarshal(recorder.body.Bytes(), &resp); err != nil {
			return
		}
		ip := gf.GetIp(c)
		realIp := strings.Split(ip, ", ")[0]
		region, err := ipUtil.GetIpLocation(realIp)
		if err != nil {
			region = "未知"
		}
		// 非真实用户不记录
		user, ok := c.Get("user")
		if !ok {
			return
		}
		userInfo, ok := user.(*UserClaims)
		if !ok {
			return
		}
		opLog := map[string]interface{}{
			"ip":           ip,
			"region":       region,
			"uid":          userInfo.ID,
			"operate_name": operate.OperateName,
			"created_at":   time.Now(),
		}

		if resp["code"] == float64(0) { // JSON解析数字默认为float64
			opLog["result"] = "成功"
			opLog["detail"] = operate.OnSuccess(c, resp)
		} else {
			opLog["detail"] = operate.OnError(c, resp)
			opLog["result"] = "失败"
		}

		_, err = model.DB().Table(operate.Table).Data(opLog).Insert()
		if err != nil {
			fmt.Printf("插入操作日志失败:%v", err)
		}
	}
}
