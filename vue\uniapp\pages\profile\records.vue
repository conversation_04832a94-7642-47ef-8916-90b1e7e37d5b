<template>
	<view class="records">
		<!-- <view class="records-time">
			2025
		</view> -->
		<view class="records-list">
			<view class="records-item" v-for="item in list" :key="item.order_id">
				<view class="records-item-info">
					<view class="records-item-money">
						￥{{ item.total_repayable_amount }}
					</view>
					<view class="records-item-time">
						借款时间 {{ item.created_at }}
					</view>
				</view>
				<view class="records-item-status">
					{{ item.status_text }}
				</view>
			</view>
			<no-data v-if="list.length <= 0"/>
		</view>
	</view>
</template>

<script setup>
	import { ref } from 'vue';
	import noData from '../components/no-data/index.vue';
import userApi from '@/api/user.js';
	import {
		onShow,
		onLoad
	} from '@dcloudio/uni-app';
	const list = ref([]);
	onLoad(async () => {
		userApi.getUserOrderHistory().then(res => {
			if(res.code == 0) {
				list.value = res.data.orders;
			}
		})
	})
	
</script>

<style lang="scss" scoped>
	page{
		background-color: #eff2f7;
	}
	.records{
		.records-time{
			padding: 30rpx;
			color: #666;
			font-size: 26rpx;
		}
		.records-list{
			padding: 20rpx 30rpx;
			.records-item{
				background-color: #fff;
				border-radius: 15rpx;
				padding: 30rpx;
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;
				.records-item-info{
					flex: 1;
					.records-item-money{
						color: #000;
						font-size: 34rpx;
						font-weight: 500;
					}
					.records-item-time{
						font-size: 24rpx;
						color: #666;
						margin-top: 10rpx;
					}
				}
				.records-item-status{
					color: #cd3e3e;
					font-size: 26rpx;
				}
			}
		}
	}
</style>
