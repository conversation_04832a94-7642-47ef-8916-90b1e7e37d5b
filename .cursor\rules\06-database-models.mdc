---
description: go/*
alwaysApply: false
---
# 数据库模型

## 模型定义

FinCore 使用 Go 结构体来定义数据模型，模型文件位于 [model/](mdc:src/model) 目录。

1. **模型结构**
   - 每个数据表对应一个结构体
   - 使用结构体标签定义字段数据库映射
   - 包含基本的 CRUD 方法

2. **命名规范**
   - 模型结构体名使用单数形式，大驼峰命名，例如 `User`、`Product`
   - 表名通常使用复数形式，例如 `users`、`products`
   - 字段名使用小驼峰或下划线分隔，保持一致

3. **字段定义**
   - 每个字段需要添加数据库标签，例如 `json:"id" db:"id"`
   - 必要字段：主键、创建时间、更新时间
   - 使用适当的数据类型，考虑性能和存储需求

## 数据库操作

1. **查询操作**
   - 使用 ORM 或原生 SQL 查询
   - 复杂查询推荐使用原生 SQL
   - 结合使用事务确保数据一致性

2. **关联关系**
   - 一对一、一对多、多对多关系通过外键实现
   - 使用结构体嵌套表示关联关系
   - 视情况使用预加载或延迟加载

3. **数据验证**
   - 数据入库前进行验证
   - 使用验证库或自定义验证函数
   - 验证规则应该定义在模型或专门的验证文件中

## 示例模型

```go
type User struct {
    ID        uint      `json:"id" db:"id"`
    Username  string    `json:"username" db:"username"`
    Password  string    `json:"password" db:"password"`
    Email     string    `json:"email" db:"email"`
    CreatedAt time.Time `json:"created_at" db:"created_at"`
    UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}
```

## 数据库迁移

1. **迁移管理**
   - 使用迁移工具管理数据库结构变更
   - 每次修改都创建新的迁移文件
   - 迁移文件包含 Up 和 Down 两种操作

2. **迁移操作**
   - 创建表：定义表结构、字段、索引和外键
   - 修改表：添加、修改或删除字段
   - 创建索引：优化查询性能

遵循以上规范，确保数据库模型的一致性、可维护性和性能。
