// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameBusinessAccount = "business_account"

// BusinessAccount 用户端-用户信息
type BusinessAccount struct {
	ID            uint32 `gorm:"column:id;type:int unsigned;primaryKey;autoIncrement:true" json:"id"`
	UID           int32  `gorm:"column:uid;type:int;not null;comment:添加用户" json:"uid"`                                         // 添加用户
	AccountID     int32  `gorm:"column:accountID;type:int;not null;comment:账号id" json:"accountID"`                             // 账号id
	BusinessID    int32  `gorm:"column:businessID;type:int;not null;comment:业务主账号id" json:"businessID"`                        // 业务主账号id
	DeptID        int32  `gorm:"column:dept_id;type:int;not null;comment:部门id" json:"dept_id"`                                 // 部门id
	Username      string `gorm:"column:username;type:varchar(30);not null;comment:用户账号" json:"username"`                       // 用户账号
	Password      string `gorm:"column:password;type:varchar(80);not null;comment:密码" json:"password"`                         // 密码
	Salt          string `gorm:"column:salt;type:varchar(30);not null;comment:密码盐" json:"salt"`                                // 密码盐
	Name          string `gorm:"column:name;type:varchar(50);not null;default:0;comment:姓名" json:"name"`                       // 姓名
	Nickname      string `gorm:"column:nickname;type:varchar(50);not null;comment:昵称" json:"nickname"`                         // 昵称
	Avatar        string `gorm:"column:avatar;type:varchar(145);not null;comment:头像" json:"avatar"`                            // 头像
	Tel           string `gorm:"column:tel;type:varchar(30);not null;comment:备用电话用户自己填写" json:"tel"`                           // 备用电话用户自己填写
	Mobile        string `gorm:"column:mobile;type:varchar(30);not null;comment:手机号码" json:"mobile"`                           // 手机号码
	Email         string `gorm:"column:email;type:varchar(50);not null;comment:邮箱" json:"email"`                               // 邮箱
	LastLoginIP   string `gorm:"column:lastLoginIp;type:varchar(30);not null;comment:最后登录IP" json:"lastLoginIp"`               // 最后登录IP
	LastLoginTime int32  `gorm:"column:lastLoginTime;type:int;not null;comment:最后登录时间" json:"lastLoginTime"`                   // 最后登录时间
	Status        bool   `gorm:"column:status;type:tinyint(1);not null;comment:状态0=正常，1=禁用" json:"status"`                     // 状态0=正常，1=禁用
	Validtime     int32  `gorm:"column:validtime;type:int;not null;comment:账号有效时间0=无限" json:"validtime"`                       // 账号有效时间0=无限
	Createtime    int32  `gorm:"column:createtime;type:int;not null;comment:创建时间" json:"createtime"`                           // 创建时间
	Updatetime    int32  `gorm:"column:updatetime;type:int;not null;comment:修改时间" json:"updatetime"`                           // 修改时间
	Address       string `gorm:"column:address;type:varchar(255);not null;comment:地址" json:"address"`                          // 地址
	City          string `gorm:"column:city;type:varchar(100);not null;comment:城市" json:"city"`                                // 城市
	Remark        string `gorm:"column:remark;type:varchar(255);not null;comment:描述" json:"remark"`                            // 描述
	Company       string `gorm:"column:company;type:varchar(100);not null;comment:公司名称" json:"company"`                        // 公司名称
	Province      string `gorm:"column:province;type:varchar(30);not null;comment:省份" json:"province"`                         // 省份
	Area          string `gorm:"column:area;type:varchar(50);not null;comment:地区" json:"area"`                                 // 地区
	FileSize      uint32 `gorm:"column:fileSize;type:int unsigned;not null;default:**********;comment:附件存储空间" json:"fileSize"` // 附件存储空间
	Loginstatus   bool   `gorm:"column:loginstatus;type:tinyint(1);not null;comment:是否登录 0=未登录，1=登录" json:"loginstatus"`       // 是否登录 0=未登录，1=登录
}

// TableName BusinessAccount's table name
func (*BusinessAccount) TableName() string {
	return TableNameBusinessAccount
}
