package log

import (
	"time"

	"go.uber.org/zap"
)

// Field 日志字段类型
type Field = zap.Field

// 常用字段构造函数

// String 字符串字段
func String(key, val string) Field {
	return zap.String(key, val)
}

// Int 整数字段
func Int(key string, val int) Field {
	return zap.Int(key, val)
}

// Int64 64位整数字段
func Int64(key string, val int64) Field {
	return zap.Int64(key, val)
}

// Uint64 64位无符号整数字段
func Uint64(key string, val uint64) Field {
	return zap.Uint64(key, val)
}

// uint类型
func Uint(key string, val uint) Field {
	return zap.Uint(key, val)
}

// Float64 浮点数字段
func Float64(key string, val float64) Field {
	return zap.Float64(key, val)
}

// Bool 布尔字段
func Bool(key string, val bool) Field {
	return zap.Bool(key, val)
}

// Time 时间字段
func Time(key string, val time.Time) Field {
	return zap.Time(key, val)
}

// Duration 持续时间字段
func Duration(key string, val time.Duration) Field {
	return zap.Duration(key, val)
}

// ErrorField 错误字段
func ErrorField(err error) Field {
	return zap.Error(err)
}

// Any 任意类型字段
func Any(key string, val interface{}) Field {
	return zap.Any(key, val)
}

// 业务相关的常用字段

// RequestID 请求ID字段
func RequestID(id string) Field {
	return String("request_id", id)
}

// UserID 用户ID字段
func UserID(id interface{}) Field {
	return Any("user_id", id)
}

// Module 模块字段
func Module(module string) Field {
	return String("module", module)
}

// Action 操作字段
func Action(action string) Field {
	return String("action", action)
}

// IP IP地址字段
func IP(ip string) Field {
	return String("ip", ip)
}

// UserAgent 用户代理字段
func UserAgent(ua string) Field {
	return String("user_agent", ua)
}

// Method HTTP方法字段
func Method(method string) Field {
	return String("method", method)
}

// URL URL字段
func URL(url string) Field {
	return String("url", url)
}

// StatusCode 状态码字段
func StatusCode(code int) Field {
	return Int("status_code", code)
}

// ResponseTime 响应时间字段
func ResponseTime(duration time.Duration) Field {
	return Duration("response_time", duration)
}

// OrderID 订单ID字段
func OrderID(id interface{}) Field {
	return Any("order_id", id)
}

// Amount 金额字段
func Amount(amount float64) Field {
	return Float64("amount", amount)
}

// Currency 货币字段
func Currency(currency string) Field {
	return String("currency", currency)
}

// TraceID 链路追踪ID字段
func TraceID(id string) Field {
	return String("trace_id", id)
}

// SpanID 跨度ID字段
func SpanID(id string) Field {
	return String("span_id", id)
}

// Database 数据库字段
func Database(db string) Field {
	return String("database", db)
}

// Table 表名字段
func Table(table string) Field {
	return String("table", table)
}

// SQLField SQL语句字段
func SQLField(sql string) Field {
	return String("sql", sql)
}

// Rows 影响行数字段
func Rows(rows int64) Field {
	return Int64("rows", rows)
}
