package thirdparty

import (
	"context"
	"fmt"
	"log"
	"time"
)

// SimpleConfig 简单配置实现
type SimpleConfig map[string]interface{}

func (c SimpleConfig) GetString(key string) string {
	if v, ok := c[key].(string); ok {
		return v
	}
	return ""
}

func (c SimpleConfig) GetInt(key string) int {
	if v, ok := c[key].(int); ok {
		return v
	}
	return 0
}

func (c SimpleConfig) GetBool(key string) bool {
	if v, ok := c[key].(bool); ok {
		return v
	}
	return false
}

func (c SimpleConfig) GetDuration(key string) time.Duration {
	if v, ok := c[key].(time.Duration); ok {
		return v
	}
	return 30 * time.Second
}

func (c SimpleConfig) GetStringMap(key string) map[string]string {
	if v, ok := c[key].(map[string]string); ok {
		return v
	}
	return nil
}

func main() {
	ctx := context.Background()

	// 电子签服务配置（完全参考现有实现）
	dianziqianConfig := SimpleConfig{
		"host":        "https://prev.asign.cn/", // 测试环境
		"app_id":      "your_app_id",            // 替换为实际的app_id
		"private_key": "your_private_key",       // 替换为实际的私钥
		"timeout":     30 * time.Second,
	}

	// 创建电子签服务
	dianziqianService, err := NewDianziqianService(dianziqianConfig)
	if err != nil {
		log.Fatalf("创建电子签服务失败: %v", err)
	}

	// 初始化服务
	if err := dianziqianService.Initialize(dianziqianConfig); err != nil {
		log.Fatalf("初始化电子签服务失败: %v", err)
	}

	fmt.Printf("电子签服务 %s (版本: %s) 初始化成功\n",
		dianziqianService.GetName(), dianziqianService.GetVersion())
	fmt.Printf("支持的方法: %v\n", dianziqianService.GetSupportedMethods())

	// 示例：OCR身份证识别
	ocrParams := map[string]interface{}{
		"image":     "",            // 图片URL
		"base64Img": "base64_data", // 图片base64数据
		"fileld":    "",            // 字段
		"side":      "front",       // 正面
	}

	resp, err := dianziqianService.Call(ctx, "OCR", ocrParams)
	if err != nil {
		log.Printf("调用OCR服务失败: %v", err)
	} else {
		fmt.Printf("OCR响应: 成功=%v, 代码=%s, 消息=%s\n",
			resp.Success, resp.Code, resp.Message)
	}

	// 示例：创建个人账户
	createAccountParams := map[string]interface{}{
		"account":    "testAccount001",
		"name":       "测试用户",
		"idCard":     "123456789012345678",
		"idCardType": 1,
		"mobile":     "***********",
	}

	resp, err = dianziqianService.Call(ctx, "CreatePersonalAccount", createAccountParams)
	if err != nil {
		log.Printf("创建个人账户失败: %v", err)
	} else {
		fmt.Printf("创建账户响应: 成功=%v, 代码=%s, 消息=%s\n",
			resp.Success, resp.Code, resp.Message)
	}

	// 短信服务配置
	smsConfig := SimpleConfig{
		"base_url": "https://api.sms.com",
		"username": "your_username",
		"password": "your_password",
		"timeout":  30 * time.Second,
	}

	// 创建短信服务
	smsService, err := NewSMSService(smsConfig)
	if err != nil {
		log.Fatalf("创建短信服务失败: %v", err)
	}

	if err := smsService.Initialize(smsConfig); err != nil {
		log.Fatalf("初始化短信服务失败: %v", err)
	}

	fmt.Printf("\n短信服务 %s (版本: %s) 初始化成功\n",
		smsService.GetName(), smsService.GetVersion())

	// 示例：发送短信
	smsParams := map[string]interface{}{
		"phone":   "***********",
		"message": "您的验证码是：123456，5分钟内有效。",
	}

	resp, err = smsService.Call(ctx, "SendSMS", smsParams)
	if err != nil {
		log.Printf("发送短信失败: %v", err)
	} else {
		fmt.Printf("发送短信响应: 成功=%v, 代码=%s, 消息=%s\n",
			resp.Success, resp.Code, resp.Message)
	}

	// 风控服务配置
	fengkongConfig := SimpleConfig{
		"base_url":   "https://api.fengkong.com",
		"machine_id": "your_machine_id",
		"secret_key": "your_secret_key",
		"algorithm":  "aes",
		"timeout":    30 * time.Second,
	}

	// 创建风控服务
	fengkongService, err := NewFengkongService(fengkongConfig)
	if err != nil {
		log.Fatalf("创建风控服务失败: %v", err)
	}

	if err := fengkongService.Initialize(fengkongConfig); err != nil {
		log.Fatalf("初始化风控服务失败: %v", err)
	}

	fmt.Printf("\n风控服务 %s (版本: %s) 初始化成功\n",
		fengkongService.GetName(), fengkongService.GetVersion())

	// 示例：风险检查
	riskParams := map[string]interface{}{
		"userId":    "user123",
		"operation": "transfer",
		"amount":    10000,
		"ip":        "***********",
		"deviceId":  "device123",
	}

	resp, err = fengkongService.Call(ctx, "RiskCheck", riskParams)
	if err != nil {
		log.Printf("风险检查失败: %v", err)
	} else {
		fmt.Printf("风险检查响应: 成功=%v, 代码=%s, 消息=%s\n",
			resp.Success, resp.Code, resp.Message)
	}

	fmt.Println("\n=== 框架特性演示 ===")
	fmt.Println("✅ 统一的服务接口")
	fmt.Println("✅ 多种认证方式支持（签名、密码、加密）")
	fmt.Println("✅ 统一的错误处理和映射")
	fmt.Println("✅ 重试机制和超时控制")
	fmt.Println("✅ 易于扩展新的第三方服务")
}

// 如何快速接入第四种服务的示例
/*
// 新服务配置
newServiceConfig := SimpleConfig{
    "base_url": "https://api.newservice.com",
    "api_key":  "your_api_key",
    "timeout":  30 * time.Second,
}

// 创建新的认证器（如果需要新的认证方式）
newAuth := auth.NewAPIKeyAuth("your_api_key")

// 创建服务
newService, err := thirdparty.NewYourNewService(newServiceConfig)
if err != nil {
    log.Fatalf("创建新服务失败: %v", err)
}

// 调用服务
resp, err := newService.Call(ctx, "YourMethod", params)
*/
