{"level":"dev.info","ts":"[2025-08-14 11:59:55.349]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b862995c28504e7e0dc65","sql":"SELECT \n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') THEN pt.amount ELSE 0 END) as total_income,\n\t\tSUM(CASE WHEN pt.type = 'REFUND' THEN pt.amount ELSE 0 END) as total_refund,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) = rb.due_date THEN pt.amount ELSE 0 END) as due_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) > rb.due_date THEN pt.amount ELSE 0 END) as overdue_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) < rb.due_date THEN pt.amount ELSE 0 END) as early_income,\n\t\tCOUNT(DISTINCT pt.user_id) as total_customers,\n\t\tCOUNT(DISTINCT pt.order_id) as total_orders\n\t FROM business_payment_transactions pt LEFT JOIN business_repayment_bills rb ON pt.bill_id = rb.id  LEFT JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type = ? LIMIT 1, [2 REFUND]","duration":"21.321ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-14 11:59:55.378]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b862995c28504e7e0dc65","sql":"SELECT count(*) as count FROM business_payment_transactions pt LEFT JOIN business_repayment_bills rb ON pt.bill_id = rb.id  LEFT JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type = ? LIMIT 1, [2 REFUND]","duration":"50.6379ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-08-14 11:59:55.415]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b862995c28504e7e0dc65","sql":"SELECT \n\t\t\tpt.id,\n\t\t\tpt.transaction_no,\n\t\t\tlo.order_no,\n\t\t\taa.name as user_name,\n\t\t\taa.mobile,\n\t\t\tpt.type,\n\t\t\tpt.withhold_type,\n\t\t\tpt.offline_payment_channel_detail,\n\t\t\tpt.amount,\n\t\t\trb.period_number,\n\t\t\tpt.completed_at,\n\t\t\trb.due_date\n\t\t FROM business_payment_transactions pt LEFT JOIN business_repayment_bills rb ON pt.bill_id = rb.id  LEFT JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type = ? ORDER BY pt.id DESC LIMIT 10, [2 REFUND]","duration":"37.338ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-14 14:20:43.727]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b8dd84a1abbc453f0def6","sql":"SELECT \n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') THEN pt.amount ELSE 0 END) as total_income,\n\t\tSUM(CASE WHEN pt.type = 'REFUND' THEN pt.amount ELSE 0 END) as total_refund,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) = rb.due_date THEN pt.amount ELSE 0 END) as due_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) > rb.due_date THEN pt.amount ELSE 0 END) as overdue_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) < rb.due_date THEN pt.amount ELSE 0 END) as early_income,\n\t\tCOUNT(DISTINCT pt.user_id) as total_customers,\n\t\tCOUNT(DISTINCT pt.order_id) as total_orders\n\t FROM business_payment_transactions pt INNER JOIN business_repayment_bills rb ON pt.bill_id = rb.id  INNER JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type IN (?,?,?,?,?) LIMIT 1, [2 REPAYMENT WITHHOLD MANUAL_WITHHOLD PARTIAL_OFFLINE_REPAYMENT REFUND]","duration":"11.7882ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 14:20:43.752]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b8dd84a1abbc453f0def6","sql":"SELECT count(*) as count FROM business_payment_transactions pt INNER JOIN business_repayment_bills rb ON pt.bill_id = rb.id  INNER JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type IN (?,?,?,?,?) LIMIT 1, [2 REPAYMENT WITHHOLD MANUAL_WITHHOLD PARTIAL_OFFLINE_REPAYMENT REFUND]","duration":"36.7323ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-14 14:20:43.768]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b8dd84a1abbc453f0def6","sql":"SELECT \n\t\t\tpt.id,\n\t\t\tpt.transaction_no,\n\t\t\tlo.order_no,\n\t\t\taa.name as user_name,\n\t\t\taa.mobile,\n\t\t\tpt.type,\n\t\t\tpt.withhold_type,\n\t\t\tpt.offline_payment_channel_detail,\n\t\t\tpt.amount,\n\t\t\trb.period_number,\n\t\t\tpt.completed_at,\n\t\t\trb.due_date\n\t\t FROM business_payment_transactions pt INNER JOIN business_repayment_bills rb ON pt.bill_id = rb.id  INNER JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type IN (?,?,?,?,?) ORDER BY pt.id DESC LIMIT 10, [2 REPAYMENT WITHHOLD MANUAL_WITHHOLD PARTIAL_OFFLINE_REPAYMENT REFUND]","duration":"15.9911ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 14:21:00.046]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"46.2765ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-08-14 14:21:00.047]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"46.5271ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-08-14 14:21:04.137]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b8dddee84ce0caad51d85","sql":"SELECT count(*) as count FROM business_payment_transactions pt INNER JOIN business_repayment_bills rb ON pt.bill_id = rb.id  INNER JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type = ? LIMIT 1, [2 REFUND]","duration":"16.5884ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 14:21:04.137]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b8dddee84ce0caad51d85","sql":"SELECT \n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') THEN pt.amount ELSE 0 END) as total_income,\n\t\tSUM(CASE WHEN pt.type = 'REFUND' THEN pt.amount ELSE 0 END) as total_refund,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) = rb.due_date THEN pt.amount ELSE 0 END) as due_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) > rb.due_date THEN pt.amount ELSE 0 END) as overdue_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) < rb.due_date THEN pt.amount ELSE 0 END) as early_income,\n\t\tCOUNT(DISTINCT pt.user_id) as total_customers,\n\t\tCOUNT(DISTINCT pt.order_id) as total_orders\n\t FROM business_payment_transactions pt INNER JOIN business_repayment_bills rb ON pt.bill_id = rb.id  INNER JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type = ? LIMIT 1, [2 REFUND]","duration":"16.5884ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 14:21:04.159]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b8dddee84ce0caad51d85","sql":"SELECT \n\t\t\tpt.id,\n\t\t\tpt.transaction_no,\n\t\t\tlo.order_no,\n\t\t\taa.name as user_name,\n\t\t\taa.mobile,\n\t\t\tpt.type,\n\t\t\tpt.withhold_type,\n\t\t\tpt.offline_payment_channel_detail,\n\t\t\tpt.amount,\n\t\t\trb.period_number,\n\t\t\tpt.completed_at,\n\t\t\trb.due_date\n\t\t FROM business_payment_transactions pt INNER JOIN business_repayment_bills rb ON pt.bill_id = rb.id  INNER JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type = ? ORDER BY pt.id DESC LIMIT 10, [2 REFUND]","duration":"22.3232ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-14 16:21:43.520]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b947379dfaf58f3ba1a79","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id ORDER BY c.id, []","duration":"41.8517ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-14 16:21:43.539]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b947379dfaf58f3ba1a79","sql":"SELECT COUNT(DISTINCT c.id) as total \n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tWHERE c.channel_status = 1, []","duration":"61.0325ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-14 16:21:43.539]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b947379dfaf58f3ba1a79","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1 AND pt.status = 2\n\t\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id ORDER BY c.id, []","duration":"61.0325ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-14 16:21:43.539]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b947379dfaf58f3ba1a79","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id, c.channel_name ORDER BY c.id, []","duration":"61.0325ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-14 16:21:43.539]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b947379dfaf58f3ba1a79","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tWHERE c.channel_status = 1\n\t\t\tAND pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') GROUP BY c.id ORDER BY c.id, []","duration":"61.0325ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-14 16:21:43.559]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b947379dfaf58f3ba1a79","sql":"SELECT DISTINCT c.id as channel_id, c.channel_name \n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 5 OFFSET 0, []","duration":"19.9934ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-14 16:21:55.193]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94763238a3789b6f3ce8","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id, c.channel_name ORDER BY c.id, []","duration":"32.0778ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-14 16:21:55.199]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94763238a3789b6f3ce8","sql":"SELECT COUNT(DISTINCT c.id) as total \n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tWHERE c.channel_status = 1, []","duration":"37.9323ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-14 16:21:55.208]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94763238a3789b6f3ce8","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id ORDER BY c.id, []","duration":"47.5647ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-14 16:21:55.209]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94763238a3789b6f3ce8","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1 AND pt.status = 2\n\t\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id ORDER BY c.id, []","duration":"48.0796ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-14 16:21:55.223]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94763238a3789b6f3ce8","sql":"SELECT DISTINCT c.id as channel_id, c.channel_name \n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 5 OFFSET 0, []","duration":"23.4214ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-14 16:21:55.223]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94763238a3789b6f3ce8","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tWHERE c.channel_status = 1\n\t\t\tAND pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') GROUP BY c.id ORDER BY c.id, []","duration":"61.8697ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-14 16:22:00.023]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"21.562ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-14 16:22:00.038]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"35.5552ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-14 16:22:00.039]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"37.7066ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-14 16:22:00.039]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"38.2362ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-14 16:22:03.650]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94782a72727066eed84d","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tWHERE c.channel_status = 1\n\t\t\tAND pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') AND b.due_date BETWEEN ? AND ? GROUP BY c.id ORDER BY c.id, [2024-01-01 2024-12-31]","duration":"30.2311ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-14 16:22:03.651]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94782a72727066eed84d","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tWHERE c.channel_status = 1 AND b.due_date BETWEEN ? AND ? GROUP BY c.id, c.channel_name ORDER BY c.id, [2024-01-01 2024-12-31]","duration":"30.7371ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-14 16:22:03.651]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94782a72727066eed84d","sql":"SELECT COUNT(DISTINCT c.id) as total \n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tWHERE c.channel_status = 1 AND b.due_date BETWEEN ? AND ?, [2024-01-01 2024-12-31]","duration":"30.7371ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-14 16:22:03.651]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94782a72727066eed84d","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1 AND pt.status = 2\n\t\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tWHERE c.channel_status = 1 AND b.due_date BETWEEN ? AND ? GROUP BY c.id ORDER BY c.id, [2024-01-01 2024-12-31]","duration":"30.7371ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-14 16:22:03.651]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94782a72727066eed84d","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tWHERE c.channel_status = 1 AND b.due_date BETWEEN ? AND ? GROUP BY c.id ORDER BY c.id, [2024-01-01 2024-12-31]","duration":"30.7371ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-14 16:22:03.682]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94782a72727066eed84d","sql":"SELECT DISTINCT c.id as channel_id, c.channel_name \n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tWHERE c.channel_status = 1 AND b.due_date BETWEEN ? AND ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 10 OFFSET 0, [2024-01-01 2024-12-31]","duration":"30.558ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-14 16:28:00.039]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"38.1134ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-14 16:28:00.055]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"51.8961ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-08-14 16:28:00.056]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"52.9274ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-14 16:28:00.056]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"53.997ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-14 16:28:08.574]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94cd21f43264cb477445","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id ORDER BY c.id, []","duration":"22.5654ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-14 16:28:08.574]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94cd21f43264cb477445","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id, c.channel_name ORDER BY c.id, []","duration":"23.076ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-14 16:28:08.575]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94cd21f43264cb477445","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tWHERE c.channel_status = 1\n\t\t\tAND pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') GROUP BY c.id ORDER BY c.id, []","duration":"23.2154ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-14 16:28:08.575]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94cd21f43264cb477445","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1 AND pt.status = 2\n\t\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id ORDER BY c.id, []","duration":"23.2154ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-14 16:28:08.590]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94cd21f43264cb477445","sql":"SELECT count(*) as count FROM channel c INNER JOIN business_loan_orders o ON c.id = o.channel_id  INNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? LIMIT 1, [1]","duration":"38.5121ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-14 16:28:08.604]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b94cd21f43264cb477445","sql":"SELECT DISTINCT c.id as channel_id, c.channel_name FROM channel c INNER JOIN business_loan_orders o ON c.id = o.channel_id  INNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 5, [1]","duration":"13.7632ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-14 16:33:04.923]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b951220f888e86e2e68a0","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id ORDER BY c.id, []","duration":"30.1224ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-14 16:33:04.953]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b951220f888e86e2e68a0","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tWHERE c.channel_status = 1\n\t\t\tAND pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') GROUP BY c.id ORDER BY c.id, []","duration":"60.0405ms","duration_ms":60}
{"level":"dev.info","ts":"[2025-08-14 16:33:04.953]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b951220f888e86e2e68a0","sql":"SELECT count(*) as count FROM channel c INNER JOIN business_loan_orders o ON c.id = o.channel_id  INNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? LIMIT 1, [1]","duration":"59.4785ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-14 16:33:04.953]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b951220f888e86e2e68a0","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id, c.channel_name ORDER BY c.id, []","duration":"59.4785ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-14 16:33:04.962]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b951220f888e86e2e68a0","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1 AND pt.status = 2\n\t\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id ORDER BY c.id, []","duration":"67.4268ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-08-14 16:33:04.966]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b951220f888e86e2e68a0","sql":"SELECT DISTINCT c.id as channel_id, c.channel_name FROM channel c INNER JOIN business_loan_orders o ON c.id = o.channel_id  INNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 10, [1]","duration":"12.9509ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-14 16:44:52.070]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b95b6c6954dc8c3cef6af","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tWHERE c.channel_status = 1\n\t\t\tAND pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') GROUP BY c.id ORDER BY c.id, []","duration":"29.8317ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-14 16:44:52.088]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b95b6c6954dc8c3cef6af","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id, c.channel_name ORDER BY c.id, []","duration":"48.2011ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-14 16:44:52.090]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b95b6c6954dc8c3cef6af","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id ORDER BY c.id, []","duration":"49.5805ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-14 16:44:52.090]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b95b6c6954dc8c3cef6af","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1 AND pt.status = 2\n\t\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id ORDER BY c.id, []","duration":"50.0876ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-08-14 16:44:52.093]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b95b6c6954dc8c3cef6af","sql":"SELECT count(*) as count FROM channel c INNER JOIN business_loan_orders o ON c.id = o.channel_id  INNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? LIMIT 1, [1]","duration":"52.3037ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-14 16:44:52.103]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b95b6c6954dc8c3cef6af","sql":"SELECT DISTINCT c.id as channel_id, c.channel_name FROM channel c INNER JOIN business_loan_orders o ON c.id = o.channel_id  INNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 5, [1]","duration":"9.3473ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-14 16:45:00.015]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"14.5207ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-14 16:45:00.018]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"16.6966ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 16:46:00.039]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"37.7604ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-14 16:46:00.039]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"36.8796ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-14 16:46:00.050]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"48.166ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-14 16:46:00.051]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"49.5506ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-14 16:51:35.859]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9614ca3da6c48db45284","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tWHERE c.channel_status = 1\n\t\t\tAND pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') GROUP BY c.id ORDER BY c.id, []","duration":"30.0623ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-14 16:51:35.911]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9614ca3da6c48db45284","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id, c.channel_name ORDER BY c.id, []","duration":"82.3171ms","duration_ms":82}
{"level":"dev.info","ts":"[2025-08-14 16:51:35.911]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9614ca3da6c48db45284","sql":"SELECT count(*) as count FROM channel c INNER JOIN business_loan_orders o ON c.id = o.channel_id  INNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? LIMIT 1, [1]","duration":"82.3171ms","duration_ms":82}
{"level":"dev.info","ts":"[2025-08-14 16:51:35.911]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9614ca3da6c48db45284","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1 AND pt.status = 2\n\t\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id ORDER BY c.id, []","duration":"81.7805ms","duration_ms":81}
{"level":"dev.info","ts":"[2025-08-14 16:51:35.911]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9614ca3da6c48db45284","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id ORDER BY c.id, []","duration":"82.3171ms","duration_ms":82}
{"level":"dev.info","ts":"[2025-08-14 16:51:35.928]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9614ca3da6c48db45284","sql":"SELECT DISTINCT c.id as channel_id, c.channel_name FROM channel c INNER JOIN business_loan_orders o ON c.id = o.channel_id  INNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 5, [1]","duration":"16.6952ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 17:10:00.031]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"27.7202ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-14 17:10:00.063]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"58.673ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-08-14 17:10:00.064]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"60.234ms","duration_ms":60}
{"level":"dev.info","ts":"[2025-08-14 17:10:00.064]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"59.6964ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-14 17:10:06.541]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b971762ccb698f3e5a72d","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tWHERE c.channel_status = 1\n\t\t\tAND pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') GROUP BY c.id ORDER BY c.id, []","duration":"50.8624ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-08-14 17:10:06.542]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b971762ccb698f3e5a72d","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id ORDER BY c.id, []","duration":"52.0379ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-14 17:10:06.542]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b971762ccb698f3e5a72d","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id, c.channel_name ORDER BY c.id, []","duration":"53.048ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-14 17:10:06.542]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b971762ccb698f3e5a72d","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tWHERE c.channel_status = 1 GROUP BY c.id ORDER BY c.id, []","duration":"53.048ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-14 17:10:06.596]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b971762ccb698f3e5a72d","sql":"SELECT count(*) as count FROM channel c INNER JOIN business_loan_orders o ON c.id = o.channel_id  INNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? LIMIT 1, [1]","duration":"105.4551ms","duration_ms":105}
{"level":"dev.info","ts":"[2025-08-14 17:10:06.622]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b971762ccb698f3e5a72d","sql":"SELECT DISTINCT c.id as channel_id, c.channel_name FROM channel c INNER JOIN business_loan_orders o ON c.id = o.channel_id  INNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 5, [1]","duration":"25.4635ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-14 17:29:00.019]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"16.732ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 17:29:00.066]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"62.7593ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-14 17:29:04.808]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b982068fa4f1c9cf4dd36","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id, [1]","duration":"48.2343ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-14 17:29:04.808]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b982068fa4f1c9cf4dd36","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id WHERE c.channel_status = ? AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') GROUP BY c.id ORDER BY c.id, [1]","duration":"48.2343ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-14 17:29:04.857]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b982068fa4f1c9cf4dd36","sql":"SELECT count(*) as count FROM channel c INNER JOIN business_loan_orders o ON c.id = o.channel_id  INNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? LIMIT 1, [1]","duration":"97.6727ms","duration_ms":97}
{"level":"dev.info","ts":"[2025-08-14 17:29:04.857]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b982068fa4f1c9cf4dd36","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"97.6727ms","duration_ms":97}
{"level":"dev.info","ts":"[2025-08-14 17:29:04.859]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b982068fa4f1c9cf4dd36","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"99.4512ms","duration_ms":99}
{"level":"dev.info","ts":"[2025-08-14 17:29:04.870]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b982068fa4f1c9cf4dd36","sql":"SELECT DISTINCT c.id as channel_id, c.channel_name FROM channel c INNER JOIN business_loan_orders o ON c.id = o.channel_id  INNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 5, [1]","duration":"11.4732ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 17:34:20.819]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9869febee99cb07c1faa","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id, [1]","duration":"12.6014ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-14 17:34:20.843]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9869febee99cb07c1faa","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id WHERE c.channel_status = ? AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD') GROUP BY c.id ORDER BY c.id, [1]","duration":"36.4638ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-14 17:34:20.850]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9869febee99cb07c1faa","sql":"SELECT count(*) as count FROM channel c INNER JOIN business_loan_orders o ON c.id = o.channel_id  INNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? LIMIT 1, [1]","duration":"43.9425ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-14 17:34:20.853]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9869febee99cb07c1faa","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"45.4085ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-14 17:34:20.856]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9869febee99cb07c1faa","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"48.6253ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-14 17:34:20.860]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9869febee99cb07c1faa","sql":"SELECT DISTINCT c.id as channel_id, c.channel_name FROM channel c INNER JOIN business_loan_orders o ON c.id = o.channel_id  INNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 10, [1]","duration":"7.8882ms","duration_ms":7}
{"level":"dev.info","ts":"[2025-08-14 17:35:00.025]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"24.1888ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-14 17:35:00.025]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"23.6726ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-14 17:36:00.061]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"60.4162ms","duration_ms":60}
{"level":"dev.info","ts":"[2025-08-14 17:36:00.061]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"61.4639ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-14 17:36:00.061]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"60.9519ms","duration_ms":60}
{"level":"dev.info","ts":"[2025-08-14 17:36:00.071]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"70.4595ms","duration_ms":70}
{"level":"dev.info","ts":"[2025-08-14 17:40:00.022]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250814174000","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"20.2046ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-14 17:40:00.053]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250814174000","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"51.1644ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-08-14 17:40:00.058]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250814174000","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"55.6854ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-14 17:40:00.059]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250814174000","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"57.2943ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-08-14 17:40:05.724]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b98ba4c1ea3d000d39dd4","sql":"\n\t\tSELECT COUNT(DISTINCT c.id) as count\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ?, [1]","duration":"23.672ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-14 17:40:05.724]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b98ba4c1ea3d000d39dd4","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"23.1534ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-14 17:40:05.730]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b98ba4c1ea3d000d39dd4","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"29.859ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-14 17:40:05.730]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b98ba4c1ea3d000d39dd4","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id, [1]","duration":"29.859ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-14 17:40:05.736]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b98ba4c1ea3d000d39dd4","sql":"\n\t\tSELECT DISTINCT c.id as channel_id, c.channel_name\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 10 OFFSET 0, [1]","duration":"12.0261ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-14 17:40:05.763]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b98ba4c1ea3d000d39dd4","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id WHERE c.channel_status = ? AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD') GROUP BY c.id ORDER BY c.id, [1]","duration":"62.9162ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-14 17:40:33.505]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b98c0c44c0f9024262da4","sql":"\n\t\tSELECT COUNT(DISTINCT c.id) as count\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ?, [1]","duration":"18.7479ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-14 17:40:33.524]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b98c0c44c0f9024262da4","sql":"\n\t\tSELECT DISTINCT c.id as channel_id, c.channel_name\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 10 OFFSET 0, [1]","duration":"18.161ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-14 17:40:33.533]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b98c0c44c0f9024262da4","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id WHERE c.channel_status = ? AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD') GROUP BY c.id ORDER BY c.id, [1]","duration":"46.9133ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-08-14 17:40:33.543]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b98c0c44c0f9024262da4","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"55.7605ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-14 17:40:33.544]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b98c0c44c0f9024262da4","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tINNER JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id, [1]","duration":"57.1436ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-08-14 17:40:33.544]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b98c0c44c0f9024262da4","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"57.1436ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-08-14 17:41:00.030]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"28.279ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-14 17:41:00.030]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"28.8236ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-14 17:45:52.899]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b990b212f3e402ab46041","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"26.608ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-14 17:45:52.928]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b990b212f3e402ab46041","sql":"\n\t\tSELECT COUNT(DISTINCT c.id) as count\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ?, [1]","duration":"55.7026ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-14 17:45:52.930]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b990b212f3e402ab46041","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id WHERE c.channel_status = ? AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD') GROUP BY c.id ORDER BY c.id, [1]","duration":"57.7446ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-08-14 17:45:52.931]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b990b212f3e402ab46041","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id, [1]","duration":"58.2822ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-08-14 17:45:52.939]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b990b212f3e402ab46041","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"66.3576ms","duration_ms":66}
{"level":"dev.info","ts":"[2025-08-14 17:45:52.939]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b990b212f3e402ab46041","sql":"\n\t\tSELECT DISTINCT c.id as channel_id, c.channel_name\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 10 OFFSET 0, [1]","duration":"9.6531ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-14 17:46:00.012]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"10.6807ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-14 17:46:00.017]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"16.3651ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 17:46:00.017]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"15.9856ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 17:46:00.017]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"16.5066ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 17:47:29.573]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9921a3f8d7a801e1171c","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id, [1]","duration":"16.7384ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 17:47:29.618]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9921a3f8d7a801e1171c","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id WHERE c.channel_status = ? AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD') GROUP BY c.id ORDER BY c.id, [1]","duration":"62.313ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-14 17:47:29.618]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9921a3f8d7a801e1171c","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"62.4536ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-14 17:47:29.618]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9921a3f8d7a801e1171c","sql":"\n\t\tSELECT COUNT(DISTINCT c.id) as count\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ?, [1]","duration":"61.9038ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-14 17:47:29.618]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9921a3f8d7a801e1171c","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"61.2434ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-14 17:47:29.633]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9921a3f8d7a801e1171c","sql":"\n\t\tSELECT DISTINCT c.id as channel_id, c.channel_name\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 10 OFFSET 0, [1]","duration":"14.2534ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-14 17:47:39.723]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9924019962f4736bdcd6","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id WHERE c.channel_status = ? AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD') GROUP BY c.id ORDER BY c.id, [1]","duration":"7.3768ms","duration_ms":7}
{"level":"dev.info","ts":"[2025-08-14 17:47:39.723]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9924019962f4736bdcd6","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"7.8833ms","duration_ms":7}
{"level":"dev.info","ts":"[2025-08-14 17:47:39.727]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9924019962f4736bdcd6","sql":"\n\t\tSELECT COUNT(DISTINCT c.id) as count\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ?, [1]","duration":"11.3804ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 17:47:39.727]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9924019962f4736bdcd6","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"11.068ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 17:47:39.727]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9924019962f4736bdcd6","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id, [1]","duration":"11.068ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-14 17:47:39.736]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9924019962f4736bdcd6","sql":"\n\t\tSELECT DISTINCT c.id as channel_id, c.channel_name\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 10 OFFSET 0, [1]","duration":"9.2142ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-14 17:48:00.019]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"18.855ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-14 17:48:00.020]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"18.1975ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-14 17:48:00.020]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"18.1975ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-14 17:48:00.020]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"18.7951ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-14 17:49:00.040]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"40.1891ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-14 17:49:00.040]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"40.1891ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-14 17:54:32.495]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99841c21b6d47ef7cb4e","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id, [1]","duration":"14.7451ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-14 17:54:32.519]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99841c21b6d47ef7cb4e","sql":"\n\t\tSELECT COUNT(DISTINCT c.id) as count\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ?, [1]","duration":"38.8984ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-14 17:54:32.519]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99841c21b6d47ef7cb4e","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"38.3004ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-14 17:54:32.530]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99841c21b6d47ef7cb4e","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id WHERE c.channel_status = ? AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD') GROUP BY c.id ORDER BY c.id, [1]","duration":"49.7372ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-14 17:54:32.531]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99841c21b6d47ef7cb4e","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"49.1376ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-14 17:54:32.535]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99841c21b6d47ef7cb4e","sql":"\n\t\tSELECT DISTINCT c.id as channel_id, c.channel_name\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 10 OFFSET 10, [1]","duration":"15.3999ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 17:54:41.234]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b998625124978c7d4ad99","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"17.417ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-14 17:54:41.235]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b998625124978c7d4ad99","sql":"\n\t\tSELECT COUNT(DISTINCT c.id) as count\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ?, [1]","duration":"17.8955ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-14 17:54:41.249]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b998625124978c7d4ad99","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id WHERE c.channel_status = ? AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD') GROUP BY c.id ORDER BY c.id, [1]","duration":"31.4372ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-14 17:54:41.249]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b998625124978c7d4ad99","sql":"\n\t\tSELECT DISTINCT c.id as channel_id, c.channel_name\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 10 OFFSET 0, [1]","duration":"13.5417ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-14 17:54:41.249]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b998625124978c7d4ad99","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id, [1]","duration":"31.4372ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-14 17:54:41.249]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b998625124978c7d4ad99","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"31.4372ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-14 17:54:44.917]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9986fd3c1a546340ae65","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id, [1]","duration":"73.7633ms","duration_ms":73}
{"level":"dev.info","ts":"[2025-08-14 17:54:44.917]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9986fd3c1a546340ae65","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id WHERE c.channel_status = ? AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD') GROUP BY c.id ORDER BY c.id, [1]","duration":"73.7633ms","duration_ms":73}
{"level":"dev.info","ts":"[2025-08-14 17:54:44.917]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9986fd3c1a546340ae65","sql":"\n\t\tSELECT COUNT(DISTINCT c.id) as count\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ?, [1]","duration":"73.7633ms","duration_ms":73}
{"level":"dev.info","ts":"[2025-08-14 17:54:44.917]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9986fd3c1a546340ae65","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"73.7633ms","duration_ms":73}
{"level":"dev.info","ts":"[2025-08-14 17:54:44.918]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9986fd3c1a546340ae65","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"74.2743ms","duration_ms":74}
{"level":"dev.info","ts":"[2025-08-14 17:54:44.935]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b9986fd3c1a546340ae65","sql":"\n\t\tSELECT DISTINCT c.id as channel_id, c.channel_name\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 10 OFFSET 0, [1]","duration":"16.8233ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-14 18:00:35.449]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99d89d657b8ca11a9751","sql":"\n\t\tSELECT COUNT(DISTINCT c.id) as count\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ?, [1]","duration":"23.1207ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-14 18:00:35.490]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99d89d657b8ca11a9751","sql":"\n\t\tSELECT DISTINCT c.id as channel_id, c.channel_name\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 10 OFFSET 0, [1]","duration":"39.0379ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-14 18:00:35.499]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99d89d657b8ca11a9751","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"73.0179ms","duration_ms":73}
{"level":"dev.info","ts":"[2025-08-14 18:00:35.511]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99d89d657b8ca11a9751","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id WHERE c.channel_status = ? AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD') GROUP BY c.id ORDER BY c.id, [1]","duration":"84.7084ms","duration_ms":84}
{"level":"dev.info","ts":"[2025-08-14 18:00:35.519]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99d89d657b8ca11a9751","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"92.3302ms","duration_ms":92}
{"level":"dev.info","ts":"[2025-08-14 18:00:35.519]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99d89d657b8ca11a9751","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id, [1]","duration":"92.8626ms","duration_ms":92}
{"level":"dev.info","ts":"[2025-08-14 18:00:41.207]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99d9f4f6fd342cf73a31","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id WHERE c.channel_status = ? AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD') GROUP BY c.id ORDER BY c.id, [1]","duration":"19.1484ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-14 18:00:41.207]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99d9f4f6fd342cf73a31","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"19.7229ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-14 18:00:41.207]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99d9f4f6fd342cf73a31","sql":"\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id, [1]","duration":"19.7229ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-14 18:00:41.207]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99d9f4f6fd342cf73a31","sql":"\n\t\tSELECT COUNT(DISTINCT c.id) as count\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ?, [1]","duration":"19.7229ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-14 18:00:41.216]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99d9f4f6fd342cf73a31","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"28.2731ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-14 18:00:41.222]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"185b99d9f4f6fd342cf73a31","sql":"\n\t\tSELECT DISTINCT c.id as channel_id, c.channel_name\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 10 OFFSET 0, [1]","duration":"15.4251ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-14 18:01:00.121]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"119.3257ms","duration_ms":119}
{"level":"dev.info","ts":"[2025-08-14 18:01:00.121]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"120.6073ms","duration_ms":120}
{"level":"dev.info","ts":"[2025-08-14 18:02:00.134]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250814180200","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"133.8406ms","duration_ms":133}
{"level":"dev.info","ts":"[2025-08-14 18:02:00.134]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250814180200","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"133.8406ms","duration_ms":133}
{"level":"dev.info","ts":"[2025-08-14 18:02:00.134]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250814180200","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"133.3367ms","duration_ms":133}
{"level":"dev.info","ts":"[2025-08-14 18:02:00.201]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250814180200","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"200.8111ms","duration_ms":200}
