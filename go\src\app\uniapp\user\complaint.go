package user

import (
	"encoding/json"
	"fincore/model"
	"fincore/utils/gf"
	"fincore/utils/results"
	"io"
	"reflect"
	"time"

	"github.com/gin-gonic/gin"
)

// 用于自动注册路由
type Complaint struct {
}

// 初始化生成路由
func init() {
	fpath := Complaint{}
	gf.Register(&fpath, reflect.TypeOf(fpath).PkgPath())
}

// GetComplaintUserInfoReq 获取用户详情请求参数
type GetComplaintUserInfoReq struct {
	UID int `json:"uid" form:"uid" query:"uid"`
}

// 提交投诉内容
func (api *Complaint) UploadComplaintContent(c *gin.Context) {
	// 获取post传过来的data
	body, _ := io.ReadAll(c.Request.Body)
	var parameter map[string]interface{}
	_ = json.Unmarshal(body, &parameter)

	// 参数校验
	uid := parameter["uid"]
	complaintContent := parameter["complaintContent"]
	imgUrls := parameter["imgUrls"]
	userName := parameter["userName"]
	telephone := parameter["telephone"]
	idCard := parameter["idCard"]

	if uid == nil || complaintContent == nil {
		results.Failed(c, "缺少必要参数", nil)
		return
	}

	// 验证用户是否存在
	userExists, err := model.DB().Table("business_app_account").
		Where("id", uid).
		Count()

	if err != nil {
		results.Failed(c, "验证用户失败", err)
		return
	}

	if userExists == 0 {
		results.Failed(c, "用户不存在，无法提交投诉", nil)
		return
	}

	// 构建插入数据
	data := map[string]interface{}{
		"uid":           uid,
		"name":          userName,
		"mobile":        telephone,
		"idCard":        idCard,
		"content":       complaintContent,
		"photoUrls":     imgUrls,
		"complaintTime": time.Now().Format("2006-01-02 15:04:05"),
	}

	// 插入数据
	_, err = model.DB().Table("user_complaint").Data(data).Insert()
	if err != nil {
		results.Failed(c, "提交投诉失败", err)
		return
	}

	// 更新用户投诉状态
	_, err = model.DB().Table("business_app_account").
		Data(map[string]interface{}{"complaintStatus": 1}).
		Where("id", uid).
		Update()
	if err != nil {
		results.Failed(c, "更新用户投诉状态失败", err)
		return
	}

	results.Success(c, "提交投诉成功", nil, nil)
}

// GetComplaintResReq 获取投诉回复请求参数
type GetComplaintResReq struct {
	UID int `json:"uid" form:"uid" query:"uid"`
}

// 获取投诉回复
func (api *Complaint) GetComplaintRes(c *gin.Context) {
	// 获取参数
	var req GetComplaintResReq
	if err := c.ShouldBind(&req); err != nil {
		results.Failed(c, "参数错误", nil)
		return
	}

	// 参数校验
	if req.UID == 0 {
		results.Failed(c, "请传入用户ID", nil)
		return
	}

	// 获取最新的投诉记录
	complaintInfo, err := model.DB().Table("user_complaint").
		Fields("content as complainContent, feedback as complainResponse").
		Where("uid", req.UID).
		Order("complaintTime desc").
		First()

	if err != nil {
		results.Failed(c, "获取投诉回复失败", err)
		return
	}

	if complaintInfo == nil {
		// 如果没有投诉记录，返回空值
		results.Success(c, "获取投诉回复成功", map[string]string{
			"complainContent":  "",
			"complainResponse": "",
		}, nil)
		return
	}

	results.Success(c, "获取投诉回复成功", complaintInfo, nil)
}
