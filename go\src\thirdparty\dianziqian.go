package thirdparty

import (
	"context"
	"fmt"

	"fincore/thirdparty/auth"
	"fincore/thirdparty/client"
	"fincore/thirdparty/error_handler"
	"fincore/thirdparty/service"
	"fincore/thirdparty/types"
)

// DianziqianService 电子签服务
type DianziqianService struct {
	*service.BaseService
}

// NewDianziqianService 创建电子签服务
func NewDianziqianService(config types.ServiceConfig) (*DianziqianService, error) {
	// 创建签名认证器（参考现有电子签实现）
	authenticator := auth.NewSignatureAuth(
		config.GetString("app_id"),
		config.GetString("private_key"),
		config.GetString("host"),
	)

	// 创建错误映射器
	errorMapper := error_handler.NewStandardErrorMapper()
	// 添加电子签特有的错误映射
	errorMapper.AddErrorMapping("10001", error_handler.ErrorInfo{
		Code: "2001", Message: "文档格式不支持", Retryable: false,
	})
	errorMapper.AddErrorMapping("10002", error_handler.ErrorInfo{
		Code: "2002", Message: "签名失败", Retryable: true,
	})

	// 创建HTTP客户端
	clientConfig := &client.ClientConfig{
		BaseURL:       config.GetString("host"),
		Timeout:       config.GetDuration("timeout"),
		Authenticator: authenticator,
		ErrorMapper:   errorMapper,
		ProxyURL:      config.GetString("proxy_url"),
	}

	httpClient := client.NewHTTPClient(clientConfig)

	// 创建基础服务
	baseService := service.NewBaseService("dianziqian", "1.0.0", httpClient, config)

	dianziqianService := &DianziqianService{
		BaseService: baseService,
	}

	// 添加支持的方法
	dianziqianService.AddMethod("CreatePersonalAccount") // 创建个人账户
	dianziqianService.AddMethod("QueryPersonalAccount")  // 查询个人账户
	dianziqianService.AddMethod("CheckPersonalName")     // 实名认证
	dianziqianService.AddMethod("OCR")                   // OCR识别
	dianziqianService.AddMethod("FaceAuth")              // 人脸认证
	dianziqianService.AddMethod("CheckFaceResult")       // 查询人脸认证结果
	dianziqianService.AddMethod("CreateContractFile")    // 创建签署文件
	dianziqianService.AddMethod("AddSigner")             // 添加签署方
	dianziqianService.AddMethod("DownloadFile")          // 下载文件

	return dianziqianService, nil
}

// Initialize 初始化服务
func (s *DianziqianService) Initialize(config types.ServiceConfig) error {
	// 验证必要的配置项
	requiredFields := []string{"host", "app_id", "private_key"}
	for _, field := range requiredFields {
		if config.GetString(field) == "" {
			return fmt.Errorf("缺少必要配置项: %s", field)
		}
	}
	return nil
}

// Call 调用服务
func (s *DianziqianService) Call(ctx context.Context, method string, params map[string]interface{}) (*types.Response, error) {
	switch method {
	case "CreatePersonalAccount":
		return s.createPersonalAccount(ctx, params)
	case "QueryPersonalAccount":
		return s.queryPersonalAccount(ctx, params)
	case "CheckPersonalName":
		return s.checkPersonalName(ctx, params)
	case "OCR":
		return s.ocr(ctx, params)
	case "FaceAuth":
		return s.faceAuth(ctx, params)
	case "CheckFaceResult":
		return s.checkFaceResult(ctx, params)
	case "CreateContractFile":
		return s.CreateContractFile(ctx, params)
	case "AddSigner":
		return s.addSigner(ctx, params)
	case "DownloadFile":
		return s.downloadFile(ctx, params)
	default:
		return nil, fmt.Errorf("不支持的方法: %s", method)
	}
}

// createPersonalAccount 创建个人账户（参考 AccountService.CreatePersonalAccount）
func (s *DianziqianService) createPersonalAccount(ctx context.Context, params map[string]interface{}) (*types.Response, error) {
	req := &types.Request{
		Method: "POST",
		Path:   "/v2/user/addPersonalUser",
		Data:   params,
	}

	return s.GetClient().Do(ctx, req)
}

// queryPersonalAccount 查询个人账户（参考 AccountService.QueryPersonalAccount）
func (s *DianziqianService) queryPersonalAccount(ctx context.Context, params map[string]interface{}) (*types.Response, error) {
	req := &types.Request{
		Method: "POST",
		Path:   "/user/getUser",
		Data:   params,
	}

	return s.GetClient().Do(ctx, req)
}

// checkPersonalName 实名认证（参考 AccountService.CheckPersonalName）
func (s *DianziqianService) checkPersonalName(ctx context.Context, params map[string]interface{}) (*types.Response, error) {
	req := &types.Request{
		Method: "POST",
		Path:   "/auth/person/identifyUrl",
		Data:   params,
	}

	return s.GetClient().Do(ctx, req)
}

// ocr OCR识别
func (s *DianziqianService) ocr(ctx context.Context, params map[string]interface{}) (*types.Response, error) {
	req := &types.Request{
		Method: "POST",
		Path:   "/user/ocrIdentify",
		Data:   params,
	}

	return s.GetClient().Do(ctx, req)
}

// faceAuth 人脸认证
func (s *DianziqianService) faceAuth(ctx context.Context, params map[string]interface{}) (*types.Response, error) {
	req := &types.Request{
		Method: "POST",
		Path:   "/auth/person/face",
		Data:   params,
	}

	return s.GetClient().Do(ctx, req)
}

// checkFaceResult 查询人脸认证结果
func (s *DianziqianService) checkFaceResult(ctx context.Context, params map[string]interface{}) (*types.Response, error) {
	req := &types.Request{
		Method: "POST",
		Path:   "/user/faceResult",
		Data:   params,
	}

	return s.GetClient().Do(ctx, req)
}

// createFile 创建文件
func (s *DianziqianService) CreateContractFile(ctx context.Context, params map[string]interface{}) (*types.Response, error) {
	req := &types.Request{
		Method: "POST",
		Path:   "/contract/create",
		Data:   params,
	}

	return s.GetClient().Do(ctx, req)
}

// addSigner 添加签署方
func (s *DianziqianService) addSigner(ctx context.Context, params map[string]interface{}) (*types.Response, error) {
	req := &types.Request{
		Method: "POST",
		Path:   "/contract/addSigner",
		Data:   params,
	}

	return s.GetClient().Do(ctx, req)
}

// downloadFile 下载文件
func (s *DianziqianService) downloadFile(ctx context.Context, params map[string]interface{}) (*types.Response, error) {
	req := &types.Request{
		Method: "POST",
		Path:   "/contract/download",
		Data:   params,
	}

	return s.GetClient().Do(ctx, req)
}
