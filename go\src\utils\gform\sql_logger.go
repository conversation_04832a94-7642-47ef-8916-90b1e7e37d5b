package gform

import (
	"context"
	"fincore/global"
	"fincore/utils/log"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// SQLLogger SQL日志适配器，集成现有log模块
type SQLLogger struct {
	logger  *log.Logger
	context context.Context
}

var _ ILogger = (*SQLLogger)(nil)

// NewSQLLogger 创建SQL日志适配器
func NewSQLLogger(ctx ...context.Context) *SQLLogger {
	sqlLogger := &SQLLogger{
		logger: log.SQL(),
	}

	// 如果传入了context，则使用它
	if len(ctx) > 0 && ctx[0] != nil {
		sqlLogger.context = ctx[0]

		// 尝试从context中提取request_id
		if requestID := getRequestIDFromContext(ctx[0]); requestID != "" {
			sqlLogger.logger = sqlLogger.logger.WithRequestID(requestID)
			return sqlLogger
		}

		if ginCtx, ok := ctx[0].(*gin.Context); ok {
			sqlLogger.logger = sqlLogger.logger.WithGinContext(ginCtx)
		}
	}

	return sqlLogger
}

// getRequestIDFromContext 从context中提取request_id
func getRequestIDFromContext(ctx context.Context) string {
	if ctx == nil {
		return ""
	}

	// 尝试从context.Value中获取request_id
	if requestID, ok := ctx.Value("request_id").(string); ok {
		return requestID
	}

	// 尝试从context.Value中获取X-Request-ID
	if requestID, ok := ctx.Value("X-Request-ID").(string); ok {
		return requestID
	}

	return ""
}

// EnableSqlLog 是否启用SQL日志
func (s *SQLLogger) EnableSqlLog() bool {
	env := global.App.Config.App.Env

	var enable bool
	// dev环境启用所有SQL日志，prod环境不启用普通SQL日志
	enable = (env == "dev")

	if enable {
		return enable
	}

	// reuqest_id 以 task_ 开头的都需要记录日志
	if s.context != nil {
		if requestID, ok := s.context.Value("request_id").(string); ok {
			enable = strings.HasPrefix(requestID, "task_")
		}
	}

	return enable
}

// EnableErrorLog 是否启用错误日志
func (s *SQLLogger) EnableErrorLog() bool {
	// 所有环境都启用错误日志
	return true
}

// EnableSlowLog 慢查询阈值（秒）
func (s *SQLLogger) EnableSlowLog() float64 {
	env := global.App.Config.App.Env
	if env == "dev" {
		// dev环境不单独记录慢查询，因为已经记录了所有SQL
		return 0
	} else {
		// prod环境只记录超过2秒的慢查询
		return 2.0
	}
}

// Sql 记录SQL日志
func (s *SQLLogger) Sql(sqlStr string, runtime time.Duration) {
	if !s.EnableSqlLog() {
		return
	}

	// 使用结构化日志记录SQL
	s.logger.WithField("sql", sqlStr).
		WithField("duration", runtime.String()).
		WithField("duration_ms", runtime.Milliseconds()).
		Info("SQL执行")
}

// Slow 记录慢查询日志
func (s *SQLLogger) Slow(sqlStr string, runtime time.Duration) {
	threshold := s.EnableSlowLog()
	if threshold <= 0 || runtime.Seconds() <= threshold {
		return
	}

	// 记录慢查询
	s.logger.WithField("sql", sqlStr).
		WithField("duration", runtime.String()).
		WithField("duration_ms", runtime.Milliseconds()).
		WithField("threshold_seconds", threshold).
		Warn("慢 SQL 检测")
}

// Error 记录错误日志
func (s *SQLLogger) Error(msg string) {
	if !s.EnableErrorLog() {
		return
	}

	s.logger.WithField("error_message", msg).
		Error("SQL执行错误")
}

// DefaultSQLLogger 默认SQL日志器工厂函数
func DefaultSQLLogger() func(e *Engin) {
	return func(e *Engin) {
		e.logger = NewSQLLogger()
	}
}

// SQLLoggerWithContext 带context的SQL日志器工厂函数
func SQLLoggerWithContext(ctx context.Context) func(e *Engin) {
	return func(e *Engin) {
		e.logger = NewSQLLogger(ctx)
	}
}
