package repayment

import (
	"context"
	"fmt"
	"time"

	"fincore/app/business/repayment"
	"fincore/app/scheduler/tasks"
	"fincore/model"
	"fincore/utils/convert"
	"fincore/utils/gform"
	"fincore/utils/log"
)

// RepaymentStatusSyncCompensationTask 还款状态同步补偿任务
// 用于处理还款账单状态与支付流水状态不一致的补偿场景
type RepaymentStatusSyncCompensationTask struct {
	*tasks.BaseTask
	paymentService *repayment.PaymentService
	logger         *log.Logger
	ctx            context.Context
}

// NewRepaymentStatusSyncCompensationTask 创建还款状态同步补偿任务
func NewRepaymentStatusSyncCompensationTask() *RepaymentStatusSyncCompensationTask {
	baseTask := tasks.NewBaseTask(
		"repayment-status-sync-compensation",
		"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景",
		// "*/30 * * * * *", // 每30秒执行一次 测试
		"0 */2 * * * *", // 每2分钟执行一次 生产
		10*time.Minute,  // 超时时间10分钟
	)

	// 设置为单例模式，避免重复执行
	baseTask.SetConcurrencyMode(tasks.ConcurrencyModeSingleton)
	// 设置重试次数和间隔
	baseTask.SetRetryCount(3).SetRetryInterval(30 * time.Second)

	logger := log.RegisterModule("repayment_sync_task", "还款状态同步任务")
	ctx := context.Background()
	return &RepaymentStatusSyncCompensationTask{
		BaseTask:       baseTask,
		paymentService: repayment.NewPaymentService(ctx),
		logger:         logger.WithContext(ctx),
		ctx:            ctx,
	}
}

// Execute 执行还款状态同步补偿任务
func (t *RepaymentStatusSyncCompensationTask) Execute(ctx context.Context) error {
	// task_ 开头，记录整个周期所有 sql 执行日志
	requestID := "task_" + t.GetName() + "_" + time.Now().Format("20060102150405")
	t.ctx = context.WithValue(t.ctx, "request_id", requestID)

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "start_execution"),
	).Info("开始执行还款状态同步补偿任务")

	startTime := time.Now()
	var processedCount, successCount, failureCount int

	// 查询符合条件的还款账单和支付流水
	transactions, err := t.getPendingRepaymentBillsWithSubmittedTransactions()
	if err != nil {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "query_transactions"),
			log.String("error", err.Error()),
		).Error("查询待同步状态的支付流水失败")
		return fmt.Errorf("查询待同步状态的支付流水失败: %v", err)
	}

	if len(transactions) == 0 {
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("operation", "no_transactions_found"),
		).Info("未找到需要同步状态的支付流水")
		return nil
	}

	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "found_transactions"),
		log.Int("transaction_count", len(transactions)),
	).Info("找到需要同步状态的支付流水")

	// 遍历处理每个支付流水
	for _, transactionData := range transactions {
		processedCount++

		// 检查上下文是否被取消
		select {
		case <-ctx.Done():
			t.logger.WithFields(
				log.String("task", t.GetName()),
				log.String("operation", "context_cancelled"),
				log.Int("processed_count", processedCount-1),
			).Warn("任务被取消，停止处理")
			return ctx.Err()
		default:
		}

		transactionNo := convert.GetStringFromMap(transactionData, "transaction_no")
		billID := convert.GetIntFromMap(transactionData, "bill_id", 0)
		transactionType := convert.GetStringFromMap(transactionData, "transaction_type")

		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("transaction_no", transactionNo),
			log.Int("bill_id", billID),
			log.String("transaction_type", transactionType),
			log.String("operation", "sync_status"),
		).Info("开始同步支付流水状态")

		// 调用状态同步方法
		err := t.syncTransactionStatus(transactionNo)
		if err != nil {
			failureCount++
			t.logger.WithFields(
				log.String("task", t.GetName()),
				log.String("transaction_no", transactionNo),
				log.Int("bill_id", billID),
				log.String("operation", "sync_status_failed"),
				log.String("error", err.Error()),
			).Error("支付流水状态同步失败")
			continue
		}

		successCount++
		t.logger.WithFields(
			log.String("task", t.GetName()),
			log.String("transaction_no", transactionNo),
			log.Int("bill_id", billID),
			log.String("operation", "sync_status_success"),
		).Info("支付流水状态同步成功")
	}

	// 记录执行统计
	duration := time.Since(startTime)
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "execution_completed"),
		log.Int("processed_count", processedCount),
		log.Int("success_count", successCount),
		log.Int("failure_count", failureCount),
		log.String("duration", duration.String()),
	).Info("还款状态同步补偿任务执行完成")

	return nil
}

// getPendingRepaymentBillsWithSubmittedTransactions 获取待同步状态的还款账单和支付流水
func (t *RepaymentStatusSyncCompensationTask) getPendingRepaymentBillsWithSubmittedTransactions() ([]gform.Data, error) {
	// 查询条件：
	// 1. 还款账单状态为：0-待支付、3-逾期待支付、7-部分还款、9-逾期部分还款
	// 2. 支付流水状态为已提交 (status = 1)
	// 3. 流水类型为还款相关：REPAYMENT、WITHHOLD、MANUAL_WITHHOLD
	// 4. 流水有第三方订单号 (third_party_order_no != '')
	query := `
		SELECT DISTINCT
			brb.id as bill_id,
			brb.order_id,
			brb.user_id,
			brb.period_number,
			brb.status as bill_status,
			brb.total_due_amount,
			brb.paid_amount,
			bpt.transaction_no,
			bpt.type as transaction_type,
			bpt.status as transaction_status,
			bpt.amount,
			bpt.third_party_order_no,
			bpt.created_at as transaction_created_at
		FROM business_repayment_bills brb
		INNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id
		WHERE brb.status IN (?, ?, ?, ?)
		  AND bpt.status = ?
		  AND bpt.type IN (?, ?, ?)
		  AND bpt.transaction_no != ''
		  AND bpt.transaction_no IS NOT NULL
		  AND bpt.deleted_at IS NULL
		ORDER BY bpt.created_at ASC
		LIMIT 100
	`

	result, err := model.DB(model.WithContext(t.ctx)).Query(query,
		model.RepaymentBillStatusUnpaid,             // 0-待支付
		model.RepaymentBillStatusOverdueUnpaid,      // 3-逾期待支付
		model.RepaymentBillStatusPartialPaid,        // 7-部分还款
		model.RepaymentBillStatusOverduePartialPaid, // 9-逾期部分还款
		model.TransactionStatusSubmitted,            // 1-已提交
		model.TransactionTypeRepayment,              // REPAYMENT-用户主动还款
		model.TransactionTypeWithhold,               // WITHHOLD-系统代扣
		model.TransactionTypeManualWithhold,         // MANUAL_WITHHOLD-管理员手动代扣
	)
	if err != nil {
		return nil, fmt.Errorf("查询数据库失败: %v", err)
	}

	return result, nil
}

// syncTransactionStatus 同步支付流水状态
// 调用 QueryPaymentStatus 方法进行状态同步
func (t *RepaymentStatusSyncCompensationTask) syncTransactionStatus(transactionNo string) (err error) {
	_, err = t.paymentService.QueryPaymentStatus(transactionNo)
	if err != nil {
		return fmt.Errorf("查询支付状态失败: %v", err)
	}
	return
}

// OnStart 任务开始执行前的回调
func (t *RepaymentStatusSyncCompensationTask) OnStart(ctx context.Context) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_starting"),
	).Info("还款状态同步补偿任务即将开始")
	return nil
}

// OnSuccess 任务执行成功后的回调
func (t *RepaymentStatusSyncCompensationTask) OnSuccess(ctx context.Context) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_success"),
	).Info("还款状态同步补偿任务执行成功")
	return nil
}

// OnError 任务执行失败后的回调
func (t *RepaymentStatusSyncCompensationTask) OnError(ctx context.Context, err error) error {
	t.logger.WithFields(
		log.String("task", t.GetName()),
		log.String("operation", "task_error"),
		log.String("error", err.Error()),
	).Error("还款状态同步补偿任务执行失败")
	return nil
}
