# Fincore 快速开始指南

## 🚀 5分钟快速部署

### 1. 环境检查

确保您的环境满足以下要求：

```bash
# 检查Go环境
go version  # 需要 1.19+

# 检查Node.js环境
node --version  # 需要 16.0+

# 检查包管理器
yarn --version  # 或 npm --version

# 注意：
* MySQL使用阿里云托管服务，无需本地安装
* Reids需要本地安装，当前河源节点无法创建redis，考虑到这个当前依赖性不强，可靠性也不强要求
```

### 2. 克隆项目

```bash
git clone <repository-url>
cd fincore
```

### 3. 手动编译UniApp（可选）

```bash
# 如果需要UniApp，请先手动编译：
# 1. 使用HBuilderX打开 vue/uniapp 目录
# 2. 选择发行 -> H5
# 3. 编译产物会在 unpackage/dist/build/h5/ 目录

# 详细说明
cat vue/uniapp/MANUAL_BUILD_GUIDE.md
```

### 4. 一键构建

```bash
# 执行完整构建（包含手动编译的UniApp）
./build.sh

# 如果不需要UniApp，可以跳过
./build.sh --skip-uniapp

# 查看构建结果
ls -la deployer/build/
```

### 4. 本地测试

```bash
# 启动Go后端（可选）
cd deployer/build/app/bin
./fincore

# 在另一个终端测试
curl http://localhost:8080/health
```

### 5. 部署到服务器

```bash
# 1. 上传deployer目录到服务器
scp -r deployer/ user@*************:/tmp/

# 2. 在服务器上执行部署
ssh user@*************
cd /tmp/deployer
sudo ./deploy.sh --start
```

## 📋 详细步骤

### 构建步骤

1. **准备环境**
   ```bash
   # 安装Go依赖
   cd go/src && go mod tidy
   
   # 安装前端依赖
   cd vue/business && yarn install
   cd vue/uniapp && npm install
   ```

2. **执行构建**
   ```bash
   # 返回项目根目录
   cd ../../
   
   # 执行构建
   ./build.sh
   ```

3. **验证构建**
   ```bash
   # 检查构建产物
   ./build.sh verify
   
   # 查看构建信息
   cat deployer/build_info.txt
   ```

### 部署步骤

1. **上传部署包**
   ```bash
   # 方式1: 使用scp上传
   scp -r deployer/ user@*************:/tmp/

   # 方式2: 使用rsync上传
   rsync -avz deployer/ user@*************:/tmp/deployer/
   ```

2. **执行部署**
   ```bash
   # 登录到服务器
   ssh user@*************

   # 切换到部署目录
   cd /tmp/deployer

   # 执行部署脚本
   sudo ./deploy.sh --start
   ```

3. **验证部署**
   ```bash
   # 检查服务状态
   cd /opt/fincore/watchdog && ./scripts/status.sh

   # 测试API
   curl http://localhost:8080/health
   ```

## 🛠️ 常用命令

### 构建命令

```bash
# 完整构建
./build.sh

# 仅构建后端
./build.sh go

# 仅构建前端
./build.sh vue

# 清理构建
./build.sh clean

# 验证构建
./build.sh verify
```

### 部署命令

```bash
# 基本部署（在服务器上执行）
sudo ./deploy.sh

# 部署并启动服务
sudo ./deploy.sh --start

# 部署并重启服务
sudo ./deploy.sh --restart

# 完整部署流程
sudo ./deploy.sh --start --check
```

### 服务管理

```bash
# 在服务器上执行（部署后）
cd /opt/fincore/watchdog

# 启动服务
./scripts/start.sh

# 停止服务
./scripts/stop.sh

# 查看状态
./scripts/status.sh

# 健康检查
./bin/health-check

# 查看日志
tail -f logs/watchdog.log
tail -f /opt/fincore/build/app/logs/app.log
```

## 🔧 配置说明

### 主要配置文件

1. **Go后端配置**
   ```bash
   # 位置
   deployer/bin/app/resource/config.yml
   
   # 主要配置项
   - 数据库连接
   - Redis配置
   - 服务端口
   - 日志级别
   ```

2. **Watchdog配置**
   ```bash
   # 位置
   deployer/watchdog/config/
   
   # 配置文件
   - watchdog.conf     # 主配置
   - services.conf     # 服务配置
   - alert.conf        # 告警配置
   ```

### 环境变量

```bash
# 构建环境变量
export BUILD_ENV=production
export BUILD_VERSION=v1.0.0
export GOOS=linux
export GOARCH=amd64

# 部署环境变量
export DEPLOY_HOST=*************
export DEPLOY_USER=fincore
export DEPLOY_PATH=/opt/fincore
```

## 🚨 故障排除

### 构建问题

```bash
# Go编译失败
cd go/src
go clean -cache
go mod download
go build

# Business前端构建失败
cd vue/business
rm -rf node_modules
yarn install
yarn build

# UniApp处理
# 1. 手动编译（推荐）
# 使用HBuilderX打开 vue/uniapp，选择发行 -> H5

# 2. 或者跳过UniApp
./build.sh --skip-uniapp

# 3. 查看手动编译指南
cat vue/uniapp/MANUAL_BUILD_GUIDE.md
```

### 部署问题

```bash
# SSH连接失败
ssh -v fincore@server

# 权限问题
ssh fincore@server 'sudo chown -R fincore:fincore /opt/fincore'

# 服务启动失败
ssh fincore@server 'sudo journalctl -u fincore -n 50'
```

### 运行时问题

```bash
# 端口被占用
sudo netstat -tuln | grep 8080
sudo lsof -i :8080

# 服务无响应
curl -v http://localhost:8080/health
ps aux | grep fincore

# 内存不足
free -h
top -p $(pgrep fincore)
```

## 📊 监控和日志

### 查看日志

```bash
# 应用日志
tail -f /opt/fincore/deployer/bin/app/runtime/app/app.log

# 监控日志
tail -f /opt/fincore/deployer/watchdog/logs/watchdog.log

# 系统日志
sudo journalctl -u fincore -f
sudo journalctl -u fincore-watchdog -f
```

### 健康检查

```bash
# 完整健康检查
/opt/fincore/deployer/watchdog/bin/health-check

# 特定检查
/opt/fincore/deployer/watchdog/bin/health-check process
/opt/fincore/deployer/watchdog/bin/health-check network
/opt/fincore/deployer/watchdog/bin/health-check api
```

### 告警测试

```bash
# 测试告警功能
/opt/fincore/deployer/watchdog/bin/alert-handler test

# 发送特定告警
/opt/fincore/deployer/watchdog/bin/alert-handler WARN fincore "测试告警"
```

## 🎯 下一步

1. **配置监控**
   - 编辑告警配置：`deployer/watchdog/config/alert.conf`
   - 配置邮件/钉钉通知
   - 测试告警功能

2. **优化性能**
   - 调整监控间隔
   - 配置日志轮转
   - 优化资源阈值

3. **安全加固**
   - 配置防火墙
   - 使用SSH密钥
   - 定期更新密码

4. **自动化**
   - 设置CI/CD流水线
   - 配置定时任务
   - 监控告警集成

## 📚 更多文档

- [完整构建部署指南](BUILD_DEPLOY.md)
- [Watchdog监控系统](deployer/watchdog/README.md)
- [故障排除指南](deployer/watchdog/TROUBLESHOOTING.md)
- [快速参考手册](deployer/watchdog/QUICK_REFERENCE.md)

## 💬 获取帮助

如有问题，请：

1. 查看日志文件
2. 运行健康检查
3. 参考故障排除指南
4. 联系技术支持

```bash
# 收集诊断信息
./deployer/watchdog/scripts/status.sh full > diagnostic_report.txt
```

---

🎉 **恭喜！您已成功部署Fincore项目！**
