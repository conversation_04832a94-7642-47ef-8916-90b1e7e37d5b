package bootstrap

import (
	"fincore/global"
	"fincore/utils/gf"
	"fincore/utils/log"
	"os"

	"go.uber.org/zap"
)

func InitializeLog() *zap.Logger {
	// 创建一个临时的简单日志器，避免在配置加载前初始化日志管理器
	config := zap.NewDevelopmentConfig()
	config.OutputPaths = []string{"stdout"}
	logger, err := config.Build()
	if err != nil {
		panic(err)
	}
	return logger
}

// ReinitializeLog 在配置加载后重新初始化日志
func ReinitializeLog() *zap.Logger {
	// 重置日志管理器以使用新配置
	log.ResetManager()

	// 创建根目录
	createRootDir()

	// 直接使用新的日志管理器获取app日志器
	manager := log.GetManager()
	return manager.GetLogger("app")
}

func createRootDir() {
	if ok, _ := gf.PathExists(global.App.Config.Log.RootDir); !ok {
		_ = os.Mkdir(global.App.Config.Log.RootDir, os.ModePerm)
	}
}
