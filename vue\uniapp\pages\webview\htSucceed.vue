<template>
	<view class="callBackContract">
		<view class="contractBox">
			<image src="/static/image/shz.png" mode="widthFix"></image>
			<text class="name">正在加速审核中</text>
			<text class="hint">相关工作人员将在当天处理</text>
			<button @click="backHome">返回首页</button>
		</view>
	</view>
</template>

<script>
	export default {
		methods: {
			backHome() {
				uni.removeStorageSync('product_id');
				uni.removeStorageSync('loan_amount');
				uni.switchTab({
					url: "/pages/index/index"
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background: linear-gradient(180deg, #e6ecf7, #f5faff);
	}
	
	.callBackContract {
		padding: 200rpx 0;
	
		.contractBox {
			display: flex;
			justify-content: center;
			flex-direction: column;
			align-items: center;
	
			image {
				width: 200rpx;
			}
	
			text.name {
				color: #444;
				font-size: 36rpx;
				font-weight: bold;
				margin: 80rpx 0 20rpx;
			}
	
			text.hint {
				color: #666;
				font-size: 26rpx;
			}
	
			button {
				width: 180rpx;
				flex-shrink: 0;
				height: 80rpx;
				line-height: 80rpx;
				background: linear-gradient(to bottom, #1a6eff 20%, #4781e3 45%);
				color: #fff;
				border: none;
				border-radius: 22px;
				font-size: 13px;
				cursor: pointer;
				padding: 0;
				box-sizing: border-box;
				text-align: center;
				white-space: nowrap;
				margin-top: 30rpx;
			}
		}
	}
</style>