package model

import (
	"fmt"
	"time"
)

// BusinessOrderOperationLogs 订单操作日志数据模型
type BusinessOrderOperationLogs struct {
	ID           int    `json:"id" gorm:"primaryKey;autoIncrement" db:"id"` // 主键ID，自增
	OrderID      int    `json:"order_id" db:"order_id"`                     // 订单ID，关联贷款订单表
	OperatorID   int    `json:"operator_id" db:"operator_id"`               // 操作员ID，关联管理员表
	OperatorName string `json:"operator_name" db:"operator_name"`           // 操作员姓名
	Action       string `json:"action" db:"action"`                         // 操作动作，如：创建订单、审核通过、放款、关闭等
	Details      string `json:"details" db:"details"`                       // 操作详情，JSON格式或文本描述，可为空
	CreatedAt    int64  `json:"created_at" db:"created_at"`                 // 创建时间，Unix时间戳
}

// TableName 指定表名
func (BusinessOrderOperationLogs) TableName() string {
	return "business_order_operation_logs"
}

// BusinessOrderOperationLogsService 订单操作日志服务
type BusinessOrderOperationLogsService struct{}

// NewBusinessOrderOperationLogsService 创建订单操作日志服务实例
func NewBusinessOrderOperationLogsService() *BusinessOrderOperationLogsService {
	return &BusinessOrderOperationLogsService{}
}

// CreateLog 创建操作日志
func (s *BusinessOrderOperationLogsService) CreateLog(log *BusinessOrderOperationLogs) error {
	now := time.Now()
	log.CreatedAt = now.Unix()

	data := map[string]interface{}{
		"order_id":      log.OrderID,
		"operator_id":   log.OperatorID,
		"operator_name": log.OperatorName,
		"action":        log.Action,
		"details":       log.Details,
		"created_at":    now,
	}

	insertID, err := DB().Table("business_order_operation_logs").InsertGetId(data)
	if err != nil {
		return fmt.Errorf("创建操作日志失败: %v", err)
	}

	// 设置插入的ID
	log.ID = int(insertID)

	return nil
}

// GetLogsByOrderID 根据订单ID获取操作日志列表
func (s *BusinessOrderOperationLogsService) GetLogsByOrderID(orderID int) ([]BusinessOrderOperationLogs, error) {
	data, err := DB().Table("business_order_operation_logs").Where("order_id", orderID).OrderBy("created_at DESC").Get()
	if err != nil {
		return nil, fmt.Errorf("查询操作日志失败: %v", err)
	}

	var logs []BusinessOrderOperationLogs
	for _, item := range data {
		var log BusinessOrderOperationLogs
		if err := mapToStruct(item, &log); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		logs = append(logs, log)
	}

	return logs, nil
}
