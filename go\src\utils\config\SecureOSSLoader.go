package config

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/spf13/viper"
	"gopkg.in/yaml.v3"
)

// FINCORE_CONFIG_ENCRYPT_KEY fincore.yml配置文件的硬编码加密密钥（32字符用于AES-256）
const FINCORE_CONFIG_ENCRYPT_KEY = "fidddrecconni=gsscceekey22024581"

// SUMPAY_SENSITIVE_FIELDS SumPay配置中需要加密的敏感字段列表

// OSSCredentials OSS访问凭证结构
type OSSCredentials struct {
	Endpoint        string `yaml:"endpoint" json:"endpoint"`
	AccessKeyID     string `yaml:"access_key_id" json:"access_key_id"`
	AccessKeySecret string `yaml:"access_key_secret" json:"access_key_secret"`
	BucketName      string `yaml:"bucket_name" json:"bucket_name"`
	EncryptionKey   string `yaml:"encryption_key" json:"encryption_key"`
}

// SecureOSSLoader 安全的OSS配置加载器
// 支持fincore.yml加密保存和证书文件临时下载
type SecureOSSLoader struct {
	credentials *OSSCredentials
	client      *oss.Client
	bucket      *oss.Bucket
	tempDir     string // 临时文件目录
}

// NewSecureOSSLoader 创建安全的OSS配置加载器
func NewSecureOSSLoader() (*SecureOSSLoader, error) {
	// 创建临时目录
	tempDir := filepath.Join(os.TempDir(), "fincore-certs", fmt.Sprintf("%d", time.Now().Unix()))
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return nil, fmt.Errorf("创建临时目录失败: %v", err)
	}

	loader := &SecureOSSLoader{
		tempDir: tempDir,
	}

	// 加载OSS凭证
	if err := loader.loadCredentials(); err != nil {
		return nil, fmt.Errorf("加载OSS凭证失败: %v", err)
	}

	// 初始化OSS客户端
	if err := loader.initOSSClient(); err != nil {
		return nil, fmt.Errorf("初始化OSS客户端失败: %v", err)
	}

	return loader, nil
}

// loadCredentials 从本地配置文件加载OSS凭证
func (loader *SecureOSSLoader) loadCredentials() error {
	// 优先从环境变量获取凭证文件路径
	credentialsPath := os.Getenv("OSS_CREDENTIALS_PATH")
	if credentialsPath == "" {
		// 默认路径
		credentialsPath = "./resource/fincore.yml"
	}

	// 检查文件是否存在
	if _, err := os.Stat(credentialsPath); os.IsNotExist(err) {
		return fmt.Errorf("OSS凭证文件不存在: %s", credentialsPath)
	}

	// 读取文件内容
	fileData, err := os.ReadFile(credentialsPath)
	if err != nil {
		return fmt.Errorf("读取OSS凭证文件失败: %v", err)
	}

	// 检查文件内容是否为加密格式（base64编码）
	var configData []byte
	if loader.isEncryptedData(fileData) {
		// 解密加密的凭证数据
		configData, err = loader.decryptWithHardcodedKey(fileData)
		if err != nil {
			return fmt.Errorf("解密OSS凭证文件失败: %v", err)
		}
	} else {
		// 直接使用明文数据（向后兼容）
		configData = fileData
	}

	// 使用viper解析YAML配置
	v := viper.New()
	v.SetConfigType("yaml")

	if err := v.ReadConfig(strings.NewReader(string(configData))); err != nil {
		return fmt.Errorf("解析OSS凭证配置失败: %v", err)
	}

	// 手动解析配置到结构体（处理下划线字段名）
	loader.credentials = &OSSCredentials{
		Endpoint:        v.GetString("endpoint"),
		AccessKeyID:     v.GetString("access_key_id"),
		AccessKeySecret: v.GetString("access_key_secret"),
		BucketName:      v.GetString("bucket_name"),
		EncryptionKey:   v.GetString("encryption_key"),
	}

	// 验证必要字段
	if loader.credentials.Endpoint == "" ||
		loader.credentials.AccessKeyID == "" ||
		loader.credentials.AccessKeySecret == "" ||
		loader.credentials.BucketName == "" ||
		loader.credentials.EncryptionKey == "" {
		return fmt.Errorf("OSS凭证配置不完整")
	}

	// 验证加密密钥长度
	if len(loader.credentials.EncryptionKey) != 32 {
		return fmt.Errorf("加密密钥长度必须为32字符")
	}

	return nil
}

// initOSSClient 初始化OSS客户端
func (loader *SecureOSSLoader) initOSSClient() error {
	client, err := oss.New(
		loader.credentials.Endpoint,
		loader.credentials.AccessKeyID,
		loader.credentials.AccessKeySecret,
	)
	if err != nil {
		return fmt.Errorf("创建OSS客户端失败: %v", err)
	}

	bucket, err := client.Bucket(loader.credentials.BucketName)
	if err != nil {
		return fmt.Errorf("获取OSS存储桶失败: %v", err)
	}

	loader.client = client
	loader.bucket = bucket

	return nil
}

// LoadConfig 从OSS加载并解密配置
func (loader *SecureOSSLoader) LoadConfig(configPath string) (*Config, error) {

	// 从OSS下载配置文件
	encryptedData, err := loader.downloadConfig(configPath)
	if err != nil {
		return nil, fmt.Errorf("下载配置文件失败: %v", err)
	}

	// 解密配置数据（第一层解密：整个配置文件）
	decryptedData, err := loader.decryptConfig(encryptedData)
	if err != nil {
		return nil, fmt.Errorf("解密配置文件失败: %v", err)
	}

	// 解析配置
	config, err := loader.parseConfig(decryptedData)
	if err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 第二层解密：解密敏感字段并添加ENC:前缀
	if err := loader.decryptSensitiveFields(config); err != nil {
		return nil, fmt.Errorf("解密敏感字段失败: %v", err)
	}

	return config, nil
}

// downloadConfig 从OSS下载配置文件
func (loader *SecureOSSLoader) downloadConfig(configPath string) ([]byte, error) {
	// 检查文件是否存在
	exists, err := loader.bucket.IsObjectExist(configPath)
	if err != nil {
		return nil, fmt.Errorf("检查OSS文件存在性失败: %v", err)
	}
	if !exists {
		return nil, fmt.Errorf("OSS中不存在配置文件: %s", configPath)
	}

	// 下载文件
	body, err := loader.bucket.GetObject(configPath)
	if err != nil {
		return nil, fmt.Errorf("从OSS下载文件失败: %v", err)
	}
	defer body.Close()

	// 读取文件内容
	data, err := io.ReadAll(body)
	if err != nil {
		return nil, fmt.Errorf("读取OSS文件内容失败: %v", err)
	}

	return data, nil
}

// decryptConfig 解密配置数据
func (loader *SecureOSSLoader) decryptConfig(encryptedData []byte) ([]byte, error) {
	// Base64解码
	decodedData, err := base64.StdEncoding.DecodeString(string(encryptedData))
	if err != nil {
		return nil, fmt.Errorf("Base64解码失败: %v", err)
	}

	// 检查数据长度
	if len(decodedData) < 12 { // nonce(12) + tag(16) + data(>=1)
		return nil, fmt.Errorf("加密数据长度不足")
	}

	// 提取nonce和密文
	nonce := decodedData[:12]
	ciphertext := decodedData[12:]

	// 创建AES-GCM解密器
	block, err := aes.NewCipher([]byte(loader.credentials.EncryptionKey))
	if err != nil {
		return nil, fmt.Errorf("创建AES密码器失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建GCM模式失败: %v", err)
	}

	// 解密数据
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("解密数据失败: %v", err)
	}

	return plaintext, nil
}

// ParseConfig 解析配置数据并解密敏感字段（公开方法）
func (loader *SecureOSSLoader) ParseConfig(configData []byte) (*Config, error) {
	return loader.parseConfig(configData)
}

// parseConfig 解析配置数据并解密敏感字段
func (loader *SecureOSSLoader) parseConfig(configData []byte) (*Config, error) {
	// 使用viper解析YAML配置
	v := viper.New()
	v.SetConfigType("yaml")

	if err := v.ReadConfig(strings.NewReader(string(configData))); err != nil {
		return nil, fmt.Errorf("解析YAML配置失败: %v", err)
	}

	// 转换为Config结构体
	config := &Config{}
	if err := v.Unmarshal(config); err != nil {
		return nil, fmt.Errorf("转换配置结构失败: %v", err)
	}

	// 解密敏感配置项
	if err := loader.decryptSensitiveFields(config); err != nil {
		return nil, fmt.Errorf("解密敏感配置失败: %v", err)
	}

	return config, nil
}

// UploadConfig 加密并上传配置到OSS
func (loader *SecureOSSLoader) UploadConfig(config *Config, configPath string) error {

	// 创建配置深拷贝以避免修改原始配置
	configCopy, err := loader.deepCopyConfig(config)
	if err != nil {
		return fmt.Errorf("创建配置副本失败: %v", err)
	}

	// 第一层加密：加密带ENC:前缀的敏感字段
	if err := loader.encryptSensitiveFields(configCopy); err != nil {
		return fmt.Errorf("加密敏感字段失败: %v", err)
	}

	// 序列化配置为YAML
	configData, err := loader.serializeConfig(configCopy)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	// 第二层加密：加密整个配置数据
	encryptedData, err := loader.encryptConfig(configData)
	if err != nil {
		return fmt.Errorf("加密配置失败: %v", err)
	}

	// 上传到OSS
	if err := loader.uploadConfig(encryptedData, configPath); err != nil {
		return fmt.Errorf("上传配置到OSS失败: %v", err)
	}

	return nil
}

// deepCopyConfig 创建配置的深拷贝
func (loader *SecureOSSLoader) deepCopyConfig(config *Config) (*Config, error) {
	// 通过序列化和反序列化来实现深拷贝
	data, err := yaml.Marshal(config)
	if err != nil {
		return nil, fmt.Errorf("序列化配置失败: %v", err)
	}

	var configCopy Config
	if err := yaml.Unmarshal(data, &configCopy); err != nil {
		return nil, fmt.Errorf("反序列化配置失败: %v", err)
	}

	return &configCopy, nil
}

// SerializeConfig 序列化配置为YAML并加密敏感字段（公开方法）
func (loader *SecureOSSLoader) SerializeConfig(config *Config) ([]byte, error) {
	return loader.serializeConfig(config)
}

// EncryptSensitiveFields 公开方法：加密敏感字段（包括SumPay字段）
func (loader *SecureOSSLoader) EncryptSensitiveFields(config *Config) error {
	// 设置默认加密密钥用于测试
	if loader.credentials == nil {
		loader.credentials = &OSSCredentials{
			EncryptionKey: "12345678901234567890123456789012", // 32字符测试密钥
		}
	}
	// 加密所有敏感字段（包括SumPay字段）
	return loader.encryptSensitiveFields(config)
}

// DecryptSensitiveFields 公开方法：解密敏感字段（包括SumPay字段）
func (loader *SecureOSSLoader) DecryptSensitiveFields(config *Config) error {
	// 设置默认加密密钥用于测试
	if loader.credentials == nil {
		loader.credentials = &OSSCredentials{
			EncryptionKey: "12345678901234567890123456789012", // 32字符测试密钥
		}
	}
	// 解密所有敏感字段（包括SumPay字段）
	return loader.decryptSensitiveFields(config)
}

// serializeConfig 序列化配置为YAML并加密敏感字段
func (loader *SecureOSSLoader) serializeConfig(config *Config) ([]byte, error) {
	// 创建配置副本以避免修改原始配置
	configCopy := *config

	// 加密敏感配置项
	if err := loader.encryptSensitiveFields(&configCopy); err != nil {
		return nil, fmt.Errorf("加密敏感配置失败: %v", err)
	}

	// 使用YAML序列化
	data, err := yaml.Marshal(&configCopy)
	if err != nil {
		return nil, fmt.Errorf("序列化配置失败: %v", err)
	}

	return data, nil
}

// decryptSensitiveFields 解密配置中的敏感字段并移除ENC:前缀
// 用于从OSS下载后的后处理，解密加密字段并移除ENC:前缀标识
func (loader *SecureOSSLoader) decryptSensitiveFields(config *Config) error {
	return loader.decryptFieldsAndRemovePrefix(reflect.ValueOf(config).Elem())
}

// decryptFieldsAndRemovePrefix 递归解密字段并移除ENC:前缀
func (loader *SecureOSSLoader) decryptFieldsAndRemovePrefix(v reflect.Value) error {
	if !v.IsValid() || !v.CanSet() {
		return nil
	}

	switch v.Kind() {
	case reflect.String:
		// 处理字符串字段，解密并移除ENC:前缀
		str := v.String()
		if str != "" {
			if strings.HasPrefix(str, "ENC:") {
				// 解密带ENC:前缀的字段
				if decrypted, err := loader.decryptSensitiveValue(str); err == nil {
					// 解密成功，设置为解密后的明文值（不带ENC:前缀）
					v.SetString(decrypted)
				}
				// 解密失败保持原值不变
			} else {
				// 尝试解密可能的Base64编码密文（没有ENC:前缀的情况）
				if loader.isLikelyEncryptedValue(str) {
					if decrypted, err := loader.decryptBase64Value(str); err == nil {
						v.SetString(decrypted)
					}
				}
			}
		}

	case reflect.Struct:
		// 递归处理结构体字段
		for i := 0; i < v.NumField(); i++ {
			field := v.Field(i)
			if field.CanSet() {
				if err := loader.decryptFieldsAndRemovePrefix(field); err != nil {
					return err
				}
			}
		}

	case reflect.Ptr:
		// 处理指针字段
		if !v.IsNil() {
			if err := loader.decryptFieldsAndRemovePrefix(v.Elem()); err != nil {
				return err
			}
		}

	case reflect.Slice, reflect.Array:
		// 处理切片和数组
		for i := 0; i < v.Len(); i++ {
			if err := loader.decryptFieldsAndRemovePrefix(v.Index(i)); err != nil {
				return err
			}
		}

	case reflect.Map:
		// 处理映射
		for _, key := range v.MapKeys() {
			mapValue := v.MapIndex(key)
			if mapValue.Kind() == reflect.Interface {
				mapValue = mapValue.Elem()
			}
			if mapValue.IsValid() && mapValue.CanSet() {
				if err := loader.decryptFieldsAndRemovePrefix(mapValue); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

// encryptSensitiveFields 加密配置中带有ENC:前缀的敏感字段
// 用于上传OSS前的预处理，只加密带ENC:前缀的字段
func (loader *SecureOSSLoader) encryptSensitiveFields(config *Config) error {
	return loader.encryptFieldsWithPrefix(reflect.ValueOf(config).Elem())
}

// encryptFieldsWithPrefix 递归加密带有ENC:前缀的字段
func (loader *SecureOSSLoader) encryptFieldsWithPrefix(v reflect.Value) error {
	if !v.IsValid() || !v.CanSet() {
		return nil
	}

	switch v.Kind() {
	case reflect.String:
		// 处理字符串字段，如果有ENC:前缀则加密
		str := v.String()
		if strings.HasPrefix(str, "ENC:") {
			// 移除ENC:前缀并加密
			plaintext := strings.TrimPrefix(str, "ENC:")
			if plaintext != "" {
				encrypted, err := loader.encryptSensitiveValue(plaintext)
				if err != nil {
					return fmt.Errorf("加密字段失败: %v", err)
				}
				v.SetString(encrypted)
			}
		}

	case reflect.Struct:
		// 递归处理结构体字段
		for i := 0; i < v.NumField(); i++ {
			field := v.Field(i)
			if field.CanSet() {
				if err := loader.encryptFieldsWithPrefix(field); err != nil {
					return err
				}
			}
		}

	case reflect.Ptr:
		// 处理指针字段
		if !v.IsNil() {
			if err := loader.encryptFieldsWithPrefix(v.Elem()); err != nil {
				return err
			}
		}

	case reflect.Slice, reflect.Array:
		// 处理切片和数组
		for i := 0; i < v.Len(); i++ {
			if err := loader.encryptFieldsWithPrefix(v.Index(i)); err != nil {
				return err
			}
		}

	case reflect.Map:
		// 处理映射
		for _, key := range v.MapKeys() {
			mapValue := v.MapIndex(key)
			if mapValue.Kind() == reflect.Interface {
				mapValue = mapValue.Elem()
			}
			if mapValue.IsValid() && mapValue.CanSet() {
				if err := loader.encryptFieldsWithPrefix(mapValue); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

// encryptConfig 加密配置数据
func (loader *SecureOSSLoader) encryptConfig(plaintext []byte) ([]byte, error) {
	// 创建AES-GCM加密器
	block, err := aes.NewCipher([]byte(loader.credentials.EncryptionKey))
	if err != nil {
		return nil, fmt.Errorf("创建AES密码器失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建GCM模式失败: %v", err)
	}

	// 生成随机nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("生成随机nonce失败: %v", err)
	}

	// 加密数据
	ciphertext := gcm.Seal(nonce, nonce, plaintext, nil)

	// Base64编码
	encodedData := base64.StdEncoding.EncodeToString(ciphertext)

	return []byte(encodedData), nil
}

// uploadConfig 上传配置到OSS
func (loader *SecureOSSLoader) uploadConfig(encryptedData []byte, configPath string) error {
	// 上传文件到OSS
	err := loader.bucket.PutObject(configPath, strings.NewReader(string(encryptedData)))
	if err != nil {
		return fmt.Errorf("上传文件到OSS失败: %v", err)
	}

	return nil
}

// ValidateConnection 验证OSS连接
func (loader *SecureOSSLoader) ValidateConnection() error {
	// 尝试列出存储桶中的对象
	_, err := loader.bucket.ListObjects(oss.MaxKeys(1))
	if err != nil {
		return fmt.Errorf("OSS连接验证失败: %v", err)
	}

	return nil
}

// ListConfigs 列出OSS中的配置文件
func (loader *SecureOSSLoader) ListConfigs(prefix string) ([]string, error) {
	var configs []string

	// 列出指定前缀的对象
	result, err := loader.bucket.ListObjects(oss.Prefix(prefix))
	if err != nil {
		return nil, fmt.Errorf("列出OSS对象失败: %v", err)
	}

	for _, object := range result.Objects {
		configs = append(configs, object.Key)
	}

	return configs, nil
}

// BackupConfig 备份OSS配置到本地
func (loader *SecureOSSLoader) BackupConfig(configPath, backupPath string) error {

	// 从OSS加载配置
	config, err := loader.LoadConfig(configPath)
	if err != nil {
		return fmt.Errorf("加载OSS配置失败: %v", err)
	}

	// 序列化配置
	configData, err := loader.serializeConfig(config)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	// 确保备份目录存在
	backupDir := filepath.Dir(backupPath)
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return fmt.Errorf("创建备份目录失败: %v", err)
	}

	// 写入备份文件
	if err := os.WriteFile(backupPath, configData, 0600); err != nil {
		return fmt.Errorf("写入备份文件失败: %v", err)
	}

	return nil
}

// ==================== 增强功能方法 ====================

// LoadConfigWithSumPayDecryption 加载配置并解密SumPay敏感字段
func (loader *SecureOSSLoader) LoadConfigWithSumPayDecryption(configPath string) (*Config, error) {
	// 首先加载基础配置
	config, err := loader.LoadConfig(configPath)
	if err != nil {
		return nil, err
	}

	// 解密所有敏感字段（包括SumPay字段）
	if err := loader.DecryptSensitiveFields(config); err != nil {
		fmt.Println("解密敏感字段失败: " + err.Error())
	}

	return config, nil
}

// SaveEncryptedFincoreConfig 加密保存fincore.yml配置到OSS
func (loader *SecureOSSLoader) SaveEncryptedFincoreConfig(credentials *OSSCredentials, ossPath string) error {
	// 序列化凭证
	credentialsData, err := yaml.Marshal(credentials)
	if err != nil {
		return fmt.Errorf("序列化OSS凭证失败: %v", err)
	}

	// 使用硬编码密钥加密
	encryptedData, err := loader.encryptWithHardcodedKey(credentialsData)
	if err != nil {
		return fmt.Errorf("加密OSS凭证失败: %v", err)
	}

	// 上传到OSS
	err = loader.bucket.PutObject(ossPath, strings.NewReader(string(encryptedData)))
	if err != nil {
		return fmt.Errorf("上传加密凭证到OSS失败: %v", err)
	}

	return nil
}

// LoadEncryptedFincoreConfig 从OSS加载并解密fincore.yml配置
func (loader *SecureOSSLoader) LoadEncryptedFincoreConfig(ossPath string) (*OSSCredentials, error) {
	// 从OSS下载加密数据
	body, err := loader.bucket.GetObject(ossPath)
	if err != nil {
		return nil, fmt.Errorf("从OSS下载加密凭证失败: %v", err)
	}
	defer body.Close()

	encryptedData, err := io.ReadAll(body)
	if err != nil {
		return nil, fmt.Errorf("读取加密凭证数据失败: %v", err)
	}

	// 使用硬编码密钥解密
	credentialsData, err := loader.decryptWithHardcodedKey(encryptedData)
	if err != nil {
		return nil, fmt.Errorf("解密OSS凭证失败: %v", err)
	}

	// 反序列化凭证
	var credentials OSSCredentials
	if err := yaml.Unmarshal(credentialsData, &credentials); err != nil {
		return nil, fmt.Errorf("反序列化OSS凭证失败: %v", err)
	}

	return &credentials, nil
}

// encryptWithHardcodedKey 使用硬编码密钥加密数据
func (loader *SecureOSSLoader) encryptWithHardcodedKey(plaintext []byte) ([]byte, error) {
	key := []byte(FINCORE_CONFIG_ENCRYPT_KEY)

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}

	ciphertext := gcm.Seal(nonce, nonce, plaintext, nil)
	return []byte(base64.StdEncoding.EncodeToString(ciphertext)), nil
}

// decryptWithHardcodedKey 使用硬编码密钥解密数据
func (loader *SecureOSSLoader) decryptWithHardcodedKey(ciphertext []byte) ([]byte, error) {
	key := []byte(FINCORE_CONFIG_ENCRYPT_KEY)

	// Base64解码
	data, err := base64.StdEncoding.DecodeString(string(ciphertext))
	if err != nil {
		return nil, err
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return nil, fmt.Errorf("密文数据太短")
	}

	nonce, ciphertext := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, err
	}

	return plaintext, nil
}

// isEncryptedData 检查数据是否为加密格式（base64编码）
func (loader *SecureOSSLoader) isEncryptedData(data []byte) bool {
	// 检查是否为有效的base64编码
	_, err := base64.StdEncoding.DecodeString(string(data))
	if err != nil {
		return false
	}

	// 检查是否包含YAML格式的明文标识符
	str := string(data)
	if strings.Contains(str, "endpoint:") || strings.Contains(str, "access_key_id:") {
		return false
	}

	return true
}

// EncryptWithHardcodedKey 公开方法：使用硬编码密钥加密数据
func (loader *SecureOSSLoader) EncryptWithHardcodedKey(plaintext []byte) ([]byte, error) {
	return loader.encryptWithHardcodedKey(plaintext)
}

// encryptSensitiveValue 加密单个敏感值
func (loader *SecureOSSLoader) encryptSensitiveValue(value string) (string, error) {
	if value == "" {
		return value, nil
	}

	// 检查是否已经加密（以特定前缀标识）
	if strings.HasPrefix(value, "ENC:") {
		return value, nil // 已经加密，直接返回
	}

	encrypted, err := loader.encryptWithHardcodedKey([]byte(value))
	if err != nil {
		return "", err
	}

	// 添加加密标识前缀
	return "ENC:" + base64.StdEncoding.EncodeToString(encrypted), nil
}

// decryptSensitiveValue 解密单个敏感值
func (loader *SecureOSSLoader) decryptSensitiveValue(value string) (string, error) {
	if value == "" {
		return value, nil
	}

	// 检查是否为加密值
	if !strings.HasPrefix(value, "ENC:") {
		return value, nil // 未加密，直接返回
	}

	// 移除加密标识前缀
	encryptedValue := strings.TrimPrefix(value, "ENC:")

	// Base64解码
	ciphertext, err := base64.StdEncoding.DecodeString(encryptedValue)
	if err != nil {
		return "", fmt.Errorf("Base64解码失败: %v", err)
	}

	decrypted, err := loader.decryptWithHardcodedKey(ciphertext)
	if err != nil {
		return "", err
	}

	return string(decrypted), nil
}

// CleanupTempFiles 清理临时文件
func (loader *SecureOSSLoader) CleanupTempFiles() error {
	return os.RemoveAll(loader.tempDir)
}

// GetTempDir 获取临时目录路径
func (loader *SecureOSSLoader) GetTempDir() string {
	return loader.tempDir
}

// isLikelyEncryptedValue 判断字符串是否可能是加密的Base64值
func (loader *SecureOSSLoader) isLikelyEncryptedValue(value string) bool {
	// 检查是否是有效的Base64编码
	if _, err := base64.StdEncoding.DecodeString(value); err != nil {
		return false
	}

	// 检查长度是否合理（加密后的数据通常比较长）
	if len(value) < 16 {
		return false
	}

	// 检查是否包含Base64字符
	for _, char := range value {
		if !((char >= 'A' && char <= 'Z') || (char >= 'a' && char <= 'z') ||
			(char >= '0' && char <= '9') || char == '+' || char == '/' || char == '=') {
			return false
		}
	}

	return true
}

// decryptBase64Value 直接解密Base64编码的密文
func (loader *SecureOSSLoader) decryptBase64Value(value string) (string, error) {
	// Base64解码
	ciphertext, err := base64.StdEncoding.DecodeString(value)
	if err != nil {
		return "", fmt.Errorf("Base64解码失败: %v", err)
	}

	// 使用硬编码密钥解密
	decryptedData, err := loader.decryptWithHardcodedKey(ciphertext)
	if err != nil {
		return "", fmt.Errorf("解密失败: %v", err)
	}

	return string(decryptedData), nil
}
