package ipUtil

import (
	"testing"
)

func TestInitIPUtil(t *testing.T) {
	if err := InitIPUtil("../../resource/developer/qqwry.dat"); err != nil {
		t.Errorf("InitIPUtil() error = %v", err)
	}
}

func TestGetIpLocation(t *testing.T) {
	InitIPUtil("../../resource/developer/qqwry.dat")
	ip := "***************"
	//ip := "************"
	region, err := GetIpLocation(ip)

	if err != nil {
		t.Errorf("GetIpLocation() error = %v", err)
	}
	if region == "" {
		t.<PERSON>rrorf("GetIpLocation() region = %v", region)
	}
	if region != "中国|江苏|南京||南京信风网络科技有限公司GreatbitDNS服务器" {
		t.Errorf("GetIpLocation() region = %v", region)
	}
}

func TestGetIpLocation2(t *testing.T) {
	InitIPUtil("../../resource/developer/qqwry.dat")

	ip := "************"
	region, err := GetIpLocation(ip)

	if err != nil {
		t.Errorf("GetIpLocation() error = %v", err)
	}
	if region == "" {
		t.Errorf("GetIpLocation() region = %v", region)
	}
	if region != "局域网||||局域网" {
		t.Errorf("GetIpLocation() region = %v", region)
	}
}

func TestGetIpLocation3(t *testing.T) {
	InitIPUtil("../../resource/developer/qqwry.dat")

	ip := "***************"

	region, err := GetIpLocation(ip)

	if err != nil {
		t.Errorf("GetIpLocation() error = %v", err)
	}
	if region == "" {
		t.Errorf("GetIpLocation() region = %v", region)
	}
	if region != "中国|广东|广州||移动" {
		t.Errorf("GetIpLocation() region = %v", region)
	}
}
