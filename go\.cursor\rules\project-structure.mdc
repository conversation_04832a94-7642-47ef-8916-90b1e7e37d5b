---
alwaysApply: true
---
# FinCore项目结构

这是一个Go语言开发的金融核心系统，主要包含以下模块：

## 目录结构
- `src/app` - 应用核心模块，包含各业务模块的实现
  - `admin` - 管理端接口
  - `business` - 业务端接口，包含订单、支付、客户管理等
  - `uniapp` - 移动端接口
  - `dian<PERSON>qian` - 电子签名模块
- `src/model` - 数据模型定义
- `src/utils` - 工具包
- `src/thirdparty` - 第三方集成服务
- `src/route` - 路由配置
- `src/global` - 全局配置
- `src/bootstrap` - 应用启动配置

## 主要入口文件
- [src/main.go](mdc:src/main.go) - 应用程序入口
- [src/bootstrap/router.go](mdc:src/bootstrap/router.go) - 路由引导
- [src/route/router.go](mdc:src/route/router.go) - 路由定义

## 配置文件
- [src/resource/config.yml](mdc:src/resource/config.yml) - 主配置文件
# FinCore项目结构

这是一个Go语言开发的金融核心系统，主要包含以下模块：

## 目录结构
- `src/app` - 应用核心模块，包含各业务模块的实现
  - `admin` - 管理端接口
  - `business` - 业务端接口，包含订单、支付、客户管理等
  - `uniapp` - 移动端接口
  - `dianziqian` - 电子签名模块
- `src/model` - 数据模型定义
- `src/utils` - 工具包
- `src/thirdparty` - 第三方集成服务
- `src/route` - 路由配置
- `src/global` - 全局配置
- `src/bootstrap` - 应用启动配置

## 主要入口文件
- [src/main.go](mdc:src/main.go) - 应用程序入口
- [src/bootstrap/router.go](mdc:src/bootstrap/router.go) - 路由引导
- [src/route/router.go](mdc:src/route/router.go) - 路由定义

## 配置文件
- [src/resource/config.yml](mdc:src/resource/config.yml) - 主配置文件
