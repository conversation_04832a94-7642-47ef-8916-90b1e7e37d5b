#!/bin/bash

# Nginx配置部署脚本

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "=== Nginx配置部署脚本 ==="

# 检查权限
if [[ $EUID -ne 0 ]]; then
    log_error "此脚本需要root权限运行"
    log_info "请使用: sudo $0"
    exit 1
fi

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
NGINX_CONF_DIR="/etc/nginx/sites-available"
NGINX_ENABLED_DIR="/etc/nginx/sites-enabled"
NGINX_HTML_DIR="/opt/fincore/nginx/html"

echo ""
echo "=== 步骤1: 检查Nginx安装 ==="

if ! command -v nginx >/dev/null 2>&1; then
    log_error "Nginx未安装"
    log_info "安装Nginx..."
    
    if command -v apt-get >/dev/null 2>&1; then
        apt-get update
        apt-get install -y nginx
    elif command -v yum >/dev/null 2>&1; then
        yum install -y nginx
    elif command -v dnf >/dev/null 2>&1; then
        dnf install -y nginx
    else
        log_error "无法自动安装Nginx，请手动安装"
        exit 1
    fi
    
    log_success "Nginx安装完成"
else
    log_success "Nginx已安装"
fi

echo ""
echo "=== 步骤2: 创建目录结构 ==="

# 创建必要目录
mkdir -p "$NGINX_HTML_DIR"
mkdir -p "/var/log/nginx"

log_success "目录创建完成"

echo ""
echo "=== 步骤3: 跳过SSL证书配置 ==="

log_info "当前配置为HTTP模式，跳过SSL证书配置"

echo ""
echo "=== 步骤3: 部署Nginx配置 ==="

# 创建sites-available和sites-enabled目录（如果不存在）
mkdir -p "$NGINX_CONF_DIR"
mkdir -p "$NGINX_ENABLED_DIR"

# 复制配置文件
CONFIG_FILES=("business.conf" "uniapp.conf")

for config_file in "${CONFIG_FILES[@]}"; do
    if [[ -f "$SCRIPT_DIR/$config_file" ]]; then
        cp "$SCRIPT_DIR/$config_file" "$NGINX_CONF_DIR/"
        log_success "复制配置文件: $config_file"
        
        # 创建软链接到sites-enabled
        if [[ ! -L "$NGINX_ENABLED_DIR/$config_file" ]]; then
            ln -sf "$NGINX_CONF_DIR/$config_file" "$NGINX_ENABLED_DIR/"
            log_success "启用配置: $config_file"
        fi
    else
        log_error "配置文件不存在: $config_file"
    fi
done

echo ""
echo "=== 步骤5: 测试Nginx配置 ==="

# 测试配置文件语法
if nginx -t; then
    log_success "Nginx配置语法检查通过"
else
    log_error "Nginx配置语法错误"
    exit 1
fi

echo ""
echo "=== 步骤6: 重启Nginx服务 ==="

# 启动并启用Nginx服务
systemctl enable nginx
systemctl restart nginx

if systemctl is-active nginx >/dev/null 2>&1; then
    log_success "Nginx服务启动成功"
else
    log_error "Nginx服务启动失败"
    systemctl status nginx
    exit 1
fi

echo ""
echo "=== 部署完成 ==="
