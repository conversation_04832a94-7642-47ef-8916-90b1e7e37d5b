package payment

import (
	"encoding/base64"
	"fmt"
	"math/rand"
	"time"

	"github.com/shopspring/decimal"
)

// GenerateRefundOrderNo 生成退款订单号
// 格式: R + 时间戳 + 随机数
// 例如: R158315313614859403
func GenerateRefundOrderNo() string {
	timestamp := time.Now().UnixNano() / 1000000 // 毫秒时间戳
	randomNum := rand.Intn(1000)                 // 3位随机数
	return fmt.Sprintf("R%d%03d", timestamp, randomNum)
}

// GenerateRefundTransactionNo 生成退款流水号
// 格式: RF + 时间戳 + 随机数
// 例如: RF1703123456789000001
func GenerateRefundTransactionNo() string {
	timestamp := time.Now().UnixNano()
	randomNum := rand.Intn(1000000)
	return fmt.Sprintf("RF%d%06d", timestamp, randomNum)
}

// EncodeNotifyURL 对回调URL进行base64编码
func EncodeNotifyURL(url string) string {
	return base64.StdEncoding.EncodeToString([]byte(url))
}

// DecodeNotifyURL 对回调URL进行base64解码
func DecodeNotifyURL(encodedURL string) (string, error) {
	decoded, err := base64.StdEncoding.DecodeString(encodedURL)
	if err != nil {
		return "", fmt.Errorf("解码回调URL失败: %v", err)
	}
	return string(decoded), nil
}

// CalculatePaidAmountDistribution 计算已还金额在担保费和资管费之间的分配
// 参数：paidAmount-已还金额，totalWaiveAmount-减免金额，dueGuaranteeFee-应还担保费，dueAssetFee-应还资管费，totalDueAmount-总应还金额
// 返回：paidGuaranteeAmount-已还担保费，paidAssetAmount-已还资管费
func CalculatePaidAmountDistribution(paidAmount, totalWaiveAmount, dueGuaranteeFee, dueAssetFee, totalDueAmount float64) (paidGuaranteeAmount, paidAssetAmount float64) {
	// 使用decimal进行高精度计算
	paidAmountDecimal := decimal.NewFromFloat(paidAmount)
	totalWaiveAmountDecimal := decimal.NewFromFloat(totalWaiveAmount)
	dueGuaranteeFeeDecimal := decimal.NewFromFloat(dueGuaranteeFee)
	totalDueAmountDecimal := decimal.NewFromFloat(totalDueAmount)

	// 当期已还金额 = paid_amount + total_waive_amount
	actualPaidAmountDecimal := paidAmountDecimal.Add(totalWaiveAmountDecimal)

	// 如果总应还金额为0，直接返回0
	if totalDueAmountDecimal.LessThanOrEqual(decimal.Zero) {
		return 0, 0
	}

	// 计算担保费占总应还金额的比例
	guaranteeRatioDecimal := dueGuaranteeFeeDecimal.Div(totalDueAmountDecimal)

	// 已还担保金额 = 当期已还金额 * 担保费比例
	paidGuaranteeAmountDecimal := actualPaidAmountDecimal.Mul(guaranteeRatioDecimal)

	// 已还资管金额 = 当期已还金额 - 已还担保金额
	paidAssetAmountDecimal := actualPaidAmountDecimal.Sub(paidGuaranteeAmountDecimal)

	// 确保金额不为负数
	if paidGuaranteeAmountDecimal.LessThan(decimal.Zero) {
		paidGuaranteeAmountDecimal = decimal.Zero
	}
	if paidAssetAmountDecimal.LessThan(decimal.Zero) {
		paidAssetAmountDecimal = decimal.Zero
	}

	paidGuaranteeAmount, _ = paidGuaranteeAmountDecimal.Float64()
	paidAssetAmount, _ = paidAssetAmountDecimal.Float64()
	return paidGuaranteeAmount, paidAssetAmount
}
