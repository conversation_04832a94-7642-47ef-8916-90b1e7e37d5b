package statistics

import "fincore/utils/jsonschema"

// GetChannelStatisticsSchema 渠道统计列表查询参数验证规则
func GetChannelStatisticsSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "渠道统计列表查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"date": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "统计日期（YYYY-MM-DD格式，可选）",
			},
			"page": {
				Type:        "number",
				Required:    false,
				Description: "页码",
				Default:     1,
			},
			"page_size": {
				Type:        "number",
				Required:    false,
				Description: "每页数量",
				Default:     10,
			},
		},
		Required: []string{},
	}
}

// GetHomeStatisticsSchema 首页数据统计查询参数验证规则
func GetHomeStatistics() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "首页数据统计查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"date_begin": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "统计日期（YYYY-MM-DD格式，可选）",
			},
			"date_end": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "统计日期（YYYY-MM-DD格式，可选）",
			},
		},
		Required: []string{},
	}
}

// GetTrendStatisticsSchema 趋势统计查询参数验证规则
func GetTrendStatisticsSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "趋势统计查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"days": {
				Type:        "number",
				Required:    true,
				Enum:        []string{"7", "30"},
				Description: "查询天数，支持7日或30日",
			},
		},
		Required: []string{"days"},
	}
}

// GetIncomeDetailsSchema 收入明细查询参数验证规则
func GetIncomeDetailsSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "收入明细查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"order_no": {
				Type:        "string",
				Required:    false,
				Description: "订单编号",
			},
			"user_name": {
				Type:        "string",
				Required:    false,
				Description: "用户姓名",
			},
			"mobile": {
				Type:        "string",
				Required:    false,
				Pattern:     "^1[3-9]\\d{9}$",
				Description: "手机号",
			},
			"fund_type": {
				Type:        "number",
				Required:    false,
				Enum:        []string{"0", "1"}, // 0: 收款 1: 退款
				Description: "款项类型，值只能为 0 - 1 的整数",
			},
			"payment_method": {
				Type:        "number",
				Required:    false,
				Enum:        []string{"0", "1", "2", "3", "4", "5", "6", "7"}, // 0-资管支付；1-资管代扣；2-担保支付；3-担保代扣；4-支付宝支付（线下）；5-微信支付（线下）；6-银行卡支付（线下）；7-信用卡支付（线下）
				Description: "收款方式，值只能为 0 - 7 的整数",
			},
			"payment_status": {
				Type:        "number",
				Required:    false,
				Enum:        []string{"0", "1", "2"}, // 0: 提前收款 1: 到期收款 2: 逾期收款
				Description: "收款状态，值只能为 0 - 2 的整数",
			},
			"payment_time_start": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "收款开始时间（YYYY-MM-DD）",
			},
			"payment_time_end": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "收款结束时间（YYYY-MM-DD）",
			},
			"bill_time_start": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "账单开始时间（YYYY-MM-DD）",
			},
			"bill_time_end": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "账单结束时间（YYYY-MM-DD）",
			},
			"page": {
				Type:        "number",
				Required:    false,
				Description: "页码",
				Default:     1,
			},
			"page_size": {
				Type:        "number",
				Required:    false,
				Description: "每页数量",
				Default:     10,
			},
		},
		Required: []string{},
	}
}

// GetChannelDueStatisticsSchema 渠道到期统计查询参数验证规则
func GetChannelDueStatisticsSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "渠道到期统计查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"channel_id": {
				Type:        "number",
				Required:    false,
				Description: "渠道ID（可选）",
			},
			"due_date_start": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "到期开始日期（YYYY-MM-DD格式，可选）",
			},
			"due_date_end": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "到期结束日期（YYYY-MM-DD格式，可选）",
			},
			"period_number": {
				Type:        "number",
				Required:    false,
				Description: "账单期数（可选）",
			},
			"is_new_user": {
				Type:        "number",
				Required:    false,
				Enum:        []string{"0", "1"}, // 0: 老用户 1: 新用户
				Description: "是否新用户，值只能为 0 或 1 的整数（可选）",
			},
			"page": {
				Type:        "number",
				Required:    false,
				Description: "页码",
				Default:     1,
			},
			"page_size": {
				Type:        "number",
				Required:    false,
				Description: "每页数量",
				Default:     10,
			},
		},
		Required: []string{}, // 所有参数都是可选的
	}
}
