<template>
	<!-- 页面容器 -->
	<view class="bind-account-page">
		<!-- 全新标签切换栏 -->
		<view class="new-tab-bar">
			<view v-for="(item, index) in tabList" :key="index" @click="switchTab(index)"
				:class="['tab-item', { active: currentTab === index }]">
				<text>{{ item.title }}</text>
			</view>
		</view>

		<!-- 表单内容（当前账户） -->
		<view class="form-container" v-show="currentTab === 0">
			<view class="form-item">
				<text class="label">当前手机号：</text>
				<input class="input" placeholder="请输入手机号" v-model="currentMobile" />
			</view>

			<view class="form-item">
				<text class="label">图形验证码：</text>
				<input class="input" v-model="graphicCode" />
				<div class="code-btn" @click="getGraphicCode">获取图形</div>
			</view>

			<view class="form-item">
				<text class="label">验证当前账户：</text>
				<input class="input" v-model="verifyCode" />
				<div class="code-btn" @click="sendVerifyCode">发送验证码</div>
			</view>

			<view class="form-item">
				<text class="label">新手机号：</text>
				<input class="input" placeholder="请输入手机号" v-model="newMobile" />
			</view>
			<button class="submit-btn" @click="startVerify">开始验证</button>
		</view>
		
		
		<!-- 紧急联系人表单 -->
		<view class="form-container" v-show="currentTab === 1">
			<view class="form-item">
				<text class="label">联系人姓名：</text>
				<input class="input"  placeholder="请输入姓名" v-model="contactName" />
			</view>
			<view class="form-item">
				<text class="label">联系人手机号：</text>
				<input class="input" placeholder="请输入手机号" v-model="contactPhone" />
			</view>
			<button class="submit-btn"  @click="saveContact">保存联系人</button>
		</view>
	</view>
</template>

<script setup>
	import {
		ref
	} from 'vue';
	import {
		onBackPress
	} from '@dcloudio/uni-app';

	// 标签数据
	const tabList = ref([{
			title: '当前账户'
		},
		{
			title: '紧急联系人'
		}
	]);
	// 当前激活的标签索引
	const currentTab = ref(0);

	// 切换标签
	const switchTab = (index) => {
		currentTab.value = index;
	};

	// 返回上一级页面
	const onBack = () => {
		uni.navigateBack();
	};

	// 监听安卓物理返回键（可选）
	onBackPress(() => {
		// onBack();
		// return true;
	});

	// 表单数据 - 当前账户
	const currentMobile = ref('');
	const graphicCode = ref('');
	const verifyCode = ref('');
	const newMobile = ref('');

	// 获取图形验证码
	const getGraphicCode = () => {
		uni.showToast({
			title: '获取图形验证码功能待实现',
			icon: 'none'
		});
	};

	// 发送验证短信
	const sendVerifyCode = () => {
		uni.showToast({
			title: '发送验证码功能待实现',
			icon: 'none'
		});
	};

	// 开始验证（换绑逻辑）
	const startVerify = () => {
		uni.showToast({
			title: '开始验证逻辑待完善',
			icon: 'none'
		});
	};

	// 表单数据 - 紧急联系人
	const contactName = ref('');
	const contactPhone = ref('');

	// 保存紧急联系人
	const saveContact = () => {
		uni.showToast({
			title: '保存紧急联系人功能待实现',
			icon: 'none'
		});
	};
</script>

<style scoped lang="scss">
	/* 页面整体样式 */
	page{
		background-color: #eff2f7;
	}
	.bind-account-page {
		font-size: 28rpx;
		color: #333;
	}

	.new-tab-bar {
		margin: 20rpx 20rpx 0;
		display: flex;
		border-bottom: 1px solid #f5f5f5;
		background-color: #fff;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 15rpx;
		overflow: hidden;
	}

	.tab-item {
		flex: 1;
		text-align: center;
		padding: 10px 0;
		color: #666;
		background-color: #ccc;
		transition: all 0.3s ease;
	}

	.tab-item.active {
		color: #000;
		background-color: #fff;
		font-weight: 500;
	}

	/* 表单容器样式 */
	.form-container {
		background-color: #fff;
		padding: 18px;
		margin: 0 20rpx;
		border-bottom-left-radius: 15rpx;
		border-bottom-right-radius: 15rpx;
	}

	/* 表单项样式 */
	.form-item {
		display: flex;
		align-items: center;
		border-bottom: 1px solid #efefef;
		padding: 0 20rpx;
	}

	.label {
		text-align: left;
		color: #666;
		margin-right: 15rpx;
	}

	.input {
		flex: 1;
		height: 100rpx;
		text-align: right;
		border: none;
		outline: none;
		font-size: 28rpx;
		color: #999;
	}

	/* 验证码按钮样式 */
	.code-btn {
		width: 78px;
		height: 30px;
		line-height: 30px;
		text-align: right;
		color: #0099ff;
		border-radius: 4px;
		font-size: 12px;
		margin-left: 8px;
	}

	/* 提交按钮样式 */
	.submit-btn {
		width: 100%;
		height: 48px;
		line-height: 48px;
		background-color: #0099ff;
		color: #fff;
		border-radius: 30px;
		font-size: 17px;
		margin-top: 180px;
	}
</style>