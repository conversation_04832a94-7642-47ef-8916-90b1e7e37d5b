<script setup lang="ts">
import { reactive, ref } from 'vue';
import  { useRouter, useRoute } from 'vue-router';
import { getBankCardList } from '@/api/usermanage';
import type { FormInstance } from 'element-plus';
import { Message } from '@arco-design/web-vue';
import { getSubmittedAmounts, manualWithhold } from '@/api/ordermanagement';
const $route = useRoute();
let dialogFormCardVisible = ref(false)
const props = defineProps({
  orderDetail: {
    type: Object,
    required: true
  }
})
const formDK = reactive({
  bill_id: 0,
  bank_card_id: '',
  withhold_type: '',
  amount: '',
  remark: ''
});

function showCardDialog(id: number) {
  formDK.bill_id = id;
  dialogFormCardVisible.value = true
  getList()
}
const cardList = ref([]);
async function getList() {
  const cardRes = await getBankCardList({ customer_id: String($route.query.uid) });
  console.log(props.orderDetail);
  cardRes.list.forEach((item: any) => {
    item.user_name = props.orderDetail.user_name
  })
  cardList.value = cardRes.list || [];
}
defineExpose({
  showCardDialog
})
const formDKRef = ref();
let dialogFormDKVisible = ref(false);
const formTimeRef = ref<FormInstance>();

function handleShowDKTime(withhold_type: string, item: any) {
  formDK.withhold_type = withhold_type;
  formDK.bank_card_id = item.id;
  formDK.amount = '';
  getDBInfo();
  dialogFormDKVisible.value = true;
}
function handleHideDKTime() {
  dialogFormDKVisible.value = false;
}
const submittedAmountsInfo = ref({});
function getDBInfo() {
  getSubmittedAmounts(formDK.bill_id).then(res => {
    console.log(res);
    submittedAmountsInfo.value = res;
  })
}
const emit = defineEmits(['uploadList']);
function handleFromDKConfirm(formDKEf: FormInstance) {
  formDKEf.validate((valid) => {
    if (valid) {
      console.log(formDK);
      console.log('submit!');
      manualWithhold({ ...formDK, amount: Number(formDK.amount) }).then(res => {
        Message.success('操作成功');
        dialogFormDKVisible.value = false;
        dialogFormCardVisible.value = false;
        formDK.bill_id = 0
        formDK.bank_card_id= ''
        formDK.withhold_type= ''
        formDK.amount= ''
        formDK.remark= ''
        emit('uploadList')
      })
    } else {
      console.log('error submit!');
    }
  });
}

</script>

<template>
  <el-dialog v-model="dialogFormCardVisible" title="银行卡信息" width="800">
    <el-table :data="cardList" style="width: 100%" empty-text="暂无订单支付记录">
      <el-table-column type="index" label="No" width="50" />
      <el-table-column prop="user_name" label="持卡人姓名" width="120"/>
      <el-table-column prop="bank_name" label="所属银行"  width="120"/>
      <el-table-column prop="bank_card_no" label="银行卡号" width="180"/>
      <el-table-column prop="bank_phone" label="绑定手机号" width="120"/>
<!--      <el-table-column prop="user_name" label="扣款次数"  />
      <el-table-column prop="user_name" label="银行卡归属" width="100"/>-->
      <el-table-column prop="created_at" label="绑定时间" width="180"/>
      <el-table-column fixed="right" label="操作" width="200">
        <template #default="scope">
          <el-space>
<!--            <el-button type="primary" size="small">订单放款换绑</el-button>
            <el-button type="danger" size="small">担保刷新</el-button>
            <el-button type="primary" size="small">担保解绑</el-button>
            <el-button type="primary" size="small">资管刷新</el-button>
            <el-button type="primary" size="small">资管解绑</el-button>-->
            <el-button type="primary" size="small" @click="handleShowDKTime('asset', scope.row)">资管代扣</el-button>
            <el-button type="primary" size="small" @click="handleShowDKTime('guarantee', scope.row)">担保代扣</el-button>
<!--            <el-button type="primary" size="small">扣款次数增加</el-button>-->
          </el-space>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>


  <!--  代扣  -->
  <el-dialog v-model="dialogFormDKVisible" :title="formDK.withhold_type == 'asset'?'资管代扣':'担保代扣'" width="500">

    <el-descriptions>
<!--      <el-descriptions-item label="总金额">-->
<!--        {{ submittedAmountsInfo.total_amount }}-->
<!--      </el-descriptions-item>-->
      <template v-if="formDK.withhold_type == 'guarantee'">
        <el-descriptions-item label="已提交担保金额">
          {{ submittedAmountsInfo.guarantee_amount }}
        </el-descriptions-item>
        <el-descriptions-item label="剩余担保金额">
          {{ submittedAmountsInfo.remaining_guarantee_fee }}
        </el-descriptions-item>
      </template>
      <template v-if="formDK.withhold_type == 'asset'">
        <el-descriptions-item label="已提交资管金额">
          {{ submittedAmountsInfo.asset_amount }}
        </el-descriptions-item>
        <el-descriptions-item label="剩余资管金额">
          {{ submittedAmountsInfo.remaining_asset_fee }}
        </el-descriptions-item>
      </template>


    </el-descriptions>

    <el-form :model="formDK" ref="formDKRef">
      <el-form-item
        label="业务类型"
        label-width="140px"
        prop="withhold_type"
        :rules="[{ required: true, message: '业务类型必选' }]"
      >
        <el-select
          v-model="formDK.withhold_type"
          placeholder="请选择业务类型"
          disabled
        >
          <el-option label="资管代扣" value="asset" />
          <el-option label="担保代扣" value="guarantee" />
        </el-select>
      </el-form-item>
      <el-form-item
        label="代扣金额"
        label-width="140px"
        prop="amount"
        :rules="[{ required: true, message: '代扣金额必填' }]"
      >
        <el-input v-model="formDK.amount" :max="formDK.withhold_type=='asset'?submittedAmountsInfo.remaining_asset_fee:submittedAmountsInfo.remaining_guarantee_fee" type="number"/>
      </el-form-item>
      <el-form-item
        label="备注"
        label-width="140px"
        prop="remark"
      >
        <el-input type="textarea" v-model="formDK.remark"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogFormDKVisible = false">取消</el-button>
        <el-button type="primary" @click="handleFromDKConfirm(formDKRef)">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="less">

</style>