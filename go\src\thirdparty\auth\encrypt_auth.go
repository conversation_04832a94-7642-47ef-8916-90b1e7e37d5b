package auth

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/des"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
)

// EncryptAuth 加密认证器（风控服务）
type EncryptAuth struct {
	machineID string
	secretKey string
	algorithm string // 加密算法：aes, des
}

// NewEncryptAuth 创建加密认证器
func NewEncryptAuth(machineID, secretKey, algorithm string) *EncryptAuth {
	return &EncryptAuth{
		machineID: machineID,
		secretKey: secretKey,
		algorithm: algorithm,
	}
}

// GetAuthType 获取认证类型
func (e *EncryptAuth) GetAuthType() AuthType {
	return AuthTypeEncrypt
}

// Authenticate 执行认证，对请求内容进行加密并添加机器ID
func (e *EncryptAuth) Authenticate(ctx context.Context, req *http.Request, bodyData []byte) error {
	// 加密请求内容
	encryptedData, err := e.encryptData(bodyData)
	if err != nil {
		return fmt.Errorf("加密数据失败: %v", err)
	}

	// 构造新的请求体
	requestBody := map[string]interface{}{
		"machineId": e.machineID,
		"data":      encryptedData,
	}

	newData, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("编码加密数据失败: %v", err)
	}

	// 更新请求体
	req.Body = io.NopCloser(strings.NewReader(string(newData)))
	req.ContentLength = int64(len(newData))

	// 设置Content-Type
	req.Header.Set("Content-Type", "application/json")

	return nil
}

// IsTokenExpired 加密认证不需要token刷新
func (e *EncryptAuth) IsTokenExpired(err error) bool {
	return false
}

// RefreshToken 加密认证不需要token刷新
func (e *EncryptAuth) RefreshToken(ctx context.Context) error {
	return nil
}

// encryptData 根据算法加密数据
func (e *EncryptAuth) encryptData(data []byte) (string, error) {
	switch e.algorithm {
	case "aes":
		return e.encryptAES(data)
	case "des":
		return e.encryptDES(data)
	default:
		return e.encryptAES(data) // 默认使用AES
	}
}

// encryptAES AES加密
func (e *EncryptAuth) encryptAES(data []byte) (string, error) {
	key := []byte(e.secretKey)
	if len(key) < 16 {
		// 补齐到16位
		for len(key) < 16 {
			key = append(key, 0)
		}
	} else if len(key) > 32 {
		key = key[:32]
	} else if len(key) > 16 && len(key) < 32 {
		// 补齐到32位
		for len(key) < 32 {
			key = append(key, 0)
		}
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	// 使用CBC模式
	blockSize := block.BlockSize()
	padding := blockSize - len(data)%blockSize
	padtext := make([]byte, len(data)+padding)
	copy(padtext, data)
	for i := len(data); i < len(padtext); i++ {
		padtext[i] = byte(padding)
	}

	iv := make([]byte, blockSize)
	if _, err := rand.Read(iv); err != nil {
		return "", err
	}

	mode := cipher.NewCBCEncrypter(block, iv)
	ciphertext := make([]byte, len(padtext))
	mode.CryptBlocks(ciphertext, padtext)

	// 将IV和密文一起返回
	result := append(iv, ciphertext...)
	return base64.StdEncoding.EncodeToString(result), nil
}

// encryptDES DES加密
func (e *EncryptAuth) encryptDES(data []byte) (string, error) {
	key := []byte(e.secretKey)
	if len(key) < 8 {
		// 补齐到8位
		for len(key) < 8 {
			key = append(key, 0)
		}
	} else if len(key) > 8 {
		key = key[:8]
	}

	block, err := des.NewCipher(key)
	if err != nil {
		return "", err
	}

	// 使用CBC模式
	blockSize := block.BlockSize()
	padding := blockSize - len(data)%blockSize
	padtext := make([]byte, len(data)+padding)
	copy(padtext, data)
	for i := len(data); i < len(padtext); i++ {
		padtext[i] = byte(padding)
	}

	iv := make([]byte, blockSize)
	if _, err := rand.Read(iv); err != nil {
		return "", err
	}

	mode := cipher.NewCBCEncrypter(block, iv)
	ciphertext := make([]byte, len(padtext))
	mode.CryptBlocks(ciphertext, padtext)

	// 将IV和密文一起返回
	result := append(iv, ciphertext...)
	return base64.StdEncoding.EncodeToString(result), nil
}
