package service

import (
	"context"

	"fincore/thirdparty/client"
	"fincore/thirdparty/types"
)

// ThirdPartyService 第三方服务接口
type ThirdPartyService interface {
	// GetName 获取服务名称
	GetName() string
	// GetVersion 获取服务版本
	GetVersion() string
	// Initialize 初始化服务
	Initialize(config types.ServiceConfig) error
	// Call 调用服务
	Call(ctx context.Context, method string, params map[string]interface{}) (*types.Response, error)
	// IsHealthy 健康检查
	IsHealthy(ctx context.Context) bool
	// GetSupportedMethods 获取支持的方法列表
	GetSupportedMethods() []string
}

// BaseService 基础服务实现
type BaseService struct {
	name    string
	version string
	client  *client.HTTPClient
	config  types.ServiceConfig
	methods []string
}

// NewBaseService 创建基础服务
func NewBaseService(name, version string, httpClient *client.HTTPClient, config types.ServiceConfig) *BaseService {
	return &BaseService{
		name:    name,
		version: version,
		client:  httpClient,
		config:  config,
		methods: make([]string, 0),
	}
}

// GetName 获取服务名称
func (s *BaseService) GetName() string {
	return s.name
}

// GetVersion 获取服务版本
func (s *BaseService) GetVersion() string {
	return s.version
}

// GetSupportedMethods 获取支持的方法列表
func (s *BaseService) GetSupportedMethods() []string {
	return s.methods
}

// AddMethod 添加支持的方法
func (s *BaseService) AddMethod(method string) {
	s.methods = append(s.methods, method)
}

// IsHealthy 健康检查
func (s *BaseService) IsHealthy(ctx context.Context) bool {
	// 默认实现：发送健康检查请求
	healthPath := s.config.GetString("health_check_path")
	if healthPath == "" {
		return true // 如果没有配置健康检查路径，默认返回健康
	}

	req := &types.Request{
		Method: "GET",
		Path:   healthPath,
	}

	resp, err := s.client.Do(ctx, req)
	return err == nil && resp.Success
}

// GetClient 获取HTTP客户端
func (s *BaseService) GetClient() *client.HTTPClient {
	return s.client
}

// GetConfig 获取配置
func (s *BaseService) GetConfig() types.ServiceConfig {
	return s.config
}
