package goredis

import (
	"fincore/global"
	"time"

	"github.com/go-redis/redis/v8"
)

/**********1.配置**********/
var rclient *redis.Client

func InitRedisClient() {

	redisConfig := global.App.Config.Redis
	rclient = redis.NewClient(&redis.Options{
		Addr:        redisConfig.Host + ":" + redisConfig.Port,        // 连接地址
		Password:    redisConfig.Password,                             // 密码
		DB:          redisConfig.DB,                                   // 数据库编号
		DialTimeout: time.Duration(redisConfig.Timeout) * time.Second, // 链接超时
	})
}
func GetRedisClient() *redis.Client {
	if rclient == nil {
		InitRedisClient()
	}

	return rclient
}
