# 定时任务调度器模块

## 🌟 核心特性

- ✅ **代码即配置**: 任务定义在代码中，类型安全，编译时检查
- ✅ **Cron表达式支持**: 完整的 Cron 语法支持，精确到秒级
- ✅ **并发控制**: 支持单例和并行两种并发模式
- ✅ **超时管理**: 软超时和硬超时双重保护
- ✅ **重试机制**: 可配置的重试次数和间隔
- ✅ **生命周期管理**: 完整的任务生命周期钩子
- ✅ **高性能**: 1,538+ 任务/秒吞吐量，650μs 平均延迟
- ✅ **内存安全**: 无内存泄漏，无 Goroutine 泄漏
- ✅ **并发安全**: 经过严格的并发安全测试
- ✅ **生产就绪**: 完善的日志、监控和错误处理

## 📁 项目结构

```
app/scheduler/
├── config/                    # 配置管理
│   └── config.go             # 配置结构和加载逻辑
├── registry/                  # 任务注册中心
│   ├── registry.go           # 任务注册和管理
│   └── discovery.go          # 任务自动发现
├── engine/                    # 调度引擎
│   ├── scheduler.go          # 基于 gocron v2 的调度引擎
│   └── executor.go           # 任务执行器
├── tasks/                     # 任务接口和示例
│   ├── interface.go          # 任务接口定义
│   ├── errors.go             # 错误定义
│   └── examples/
│       └── data_cleanup_task.go  # 示例任务
├── manager.go                 # 调度器管理器
├── scheduler.go              # 模块入口
├── README.md                 # 使用文档
└── STRESS_TEST_REPORT.md     # 压力测试报告
```

## 🚀 快速开始

### 1. 创建任务

```go
package examples

import (
    "context"
    "fincore/app/scheduler/tasks"
    "time"
)

// MyTask 自定义任务
type MyTask struct {
    *tasks.BaseTask
}

// NewMyTask 创建任务实例
func NewMyTask() *MyTask {
    baseTask := tasks.NewBaseTask(
        "my_task",                    // 任务名称
        "我的定时任务",                // 任务描述
        "0 */6 * * *",               // 每6小时执行一次
        10*time.Minute,              // 10分钟超时
    )
    
    // 配置任务属性
    baseTask.SetConcurrencyMode(tasks.ConcurrencyModeSingleton).  // 单例模式
        SetRetryCount(3).                                         // 重试3次
        SetRetryInterval(5 * time.Second)                         // 重试间隔5秒
    
    return &MyTask{BaseTask: baseTask}
}

// Execute 实现任务执行逻辑
func (t *MyTask) Execute(ctx context.Context) error {
    // 检查上下文是否已取消
    select {
    case <-ctx.Done():
        return ctx.Err()
    default:
    }
    
    // 执行业务逻辑
    // ... 你的业务代码 ...
    
    return nil
}

// 可选：实现生命周期钩子
func (t *MyTask) OnStart(ctx context.Context) error {
    // 任务开始前的准备工作
    return nil
}

func (t *MyTask) OnSuccess(ctx context.Context) error {
    // 任务成功后的处理
    return nil
}

func (t *MyTask) OnError(ctx context.Context, err error) error {
    // 任务失败后的处理
    return nil
}

func (t *MyTask) OnComplete(ctx context.Context) error {
    // 任务完成后的清理工作
    return nil
}

// 注册任务（在 init 函数中）
func init() {
    scheduler.RegisterTask(NewMyTask())
}
```

### 2. 配置文件

创建 `resource/scheduler_dev.yml`:

```yaml
scheduler:
  name: "fincore-scheduler-dev"
  timezone: "Asia/Shanghai"
  max_concurrent_jobs: 10
  enable_metrics: true
  log_level: "info"
  
  # 任务执行配置
  task_execution:
    default_timeout: "30m"
    max_retry_count: 3
    retry_interval: "5s"
    
  # 性能配置
  performance:
    worker_pool_size: 20
    queue_buffer_size: 1000
    gc_interval: "1h"
```

### 3. 启动调度器

在 `main.go` 中集成：

```go
package main

import (
    "fincore/app/scheduler"
    "log"
)

func main() {
    // 初始化调度器
    if err := scheduler.Initialize(); err != nil {
        log.Fatalf("初始化调度器失败: %v", err)
    }
    
    // 启动调度器
    if err := scheduler.Start(); err != nil {
        log.Fatalf("启动调度器失败: %v", err)
    }
    
    // 你的应用程序逻辑...
    
    // 优雅关闭
    defer func() {
        if err := scheduler.Stop(); err != nil {
            log.Printf("停止调度器失败: %v", err)
        }
    }()
}
```

## 📖 详细使用指南

### 任务接口

所有任务必须实现 `TaskInterface` 接口：

```go
type TaskInterface interface {
    // 基本信息
    GetName() string
    GetDescription() string
    GetSchedule() string
    GetTimeout() time.Duration
    
    // 执行逻辑
    Execute(ctx context.Context) error
    
    // 配置属性
    GetConcurrencyMode() ConcurrencyMode
    GetRetryCount() int
    GetRetryInterval() time.Duration
    
    // 生命周期钩子（可选实现）
    OnStart(ctx context.Context) error
    OnSuccess(ctx context.Context) error
    OnError(ctx context.Context, err error) error
    OnComplete(ctx context.Context) error
}
```

### 并发模式

#### 单例模式 (Singleton)
```go
baseTask.SetConcurrencyMode(tasks.ConcurrencyModeSingleton)
```
- 同一任务同时只能有一个实例执行
- 适用于数据处理、文件操作等需要独占资源的任务

#### 并行模式 (Parallel)
```go
baseTask.SetConcurrencyMode(tasks.ConcurrencyModeParallel)
```
- 允许同一任务多个实例并行执行
- 适用于独立的计算任务、API调用等

### Cron 表达式

支持标准的 Cron 表达式格式：

```
┌─────────────── 秒 (0-59)
│ ┌───────────── 分 (0-59)
│ │ ┌─────────── 时 (0-23)
│ │ │ ┌───────── 日 (1-31)
│ │ │ │ ┌─────── 月 (1-12)
│ │ │ │ │ ┌───── 周 (0-6, 0=周日)
│ │ │ │ │ │
* * * * * *
```

**常用示例**:
- `0 0 2 * * *` - 每天凌晨2点执行
- `0 */30 * * * *` - 每30分钟执行
- `0 0 9 * * 1-5` - 工作日上午9点执行
- `0 0 0 1 * *` - 每月1号执行

### 错误处理

```go
func (t *MyTask) Execute(ctx context.Context) error {
    // 业务逻辑
    if err := doSomething(); err != nil {
        // 返回错误会触发重试机制
        return fmt.Errorf("执行失败: %w", err)
    }
    
    // 检查上下文取消
    select {
    case <-ctx.Done():
        return ctx.Err() // 超时或取消
    default:
    }
    
    return nil
}
```

## 🔧 高级功能

### 动态任务管理

```go
// 运行时注册任务
task := NewMyTask()
if err := scheduler.RegisterTask(task); err != nil {
    log.Printf("注册任务失败: %v", err)
}

// 运行时注销任务
if err := scheduler.UnregisterTask("my_task"); err != nil {
    log.Printf("注销任务失败: %v", err)
}

// 获取任务状态
status := scheduler.GetTaskStatus("my_task")
```

### 任务监控

```go
// 获取调度器状态
manager := scheduler.GetManager()
stats := manager.GetStats()

fmt.Printf("注册任务数: %d\n", stats.TotalTasks)
fmt.Printf("运行中任务: %d\n", stats.RunningTasks)
fmt.Printf("成功执行: %d\n", stats.SuccessCount)
fmt.Printf("失败次数: %d\n", stats.FailureCount)
```

### 自定义日志

```go
import "fincore/utils/log"

func (t *MyTask) Execute(ctx context.Context) error {
    logger := log.GetModule("my_task")
    
    logger.Info("任务开始执行",
        log.String("task_name", t.GetName()),
        log.String("schedule", t.GetSchedule()),
    )
    
    // 业务逻辑...
    
    logger.Info("任务执行完成")
    return nil
}
```

## 🎯 最佳实践

### 1. 任务设计原则

**保持任务幂等性**:
```go
func (t *DataSyncTask) Execute(ctx context.Context) error {
    // 检查是否已经处理过
    if t.isAlreadyProcessed() {
        return nil // 幂等返回
    }

    // 执行业务逻辑
    return t.processData()
}
```

**合理设置超时时间**:
```go
// 根据任务复杂度设置合理的超时时间
baseTask := tasks.NewBaseTask(
    "heavy_task",
    "重型数据处理任务",
    "0 2 * * *",
    2*time.Hour, // 重型任务设置较长超时时间
)
```

**优雅处理上下文取消**:

```go
func (t *MyTask) Execute(ctx context.Context) error {
    for i := 0; i < 1000; i++ {
        // 定期检查上下文状态
        select {
        case <-ctx.Done():
            return ctx.Err()
        default:
        }

        // 执行一小部分工作
        if err := t.processItem(i); err != nil {
            return err
        }
    }
    return nil
}
```

### 2. 性能优化

**批量处理**:
```go
func (t *BatchTask) Execute(ctx context.Context) error {
    const batchSize = 100

    for {
        items, err := t.getNextBatch(batchSize)
        if err != nil {
            return err
        }
        if len(items) == 0 {
            break
        }

        if err := t.processBatch(items); err != nil {
            return err
        }

        // 检查上下文
        select {
        case <-ctx.Done():
            return ctx.Err()
        default:
        }
    }
    return nil
}
```

**资源池化**:
```go
type DatabaseTask struct {
    *tasks.BaseTask
    dbPool *sql.DB // 使用连接池
}

func (t *DatabaseTask) Execute(ctx context.Context) error {
    conn, err := t.dbPool.Conn(ctx)
    if err != nil {
        return err
    }
    defer conn.Close()

    // 使用连接执行数据库操作
    return t.executeQuery(ctx, conn)
}
```

### 3. 错误处理策略

**分类错误处理**:
```go
func (t *MyTask) Execute(ctx context.Context) error {
    if err := t.doWork(); err != nil {
        // 区分可重试和不可重试的错误
        if isRetryableError(err) {
            return err // 触发重试
        } else {
            // 记录错误但不重试
            log.Error("不可重试的错误", zap.Error(err))
            return nil
        }
    }
    return nil
}

func isRetryableError(err error) bool {
    // 网络错误、临时性错误等可以重试
    return errors.Is(err, context.DeadlineExceeded) ||
           errors.Is(err, syscall.ECONNREFUSED)
}
```

## 📊 监控和运维

### 健康检查

```go
// 检查调度器健康状态
func healthCheck() error {
    manager := scheduler.GetManager()
    if manager == nil {
        return errors.New("调度器未初始化")
    }

    if !manager.IsRunning() {
        return errors.New("调度器未运行")
    }

    stats := manager.GetStats()
    if stats.FailureRate > 0.1 { // 失败率超过10%
        return errors.New("任务失败率过高")
    }

    return nil
}
```

### 性能指标

```go
// 获取性能指标
type Metrics struct {
    TotalTasks      int     `json:"total_tasks"`
    RunningTasks    int     `json:"running_tasks"`
    SuccessCount    int64   `json:"success_count"`
    FailureCount    int64   `json:"failure_count"`
    FailureRate     float64 `json:"failure_rate"`
    AvgExecutionTime time.Duration `json:"avg_execution_time"`
}

func getMetrics() *Metrics {
    manager := scheduler.GetManager()
    stats := manager.GetStats()

    return &Metrics{
        TotalTasks:      stats.TotalTasks,
        RunningTasks:    stats.RunningTasks,
        SuccessCount:    stats.SuccessCount,
        FailureCount:    stats.FailureCount,
        FailureRate:     stats.FailureRate,
        AvgExecutionTime: stats.AvgExecutionTime,
    }
}
```

## 🔧 配置参考

### 完整配置示例

```yaml
scheduler:
  # 基本配置
  name: "fincore-scheduler"
  timezone: "Asia/Shanghai"
  max_concurrent_jobs: 20
  enable_metrics: true
  log_level: "info"

  # 任务执行配置
  task_execution:
    default_timeout: "30m"
    max_retry_count: 3
    retry_interval: "5s"
    enable_panic_recovery: true

  # 性能配置
  performance:
    worker_pool_size: 50
    queue_buffer_size: 2000
    gc_interval: "30m"
    memory_limit: "1GB"

  # 监控配置
  monitoring:
    enable_health_check: true
    health_check_interval: "1m"
    metrics_export_interval: "30s"
    slow_task_threshold: "5m"

  # 日志配置
  logging:
    enable_task_logs: true
    log_execution_time: true
    log_memory_usage: true
    max_log_size: "100MB"
```

### 环境变量

```bash
# 环境标识
ENV=dev|test|prod

# 调度器配置
SCHEDULER_NAME=fincore-scheduler
SCHEDULER_TIMEZONE=Asia/Shanghai
SCHEDULER_MAX_CONCURRENT_JOBS=20

# 性能配置
SCHEDULER_WORKER_POOL_SIZE=50
SCHEDULER_MEMORY_LIMIT=**********  # 1GB in bytes
```

## 🚨 故障排除

### 常见问题

**1. 任务不执行**
```bash
# 检查任务是否注册
curl http://localhost:8108/api/scheduler/tasks

# 检查 Cron 表达式
# 使用在线工具验证: https://crontab.guru/

# 检查日志
tail -f logs/scheduler.log
```

**2. 内存使用过高**
```go
// 在任务中添加内存监控
func (t *MyTask) Execute(ctx context.Context) error {
    var m runtime.MemStats
    runtime.ReadMemStats(&m)

    if m.Alloc > 100*1024*1024 { // 100MB
        log.Warn("内存使用过高", zap.Uint64("alloc", m.Alloc))
    }

    // 执行业务逻辑
    return nil
}
```

**3. 任务执行超时**
```go
// 增加超时时间或优化任务逻辑
baseTask.SetTimeout(60 * time.Minute) // 增加到60分钟

// 或者分解任务
func (t *LargeTask) Execute(ctx context.Context) error {
    // 将大任务分解为小任务
    return t.executeInChunks(ctx, 1000) // 每次处理1000条
}
```

## 📈 性能基准

根据压力测试结果：

| 指标 | 性能表现 | 说明 |
|------|----------|------|
| 吞吐量 | 1,538 任务/秒 | 高并发处理能力 |
| 延迟 | 650μs 平均 | 低延迟响应 |
| 内存使用 | 279 bytes/任务 | 内存高效 |
| 并发安全 | 100% 通过 | 无数据竞争 |
| 内存泄漏 | 0 | 无内存泄漏 |
| Goroutine泄漏 | 0 | 无资源泄漏 |

详细测试报告请参考: [STRESS_TEST_REPORT.md](STRESS_TEST_REPORT.md)
