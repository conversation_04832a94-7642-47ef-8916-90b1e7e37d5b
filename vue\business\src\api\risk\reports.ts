import { defHttp } from '@/utils/http';

enum Api {
  getReports = '/risk/riskcontroller/getReports',
}

// 风控报告查询参数
export interface RiskReportParams {
  customer_id?: number;
  start_date?: string;
  end_date?: string;
}

// 风控报告数据结构
export interface RiskReportItem {
  evaluation_id: string;
  risk_score: number;
  risk_result: string;
  credit_limit: number;
  evaluation_time: string;
  raw_data: {
    leida_v4: {
      apply_report_detail: Record<string, string>;
      behavior_report_detail: Record<string, string>;
      current_report_detail: Record<string, string>;
    };
  };
}

// API 响应结构
export interface RiskReportResponse {
  code: number;
  data: RiskReportItem[];
  message?: string;
}

// 获取风控报告列表
export function getRiskReports(params: RiskReportParams) {
  return defHttp.get<RiskReportResponse>(
    { 
      url: Api.getReports, 
      params 
    }, 
    { 
      errorMessageMode: 'message' 
    }
  );
}